package com.xunge.dao.towerrent.mobile.impl;


import com.xunge.core.page.Page;
import com.xunge.dao.AbstractBaseDao;
import com.xunge.dao.towerrent.mobile.ITtToNcmsRentInformationDao;
import com.xunge.filter.PageInterceptor;
import com.xunge.model.towerrent.mobile.TtToNcmsRentInformationVo;

import java.util.List;
import java.util.Map;

public class TtToNcmsRentInformationDaoImpl extends AbstractBaseDao implements ITtToNcmsRentInformationDao {

    final String nameSpage = "com.xunge.dao.towerrent.mobile.TtToNcmsRentInformationMapper.";

    @Override
    public Page<TtToNcmsRentInformationVo> queryByCondition(Map<String, Object> map, int pageNum, int pageSize) {
        PageInterceptor.startPage(pageNum, pageSize);
        this.getSqlSession().selectList(nameSpage + "queryByCondition", map);
        return PageInterceptor.endPage();
    }

    @Override
    public List<TtToNcmsRentInformationVo> exportTtToNcmsRentInfos(Map<String, Object> paraMap) {
        return this.getSqlSession().selectList(nameSpage + "queryByCondition", paraMap);
    }

    @Override
    public TtToNcmsRentInformationVo queryOneTtToNcmsRentInfo(String rentinformationId) {
        return this.getSqlSession().selectOne(nameSpage + "queryOneTtToNcmsRentInfo", rentinformationId);
    }
}
