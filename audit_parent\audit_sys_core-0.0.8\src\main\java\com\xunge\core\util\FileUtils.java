/**
 * Copyright &copy; 2012-2016 <a href="https://github.com/thinkgem/jeesite">JeeSite</a> All rights reserved.
 */
package com.xunge.core.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.MalformedURLException;
import java.net.URL;
import java.net.URLConnection;
import java.net.URLEncoder;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Iterator;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * import org.apache.tools.zip.ZipEntry; import org.apache.tools.zip.ZipFile; import
 * org.apache.tools.zip.ZipOutputStream; import com.google.common.collect.Lists;
 * <p>
 * <p>
 * 文件操作工具类 实现文件的创建、删除、复制、压缩、解压以及目录的创建、删除、复制、压缩解压等功能
 *
 * <AUTHOR>
 * @version 2015-3-16
 */
@Slf4j
public class FileUtils extends org.apache.commons.io.FileUtils {

    private static PropertiesLoader loader = new PropertiesLoader("properties/sysConfig.properties");
    public static final String basePath = loader.getProperty("UploadUrls");

    /**
     * 复制单个文件，如果目标文件存在，则不覆盖
     *
     * @param srcFileName  待复制的文件名
     * @param descFileName 目标文件名
     * @return 如果复制成功，则返回true，否则返回false
     */
    public static boolean copyFile(String srcFileName, String descFileName) {
        return FileUtils.copyFileCover(srcFileName, descFileName, false);
    }

    /**
     * 复制单个文件
     *
     * @param srcFileName  待复制的文件名
     * @param descFileName 目标文件名
     * @param coverlay     如果目标文件已存在，是否覆盖
     * @return 如果复制成功，则返回true，否则返回false
     */
    public static boolean copyFileCover(String srcFileName, String descFileName, boolean coverlay) {
        File srcFile = new File(srcFileName);
        // 判断源文件是否存在
        if (!srcFile.exists()) {
            log.debug("复制文件失败，源文件 " + srcFileName + " 不存在!");
            return false;
        }
        // 判断源文件是否是合法的文件
        else if (!srcFile.isFile()) {
            log.debug("复制文件失败，" + srcFileName + " 不是一个文件!");
            return false;
        }
        File descFile = new File(descFileName);
        // 判断目标文件是否存在
        if (descFile.exists()) {
            // 如果目标文件存在，并且允许覆盖
            if (coverlay) {
                log.debug("目标文件已存在，准备删除!");
                if (!FileUtils.delFile(descFileName)) {
                    log.debug("删除目标文件 " + descFileName + " 失败!");
                    return false;
                }
            } else {
                log.debug("复制文件失败，目标文件 " + descFileName + " 已存在!");
                return false;
            }
        } else {
            if (!descFile.getParentFile().exists()) {
                // 如果目标文件所在的目录不存在，则创建目录
                log.debug("目标文件所在的目录不存在，创建目录!");
                // 创建目标文件所在的目录
                if (!descFile.getParentFile().mkdirs()) {
                    log.debug("创建目标文件所在的目录失败!");
                    return false;
                }
            }
        }

        // 准备复制文件
        // 读取的位数
        int readByte = 0;
        InputStream ins = null;
        OutputStream outs = null;
        try {
            // 打开源文件
            ins = new FileInputStream(srcFile);
            // 打开目标文件的输出流
            outs = new FileOutputStream(descFile);
            byte[] buf = new byte[1024];
            // 一次读取1024个字节，当readByte为-1时表示文件已经读取完毕
            while ((readByte = ins.read(buf)) != -1) {
                // 将读取的字节流写入到输出流
                outs.write(buf, 0, readByte);
            }
            log.debug("复制单个文件 " + srcFileName + " 到" + descFileName + "成功!");
            return true;
        } catch (Exception e) {
            log.debug("复制文件失败：" + e.getMessage());
            return false;
        } finally {
            // 关闭输入输出流，首先关闭输出流，然后再关闭输入流
            if (outs != null) {
                try {
                    outs.close();
                } catch (IOException e) {
                    log.error("FileUtils 出错", e);
                }
            }
            if (ins != null) {
                try {
                    ins.close();
                } catch (IOException e) {
                    log.error("FileUtils 出错", e);
                }
            }
        }
    }

    /**
     * 复制整个目录的内容，如果目标目录存在，则不覆盖
     *
     * @param srcDirName  源目录名
     * @param descDirName 目标目录名
     * @return 如果复制成功返回true，否则返回false
     */
    public static boolean copyDirectory(String srcDirName, String descDirName) {
        return FileUtils.copyDirectoryCover(srcDirName, descDirName, false);
    }

    /**
     * 复制整个目录的内容
     *
     * @param srcDirName  源目录名
     * @param descDirName 目标目录名
     * @param coverlay    如果目标目录存在，是否覆盖
     * @return 如果复制成功返回true，否则返回false
     */
    public static boolean copyDirectoryCover(String srcDirName, String descDirName, boolean coverlay) {
        File srcDir = new File(srcDirName);
        // 判断源目录是否存在
        if (!srcDir.exists()) {
            log.debug("复制目录失败，源目录 " + srcDirName + " 不存在!");
            return false;
        }
        // 判断源目录是否是目录
        else if (!srcDir.isDirectory()) {
            log.debug("复制目录失败，" + srcDirName + " 不是一个目录!");
            return false;
        }
        // 如果目标文件夹名不以文件分隔符结尾，自动添加文件分隔符
        String descDirNames = descDirName;
        if (!descDirNames.endsWith(File.separator)) {
            descDirNames = descDirNames + File.separator;
        }
        File descDir = new File(descDirNames);
        // 如果目标文件夹存在
        if (descDir.exists()) {
            if (coverlay) {
                // 允许覆盖目标目录
                log.debug("目标目录已存在，准备删除!");
                if (!FileUtils.delFile(descDirNames)) {
                    log.debug("删除目录 " + descDirNames + " 失败!");
                    return false;
                }
            } else {
                log.debug("目标目录复制失败，目标目录 " + descDirNames + " 已存在!");
                return false;
            }
        } else {
            // 创建目标目录
            log.debug("目标目录不存在，准备创建!");
            if (!descDir.mkdirs()) {
                log.debug("创建目标目录失败!");
                return false;
            }

        }

        boolean flag = true;
        // 列出源目录下的所有文件名和子目录名
        File[] files = srcDir.listFiles();
        for (int i = 0; i < files.length; i++) {
            // 如果是一个单个文件，则直接复制
            if (files[i].isFile()) {
                flag = FileUtils.copyFile(files[i].getAbsolutePath(), descDirName + files[i].getName());
                // 如果拷贝文件失败，则退出循环
                if (!flag) {
                    break;
                }
            }
            // 如果是子目录，则继续复制目录
            if (files[i].isDirectory()) {
                flag = FileUtils.copyDirectory(files[i].getAbsolutePath(), descDirName + files[i].getName());
                // 如果拷贝目录失败，则退出循环
                if (!flag) {
                    break;
                }
            }
        }

        if (!flag) {
            log.debug("复制目录 " + srcDirName + " 到 " + descDirName + " 失败!");
            return false;
        }
        log.debug("复制目录 " + srcDirName + " 到 " + descDirName + " 成功!");
        return true;

    }

    /**
     * 删除文件，可以删除单个文件或文件夹
     *
     * @param fileName 被删除的文件名
     * @return 如果删除成功，则返回true，否是返回false
     */
    public static boolean delFile(String fileName) {
        File file = new File(fileName);
        if (!file.exists()) {
            log.debug(fileName + " 文件不存在!");
            return true;
        } else {
            if (file.isFile()) {
                return FileUtils.deleteFile(fileName);
            } else {
                return FileUtils.deleteDirectory(fileName);
            }
        }
    }

    /**
     * 删除单个文件
     *
     * @param fileName 被删除的文件名
     * @return 如果删除成功，则返回true，否则返回false
     */
    public static boolean deleteFile(String fileName) {
        File file = new File(fileName);
        if (file.exists() && file.isFile()) {
            if (file.delete()) {
                log.debug("删除文件 " + fileName + " 成功!");
                return true;
            } else {
                log.debug("删除文件 " + fileName + " 失败!");
                return false;
            }
        } else {
            log.debug(fileName + " 文件不存在!");
            return true;
        }
    }

    /**
     * 删除目录及目录下的文件
     *
     * @param dirName 被删除的目录所在的文件路径
     * @return 如果目录删除成功，则返回true，否则返回false
     */
    public static boolean deleteDirectory(String dirName) {
        String dirNames = dirName;
        if (!dirNames.endsWith(File.separator)) {
            dirNames = dirNames + File.separator;
        }
        File dirFile = new File(dirNames);
        if (!dirFile.exists() || !dirFile.isDirectory()) {
            log.debug(dirNames + " 目录不存在!");
            return true;
        }
        boolean flag = true;
        // 列出全部文件及子目录
        File[] files = dirFile.listFiles();
        for (int i = 0; i < files.length; i++) {
            // 删除子文件
            if (files[i].isFile()) {
                flag = FileUtils.deleteFile(files[i].getAbsolutePath());
                // 如果删除文件失败，则退出循环
                if (!flag) {
                    break;
                }
            }
            // 删除子目录
            else if (files[i].isDirectory()) {
                flag = FileUtils.deleteDirectory(files[i].getAbsolutePath());
                // 如果删除子目录失败，则退出循环
                if (!flag) {
                    break;
                }
            }
        }

        if (!flag) {
            log.debug("删除目录失败!");
            return false;
        }
        // 删除当前目录
        if (dirFile.delete()) {
            log.debug("删除目录 " + dirName + " 成功!");
            return true;
        } else {
            log.debug("删除目录 " + dirName + " 失败!");
            return false;
        }

    }

    /**
     * 创建单个文件
     *
     * @param descFileName 文件名，包含路径
     * @return 如果创建成功，则返回true，否则返回false
     */
    public static boolean createFile(String descFileName) {
        File file = new File(descFileName);
        if (file.exists()) {
            log.debug("文件 " + descFileName + " 已存在!");
            return false;
        }
        if (descFileName.endsWith(File.separator)) {
            log.debug(descFileName + " 为目录，不能创建目录!");
            return false;
        }
        if (!file.getParentFile().exists()) {
            // 如果文件所在的目录不存在，则创建目录
            if (!file.getParentFile().mkdirs()) {
                log.debug("创建文件所在的目录失败!");
                return false;
            }
        }

        // 创建文件
        try {
            if (file.createNewFile()) {
                log.debug(descFileName + " 文件创建成功!");
                return true;
            } else {
                log.debug(descFileName + " 文件创建失败!");
                return false;
            }
        } catch (Exception e) {
            log.error(descFileName + " 文件创建失败!", e);
            return false;
        }

    }

    /**
     * 创建目录
     *
     * @param descDirName 目录名,包含路径
     * @return 如果创建成功，则返回true，否则返回false
     */
    public static boolean createDirectory(String descDirName) {
        String descDirNames = descDirName;
        if (!descDirNames.endsWith(File.separator)) {
            descDirNames = descDirNames + File.separator;
        }
        File descDir = new File(descDirNames);
        if (descDir.exists()) {
            log.debug("目录 " + descDirNames + " 已存在!");
            return false;
        }
        // 创建目录
        if (descDir.mkdirs()) {
            log.debug("目录 " + descDirNames + " 创建成功!");
            return true;
        } else {
            log.debug("目录 " + descDirNames + " 创建失败!");
            return false;
        }

    }

    /**
     * 压缩文件或目录
     * @param srcDirName 压缩的根目录
     * @param fileName 根目录下的待压缩的文件名或文件夹名，其中*或""表示跟目录下的全部文件
     * @param descFileName 目标zip文件
     */
    //	public static void zipFiles(String srcDirName, String fileName,
    //			String descFileName) {
    //		// 判断目录是否存在
    //		if (srcDirName == null) {
    //			log.debug("文件压缩失败，目录 " + srcDirName + " 不存在!");
    //			return;
    //		}
    //		File fileDir = new File(srcDirName);
    //		if (!fileDir.exists() || !fileDir.isDirectory()) {
    //			log.debug("文件压缩失败，目录 " + srcDirName + " 不存在!");
    //			return;
    //		}
    //		String dirPath = fileDir.getAbsolutePath();
    //		File descFile = new File(descFileName);
    //		try {
    //			ZipOutputStream zouts = new ZipOutputStream(new FileOutputStream(
    //					descFile));
    //			if ("*".equals(fileName) || "".equals(fileName)) {
    //				FileUtils.zipDirectoryToZipFile(dirPath, fileDir, zouts);
    //			} else {
    //				File file = new File(fileDir, fileName);
    //				if (file.isFile()) {
    //					FileUtils.zipFilesToZipFile(dirPath, file, zouts);
    //				} else {
    //					FileUtils
    //							.zipDirectoryToZipFile(dirPath, file, zouts);
    //				}
    //			}
    //			zouts.close();
    //			log.debug(descFileName + " 文件压缩成功!");
    //		} catch (Exception e) {
    //			log.debug("文件压缩失败",e.getMessage());
    //		}
    //
    //	}

    /**
     * 解压缩ZIP文件，将ZIP文件里的内容解压到descFileName目录下
     * @param zipFileName 需要解压的ZIP文件
     * @param descFileName 目标文件
     */
    //	public static boolean unZipFiles(String zipFileName, String descFileName) {
    //		String descFileNames = descFileName;
    //		if (!descFileNames.endsWith(File.separator)) {
    //			descFileNames = descFileNames + File.separator;
    //		}
    //        try {
    //			// 根据ZIP文件创建ZipFile对象
    //			ZipFile zipFile = new ZipFile(zipFileName);
    //			ZipEntry entry = null;
    //			String entryName = null;
    //			String descFileDir = null;
    //			byte[] buf = new byte[4096];
    //			int readByte = 0;
    //			// 获取ZIP文件里所有的entry
    //			@SuppressWarnings("rawtypes")
    //			Enumeration enums = zipFile.getEntries();
    //			// 遍历所有entry
    //			while (enums.hasMoreElements()) {
    //				entry = (ZipEntry) enums.nextElement();
    //				// 获得entry的名字
    //				entryName = entry.getName();
    //				descFileDir = descFileNames + entryName;
    //				if (entry.isDirectory()) {
    //					// 如果entry是一个目录，则创建目录
    //					new File(descFileDir).mkdirs();
    //					continue;
    //				} else {
    //					// 如果entry是一个文件，则创建父目录
    //					new File(descFileDir).getParentFile().mkdirs();
    //				}
    //				File file = new File(descFileDir);
    //				// 打开文件输出流
    //				OutputStream os = new FileOutputStream(file);
    //				// 从ZipFile对象中打开entry的输入流
    //		        InputStream is = zipFile.getInputStream(entry);
    //				while ((readByte = is.read(buf)) != -1) {
    //					os.write(buf, 0, readByte);
    //				}
    //				os.close();
    //				is.close();
    //			}
    //			zipFile.close();
    //			log.debug("文件解压成功!");
    //			return true;
    //		} catch (Exception e) {
    //			log.debug("文件解压失败：" + e.getMessage());
    //			return false;
    //		}
    //	}

    /**
     * 将目录压缩到ZIP输出流
     * @param dirPath 目录路径
     * @param fileDir 文件信息
     * @param zouts 输出流
     */
    //	public static void zipDirectoryToZipFile(String dirPath, File fileDir, ZipOutputStream zouts) {
    //		if (fileDir.isDirectory()) {
    //			File[] files = fileDir.listFiles();
    //			// 空的文件夹
    //			if (files.length == 0) {
    //				// 目录信息
    //				ZipEntry entry = new ZipEntry(getEntryName(dirPath, fileDir));
    //				try {
    //					zouts.putNextEntry(entry);
    //					zouts.closeEntry();
    //				} catch (Exception e) {
    //					log.error("FileUtils 出错",e);
    //				}
    //				return;
    //			}
    //
    //			for (int i = 0; i < files.length; i++) {
    //				if (files[i].isFile()) {
    //					// 如果是文件，则调用文件压缩方法
    //					FileUtils
    //							.zipFilesToZipFile(dirPath, files[i], zouts);
    //				} else {
    //					// 如果是目录，则递归调用
    //					FileUtils.zipDirectoryToZipFile(dirPath, files[i],
    //							zouts);
    //				}
    //			}
    //		}
    //	}

    /**
     * 将文件压缩到ZIP输出流
     * @param dirPath 目录路径
     * @param file 文件
     * @param zouts 输出流
     */
    //	public static void zipFilesToZipFile(String dirPath, File file, ZipOutputStream zouts) {
    //		FileInputStream fin = null;
    //		ZipEntry entry = null;
    //		// 创建复制缓冲区
    //		byte[] buf = new byte[4096];
    //		int readByte = 0;
    //		if (file.isFile()) {
    //			try {
    //				// 创建一个文件输入流
    //				fin = new FileInputStream(file);
    //				// 创建一个ZipEntry
    //				entry = new ZipEntry(getEntryName(dirPath, file));
    //				// 存储信息到压缩文件
    //				zouts.putNextEntry(entry);
    //				// 复制字节到压缩文件
    //				while ((readByte = fin.read(buf)) != -1) {
    //					zouts.write(buf, 0, readByte);
    //				}
    //				zouts.closeEntry();
    //				fin.close();
    //				System.out
    //						.println("添加文件 " + file.getAbsolutePath() + " 到zip文件中!");
    //			} catch (Exception e) {
    //				log.error("FileUtils 出错",e);
    //			}
    //		}
    //	}

    /**
     * 写入文件
     *
     * @param fileName 要写入的文件
     */
    public static void writeToFile(String fileName, String content, boolean append) {
        try {
            FileUtils.write(new File(fileName), content, "UTF-8", append);
            log.debug("文件 " + fileName + " 写入成功!");
        } catch (IOException e) {
            log.debug("文件 " + fileName + " 写入失败! " + e.getMessage());
        }
    }

    /**
     * 写入文件
     *
     * @param fileName 要写入的文件
     */
    public static void writeToFile(String fileName, String content, String encoding, boolean append) {
        try {
            FileUtils.write(new File(fileName), content, encoding, append);
            log.debug("文件 " + fileName + " 写入成功!");
        } catch (IOException e) {
            log.debug("文件 " + fileName + " 写入失败! " + e.getMessage());
        }
    }

    /**
     * 获取待压缩文件在ZIP文件中entry的名字，即相对于跟目录的相对路径名
     *
     * @param dirPath 目录名
     * @param file    entry文件名
     * @return
     */
    private static String getEntryName(String dirPath, File file) {
        String dirPaths = dirPath;
        if (!dirPaths.endsWith(File.separator)) {
            dirPaths = dirPaths + File.separator;
        }
        String filePath = file.getAbsolutePath();
        // 对于目录，必须在entry名字后面加上"/"，表示它将以目录项存储
        if (file.isDirectory()) {
            filePath += "/";
        }
        int index = filePath.indexOf(dirPaths);

        return filePath.substring(index + dirPaths.length());
    }

    //	/**
    //	 * 获目录下的文件列表
    //	 * @param dir 搜索目录
    //	 * @param searchDirs 是否是搜索目录
    //	 * @return 文件列表
    //	 */
    //	public static List<String> findChildrenList(File dir, boolean searchDirs) {
    //		List<String> files = Lists.newArrayList();
    //		for (String subFiles : dir.list()) {
    //			File file = new File(dir + "/" + subFiles);
    //			if (((searchDirs) && (file.isDirectory())) || ((!searchDirs) && (!file.isDirectory()))) {
    //				files.add(file.getName());
    //			}
    //		}
    //		return files;
    //	}

    /**
     * 根据“文件名的后缀”获取文件内容类型（而非根据File.getContentType()读取的文件类型）
     *
     * @param returnFileName 带验证的文件名
     * @return 返回文件类型
     */
    public static String getContentType(String returnFileName) {
        String contentType = "application/octet-stream";
        if (returnFileName.lastIndexOf(".") < 0) {
            return contentType;
        }
        returnFileName = returnFileName.toLowerCase();
        returnFileName = returnFileName.substring(returnFileName.lastIndexOf(".") + 1);
        if (returnFileName.equals("html") || returnFileName.equals("htm") || returnFileName.equals("shtml")) {
            contentType = "text/html";
        } else if (returnFileName.equals("apk")) {
            contentType = "application/vnd.android.package-archive";
        } else if (returnFileName.equals("sis")) {
            contentType = "application/vnd.symbian.install";
        } else if (returnFileName.equals("sisx")) {
            contentType = "application/vnd.symbian.install";
        } else if (returnFileName.equals("exe")) {
            contentType = "application/x-msdownload";
        } else if (returnFileName.equals("msi")) {
            contentType = "application/x-msdownload";
        } else if (returnFileName.equals("css")) {
            contentType = "text/css";
        } else if (returnFileName.equals("xml")) {
            contentType = "text/xml";
        } else if (returnFileName.equals("gif")) {
            contentType = "image/gif";
        } else if (returnFileName.equals("jpeg") || returnFileName.equals("jpg")) {
            contentType = "image/jpeg";
        } else if (returnFileName.equals("js")) {
            contentType = "application/x-javascript";
        } else if (returnFileName.equals("atom")) {
            contentType = "application/atom+xml";
        } else if (returnFileName.equals("rss")) {
            contentType = "application/rss+xml";
        } else if (returnFileName.equals("mml")) {
            contentType = "text/mathml";
        } else if (returnFileName.equals("txt")) {
            contentType = "text/plain";
        } else if (returnFileName.equals("jad")) {
            contentType = "text/vnd.sun.j2me.app-descriptor";
        } else if (returnFileName.equals("wml")) {
            contentType = "text/vnd.wap.wml";
        } else if (returnFileName.equals("htc")) {
            contentType = "text/x-component";
        } else if (returnFileName.equals("png")) {
            contentType = "image/png";
        } else if (returnFileName.equals("tif") || returnFileName.equals("tiff")) {
            contentType = "image/tiff";
        } else if (returnFileName.equals("wbmp")) {
            contentType = "image/vnd.wap.wbmp";
        } else if (returnFileName.equals("ico")) {
            contentType = "image/x-icon";
        } else if (returnFileName.equals("jng")) {
            contentType = "image/x-jng";
        } else if (returnFileName.equals("bmp")) {
            contentType = "image/x-ms-bmp";
        } else if (returnFileName.equals("svg")) {
            contentType = "image/svg+xml";
        } else if (returnFileName.equals("jar") || returnFileName.equals("var") || returnFileName.equals("ear")) {
            contentType = "application/java-archive";
        } else if (returnFileName.equals("doc")) {
            contentType = "application/msword";
        } else if (returnFileName.equals("pdf")) {
            contentType = "application/pdf";
        } else if (returnFileName.equals("rtf")) {
            contentType = "application/rtf";
        } else if (returnFileName.equals("xls")) {
            contentType = "application/vnd.ms-excel";
        } else if (returnFileName.equals("ppt")) {
            contentType = "application/vnd.ms-powerpoint";
        } else if (returnFileName.equals("7z")) {
            contentType = "application/x-7z-compressed";
        } else if (returnFileName.equals("rar")) {
            contentType = "application/x-rar-compressed";
        } else if (returnFileName.equals("swf")) {
            contentType = "application/x-shockwave-flash";
        } else if (returnFileName.equals("rpm")) {
            contentType = "application/x-redhat-package-manager";
        } else if (returnFileName.equals("der") || returnFileName.equals("pem") || returnFileName.equals("crt")) {
            contentType = "application/x-x509-ca-cert";
        } else if (returnFileName.equals("xhtml")) {
            contentType = "application/xhtml+xml";
        } else if (returnFileName.equals("zip")) {
            contentType = "application/zip";
        } else if (returnFileName.equals("mid") || returnFileName.equals("midi") || returnFileName.equals("kar")) {
            contentType = "audio/midi";
        } else if (returnFileName.equals("mp3")) {
            contentType = "audio/mpeg";
        } else if (returnFileName.equals("ogg")) {
            contentType = "audio/ogg";
        } else if (returnFileName.equals("m4a")) {
            contentType = "audio/x-m4a";
        } else if (returnFileName.equals("ra")) {
            contentType = "audio/x-realaudio";
        } else if (returnFileName.equals("3gpp") || returnFileName.equals("3gp")) {
            contentType = "video/3gpp";
        } else if (returnFileName.equals("mp4")) {
            contentType = "video/mp4";
        } else if (returnFileName.equals("mpeg") || returnFileName.equals("mpg")) {
            contentType = "video/mpeg";
        } else if (returnFileName.equals("mov")) {
            contentType = "video/quicktime";
        } else if (returnFileName.equals("flv")) {
            contentType = "video/x-flv";
        } else if (returnFileName.equals("m4v")) {
            contentType = "video/x-m4v";
        } else if (returnFileName.equals("mng")) {
            contentType = "video/x-mng";
        } else if (returnFileName.equals("asx") || returnFileName.equals("asf")) {
            contentType = "video/x-ms-asf";
        } else if (returnFileName.equals("wmv")) {
            contentType = "video/x-ms-wmv";
        } else if (returnFileName.equals("avi")) {
            contentType = "video/x-msvideo";
        }
        return contentType;
    }

    /**
     * 修正路径，将 \\ 或 / 等替换为 File.separator
     *
     * @param path 待修正的路径
     * @return 修正后的路径
     */
    public static String path(String path) {
        String p = org.apache.commons.lang3.StringUtils.replace(path, "\\", "/");
        p = org.apache.commons.lang3.StringUtils.join(StrUtil.split(p, "/"), "/");
        if (!org.apache.commons.lang3.StringUtils.startsWithAny(p, "/") && org.apache.commons.lang3.StringUtils.startsWithAny(path, "\\", "/")) {
            p += "/";
        }
        if (!org.apache.commons.lang3.StringUtils.endsWithAny(p, "/") && org.apache.commons.lang3.StringUtils.endsWithAny(path, "\\", "/")) {
            p = p + "/";
        }
        if (path != null && path.startsWith("/")) {
            p = "/" + p; // linux下路径
        }
        return p;
    }

    /**
     * 获取文件扩展名(返回小写)
     *
     * @param fileName 文件名
     * @return 例如：test.jpg 返回： jpg
     */
    public static String getFileExtension(String fileName) {
        if ((fileName == null) || (fileName.lastIndexOf(".") == -1) || (fileName.lastIndexOf(".") == fileName.length() - 1)) {
            return null;
        }
        return org.apache.commons.lang3.StringUtils.lowerCase(fileName.substring(fileName.lastIndexOf(".") + 1));
    }

    /**
     * 获取文件名，不包含扩展名
     *
     * @param fileName 文件名
     * @return 例如：d:\files\test.jpg 返回：d:\files\test
     */
    public static String getFileNameWithoutExtension(String fileName) {
        if ((fileName == null) || (fileName.lastIndexOf(".") == -1)) {
            return null;
        }
        return fileName.substring(0, fileName.lastIndexOf("."));
    }

    /**
     * 向浏览器发送文件下载，支持断点续传
     *
     * @param fileName 要下载的文件
     * @param request  请求对象
     * @param response 响应对象
     * @return 返回错误信息，无错误信息返回null
     */
    public static String downFile(Workbook workBook, String fileName, HttpServletRequest request, HttpServletResponse response) {

        response.reset();
        OutputStream out = null;
        try {
            request.setCharacterEncoding("UTF-8");
            response.setCharacterEncoding("UTF-8");
            response.setContentType("application/x-download");
            //            fileName = URLEncoder.encode(fileName, "UTF-8");

			/*if (request.getHeader("User-Agent").toUpperCase().indexOf("MSIE") > 0) {
				fileName = URLEncoder.encode(fileName, "UTF-8");
			}
			else {
				fileName = new String(fileName.getBytes("UTF-8"), "ISO8859-1");
			}*/

            String userAgent = request.getHeader("USER-AGENT");
            //IE浏览器 window10 Edge
            if (StringUtils.contains(userAgent, "MSIE")
                    || StringUtils.contains(userAgent, "Trident")
                    || StringUtils.contains(userAgent, "Edge")) {
                fileName = URLEncoder.encode(fileName, "UTF-8");
                //google,火狐浏览器
            } else if (StringUtils.contains(userAgent, "Mozilla")) {
                fileName = new String(fileName.getBytes(), "ISO8859-1");
            } else {
                //其他浏览器
                fileName = URLEncoder.encode(fileName, "UTF-8");
            }

            response.addHeader("Content-Disposition", "attachment;filename=" + fileName);
            out = response.getOutputStream();
            workBook.write(out);
            out.flush();
        } catch (Exception e) {
            log.error("FileUtils 出错", e);
        } finally {

            try {
                if (out != null) {
                    out.close();
                }
            } catch (IOException e) {
                log.error("FileUtils 出错", e);
            }
        }
        return null;

    }

    /**
     * 文件下载
     *
     * @param path 文件路径
     * @param name 重新命名文件，如果不传则使用path指定的文件名
     */
    public static HttpServletResponse download(String path, String name, HttpServletRequest request, HttpServletResponse response) throws Exception {
        OutputStream toClient = null;
        InputStream fis = null;
        try {
            response.setCharacterEncoding("UTF-8");
            response.setContentType("application/x-download");
            //指定为文件系统路径
            path = basePath + path;
            // path是指欲下载的文件的路径。
            File file = new File(path);
            // 取得文件名。
            String filename = file.getName();
            // 取得文件的后缀名。
            String ext = filename.substring(filename.lastIndexOf(".") + 1).toUpperCase();
            if (StrUtil.isNotBlank(name)) {//如果有新名称，则重命名
                filename = name;
            }

            if (request.getHeader("User-Agent").toUpperCase().indexOf("MSIE") > 0) {
                response.addHeader("Content-Disposition", "attachment;filename=\"" + URLEncoder.encode(filename, "UTF-8") + "\"");
            } else {
                response.addHeader("Content-Disposition", "attachment;filename=\"" + new String(filename.getBytes("UTF-8"), "ISO8859-1") + "\"");
            }

			/*// 清空response
			response.reset();*/
            // 设置response的Header
            //response.addHeader("Content-Disposition", "attachment;filename=" + filename);
            response.addHeader("Content-Length", "" + file.length());
            response.setContentType("application/octet-stream");
            // 以流的形式下载文件。
            fis = new BufferedInputStream(new FileInputStream(path));
            toClient = new BufferedOutputStream(response.getOutputStream());
            byte[] buffer = new byte[fis.available()];
            //循环将输入流中的内容读取到缓冲区当中
            while (true) {
                int read = fis.read(buffer);
                //判断是不是读到了数据流的末尾 ，防止出现死循环。
                if (read == -1) {
                    break;
                }
            }
            //输出缓冲区的内容到浏览器，实现文件下载
            toClient.write(buffer, 0, buffer.length);
            toClient.flush();
        } catch (IOException e) {
            log.error("FileUtils 出错", e);
            throw e;
        } finally {

            try {
                if (toClient != null) {
                    toClient.close();
                }
            } catch (IOException e) {
                log.error("FileUtils 出错", e);
            }
            try {
                if (fis != null) {
                    fis.close();
                }
            } catch (IOException e) {
                log.error("FileUtils 出错", e);
            }
        }
        return response;
    }

    //绝对路径下载文件
    public static HttpServletResponse downloadByFilePath(String path, String name, HttpServletRequest request, HttpServletResponse response) {
        InputStream fis = null;
        OutputStream toClient = null;
        try {
            response.setCharacterEncoding("UTF-8");
            response.setContentType("application/x-download");
            //指定为文件系统路径

            // path是指欲下载的文件的路径。
            File file = new File(path);
            // 取得文件名。
            String filename = file.getName();
            // 取得文件的后缀名。
            String ext = filename.substring(filename.lastIndexOf(".") + 1).toUpperCase();
            if (StrUtil.isNotBlank(name)) {//如果有新名称，则重命名
                filename = name;
            }

            if (request.getHeader("User-Agent").toUpperCase().indexOf("MSIE") > 0) {
                response.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(filename, "UTF-8"));
            } else {
                response.addHeader("Content-Disposition", "attachment;filename=" + new String(filename.getBytes("UTF-8"), "ISO8859-1"));
            }

			/*// 清空response
			response.reset();*/
            // 设置response的Header
            //response.addHeader("Content-Disposition", "attachment;filename=" + filename);
            response.addHeader("Content-Length", "" + file.length());
            response.setContentType("application/octet-stream");
            // 以流的形式下载文件。
            fis = new BufferedInputStream(new FileInputStream(path));
            toClient = new BufferedOutputStream(response.getOutputStream());
            byte[] buffer = new byte[fis.available()];
            //循环将输入流中的内容读取到缓冲区当中
            while (true) {
                int read = fis.read(buffer);
                //判断是不是读到了数据流的末尾 ，防止出现死循环。
                if (read == -1 || read == 0) {
                    break;
                }
            }
            //输出缓冲区的内容到浏览器，实现文件下载
            toClient.write(buffer, 0, buffer.length);
            toClient.flush();
        } catch (IOException e) {
            log.error("FileUtils 出错", e);
        } finally {

            try {
                if (toClient != null) {
                    toClient.close();
                }
            } catch (IOException e) {
                log.error("FileUtils 出错", e);
            }
            try {
                if (toClient != null) {
                    fis.close();
                }
            } catch (IOException e) {
                log.error("FileUtils 出错", e);
            }
        }
        return response;
    }

    /**
     * 校验上传的文件后缀名是否在系统允许范围内
     *
     * @param suffixName
     * @return
     */
    public static boolean validataFileSuffix(String suffixName) {
        PropertiesLoader loader = new PropertiesLoader("properties/sysConfig.properties");
        String subffixStr = loader.getProperty("NCMS_COMMON_FILE_SUFFIX");
        List<String> NCMS_COMMON_FILE_SUFFIX = new ArrayList<>();
        NCMS_COMMON_FILE_SUFFIX.addAll(Arrays.asList(subffixStr.split(",")));
        if (StringUtils.isNotBlank(suffixName)) {
            return NCMS_COMMON_FILE_SUFFIX.contains(suffixName);
        }
        return false;
    }

    /**
     * 校验上传的文件后缀名是否在系统允许范围内
     * 自定义 在配置文件中对应的规则
     *
     * @param suffixName
     * @return
     */
    public static boolean validataFileSuffixByCustom(String suffixName, String SuffixProperties) {
        PropertiesLoader loader = new PropertiesLoader("properties/sysConfig.properties");
        String subffixStr = loader.getProperty(SuffixProperties);
        List<String> NCMS_COMMON_FILE_SUFFIX = new ArrayList<>();
        NCMS_COMMON_FILE_SUFFIX.addAll(Arrays.asList(subffixStr.split(",")));
        if (StringUtils.isNotBlank(suffixName)) {
            return NCMS_COMMON_FILE_SUFFIX.contains(suffixName);
        }
        return false;
    }

    public static DataOutputStream getDataOutputStream(String fileName, HttpServletRequest request, HttpServletResponse response) throws IOException {
        response.reset();
        request.setCharacterEncoding("UTF-8");
        response.setCharacterEncoding("UTF-8");
        response.setContentType("application/x-download");
        String userAgent = request.getHeader("USER-AGENT");
        //IE浏览器 window10 Edge
        if (StringUtils.contains(userAgent, "MSIE")
                || StringUtils.contains(userAgent, "Trident")
                || StringUtils.contains(userAgent, "Edge")) {
            fileName = URLEncoder.encode(fileName, "UTF-8");
            //google,火狐浏览器
        } else if (StringUtils.contains(userAgent, "Mozilla")) {
            fileName = new String(fileName.getBytes(), "ISO8859-1");
        } else {
            //其他浏览器
            fileName = URLEncoder.encode(fileName, "UTF-8");
        }
        response.addHeader("Content-Disposition", "attachment;filename=" + fileName);
        DataOutputStream bos = new DataOutputStream(response.getOutputStream());
        bos.write(new byte[]{(byte) 0xEF, (byte) 0xBB, (byte) 0xBF});
        return bos;
    }

    public static void writeOutStream(String content, DataOutputStream bos) throws UnsupportedEncodingException, IOException {
        bos.write(content.getBytes("utf-8"));
        bos.writeBytes("\r\n");
    }

    public static void flush(DataOutputStream bos) throws IOException {
        bos.flush();
    }

    public void downloadLocal(HttpServletResponse response) throws FileNotFoundException {
        // 下载本地文件
        String fileName = "Operator.doc".toString(); // 文件的默认保存名
        InputStream inStream = null;
        try {
            // 读到流中
            inStream = new FileInputStream("c:/Operator.doc");// 文件的存放路径
            // 设置输出的格式
            response.reset();
            response.setContentType("bin");
            response.addHeader("Content-Disposition", "attachment; filename=\"" + fileName + "\"");
            // 循环取出流中的数据
            byte[] b = new byte[100];
            int len;
            while ((len = inStream.read(b)) > 0) {
                response.getOutputStream().write(b, 0, len);
            }
        } catch (IOException e) {
            log.error("FileUtils 出错", e);
        }finally {
            try {
                if (inStream!=null) {
                    inStream.close();
                }
            }catch (Exception e){
                log.error("FileUtils 出错", e);
            }

        }
    }

    public void downloadNet(HttpServletResponse response) throws MalformedURLException {
        // 下载网络文件
        int bytesum = 0;
        int byteread = 0;

        URL url = new URL("windine.blogdriver.com/logo.gif");
        FileOutputStream fs =null;
        try {
            URLConnection conn = url.openConnection();
            InputStream inStream = conn.getInputStream();
            fs = new FileOutputStream("c:/abc.gif");

            byte[] buffer = new byte[1204];
            int length;
            while ((byteread = inStream.read(buffer)) != -1) {
                bytesum += byteread;
                System.out.println(bytesum);
                fs.write(buffer, 0, byteread);
            }
        } catch (FileNotFoundException e) {
            log.error("FileUtils 出错", e);
        } catch (IOException e) {
            log.error("FileUtils 出错", e);
        }finally {
            try {
                if (fs!=null){
                    fs.close();
                }
            }catch (Exception e){
                log.error("FileUtils 出错", e);
            }

        }
    }

    /**
     * 获取整个目录下的文件
     */
    public static List<String> getDirectoryFiles(String directoryPath) {
        try (Stream<Path> walk = Files.walk(Paths.get(directoryPath))){
            List<String> result = walk.filter(Files::isRegularFile)
                    .map(x -> x.toString()).collect(Collectors.toList());
            return result;
        }catch (Exception e){
            log.error("FileUtils getDirectoryFiles 出错", e);
        }
        return null;
    }
    /**
     * 获取文件夹下指定类型的文件路径
     */
    public static List<String> getDirectoryFileTypes(String directoryPath,String[] fileTypes){
        List<String> result = getDirectoryFiles(directoryPath);
        if (CollectionUtil.isNotEmpty(result) && null!=fileTypes){
            List<String> types = new ArrayList<>(Arrays.asList(fileTypes));

            Iterator<String> iterator = result.iterator();// NOSONAR
            while (iterator.hasNext()){
                String name = iterator.next();
                name = name.substring(name.lastIndexOf(".")+1,name.length()).toUpperCase();
                if (!types.contains(name)){
                    iterator.remove();
                }
            }
        }
        return result;
    }

    /**
     * 将多个路径下源文件拷贝到另一个路径下
     * @param sourcePathList 源文件路径集合
     * @param destPath 目标文件路径
     * @throws IOException IO异常
     */
    public static void copyFile(List<String> sourcePathList, String destPath){
        try {
            for (String filePath : sourcePathList) {
                File oldFile = new File(filePath);
                File newFile = new File(destPath + File.separator + oldFile.getName());
                if(!newFile.getParentFile().exists()){
                    newFile.getParentFile().mkdirs();
                }
                if(!newFile.exists()){
                    newFile.createNewFile();
                }

                if(!newFile.exists()){
                    Files.copy(oldFile.toPath(), newFile.toPath());
                }else{
                    newFile.delete();
                    Files.copy(oldFile.toPath(), newFile.toPath());
                }
            }
        }catch (IOException e){
            log.error(e.getMessage(), e);
        }
    }
}
