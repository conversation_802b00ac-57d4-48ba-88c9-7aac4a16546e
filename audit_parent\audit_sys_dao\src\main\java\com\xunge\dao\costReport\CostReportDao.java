package com.xunge.dao.costReport;

import com.xunge.model.Code;

import java.util.List;
import java.util.Map;

public interface CostReportDao {

    void insert(List<Map<String, Object>> datas);

    void insertCode(List<Code> datas);

    List<Map<String, Object>> getTotalCompany(Map<String, Object> params);

    List<Map<String, Object>> queryCostReportMonth(Map<String, Object> params);

    List<Map<String, Object>> queryCostReoprtLastYear(Map<String, Object> params);

    List<Map<String, Object>> getCompany(String prvCode);

    List<Map<String, Object>> queryByCompanyCodeAndDate(Map<String, Object> params);

    void deleteByDate(List<String> list);

    void insertStatu(Map<String, Object> map);

    void updataStatu(Map<String, Object> map);


    List<Map<String, Object>> findSStatus(Map<String, Object> map);

    List<Map<String, Object>> selectLogs(Map<String, Object> map);
}

