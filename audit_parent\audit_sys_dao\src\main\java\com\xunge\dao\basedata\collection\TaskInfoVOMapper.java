package com.xunge.dao.basedata.collection;

import com.xunge.model.activity.Act;
import com.xunge.model.basedata.colletion.DseleInterfaceBillCost;
import com.xunge.model.basedata.colletion.TaskInfo;
import com.xunge.model.basedata.colletion.TaskInfoVO;
import com.xunge.model.portal.ActPushErr;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

public interface TaskInfoVOMapper {
    boolean deleteByPrimaryKey(String taskId);

    boolean insert(TaskInfoVO record);

    boolean insertSelective(TaskInfoVO record);

    TaskInfoVO selectByPrimaryKey(String taskId);

    boolean updateByPrimaryKeySelective(TaskInfoVO record);

    boolean updateByPrimaryKey(TaskInfoVO record);

    /**
     * 根据条件获取任务
     */
    List<TaskInfoVO> getTaskInfoByCollectionType(TaskInfoVO task);

    List<TaskInfoVO> selectByStatus(Integer status);

    /**
     * 根据接口路径模糊查询
     */
    public List<TaskInfoVO> queryTaskInfo(Map<String, Object> map);

    /*****************************************/
    //统一门户推送错误记录
    void addPortalPushErr(ActPushErr pushErr);

    void delPortalPushErr(String itemId);

    List<ActPushErr> queryToDoPushErr();

    void addPortalDrafter(@Param("key") String key, @Param("userId") String userId);

    String getPortalDrafter(String key);

    void updPortalDrafter(@Param("key") String key, @Param("userId") String userId);

    void addPortalLogInfo(@Param("id") String id, @Param("type") String type, @Param("msg") String msg);
    /**
     * 条件查询直供电接口采集账单运行数据
     * @param paraMap
     * @return
     */
    List<DseleInterfaceBillCost> queryDseleInterfaceBillCost(Map<String, Object> paraMap);

    /**
     * 查询导出/勾选导出直供电接口采集账单运行数据
     * @param paraMap
     * @return
     */
    List<DseleInterfaceBillCost> exportDseleInterfaceBillCost(Map<String, Object> paraMap);

    /**
     * 根据definitionIds查询Task列表
     */
    List<Act> queryTaskByDefinitionId(@Param("definitionIds") List<String> definitionIds);

    List<TaskInfo> queryAuditingActInfo(@Param("businessKeyList") List<String> businessKeyList);

    /**
     * 根据业务表查询所有某个业务存在待办的当前流程审核人
     * @param  businessTable
     * @return
     */
    List<Act> queryCurrentAuditUser(@Param("businessTable") String businessTable);

    /**
     * 查询后付费报账金额
     * @param billaccountpaymentdetailId
     * @return
     */
    BigDecimal selectElePaymentBillAmount(String billaccountpaymentdetailId);
    /**
     * 查询塔维后付费报账金额
     * @param billaccountpaymentdetailId
     * @return
     */
    BigDecimal selectTelePaymentBillAmount(String billaccountpaymentdetailId);
    /**
     * 查询核销报账金额
     * @param verificationId
     * @return
     */
    BigDecimal selectEleVerificationBillAmount(String verificationId);
    /**
     * 查询预付费报账金额
     * @param loanId
     * @return
     */
    BigDecimal selectEleLoanBillAmount(String loanId);
}