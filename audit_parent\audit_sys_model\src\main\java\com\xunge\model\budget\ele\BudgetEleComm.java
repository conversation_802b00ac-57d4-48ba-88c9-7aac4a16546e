package com.xunge.model.budget.ele;

/**
 * @author: Liang<PERSON><PERSON>g
 * Date: 2022/7/5 14:44
 * Description: 预算管理电费公共配置类
 */
public class BudgetEleComm {

    /**
     * 成本影响默认值
     */
    public static final String[] COSTAFFECTS = new String[]{"年转改直影响","年电价调整影响","年IDC机房电费调整影响"};

    public static final Integer[] SITE_TYPE1 = new Integer[]{1,2,3,4,5,6,7,8,10};

    public static final Double[] SITE_TYPE2 = new Double[]{1d,2d,3d,3.1,3.2,4d,5d,6d,7d,8d,10d};

    public static final String[] PRV_ID_ALL= new String[]{"110000","120000","130000","140000","150000","210000","220000","230000","310000","320000","330000","340000","350000","360000","370000","410000","420000","430000","440000","450000","460000","500000","510000","520000","530000","540000","610000","620000","630000","640000","650000","710000"};
    public static final String[] PRV_NAME_ALL = new String[]{"北京","天津","河北","山西","内蒙","辽宁","吉林","黑龙江","上海","江苏","浙江","安徽","福建","江西","山东","河南","湖北","湖南","广东","广西","海南","重庆","四川","贵州","云南","西藏","陕西","甘肃","青海","宁夏","新疆","台湾"};

    /**
     * 站点类型枚举值转中文名称
     * @param siteType 站点类型枚举值
     * @return 中文名称
     */
    public static String siteTypeToName(Double siteType){
        if (siteType == 0) {
            return "合计";
        } else if (siteType == 1) {
            return "核心机楼";
        } else if (siteType == 2) {
            return "汇聚传输站点";
        } else if (siteType == 3) {
            return "基站";
        } else if (siteType == 4) {
            return "室分及WLAN";
        } else if (siteType == 5) {
            return "家客集客";
        } else if (siteType == 6) {
            return "IDC机房";
        } else if (siteType == 7) {
            return "基地";
        } else if (siteType == 8) {
            return "其他";
        } else if (siteType == 3.1) {
            return "基站-5G 700M设备";
        } else if (siteType == 3.2) {
            return "基站-5G 2.6G设备";
        } else if (siteType == 10) {
            return "汇总";
        }
        return "-";
    }
    public static Double siteNameToSiteType(String siteType){
        if (siteType.equals("合计")) {
            return 0.0;
        } else if (siteType.equals("核心机楼")) {
            return 1.0;
        } else if (siteType.equals("汇聚传输站点")) {
            return 2.0;
        } else if (siteType.equals("基站")) {
            return 3.0;
        } else if (siteType.equals("室分及WLAN")) {
            return 4.0;
        } else if (siteType.equals("家客集客")) {
            return 5.0;
        } else if (siteType.equals("IDC机房")) {
            return 6.0;
        } else if (siteType.equals("基地")) {
            return 7.0;
        } else if (siteType.equals("其他")) {
            return 8.0;
        } else if (siteType.equals("基站-5G 700M设备")) {
            return 3.1;
        } else if (siteType.equals("基站-5G 2.6G设备")) {
            return 3.2;
        } else if (siteType.equals("汇总")) {
            return 10.0;
        }
        return null;
    }


    /**
     * 规模校正表 站点类型单位
     * @param siteType 站点类型
     * @return 单位
     */
    public static String siteTypetoScaleUnit(Double siteType){
        if (siteType==1||siteType==7){
            return "度/月";
        }else if (siteType==5){
            return "台";
        }else if (siteType==6){
            return "机架";
        }else if(siteType==2||siteType==3||siteType==3.1||siteType==3.2||siteType==4){
            return "站";
        }
        return "";
    }


    public static String prvNameToprvId(String prvName){
        for (int i = 0; i < PRV_NAME_ALL.length; i++) {
            if (prvName.equals(PRV_NAME_ALL[i])){
                return PRV_ID_ALL[i];
            }
        }
        return null;
    }





}
