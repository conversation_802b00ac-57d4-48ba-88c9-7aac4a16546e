package com.xunge.dao.system.dictionary;

import com.xunge.core.page.Page;
import com.xunge.model.system.dictionary.AttachmentRequiredVO;
import com.xunge.model.system.dictionary.DictionaryGroupVO;
import com.xunge.model.system.dictionary.DictionaryVO;

import java.util.List;
import java.util.Map;

public interface IDictionaryDao {

    /**
     * 查询数据字典分组列表，根据传入的省份ID
     *
     * @return
     */
    public List<DictionaryGroupVO> queryAllDictionaryGroup(String prv_id);

    /**
     * 根据条件查询所有数据字典列表
     *
     * @param dictionaryVO
     * @return
     */
    public Page<List<DictionaryVO>> queryDictionary(Map<String, Object> param, int pageNumber, int pageSize);

    /**
     * 根据条件查询所有数据字典列表
     *
     * @param dictionaryVO
     * @return
     */
    public DictionaryVO queryDictionaryByID(Map<String, Object> param);

    public List<DictionaryVO> queryDictionaryByName(Map<String, Object> param);

    /**
     * 根据字典codes查询字典组
     *
     * @param param
     * @return
     */
    List<DictionaryGroupVO> queryDictionarysByCodes(Map<String, Object> param);

    /**
     * 新增一条数据字典数据
     *
     * @param dictionaryVO
     * @return
     */
    public int insertDictionary(DictionaryVO dictionaryVO);

    /**
     * 批量更新数据字典状态
     *
     * @param dictionaryVO
     * @return
     */
    public int updateDictionaryStateBatch(Map<String, Object> paramMap);

    /**
     * 更新一条数据字典数据
     *
     * @param dictionaryVO
     * @return
     */
    public int updateDictionary(DictionaryVO dictionaryVO);

    /**
     * 更新数据字典数据
     *
     * @param dictionaryVO
     * @return
     */
    public int updateDictionaryByName(DictionaryVO dictionaryVO);

    /**
     * 更新值和状态
     * @param dictionaryVO
     * @return
     */
    public int updateValueAndStateByName(DictionaryVO dictionaryVO);

    /**
     * 删除一组数据字典
     *
     * @param dictgroup_id
     * @return
     */
    public int delDictionaryByGroupID(String dictgroup_id);

    /**
     * 根据字典编码查询所有数据字典列表
     *
     * @param dictgroup_code
     * @return
     */
    public List<Map<String, Object>> queryDictionaryByCode(Map<String, Object> map);

    /**
     * 查询字典分组以及分组下所有数据
     *
     * @param prv_id
     * @return
     */
    public List<DictionaryGroupVO> queryDictionaryGroup(String prv_id);

    /**
     * 根据DictName删除数据字典数据
     *
     * @param cmccRatioDiff
     */
    int deleteDictionaryByDictName(String cmccRatioDiff);

    /**
     * 根据条件查询字典
     *
     * @param dictionary
     * @return
     */
    DictionaryVO queryDictionaryByCondition(DictionaryVO dictionary);

    /**
     * 根据条件查询字典
     *
     * @param dictionaryList
     * @return
     */
    List<DictionaryVO> queryDictionaryListByCondition(List<DictionaryVO> dictionaryList);

    /**
     * 根据名称查询字典
     *
     * @param names names
     * @return List<DictionaryVO>
     */
    List<DictionaryVO> queryDictionaryListByName(List<String> names);

    /**
     * 新增网络电费必填附件设置
     * @param attachmentRequiredVO
     * @return
     */
    int insertAttachmentRequired(AttachmentRequiredVO attachmentRequiredVO);

    /**
     * 删除网络电费必填附件设置
     * @param dictId
     * @return
     */
    int deleteAttachmentRequiredByDictId(String dictId);

    /**
     * 查询网络电费必填附件设置
     * @param dictId
     * @return
     */
    List<AttachmentRequiredVO> selectAttachmentRequired(Map<String,Object> paramMap);

    /**
     * 查询字典
     * @param dictionaryVO
     * @return
     */
    List<DictionaryVO> selectDictList(DictionaryVO dictionaryVO);

    DictionaryVO selectDictionaryOne(DictionaryVO dictionaryVO);
}
