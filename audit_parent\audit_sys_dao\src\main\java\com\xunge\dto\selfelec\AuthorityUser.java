package com.xunge.dto.selfelec;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * 用户信息及用户权限范围
 *
 * <AUTHOR>
 * @date 2021/12/9 10:50
 */
@Getter
@Setter
@ToString
public class AuthorityUser implements Serializable {
    private static final long serialVersionUID = 7942713370136210494L;
    private String prvId;
    private String prvCode;
    /**
     * 用户权限区域,已被pregId过滤
     */
    private List<String> regIds;
    private String userId;
    /**
     * 用户部门
     */
    private List<String> depIds;
    private String username;
    /**
     * 所有的市级区域
     */
    private List<String> pregIds;
    /**
     * 是否包含所有区域
     */
    private boolean allReg=false;
}
