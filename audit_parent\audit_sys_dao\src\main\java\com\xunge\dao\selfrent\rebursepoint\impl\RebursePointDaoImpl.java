package com.xunge.dao.selfrent.rebursepoint.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.xunge.comm.StateComm;
import com.xunge.core.page.Page;
import com.xunge.dao.AbstractBaseDao;
import com.xunge.dao.selfrent.rebursepoint.IRebursePointDao;
import com.xunge.dao.system.user.ISysUserDao;
import com.xunge.filter.PageInterceptor;
import com.xunge.model.selfrent.contract.BillContractVO;
import com.xunge.model.selfrent.contract.DatContractVO;
import com.xunge.model.selfrent.contract.RentContractVO;
import com.xunge.model.selfrent.rebursepoint.RentBillaccountChangeVO;
import com.xunge.model.selfrent.rebursepoint.RentBillaccountEptVO;
import com.xunge.model.selfrent.rebursepoint.RentBillaccountVO;
import com.xunge.model.selfrent.resource.DatBaseResourceVO;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 报账点DAO
 *
 * <AUTHOR>
 */
@SuppressWarnings("unchecked")
public class RebursePointDaoImpl extends AbstractBaseDao implements IRebursePointDao {

    final String RebursePointNamespace = "com.xunge.mapping.RebursePointVOMapper.";//报账点表
    //资源表
    final String DatResourceNamespace = "com.xunge.mapping.DatResourceVOMapper.";
    //房屋租赁合同表
    final String RentContractNamespace = "com.xunge.mapping.RentContractVOMapper.";

    final String BillContractAgreementNameSpace = "com.xunge.mapping.BillContractAgreementVOMapper.";
    @Autowired
    ISysUserDao sysUserDao;

    @Override
    public RentContractVO queryBillaccountContractById(Map<String, Object> parMap) {
        return this.getSqlSession().selectOne(RentContractNamespace + "queryBillaccountContractById", parMap);
    }

    @Override
    public RentContractVO queryBillaccountContractByIdFcontract(Map<String, Object> parMap) {
        return this.getSqlSession().selectOne(RentContractNamespace + "queryBillaccountContractByIdFcontract", parMap);
    }

    @Override
    public PageInfo<RentBillaccountVO> queryRembursePointInfo(Map<String, Object> pMap, int pageNumber, int pageSize) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("userId", pMap.get("userId"));
        map.put("state", StateComm.STATE_0);
        List<String> deptIds = sysUserDao.queryUserDepartment(map);
        String userDepts = transferToRegex(deptIds);
        pMap.put("userDepts", userDepts);
        PageHelper.startPage(pageNumber, pageSize);
        List<RentBillaccountVO> datas = this.getSqlSession().selectList(RebursePointNamespace + "queryRembursePointInfo", pMap);
        PageInfo<RentBillaccountVO> pageinfo = new PageInfo<RentBillaccountVO>(datas);
        return pageinfo;
        //		PageInterceptor.startPage(pageNumber, pageSize);
        //		this.getSqlSession().selectList(RebursePointNamespace+"queryRembursePointInfo",pMap);
        //		return PageInterceptor.endPage();
    }

    private String transferToRegex(List<String> deptIds) {
        String userDepts = "";
        for (String dept : deptIds) {
            userDepts += dept + "|";
        }

        if (null == userDepts || userDepts.equals("")) {
            userDepts = "=";
        } else {
            userDepts = userDepts.substring(0, userDepts.length() - 1);
        }
        return userDepts;
    }

    @Override
    public Page<List<RentContractVO>> queryContractAgreement(Map<String, Object> pMap, int pageNumber, int pageSize) {
        PageInterceptor.startPage(pageNumber, pageSize);
        this.getSqlSession().selectList(RentContractNamespace + "queryContractAgreement", pMap);
        return PageInterceptor.endPage();
    }

    @Override
    public Page<List<RentContractVO>> queryContractAgreementFcontract(Map<String, Object> pMap, int pageNumber, int pageSize) {
        PageInterceptor.startPage(pageNumber, pageSize);
        this.getSqlSession().selectList(RentContractNamespace + "queryContractAgreementFcontract", pMap);
        return PageInterceptor.endPage();
    }

    @Override
    public int insertBillAcount(RentBillaccountVO rentBillaccount) {
        return this.getSqlSession().insert(RebursePointNamespace + "insertBillAcount", rentBillaccount);
    }

    @Override
    public int insertOperateTime(RentBillaccountVO rentBillaccount) {
        return this.getSqlSession().insert(RebursePointNamespace + "insertOperateTime", rentBillaccount);
    }

    @Override
    public void deleteBillAcount(Map<String, Object> paraMap) {
        this.getSqlSession().delete(RebursePointNamespace + "deleteBillAcount", paraMap);
    }

    @Override
    public List<DatBaseResourceVO> queryContractByResourceId(Map<String, Object> paraMap) {
        return this.getSqlSession().selectList(DatResourceNamespace + "queryContractByResourceId", paraMap);
    }

    @Override
    public RentContractVO queryContractById(Map<String, Object> paraMap) {

        return this.getSqlSession().selectOne(RentContractNamespace + "queryContractById", paraMap);
    }

    @Override
    public List<DatBaseResourceVO> queryResource(Map<String, Object> pMap) {
        return this.getSqlSession().selectList(DatResourceNamespace + "queryResource", pMap);
    }

    @Override
    public List<DatBaseResourceVO> queryBatchResource(Map<String, Object> pMap) {
        return this.getSqlSession().selectList(DatResourceNamespace + "queryBatchResource", pMap);
    }

    @Override
    public List<DatBaseResourceVO> queryAssociatedBillResourceByResourceId(DatBaseResourceVO vo) {
        return this.getSqlSession().selectList(DatResourceNamespace + "queryAssociatedBillResourceByResourceId", vo);
    }

    @Override
    public RentBillaccountVO queryBillAccountById(Map<String, Object> paraMap) {
        return this.getSqlSession().selectOne(RebursePointNamespace + "queryBillAccountById", paraMap);
    }

    @Override
    public List<RentBillaccountVO> queryBillAccountByIdList(List<String> ids) {
        return this.getSqlSession().selectList(RebursePointNamespace + "queryBillAccountByIdList", ids);
    }

    @Override
    public int countById(Map<String, Object> paraMap) {
        return this.getSqlSession().selectOne(RebursePointNamespace + "countById", paraMap);
    }

    @Override
    public Map<String, Object> queryBeanById(Map<String, Object> paraMap) {
        return this.getSqlSession().selectOne(RebursePointNamespace + "queryBeanById", paraMap);
    }

    @Override
    public int billAccountSubmitAudit(Map<String, Object> map) {
        return this.getSqlSession().update(RebursePointNamespace + "billAccountSubmitAudit", map);

    }

    @Override
    public PageInfo<RentBillaccountVO> queryRembursePointVO(Map<String, Object> paraMap, int pageNumber, int pageSize) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("userId", paraMap.get("userId"));
        map.put("state", StateComm.STATE_0);
        List<String> deptIds = sysUserDao.queryUserDepartment(map);
        String userDepts = transferToRegex(deptIds);
        paraMap.put("userDepts", userDepts);
        PageHelper.startPage(pageNumber, pageSize);
        List<RentBillaccountVO> datas = this.getSqlSession().selectList(RebursePointNamespace + "queryRembursePointVO", paraMap);
        PageInfo<RentBillaccountVO> pageinfo = new PageInfo<RentBillaccountVO>(datas);
        return pageinfo;
        //		PageInterceptor.startPage(pageNumber, pageSize);
        //		this.getSqlSession().selectList(RebursePointNamespace+"queryRembursePointVO",paraMap);
        //		return PageInterceptor.endPage();
    }

    @Override
    public RentBillaccountVO queryPaymentMethod(String billAccountId) {
        return this.getSqlSession().selectOne(RebursePointNamespace + "queryPaymentMethod", billAccountId);
    }

    @Override
    public List<RentBillaccountVO> queryRembursePointInfo(Map<String, Object> map) {
        return this.getSqlSession().selectList(RebursePointNamespace + "queryRembursePointInfo", map);
    }

    @Override
    public int insertBatchSelective(List<RentBillaccountVO> list) {
        return this.getSqlSession().insert(RebursePointNamespace + "insertBatchSelective", list);

    }

    @Override
    public List<String> queryBillaccountByCode(String billaccountCode) {
        return this.getSqlSession().selectList(RebursePointNamespace + "queryBillaccountByCode", billaccountCode);
    }

    @Override
    public void updateBillaccountState(Map<String, Object> paraMap) {
        this.getSqlSession().update(RebursePointNamespace + "updateBillaccountState", paraMap);

    }

    @Override
    public List<Map<String, Object>> selectResourceRelations(Map<String, Object> map) {
        return this.getSqlSession().selectList(RebursePointNamespace + "selectResourceRelations", map);
    }

    @Override
    public List<RentBillaccountVO> queryRembursePointVO(Map<String, Object> paraMap) {
        return this.getSqlSession().selectList(RebursePointNamespace + "queryRembursePointVO", paraMap);
    }

    @Override
    public List<RentBillaccountEptVO> queryRembursePointVOForExport(Map<String, Object> paraMap) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("userId", paraMap.get("userId"));
        map.put("state", StateComm.STATE_0);
        List<String> deptIds = sysUserDao.queryUserDepartment(map);
        String userDepts = transferToRegex(deptIds);
        paraMap.put("userDepts", userDepts);
        return this.getSqlSession().selectList(RebursePointNamespace + "queryRembursePointVOForExport", paraMap);
    }

    @Override
    public int queryPaymentByBillid(String billaccountId) {
        return this.getSqlSession().selectOne(RebursePointNamespace + "queryPaymentByBillid", billaccountId);
    }

    @Override
    public int updateBillaccountStateChange(Map paramMap) {
        return this.getSqlSession().update(RebursePointNamespace + "updateBillaccountStateChange", paramMap);
    }

    @Override
    public void deleteContractbillaccountRelation(String billaccountId) {
        this.getSqlSession().update(RebursePointNamespace + "deleteContractbillaccountRelation", billaccountId);
    }

    @Override
    public void deleteResourcebillaccountRelation(String billaccountId) {
        this.getSqlSession().update(RebursePointNamespace + "deleteResourcebillaccountRelation", billaccountId);
    }

    @Override
    public void deleteBillaccountInfo(String billaccountId) {
        this.getSqlSession().update(RebursePointNamespace + "deleteBillaccountInfo", billaccountId);
    }

    @Override
    public List<RentBillaccountVO> queryRembursePointInfoByRemoveResource(Map<String, Object> pMap) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("userId", pMap.get("userId"));
        map.put("state", StateComm.STATE_0);
        List<String> deptIds = sysUserDao.queryUserDepartment(map);
        String userDepts = transferToRegex(deptIds);
        pMap.put("userDepts", userDepts);
        List<RentBillaccountVO> datas = this.getSqlSession().selectList(RebursePointNamespace + "queryRembursePointInfo", pMap);
        return datas;
    }

    @Override
    public List<RentBillaccountVO> queryRembursePointVOByRemoveResource(Map<String, Object> paraMap) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("userId", paraMap.get("userId"));
        map.put("state", StateComm.STATE_0);
        List<String> deptIds = sysUserDao.queryUserDepartment(map);
        String userDepts = transferToRegex(deptIds);
        paraMap.put("userDepts", userDepts);
        List<RentBillaccountVO> datas = this.getSqlSession().selectList(RebursePointNamespace + "queryRembursePointVO", paraMap);
        return datas;
    }

    @Override
    public void insertAcountTypeChangeLog(Map<String, Object> map) {
        this.getSqlSession().update(RebursePointNamespace + "insertAcountTypeChangeLog", map);
    }


    @Override
    public List<BillContractVO> queryContractByBillId(String billAccountId) {
        return this.getSqlSession().selectList(BillContractAgreementNameSpace + "queryContractByBillId", billAccountId);
    }

    @Override
    public int insertBillContract(List<BillContractVO> insert) {
        return this.getSqlSession().insert(BillContractAgreementNameSpace + "insertBillContract", insert);
    }

    @Override
    public int updateDeleteTimeByBillId(BillContractVO billContractVO) {
        return this.getSqlSession().update(BillContractAgreementNameSpace + "updateDeleteTimeByBillId", billContractVO);
    }

    @Override
    public List<Map<String, Object>> queryAccountTypeChangeLog(String billAccountId) {
        return this.getSqlSession().selectList(RebursePointNamespace + "queryAccountTypeChangeLog", billAccountId);
    }

    @Override
    public List<DatContractVO> queryContAgreementByBillId(Map<String, Object> paraMap) {
        return this.getSqlSession().selectList(BillContractAgreementNameSpace + "queryContAgreementByBillId", paraMap);
    }

    @Override
    public List<BillContractVO> queryContractAgreementByBillId(String billaccountId) {
        return this.getSqlSession().selectList(BillContractAgreementNameSpace + "queryContractAgreementByBillId", billaccountId);
    }

    @Override
    public Page<List<RentContractVO>> queryContractAgreementFcontractNew(Map<String, Object> pMap, int pageNumber, int pageSize) {
        PageInterceptor.startPage(pageNumber, pageSize);
        this.getSqlSession().selectList(RentContractNamespace + "queryContractAgreementFcontractNew", pMap);
        return PageInterceptor.endPage();
    }

    @Override
    public int insertBillAccountChange(List<Map<String, Object>> mapList) {
        return this.getSqlSession().insert(RebursePointNamespace + "insertBillAccountChange", mapList);
    }

    @Override
    public int updateBillAccountChange(Map<String, Object> map) {
        return this.getSqlSession().update(RebursePointNamespace + "updateBillAccountChange", map);
    }

    @Override
    public int updateBillAccountChangeAuditStateById(Map<String, Object> map) {
        return this.getSqlSession().update(RebursePointNamespace + "updateBillAccountChangeAuditStateById", map);
    }

    @Override
    public int deleteBillAccountChange(List<String> deleteList) {
        return this.getSqlSession().update(RebursePointNamespace + "deleteBillAccountChange", deleteList);
    }

    @Override
    public int deleteBillAccountChangeByBillaccountIdAndState(Map<String, Object> map) {
        return this.getSqlSession().update(RebursePointNamespace + "deleteBillAccountChangeByBillaccountIdAndState", map);
    }

    @Override
    public List<RentBillaccountChangeVO> selertBillAccountChange(Map<String, Object> map) {
        return this.getSqlSession().selectList(RebursePointNamespace + "selertBillAccountChange", map);
    }

    @Override
    public List<RentContractVO> queryContractAgreementFcontractRebuild(Map<String, Object> map) {
        return this.getSqlSession().selectList(RentContractNamespace + "queryContractAgreementFcontractRebuild", map);
    }

    @Override
    public List<RentContractVO> queryContractAgreementRebuild(Map<String, Object> map) {
        return this.getSqlSession().selectList(RentContractNamespace + "queryContractAgreementRebuild", map);
    }

    @Override
    public List<RentContractVO> queryContractAgreementFcontractNewRebuild(Map<String, Object> map) {
        return this.getSqlSession().selectList(RentContractNamespace + "queryContractAgreementFcontractNewRebuild", map);
    }


}
