package com.xunge.dto.selfelec.meter;

import com.xunge.dto.selfelec.PageInfo;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/2/25 11:41
 */
@Getter
@Setter
public class MeterQueryDto extends PageInfo implements Serializable {

    private static final long serialVersionUID = -797050687212121651L;

    private String meterCode;
    private String meterState;
    private String meterType;
    private String relateResState;
    private String accountNumber;
    private String billaccountCode;
    /**
     * 电表抄表管理，type:1,其他为null
     */
    private String type;
    private String pregId;
    private String regId;

    private String countAttachMent;

    /**
     * 审核状态
     */
    private Integer auditingState;

    private String importFlag;

}
