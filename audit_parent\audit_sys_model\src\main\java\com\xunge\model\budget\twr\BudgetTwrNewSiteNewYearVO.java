package com.xunge.model.budget.twr;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2022/7/28
 * @description 新年新建站在新年的费用
 */
@Data
public class BudgetTwrNewSiteNewYearVO {
    /**
     * 省份ID
     */
    private String prvId;

    /**
     * 省份名称
     */
    private String prvName;
    /**
     * 年份
     */
    private Integer onYear;
    /**
     * 业务类型({塔类,1}, {室分,2}, {微站,3}, {传输,4}, {非标,5}, {合计,6})
     */
    private Integer productType;
    /**
     * 单站服务费（本年新建站年均值）
     */
    private BigDecimal newSiteCurrentYearFee;
    /**
     * 单订单服务费（共址订单年均值）
     */
    private BigDecimal newSiteShareYearFee;
    /**
     * 本年共址改造订单平均产品单元数
     */
    private BigDecimal newSiteCurrentUnitNumber;
    /**
     * 新年新建站址数
     */
    private Integer newSiteNumber;
    /**
     * 新年共址改造订单数
     */
    private Integer newSiteShareOrderNumber;
    /**
     * 月份数（新址新建）
     */
    private BigDecimal newSiteCurrentMonthNumber;

    /**
     * 月份数（共址改造）
     */
    private BigDecimal newSiteShareMonthNumber;
    /**
     * 新年共址改造站址平均产品单元数
     */
    private BigDecimal newSiteShareUnitNumber;
    /**
     * 新年新建站在新年的费用-预算金额
     */
    private BigDecimal newSiteBudgetFee;
    /**
     * 新年新建站在新年的费用-核减金额
     */
    private BigDecimal newSiteSubtractFee;

    /**
     * 调整金额
     */
    private BigDecimal newSiteAdjustFee;
    private BigDecimal newSiteAdjustFeeAfter;

    private String newSiteRemark;

}
