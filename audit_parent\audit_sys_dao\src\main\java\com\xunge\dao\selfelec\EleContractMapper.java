package com.xunge.dao.selfelec;

import com.xunge.model.selfelec.EleContract;
import com.xunge.model.selfelec.EleContractExample;
import com.xunge.model.selfelec.VEleBillaccountcontract;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface EleContractMapper {

    int countByExample(EleContractExample example);


    int deleteByExample(EleContractExample example);


    int deleteByPrimaryKey(String elecontractId);


    int insert(EleContract record);


    int insertSelective(EleContract record);


    List<EleContract> selectByExample(EleContractExample example);


    EleContract selectByPrimaryKey(String elecontractId);


    int updateByExampleSelective(@Param("record") EleContract record, @Param("example") EleContractExample example);


    int updateByExample(@Param("record") EleContract record, @Param("example") EleContractExample example);


    int updateByPrimaryKeySelective(EleContract record);


    int updateByPrimaryKey(EleContract record);

    List<Map<String, Object>> selectContractNumByCondition(Map<String, Object> param);

    /**
     * 查询未关联合同数
     *
     * @param param
     * @return
     */
    int selectNolinkCont(Map<String, Object> param);

    /**
     * 查询所有合同数
     *
     * @param param
     * @return
     */
    int selectAllContract(Map<String, Object> param);

    /**
     * 根据报账点id查询合同
     *
     * @param
     * @return
     * @date 2018年08月29日
     * <AUTHOR>
     */
    EleContract queryContractByBillaccountId(String billaccountId);

    /**
     * 根据报账点id实时查合同信息
     *
     * @param billaccountIds 报账点id集合
     * @return 合同信息
     */
    List<VEleBillaccountcontract> queryContractInfoByBillaccountIds(@Param("billaccountIds") List<String> billaccountIds);

    /**
     * 根据合同id查是否已经存在（塔维拷贝到网络电费的）
     * @param elecontractId 指向塔维合同id
     * @return 实际合同id
     */
    EleContract selectByTeleContractId(String elecontractId);

    /**
     * 复制 tele_contract 到 ele_contract
     *
     * @param eleContractId 新生成的电费合同id
     * @param contractId    新生成的合同id
     * @param id            需要修改的电费合同id
     */
    void copyToEleContract(@Param("eleContractId") String eleContractId, @Param("contractId") String contractId, @Param("id") String id);

    /**
     * 复制tele_dat_contract 到 dat_contract
     *
     * @param contractId 新生成的合同id
     * @param id         需要修改的电费合同id
     */
    void copyToDatContract(@Param("contractId") String contractId, @Param("id") String id);

    /**
     * 置空电费合同指向塔维id字段
     *
     * @param relationId 关联关系id
     */
    void updateContractByRelationId(@Param("relationId") String relationId);

}