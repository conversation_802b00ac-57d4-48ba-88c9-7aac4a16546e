package com.xunge.dao.budget.ele;

import com.xunge.model.budget.ele.BudgetEleElectorCostVO;
import com.xunge.model.budget.ele.BudgetEleParamVO;
import com.xunge.model.budget.ele.BudgetEleScaleRegulateVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @author: LiangCheng
 * Date: 2022/7/5 16:55
 * Description: 规模校正表
 */
public interface BudgetEleScaleRegulateMapper {

    /**
     * 保存规模校正
     */
    void saveEleScaleRegulate(BudgetEleParamVO budgetEleParamVOe);

    List<BudgetEleScaleRegulateVO> getScaleRegulate(BudgetEleParamVO budgetEleParamVO);

    void editEleScaleRegulateGroupDraft(BudgetEleScaleRegulateVO budgetEleScaleRegulateVO);

    void editEleScaleRegulate(BudgetEleScaleRegulateVO budgetEleScaleRegulateVO);

    void delBudgetEleData(BudgetEleParamVO budgetEleParamVO);


}
