package com.xunge.dao.energManage;

import com.xunge.model.energManage.EnergTableVO;
import com.xunge.model.energManage.PrvCityVO;
import com.xunge.model.system.region.RegionVO;

import java.util.List;
import java.util.Map;

public interface IEnergManageDao {

    /**
     * 获取所有省
     */
    public List<PrvCityVO> queryProvs(Map<String, Object> map);

    /**
     * 获取所有省下地市
     */
    public List<PrvCityVO> queryRegions(Map<String, Object> map);

    /**
     * 获取用户所有管理权限省
     */
    public List<RegionVO> queryManaProvs(Map<String, Object> map);

    /**
     * 获取用户所有管理权限区
     */
    public List<RegionVO> queryManaRegions(Map<String, Object> map);

    /**
     * 查询所选日期内电量耗损
     *
     * @param map
     * @return
     */
    List<EnergTableVO> queryAllEnerg(Map<String, Object> map);

}
