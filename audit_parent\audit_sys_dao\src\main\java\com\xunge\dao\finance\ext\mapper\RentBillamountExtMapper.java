package com.xunge.dao.finance.ext.mapper;

import com.xunge.model.finance.ext.accClaim.Billamount;
import com.xunge.model.finance.ext.accClaim.Billamountdetail;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface RentBillamountExtMapper {

    Billamount selectByPrimaryKey(String billamountId);

    List<Billamount> queryRentBillamountByIds(Map<String, Object> map);

    int updateStateByPrimaryKey(Billamount billamount);

    int updateSecondBillamountId(Billamountdetail amountDetail);

    int updateSecondBillamountIdByPayment(Billamountdetail amountDetail);

    /**
     * 更新汇总单相关的财务返回单号
     * @param billamountId
     * @param claimNum
     */
    void updateClaimNumByBillamountId(@Param("billamountId") String billamountId, @Param("claimNum") String claimNum);
}
