package com.xunge.core.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/11/9 10:43
 */
@Slf4j
public class MapUtil {
    /**
     * 将object加入map中
     */
    public static Map<String, Object> addMap(Map<String, Object> sourceMap, Object object) {
        if (sourceMap == null) {
            sourceMap = new HashMap<>();
        }
        Class<?> aClass = object.getClass();
        Field[] fields = aClass.getDeclaredFields();
        for (Field field : fields) {
            field.setAccessible(true);
            String key = field.getName();
            if (sourceMap.containsKey(key)) {
                continue;
            }
            try {
                Object value = field.get(object);
                //为空不加入
                if (value != null) {
                    if (value instanceof String) {
                        String strValue = (String) value;
                        //如果为空字符串，就不再加入map中
                        if (StringUtils.isNotBlank(strValue)) {
                            sourceMap.put(key, strValue.trim());
                        }
                    } else {
                        sourceMap.put(key, value);
                    }
                }
            } catch (IllegalAccessException e) {
                log.error("add map error", e);
            }
        }
        return sourceMap;
    }

    /**
     * 统一单价类型为Integer
     *
     * @param paramMap
     */
    public static void dealPriceType(Map<String, Object> paramMap) {
        if (paramMap == null) {
            return;
        }
        Object priceType = paramMap.get("priceType");
        if (priceType == null) {
            return;
        }
        if (priceType.toString().equals("")) {
            paramMap.put("priceType", null);
            return;
        }
        Integer type = Integer.valueOf(priceType.toString());
        paramMap.put("priceType", type);
    }
}
