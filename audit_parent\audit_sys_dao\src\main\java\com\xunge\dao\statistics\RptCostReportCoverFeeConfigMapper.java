package com.xunge.dao.statistics;


import com.xunge.model.statistics.CoverFeeQuery;
import com.xunge.model.statistics.RptCostReportCoverFeeConfig;
import com.xunge.model.system.region.SysRegionVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface RptCostReportCoverFeeConfigMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(RptCostReportCoverFeeConfig record);

    int insertBatch(List<RptCostReportCoverFeeConfig> list);

    int insertSelective(RptCostReportCoverFeeConfig record);

    RptCostReportCoverFeeConfig selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(RptCostReportCoverFeeConfig record);

    int updateByPrimaryKey(RptCostReportCoverFeeConfig record);

    List<RptCostReportCoverFeeConfig> selectList(@Param("feeQuery") CoverFeeQuery feeQuery, @Param("idList") List<String> idList);

    int checkCoverFeeConfig(@Param("feeQuery") CoverFeeQuery feeQuery);

    List<SysRegionVO> getRegions(@Param("feeQuery") CoverFeeQuery feeQuery, @Param("prvId") String prvId);

    /**
     * 查询计提类型
     *
     * @param type 1 租费   2 三方塔
     * @return 大于0 则是差额计提
     */
    int queryAccrualTypeCount(@Param("type") Integer type);

}