package com.xunge.comm.job;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.HttpStatus;
import org.apache.http.NameValuePair;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.*;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.params.CoreConnectionPNames;
import org.apache.http.util.EntityUtils;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 基于 httpclient 4.3.1版本的 http工具类
 */
@Slf4j
public class HttpTookit {

    //	private static final CloseableHttpClient httpClient;
    public static final String CHARSET = "UTF-8";
    //	static {
    //		String MaxTotal = null;// PropertyUtil.getPropertyByKey("httpclient.MaxTotal");
    //		String MaxPerRoute = null;//PropertyUtil.getPropertyByKey("httpclient.MaxPerRoute");
    //		if (StringUtils.isBlank(MaxTotal) || !StringUtils.isNumeric(MaxTotal)) {
    //			MaxTotal = "100";
    //		}
    //		if (StringUtils.isBlank(MaxPerRoute) || !StringUtils.isNumeric(MaxPerRoute)) {
    //			MaxPerRoute = "100";
    //		}
    //		RequestConfig config = RequestConfig.custom().setConnectTimeout(6 * 60 * 1000).setSocketTimeout(6 * 60 * 1000)
    //				.build();
    //		PoolingHttpClientConnectionManager cm = new PoolingHttpClientConnectionManager();
    //		cm.setMaxTotal(Integer.valueOf(MaxTotal));//连接池最大并发连接数
    //		cm.setDefaultMaxPerRoute(Integer.valueOf(MaxPerRoute));//单路由最大并发数
    //		httpClient = HttpClientBuilder.create().setDefaultRequestConfig(config).setConnectionManager(cm).build();
    //	}

    public static String doGet(String url, Map<String, String> params) {
        return doGet(url, params, null, CHARSET);
    }

    public static String doPost(String url, Map<String, String> params) {
        return doPost(url, params, null, CHARSET);
    }

    public static String doDelete(String url, Map<String, String> params) {
        return doDelete(url, params, null, CHARSET);
    }

    public static String doPut(String url, Map<String, String> params) {
        return doPut(url, params, null, CHARSET);
    }

    public static String doGet(String url, Map<String, String> params, Map<String, String> headers) {
        return doGet(url, params, headers, CHARSET);
    }

    public static String doPost(String url, Map<String, String> params, Map<String, String> headers) {
        return doPost(url, params, headers, CHARSET);
    }

    public static String doDelete(String url, Map<String, String> params, Map<String, String> headers) {
        return doDelete(url, params, headers, CHARSET);
    }

    public static String doPostWithJson(String url, String json, Map<String, String> headers) {
        return doPostWithJson(url, json, headers, CHARSET, null);
    }

    public static String doPostWithJson(String url, String json, Map<String, String> headers, RequestConfig config) {
        return doPostWithJson(url, json, headers, CHARSET, config);
    }

    public static String doPut(String url, Map<String, String> params, Map<String, String> headers) {
        return doPut(url, params, headers, CHARSET);
    }

    public static String doPutWithJson(String url, String json, Map<String, String> headers) {
        return doPutWithJson(url, json, headers, CHARSET);
    }

    /**
     * HTTP Get 获取内容
     *
     * @param url     请求的url地址 ?之前的地址
     * @param params  请求的参数
     * @param charset 编码格式
     * @return 页面内容
     */
    public static String doGet(String url, Map<String, String> params, Map<String, String> headers, String charset) {
        if (StringUtils.isBlank(url)) {
            return null;
        }
        CloseableHttpClient httpClient = HttpClients.createDefault();
        HttpGet httpGet = null;
        CloseableHttpResponse response = null;
        try {
            if (params != null && !params.isEmpty()) {
                List<NameValuePair> pairs = new ArrayList<NameValuePair>(params.size());
                for (Map.Entry<String, String> entry : params.entrySet()) {
                    String value = entry.getValue();
                    if (value != null) {
                        pairs.add(new BasicNameValuePair(entry.getKey(), value));
                    }
                }
                url += "?" + EntityUtils.toString(new UrlEncodedFormEntity(pairs, charset));
            }
            httpGet = new HttpGet(url);
            if (headers != null && !headers.isEmpty()) {
                for (Map.Entry<String, String> entry : headers.entrySet()) {
                    httpGet.addHeader(entry.getKey(), entry.getValue());
                }
            }
            RequestConfig requestConfig = RequestConfig.custom().setSocketTimeout(6 * 60 * 1000).setConnectTimeout(6 * 60 * 1000).build();// 设置请求和传输超时时间
            httpGet.setConfig(requestConfig);
            response = httpClient.execute(httpGet);
            int statusCode = response.getStatusLine().getStatusCode();
            if (statusCode != 200) {
                httpGet.abort();
                throw new RuntimeException("HttpClient,error status code :" + statusCode);
            }
            HttpEntity entity = response.getEntity();
            String result = null;
            if (entity != null) {
                result = EntityUtils.toString(entity, "utf-8");
            }
            EntityUtils.consume(entity);
            return result;
        } catch (Exception e) {
            log.error("HttpTookit 出错", e);
        } finally {
            try {
                if (response != null) {
                    response.close();
                }
            } catch (Exception e) {
                log.error("HttpTookit 出错", e);
            }
            //			try {
            //				if(httpPost!=null){
            //					httpPost.releaseConnection();
            //				}
            //			} catch (Exception e) {
            //				log.error("HttpTookit 出错",e);
            //			}
            try {
                if (httpClient != null) {
                    httpClient.close();
                }
            } catch (IOException e) {
                log.error("HttpTookit 出错", e);
            }
        }
        return null;
    }

    /**
     * HTTP Post 获取内容
     *
     * @param url     请求的url地址 ?之前的地址
     * @param params  请求的参数
     * @param charset 编码格式
     * @return 页面内容
     */
    public static String doPost(String url, Map<String, String> params, Map<String, String> headers, String charset) {
        if (StringUtils.isBlank(url)) {
            return null;
        }
        CloseableHttpClient httpClient = HttpClients.createDefault();
        HttpPost httpPost = null;
        CloseableHttpResponse response = null;
        try {
            List<NameValuePair> pairs = null;
            if (params != null && !params.isEmpty()) {
                pairs = new ArrayList<NameValuePair>(params.size());
                for (Map.Entry<String, String> entry : params.entrySet()) {
                    String value = entry.getValue();
                    if (value != null) {
                        pairs.add(new BasicNameValuePair(entry.getKey(), value));
                    }
                }
            }
            httpPost = new HttpPost(url);
            if (pairs != null && pairs.size() > 0) {
                httpPost.setEntity(new UrlEncodedFormEntity(pairs, CHARSET));
            }
            if (headers != null && !headers.isEmpty()) {
                for (Map.Entry<String, String> entry : headers.entrySet()) {
                    httpPost.addHeader(entry.getKey(), entry.getValue());
                }
            }
            response = httpClient.execute(httpPost);
            int statusCode = response.getStatusLine().getStatusCode();
            if (statusCode != 200) {
                httpPost.abort();
                throw new RuntimeException("HttpClient,error status code :" + statusCode);
            }
            HttpEntity entity = response.getEntity();
            String result = null;
            if (entity != null) {
                result = EntityUtils.toString(entity, "utf-8");
            }
            EntityUtils.consume(entity);
            return result;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        } finally {
            try {
                if (response != null) {
                    response.close();
                }
            } catch (Exception e) {
                log.error("HttpTookit 出错", e);
            }
            //			try {
            //				if(httpPost!=null){
            //					httpPost.releaseConnection();
            //				}
            //			} catch (Exception e) {
            //				log.error("HttpTookit 出错",e);
            //			}
            try {
                if (httpClient != null) {
                    httpClient.close();
                }
            } catch (IOException e) {
                log.error("HttpTookit 出错", e);
            }
        }
        return null;
    }

    /**
     * HTTP Post 获取内容
     *
     * @param url     请求的url地址 ?之前的地址
     * @param json    请求的参数
     * @param charset 编码格式
     * @return 页面内容
     */
    public static String doPostWithJson(String url, String json, Map<String, String> headers, String charset, RequestConfig config) {
        if (StringUtils.isBlank(url)) {
            return null;
        }
        CloseableHttpClient httpClient = HttpClients.createDefault();
        HttpPost httpPost = null;
        CloseableHttpResponse response = null;
        try {
            httpPost = new HttpPost(url);
            if(config != null) {
                httpPost.setConfig(config);
            }
            if (headers != null && !headers.isEmpty()) {
                for (Map.Entry<String, String> entry : headers.entrySet()) {
                    httpPost.addHeader(entry.getKey(), entry.getValue());
                }
            }
            StringEntity s = new StringEntity(json, "UTF-8");
            s.setContentEncoding("UTF-8");
            s.setContentType("application/json");//发送json数据需要设置contentType
            httpPost.setEntity(s);
            response = httpClient.execute(httpPost);
            int statusCode = response.getStatusLine().getStatusCode();
            if (statusCode != 200) {
                httpPost.abort();
                throw new RuntimeException("HttpClient,error status code :" + statusCode);
            }
            HttpEntity entity = response.getEntity();
            String result = null;
            if (entity != null) {
                result = EntityUtils.toString(entity, "utf-8");
            }
            EntityUtils.consume(entity);
            return result;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new RuntimeException(e.getMessage());
        } finally {
            try {
                if (response != null) {
                    response.close();
                }
            } catch (Exception e) {
                log.error("HttpTookit 出错", e);
            }
            //			try {
            //				if(httpPost!=null){
            //					httpPost.releaseConnection();
            //				}
            //			} catch (Exception e) {
            //				log.error("HttpTookit 出错",e);
            //			}
            try {
                if (httpClient != null) {
                    httpClient.close();
                }
            } catch (IOException e) {
                log.error("HttpTookit 出错", e);
            }
        }
    }

    /**
     * HTTP Delete 获取内容
     *
     * @param url     请求的url地址 ?之前的地址
     * @param params  请求的参数
     * @param charset 编码格式
     * @return 页面内容
     */
    public static String doDelete(String url, Map<String, String> params, Map<String, String> headers, String charset) {
        if (StringUtils.isBlank(url)) {
            return null;
        }
        CloseableHttpClient httpClient = HttpClients.createDefault();
        CloseableHttpResponse response = null;
        HttpDelete httpDelete = null;
        try {
            if (params != null && !params.isEmpty()) {
                List<NameValuePair> pairs = new ArrayList<NameValuePair>(params.size());
                for (Map.Entry<String, String> entry : params.entrySet()) {
                    String value = entry.getValue();
                    if (value != null) {
                        pairs.add(new BasicNameValuePair(entry.getKey(), value));
                    }
                }
                url += "?" + EntityUtils.toString(new UrlEncodedFormEntity(pairs, charset));
            }
            httpDelete = new HttpDelete(url);
            if (headers != null && !headers.isEmpty()) {
                for (Map.Entry<String, String> entry : headers.entrySet()) {
                    httpDelete.addHeader(entry.getKey(), entry.getValue());
                }
            }
            RequestConfig requestConfig = RequestConfig.custom().setSocketTimeout(6 * 60 * 1000).setConnectTimeout(6 * 60 * 1000).build();// 设置请求和传输超时时间
            httpDelete.setConfig(requestConfig);
            response = httpClient.execute(httpDelete);
            int statusCode = response.getStatusLine().getStatusCode();
            if (statusCode != 200) {
                httpDelete.abort();
                throw new RuntimeException("HttpClient,error status code :" + statusCode);
            }
            HttpEntity entity = response.getEntity();
            String result = null;
            if (entity != null) {
                result = EntityUtils.toString(entity, "utf-8");
            }
            EntityUtils.consume(entity);
            return result;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        } finally {
            try {
                if (response != null) {
                    response.close();
                }
            } catch (Exception e) {
                log.error("HttpTookit 出错", e);
            }
            //			try {
            //				if(httpPost!=null){
            //					httpPost.releaseConnection();
            //				}
            //			} catch (Exception e) {
            //				log.error("HttpTookit 出错",e);
            //			}
            try {
                if (httpClient != null) {
                    httpClient.close();
                }
            } catch (IOException e) {
                log.error("HttpTookit 出错", e);
            }
        }
        return null;
    }

    /**
     * HTTP Post 获取内容
     *
     * @param url     请求的url地址 ?之前的地址
     * @param params  请求的参数
     * @param charset 编码格式
     * @return 页面内容
     */
    public static String doPut(String url, Map<String, String> params, Map<String, String> headers, String charset) {
        if (StringUtils.isBlank(url)) {
            return null;
        }
        CloseableHttpClient httpClient = HttpClients.createDefault();
        CloseableHttpResponse response = null;
        HttpPut httpPut = null;
        try {
            List<NameValuePair> pairs = null;
            if (params != null && !params.isEmpty()) {
                pairs = new ArrayList<NameValuePair>(params.size());
                for (Map.Entry<String, String> entry : params.entrySet()) {
                    String value = entry.getValue();
                    if (value != null) {
                        pairs.add(new BasicNameValuePair(entry.getKey(), value));
                    }
                }
            }
            httpPut = new HttpPut(url);
            if (pairs != null && pairs.size() > 0) {
                httpPut.setEntity(new UrlEncodedFormEntity(pairs, CHARSET));
            }
            if (headers != null && !headers.isEmpty()) {
                for (Map.Entry<String, String> entry : headers.entrySet()) {
                    httpPut.addHeader(entry.getKey(), entry.getValue());
                }
            }
            response = httpClient.execute(httpPut);
            int statusCode = response.getStatusLine().getStatusCode();
            if (statusCode != 200) {
                httpPut.abort();
                throw new RuntimeException("HttpClient,error status code :" + statusCode);
            }
            HttpEntity entity = response.getEntity();
            String result = null;
            if (entity != null) {
                result = EntityUtils.toString(entity, "utf-8");
            }
            EntityUtils.consume(entity);
            return result;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            ;
        } finally {
            try {
                if (response != null) {
                    response.close();
                }
            } catch (Exception e) {
                log.error("HttpTookit 出错", e);
            }
            //			try {
            //				if(httpPost!=null){
            //					httpPost.releaseConnection();
            //				}
            //			} catch (Exception e) {
            //				log.error("HttpTookit 出错",e);
            //			}
            try {
                if (httpClient != null) {
                    httpClient.close();
                }
            } catch (IOException e) {
                log.error("HttpTookit 出错", e);
            }
        }
        return null;
    }

    public static String doPutWithJson(String url, String json, Map<String, String> headers, String charset) {
        if (StringUtils.isBlank(url)) {
            return null;
        }
        CloseableHttpClient httpClient = HttpClients.createDefault();
        HttpPut httpPut = null;
        CloseableHttpResponse response = null;
        try {
            httpPut = new HttpPut(url);
            if (headers != null && !headers.isEmpty()) {
                for (Map.Entry<String, String> entry : headers.entrySet()) {
                    httpPut.addHeader(entry.getKey(), entry.getValue());
                }
            }
            StringEntity s = new StringEntity(json, "UTF-8");
            s.setContentEncoding("UTF-8");
            s.setContentType("application/json");//发送json数据需要设置contentType
            httpPut.setEntity(s);
            response = httpClient.execute(httpPut);
            int statusCode = response.getStatusLine().getStatusCode();
            if (statusCode != 200) {
                httpPut.abort();
                throw new RuntimeException("HttpClient,error status code :" + statusCode);
            }
            HttpEntity entity = response.getEntity();
            String result = null;
            if (entity != null) {
                result = EntityUtils.toString(entity, "utf-8");
            }
            EntityUtils.consume(entity);
            return result;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        } finally {
            try {
                if (response != null) {
                    response.close();
                }
            } catch (Exception e) {
                log.error("HttpTookit 出错", e);
            }
            //			try {
            //				if(httpPost!=null){
            //					httpPost.releaseConnection();
            //				}
            //			} catch (Exception e) {
            //				log.error("HttpTookit 出错",e);
            //			}
            try {
                if (httpClient != null) {
                    httpClient.close();
                }
            } catch (IOException e) {
                log.error("HttpTookit 出错", e);
            }
        }
        return null;
    }

    //	public static void main(String[] args) {
    //		HttpTookit.doGet("http://www.163.com", null);
    //		for(int i=0; i< 20 ; i++) {
    //			long t1 = System.currentTimeMillis();
    //			System.out.println(HttpTookit.doGet("http://www.baidu.com", null));
    //			long t2 = System.currentTimeMillis();
    //			System.out.println(t2 - t1);
    //			try {
    //				Thread.sleep(3000);
    //			} catch (InterruptedException e) {
    //				log.error("HttpTookit 出错",e);
    //			}
    //		}
    //	}

    public static String post(String param, String url) {
        String result = "";
        HttpPost post = new HttpPost(url);
        try {
            CloseableHttpClient httpClient = HttpClients.createDefault();
            RequestConfig requestConfig = RequestConfig.custom()
                    // 默认连接超时100ms
                    .setConnectionRequestTimeout(10000)
                    // 请求超时400ms
                    .setSocketTimeout(60000)
                    .build();
            post.setConfig(requestConfig);
            post.setHeader("Content-Type", "application/json;charset=utf-8");
            //post.addHeader("Authorization", "Basic YWRtaW46");
            StringEntity postingString = new StringEntity(param, "utf-8");
            post.setEntity(postingString);
            HttpResponse response = httpClient.execute(post);

            InputStream in = response.getEntity().getContent();
            BufferedReader br = new BufferedReader(new InputStreamReader(in, "utf-8"));
            StringBuilder strber = new StringBuilder();
            String line = null;
            while ((line = br.readLine()) != null) {
                strber.append(line + '\n');
            }
            br.close();
            in.close();
            result = strber.toString();
            if (response.getStatusLine().getStatusCode() != HttpStatus.SC_OK) {
                result = "服务器异常";
            }
        } catch (Exception e) {
            log.error("HttpTookit 出错", e);
            result = "服务器异常";
            //throw new RuntimeException(e);
        } finally {
            post.abort();
        }
        return result;
    }

    public static String post(String param, String url, int connectTimeout) {
        HttpPost post = new HttpPost(url);
        try {
            CloseableHttpClient httpClient = HttpClients.createDefault();
            RequestConfig requestConfig = RequestConfig.custom().setConnectTimeout(connectTimeout).build();
            post.setConfig(requestConfig);
            post.setHeader("Content-Type", "application/json;charset=utf-8");
            StringEntity postingString = new StringEntity(param, "utf-8");
            post.setEntity(postingString);
            HttpResponse response = httpClient.execute(post);
            InputStream in = response.getEntity().getContent();
            BufferedReader br = new BufferedReader(new InputStreamReader(in, "utf-8"));
            StringBuilder strber = new StringBuilder();
            String line = null;
            while ((line = br.readLine()) != null) {
                strber.append(line + '\n');
            }
            br.close();
            in.close();
            if (response.getStatusLine().getStatusCode() != HttpStatus.SC_OK) {
                return null;
            }
            return strber.toString();
        } catch (Exception e) {
            log.error("HttpTookit post 调用出错", e);
            return null;
        } finally {
            post.abort();
        }
    }
}