package com.xunge.model.activity;

import com.xunge.core.util.StrUtil;
import com.xunge.model.system.user.SysUserVO;

import java.io.Serializable;
import java.util.Date;

/**
 * 工作流Entity
 *
 * <AUTHOR>
 */
public class ActHistoicFlow implements Serializable {
    /**
     *
     */
    private static final long serialVersionUID = 781304658798409233L;
    /**
     * 任务标题
     */
    private String title;
    /**
     * 任务意见
     */
    private String comment;
    /**
     * 任务执行人名称
     */
    private String assignee;
    /**
     * 开始查询日期
     */
    private Date beginDate;
    /**
     * 结束查询日期
     */
    private Date endDate;
    /**
     * 历时
     */
    private String durationTime;
    private String regName;
    private SysUserVO optUser;
    /**
     * 审核结果  开始，通过，驳回，撤回
     */
    private String auditMsg;

    public ActHistoicFlow(String title, String comment, String assignee, Date beginDate, Date endDate, String durationTime) {
        super();
        this.title = title;
        this.comment = comment;
        this.assignee = assignee;
        this.beginDate = beginDate;
        this.endDate = endDate;
        this.durationTime = durationTime;
    }

    public ActHistoicFlow() {
        super();
        // TODO Auto-generated constructor stub
    }

    public String getAuditMsg() {
        return auditMsg;
    }

    public void setAuditMsg(String auditMsg) {
        this.auditMsg = auditMsg;
    }

    public String getRegName() {
        return regName;
    }

    public void setRegName(String regName) {
        this.regName = regName;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = StrUtil.isBlank(comment) ? "无" : comment;
    }

    public String getAssignee() {
        return assignee;
    }

    public void setAssignee(String assignee) {
        this.assignee = assignee;
    }

    public Date getBeginDate() {
        return beginDate;
    }

    public void setBeginDate(Date beginDate) {
        this.beginDate = beginDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public SysUserVO getOptUser() {
        return optUser;
    }

    public void setOptUser(SysUserVO optUser) {
        this.optUser = optUser;
    }

    public String getDurationTime() {
        return durationTime;
    }

    public void setDurationTime(String durationTime) {
        this.durationTime = durationTime;
    }

}
