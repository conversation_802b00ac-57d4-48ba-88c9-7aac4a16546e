package com.xunge.dao.report.Impl;

import com.xunge.dao.AbstractBaseDao;
import com.xunge.dao.report.IrptElectricmeterDao;

import java.util.List;
import java.util.Map;

public class RptElectricmeterDaoImpl extends AbstractBaseDao implements IrptElectricmeterDao {

    final String Namespace = "com.xunge.mapping.RptElectricmeterVOMapper.";

    @Override
    public List<Map<String, Object>> queryElectricmeterByPrv(Map<String, Object> map) {
        return this.getSqlSession().selectList(Namespace + "queryElectricmeterByPrv", map);
    }

    @Override
    public List<Map<String, Object>> queryElectricmeterByPreg(Map<String, Object> map) {
        return this.getSqlSession().selectList(Namespace + "queryElectricmeterByPreg", map);
    }

    @Override
    public List<Map<String, Object>> queryElectricmeter(Map<String, Object> map) {
        return this.getSqlSession().selectList(Namespace + "queryElectricmeter", map);
    }

}