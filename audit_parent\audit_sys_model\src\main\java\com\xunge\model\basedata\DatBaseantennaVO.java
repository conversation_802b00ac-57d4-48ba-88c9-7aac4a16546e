package com.xunge.model.basedata;

import com.xunge.model.BaseActVO;

import java.math.BigDecimal;
import java.util.Date;

public class DatBaseantennaVO extends BaseActVO {
    //天线id
    private String antennaId;
    //省份代码
    private String provinceCode;
    //省份id
    private String prvId;
    //天线cid
    private String antennaCid;
    //天线名称
    private String antennaName;
    //设备厂家
    private String vendor;
    //设备型号
    private String neModel;
    //所属铁塔
    private String relatedTowerCid;
    //所属铁塔名称
    private String relatedTowerCidName;
    //所在铁塔平台
    private Integer relatedTowerPlatform;
    //天线挂高
    private BigDecimal antennaHeight;
    //入网时间
    private Date admissionDate;
    //天线类型
    private String antennaType;
    //铁塔公司业务确认单号
    private String ctcBusinessConfirmNum;
    //数据来源
    private Integer dataFrom;
    //创建用户
    private String createUser;
    //创建用户ip
    private String createIp;
    //创建时间
    private Date createTime;
    //修改用户
    private String updateUser;
    //修改用户ip
    private String updateIp;
    //修改时间
    private Date updateTime;
    //天线状态
    private Integer antennaState;
    //审核状态
    private Integer auditingState;

    private DatBasetower datBasetower;

    // 部门绑定
    private String belongDept;
    // 部门名称
    private String deptName;

    private String startTime;

    private String antennaNote;

    private String create_user;

    private String auditingUserId;

    //是否属于电信普遍服务
    private Integer ifTeleCmnServ;
    //电信普遍服务项目编码
    private String teleCmnServProCode;
    //电信普遍服务项目名称
    private String teleCmnServProName;

    public void setIfTeleCmnServ(Integer ifTeleCmnServ) {
        this.ifTeleCmnServ = ifTeleCmnServ;
    }

    public void setTeleCmnServProCode(String teleCmnServProCode) {
        this.teleCmnServProCode = teleCmnServProCode;
    }

    public void setTeleCmnServProName(String teleCmnServProName) {
        this.teleCmnServProName = teleCmnServProName;
    }

    public Integer getIfTeleCmnServ() {
        return ifTeleCmnServ;
    }

    public String getTeleCmnServProCode() {
        return teleCmnServProCode;
    }

    public String getTeleCmnServProName() {
        return teleCmnServProName;
    }

    public String getAuditingUserId() {
        return auditingUserId;
    }

    public void setAuditingUserId(String auditingUserId) {
        this.auditingUserId = auditingUserId;
    }

    public Integer getAuditingState() {
        return auditingState;
    }

    public void setAuditingState(Integer auditingState) {
        this.auditingState = auditingState;
    }

    public String getAntennaId() {
        return antennaId;
    }

    public void setAntennaId(String antennaId) {
        this.antennaId = antennaId == null ? null : antennaId.trim();
    }

    public String getProvinceCode() {
        return provinceCode;
    }

    public void setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode == null ? null : provinceCode.trim();
    }

    public String getAntennaCid() {
        return antennaCid;
    }

    public void setAntennaCid(String antennaCid) {
        this.antennaCid = antennaCid == null ? null : antennaCid.trim();
    }

    public String getAntennaName() {
        return antennaName;
    }

    public void setAntennaName(String antennaName) {
        this.antennaName = antennaName == null ? null : antennaName.trim();
    }

    public String getVendor() {
        return vendor;
    }

    public void setVendor(String vendor) {
        this.vendor = vendor == null ? null : vendor.trim();
    }

    public String getNeModel() {
        return neModel;
    }

    public void setNeModel(String neModel) {
        this.neModel = neModel == null ? null : neModel.trim();
    }

    public String getRelatedTowerCid() {
        return relatedTowerCid;
    }

    public void setRelatedTowerCid(String relatedTowerCid) {
        this.relatedTowerCid = relatedTowerCid == null ? null : relatedTowerCid.trim();
    }

    public Integer getRelatedTowerPlatform() {
        return relatedTowerPlatform;
    }

    public void setRelatedTowerPlatform(Integer relatedTowerPlatform) {
        this.relatedTowerPlatform = relatedTowerPlatform;
    }

    public Date getAdmissionDate() {
        return admissionDate;
    }

    public void setAdmissionDate(Date admissionDate) {
        this.admissionDate = admissionDate;
    }

    public String getAntennaType() {
        return antennaType;
    }

    public void setAntennaType(String antennaType) {
        this.antennaType = antennaType == null ? null : antennaType.trim();
    }

    public String getCtcBusinessConfirmNum() {
        return ctcBusinessConfirmNum;
    }

    public void setCtcBusinessConfirmNum(String ctcBusinessConfirmNum) {
        this.ctcBusinessConfirmNum = ctcBusinessConfirmNum == null ? null : ctcBusinessConfirmNum.trim();
    }

    public Integer getAntennaState() {
        return antennaState;
    }

    public void setAntennaState(Integer antennaState) {
        this.antennaState = antennaState;
    }

    public String getRelatedTowerCidName() {
        return relatedTowerCidName;
    }

    public void setRelatedTowerCidName(String relatedTowerCidName) {
        this.relatedTowerCidName = relatedTowerCidName;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((admissionDate == null) ? 0 : admissionDate.hashCode());
        result = prime * result + ((antennaCid == null) ? 0 : antennaCid.hashCode());
        result = prime * result + ((antennaHeight == null) ? 0 : antennaHeight.hashCode());
        result = prime * result + ((antennaName == null) ? 0 : antennaName.hashCode());
        result = prime * result + ((antennaType == null) ? 0 : antennaType.hashCode());
        result = prime * result + ((ctcBusinessConfirmNum == null) ? 0 : ctcBusinessConfirmNum.hashCode());
        result = prime * result + ((neModel == null) ? 0 : neModel.hashCode());
        result = prime * result + ((provinceCode == null) ? 0 : provinceCode.hashCode());
        result = prime * result + ((relatedTowerCid == null) ? 0 : relatedTowerCid.hashCode());
        result = prime * result + ((relatedTowerPlatform == null) ? 0 : relatedTowerPlatform.hashCode());
        result = prime * result + ((vendor == null) ? 0 : vendor.hashCode());
        return result;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;
        DatBaseantennaVO other = (DatBaseantennaVO) obj;
        if (admissionDate == null) {
            if (other.admissionDate != null) return false;
        } else if (!admissionDate.equals(other.admissionDate)) return false;
        if (antennaCid == null) {
            if (other.antennaCid != null) return false;
        } else if (!antennaCid.equals(other.antennaCid)) return false;
        if (antennaHeight == null) {
            if (other.antennaHeight != null) return false;
        } else if (!antennaHeight.equals(other.antennaHeight)) return false;
        if (antennaName == null) {
            if (other.antennaName != null) return false;
        } else if (!antennaName.equals(other.antennaName)) return false;
        if (antennaType == null) {
            if (other.antennaType != null) return false;
        } else if (!antennaType.equals(other.antennaType)) return false;
        if (ctcBusinessConfirmNum == null) {
            if (other.ctcBusinessConfirmNum != null) return false;
        } else if (!ctcBusinessConfirmNum.equals(other.ctcBusinessConfirmNum)) return false;
        if (neModel == null) {
            if (other.neModel != null) return false;
        } else if (!neModel.equals(other.neModel)) return false;
        if (provinceCode == null) {
            if (other.provinceCode != null) return false;
        } else if (!provinceCode.equals(other.provinceCode)) return false;
        if (relatedTowerCid == null) {
            if (other.relatedTowerCid != null) return false;
        } else if (!relatedTowerCid.equals(other.relatedTowerCid)) return false;
        if (relatedTowerPlatform == null) {
            if (other.relatedTowerPlatform != null) return false;
        } else if (!relatedTowerPlatform.equals(other.relatedTowerPlatform)) return false;
        if (vendor == null) {
            if (other.vendor != null) return false;
        } else if (!vendor.equals(other.vendor)) return false;
        return true;
    }

    public Integer getDataFrom() {
        return dataFrom;
    }

    public void setDataFrom(Integer dataFrom) {
        this.dataFrom = dataFrom;
    }

    public String getPrvId() {
        return prvId;
    }

    public void setPrvId(String prvId) {
        this.prvId = prvId;
    }

    public BigDecimal getAntennaHeight() {
        return antennaHeight;
    }

    public void setAntennaHeight(BigDecimal antennaHeight) {
        this.antennaHeight = antennaHeight;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public String getCreateIp() {
        return createIp;
    }

    public void setCreateIp(String createIp) {
        this.createIp = createIp;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
    }

    public String getUpdateIp() {
        return updateIp;
    }

    public void setUpdateIp(String updateIp) {
        this.updateIp = updateIp;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public DatBasetower getDatBasetower() {
        return datBasetower;
    }

    public void setDatBasetower(DatBasetower datBasetower) {
        this.datBasetower = datBasetower;
    }

    public String getBelongDept() {
        return belongDept;
    }

    public void setBelongDept(String belongDept) {
        this.belongDept = belongDept;
    }

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getAntennaNote() {
        return antennaNote;
    }

    public void setAntennaNote(String antennaNote) {
        this.antennaNote = antennaNote;
    }

    public String getCreate_user() {
        return create_user;
    }

    public void setCreate_user(String create_user) {
        this.create_user = create_user;
    }
}