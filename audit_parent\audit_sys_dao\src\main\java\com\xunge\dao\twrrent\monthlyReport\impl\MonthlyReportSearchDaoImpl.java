package com.xunge.dao.twrrent.monthlyReport.impl;

import com.xunge.core.page.Page;
import com.xunge.dao.AbstractBaseDao;
import com.xunge.dao.twrrent.monthlyReport.IMonthlyReportSearchDao;
import com.xunge.filter.PageInterceptor;
import com.xunge.model.towerrent.monthlyReport.*;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 月报查询
 */
@Repository("monthlyReportSearchDao")
public class MonthlyReportSearchDaoImpl extends AbstractBaseDao implements IMonthlyReportSearchDao {

    private final String namespace = "com.xunge.dao.twrrent.monthlyReport.TwrMonthlyReportMapper.";

    @Override
    public Page<RptMonthlyTwrSite> qyeryPageTwrSiteReportPrv(Map<String, Object> param, int pageSize, int pageNum) {
        PageInterceptor.startPage(pageNum, pageSize);
        this.getSqlSession().selectList(namespace + "qyeryPageTwrSiteReportPrv", param);
        return PageInterceptor.endPage();
    }

    @Override
    public List<RptMonthlyTwrSite> queryListTwrSiteReportPrv(Map<String, Object> param) {
        return this.getSqlSession().selectList(namespace + "qyeryPageTwrSiteReportPrv", param);
    }

    @Override
    public Page<RptMonthlyTwrSite> qyeryPageTwrSiteReportCity(Map<String, Object> param, int pageSize, int pageNum) {
        PageInterceptor.startPage(pageNum, pageSize);
        this.getSqlSession().selectList(namespace + "qyeryPageTwrSiteReportCity", param);
        return PageInterceptor.endPage();
    }

    @Override
    public List<RptMonthlyTwrSite> queryListTwrSiteReportCity(Map<String, Object> param) {
        return this.getSqlSession().selectList(namespace + "qyeryPageTwrSiteReportCity", param);
    }

    @Override
    public Page<RptMonthlyTwrSite> qyeryPageTwrSiteReportRegion(Map<String, Object> param, int pageSize, int pageNum) {
        PageInterceptor.startPage(pageNum, pageSize);
        this.getSqlSession().selectList(namespace + "qyeryPageTwrSiteReportRegion", param);
        return PageInterceptor.endPage();
    }

    @Override
    public List<RptMonthlyTwrSite> queryListTwrSiteReportRegion(Map<String, Object> param) {
        return this.getSqlSession().selectList(namespace + "qyeryPageTwrSiteReportRegion", param);
    }

    @Override
    public List<TwrSiteMoneyCompareWithService> getCompareInfoPrv(Map<String, Object> param) {
        return this.getSqlSession().selectList(namespace + "getCompareInfoPrv", param);
    }

    @Override
    public List<TwrSiteMoneyCompareWithService> getCompareInfoCity(Map<String, Object> param) {
        return this.getSqlSession().selectList(namespace + "getCompareInfoCity", param);
    }

    @Override
    public List<TwrSiteMoneyCompareWithService> getCompareInfoRegion(Map<String, Object> param) {
        return this.getSqlSession().selectList(namespace + "getCompareInfoRegion", param);
    }

    @Override
    public List<RptMonthlyTwrServiceCost> queryTowerServiceReportSearchPrv(Map<String, Object> map) {
        return this.getSqlSession().selectList(namespace + "queryTowerServiceReportSearchPrv", map);
    }

    @Override
    public List<RptMonthlyTwrServiceCost> queryTowerServiceReportSearchPreg(Map<String, Object> map) {
        return this.getSqlSession().selectList(namespace + "queryTowerServiceReportSearchPreg", map);
    }

    @Override
    public List<RptMonthlyTwrServiceCost> queryTowerServiceReportSearchReg(Map<String, Object> map) {
        return this.getSqlSession().selectList(namespace + "queryTowerServiceReportSearchReg", map);
    }

    //
    @Override
    public List<RptMonthlyBudgetAndProgress> querybudgetAndProgressPrv(Map<String, Object> map) {
        return this.getSqlSession().selectList(namespace + "querybudgetAndProgressPrv", map);
    }

    @Override
    public List<RptMonthlyBudgetAndProgress> querybudgetAndProgressCity(Map<String, Object> map) {
        return this.getSqlSession().selectList(namespace + "querybudgetAndProgressCity", map);
    }

    @Override
    public List<RptMonthlyBudgetAndProgress> querybudgetAndProgressReg(Map<String, Object> map) {
        return this.getSqlSession().selectList(namespace + "querybudgetAndProgressReg", map);
    }

    @Override
    public BigDecimal queryTotalAmount(Map<String, Object> map) {
        return this.getSqlSession().selectOne(namespace + "queryTotalAmount", map);
    }

    @Override
    public Integer queryNumAmount(Map<String, Object> map) {
        return this.getSqlSession().selectOne(namespace + "queryNumAmount", map);
    }


    @Override
    public List<RptMonthlyIconTowerCost> queryIronTowerReportSearchPrv(Map<String, Object> map) {
        return this.getSqlSession().selectList(namespace + "queryIronTowerReportSearchPrv", map);
    }

    @Override
    public List<RptMonthlyIconTowerCost> queryIronTowerReportSearchPreg(Map<String, Object> map) {
        return this.getSqlSession().selectList(namespace + "queryIronTowerReportSearchPreg", map);
    }

    @Override
    public List<RptMonthlyIconTowerCost> queryIronTowerReportSearchReg(Map<String, Object> map) {
        return this.getSqlSession().selectList(namespace + "queryIronTowerReportSearchReg", map);
    }

    @Override
    public List<RptMonthlyTwrServiceCost> queryIronTowerServiceReportSearchPrv(Map<String, Object> map) {
        return this.getSqlSession().selectList(namespace + "queryIronTowerServiceReportSearchPrv", map);
    }

    @Override
    public List<RptMonthlyTwrServiceCost> queryIronTowerServiceReportSearchPreg(Map<String, Object> map) {
        return this.getSqlSession().selectList(namespace + "queryIronTowerServiceReportSearchPreg", map);
    }

    @Override
    public List<RptMonthlyTwrServiceCost> queryIronTowerServiceReportSearchReg(Map<String, Object> map) {
        return this.getSqlSession().selectList(namespace + "queryIronTowerServiceReportSearchReg", map);
    }

    @Override
    public List<RptTowerAccount> queryTowerAccountReportPrv(Map<String, Object> param) {
        return this.getSqlSession().selectList(namespace + "queryTowerAccountReportPrv", param);
    }

    @Override
    public List<RptTowerAccount> queryTowerAccountReportPreg(Map<String, Object> param) {
        return this.getSqlSession().selectList(namespace + "queryTowerAccountReportPreg", param);
    }

    @Override
    public List<RptTowerAccount> queryTowerAccountReportReg(Map<String, Object> param) {
        return this.getSqlSession().selectList(namespace + "queryTowerAccountReportReg", param);
    }

    @Override
    public List<RptMonthlyBudgetAndProgressNew> getBudgetAndProgressAmoritizeDataWhole(Map<String, Object> paraMap) {
        return this.getSqlSession().selectList(namespace + "getBudgetAndProgressAmoritizeDataWhole", paraMap);
    }

    @Override
    public List<RptMonthlyBudgetAndProgressNew> getBudgetAndProgressAmoritizeDataPrv(Map<String, Object> paraMap) {
        return this.getSqlSession().selectList(namespace + "getBudgetAndProgressAmoritizeDataPrv", paraMap);
    }

    @Override
    public List<RptMonthlyBudgetAndProgressNew> getBudgetAndProgressAmoritizeDataPreg(Map<String, Object> paraMap) {
        return this.getSqlSession().selectList(namespace + "getBudgetAndProgressAmoritizeDataPreg", paraMap);
    }

    @Override
    public List<RptMonthlyIconTowerCost> queryTowerServiceFeeSearchPrv(Map<String, Object> paraMap) {
        return this.getSqlSession().selectList(namespace + "queryTowerServiceFeeSearchPrv", paraMap);
    }

    @Override
    public List<RptMonthlyIconTowerCost> queryTowerServiceFeeSearchPreg(Map<String, Object> paraMap) {
        return this.getSqlSession().selectList(namespace + "queryTowerServiceFeeSearchPreg", paraMap);
    }
}
