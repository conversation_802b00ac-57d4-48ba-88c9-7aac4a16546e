package com.xunge.model.basedata;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.xunge.model.BaseActVO;

import java.math.BigDecimal;
import java.util.Date;

public class DatBasetower extends BaseActVO {
    //铁塔id
    // @Excel(name="关联铁塔编码",orderNum="13")
    private String towerId;
    //铁塔cid
    @Excel(name = "关联铁塔CID", orderNum = "15")
    private String towerCid;
    //省份代码
    private String provinceCode;
    //省份id
    private String prvId;
    private String prvSname;
    private String pregId;
    private String pregName;
    private String regId;
    private String regName;
    private String towerSiteCode;
    //铁塔名称
    @Excel(name = "关联铁塔名称", orderNum = "16")
    private String towerName;
    //铁塔塔身高度
    private BigDecimal towerHeight;
    //铁塔平台数量
    private Integer platformNumber;
    //所属机房/位置点
    private String relatedNeCid;
    //所属机房/位置点名称
    private String relatedNeCidName;
    //铁塔类型
    private String towerType;
    @Excel(name = "产权性质", isImportField = "true", orderNum = "17", needMerge = true, replace = {"自有（自建)_1", "自有（合建）_2",
            "自有（购买）_3", "租用_4", "用户所有_5", "其他_6"})
    //产权性质
    private Integer towerProperty;
    //产权单位
    @Excel(name = "产权单位", isImportField = "true", orderNum = "18", needMerge = true, replace = {"中国铁塔_1", "中国移动_2", "中国联通_3",
            "中国电信_4", "中国铁通_5", "中国广电_6", "业主_7", "其他_8"})
    private Integer towerOwner;
    //共享单位
    private Integer shareAttribute;
    //入网时间
    private Date admissionDate;
    //退网时间
    private Date retireTime;
    //系统数量
    private Integer systemNumber;
    //铁塔产品种类
    private Integer towerCategory;
    //RRU是否上塔
    private Integer rruOnTower;
    //铁塔公司业务确认单号
    private String ctcBusinessConfirmNum;
    //共享信息
    private Integer shareInformation;
    //铁塔公司维护等级
    private Integer ctcMaintenceLevel;
    //风压系数
    private BigDecimal windPressure;
    //数据来源
    private Integer dataFrom;
    //创建用户
    private String create_user;
    //创建用户ip
    private String create_ip;
    //创建时间
    private Date create_time;
    //修改用户
    private String update_user;
    //修改用户ip
    private String update_ip;
    //修改时间
    private Date update_time;
    //铁塔状态
    private Integer towerState;

    private String startTime;
    private String operateUserId;

    /**
     * 审核状态
     */
    private String auditState;

    // 部门绑定
    private String belongDept;

    // 部门名称
    private String deptName;

    private String towerNote;

    private String auditingUserId;


    //安装位置类型
    private String locationType;
    //是否属于电信普遍服务
    private Integer ifTeleCmnServ;
    //电信普遍服务项目编码
    private String teleCmnServProCode;
    //电信普遍服务项目名称
    private String teleCmnServProName;

    public void setLocationType(String locationType) {
        this.locationType = locationType;
    }

    public void setIfTeleCmnServ(Integer ifTeleCmnServ) {
        this.ifTeleCmnServ = ifTeleCmnServ;
    }

    public void setTeleCmnServProCode(String teleCmnServProCode) {
        this.teleCmnServProCode = teleCmnServProCode;
    }

    public void setTeleCmnServProName(String teleCmnServProName) {
        this.teleCmnServProName = teleCmnServProName;
    }

    public String getLocationType() {
        return locationType;
    }

    public Integer getIfTeleCmnServ() {
        return ifTeleCmnServ;
    }

    public String getTeleCmnServProCode() {
        return teleCmnServProCode;
    }

    public String getTeleCmnServProName() {
        return teleCmnServProName;
    }

    public String getAuditingUserId() {
        return auditingUserId;
    }

    public void setAuditingUserId(String auditingUserId) {
        this.auditingUserId = auditingUserId;
    }

    public String getTowerId() {
        return towerId;
    }

    public void setTowerId(String towerId) {
        this.towerId = towerId == null ? null : towerId.trim();
    }

    public String getTowerCid() {
        return towerCid;
    }

    public void setTowerCid(String towerCid) {
        this.towerCid = towerCid == null ? null : towerCid.trim();
    }

    public String getProvinceCode() {
        return provinceCode;
    }

    public void setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode == null ? null : provinceCode.trim();
    }

    public String getTowerName() {
        return towerName;
    }

    public void setTowerName(String towerName) {
        this.towerName = towerName == null ? null : towerName.trim();
    }

    public Integer getPlatformNumber() {
        return platformNumber;
    }

    public void setPlatformNumber(Integer platformNumber) {
        this.platformNumber = platformNumber;
    }

    public String getRelatedNeCid() {
        return relatedNeCid;
    }

    public void setRelatedNeCid(String relatedNeCid) {
        this.relatedNeCid = relatedNeCid == null ? null : relatedNeCid.trim();
    }

    public String getTowerType() {
        return towerType;
    }

    public void setTowerType(String towerType) {
        this.towerType = towerType == null ? null : towerType.trim();
    }

    public Integer getTowerProperty() {
        return towerProperty;
    }

    public void setTowerProperty(Integer towerProperty) {
        this.towerProperty = towerProperty;
    }

    public Integer getTowerOwner() {
        return towerOwner;
    }

    public void setTowerOwner(Integer towerOwner) {
        this.towerOwner = towerOwner;
    }

    public Integer getShareAttribute() {
        return shareAttribute;
    }

    public void setShareAttribute(Integer shareAttribute) {
        this.shareAttribute = shareAttribute;
    }

    public Date getAdmissionDate() {
        return admissionDate;
    }

    public void setAdmissionDate(Date admissionDate) {
        this.admissionDate = admissionDate;
    }

    public Integer getSystemNumber() {
        return systemNumber;
    }

    public void setSystemNumber(Integer systemNumber) {
        this.systemNumber = systemNumber;
    }

    public Integer getTowerCategory() {
        return towerCategory;
    }

    public void setTowerCategory(Integer towerCategory) {
        this.towerCategory = towerCategory;
    }

    public Integer getRruOnTower() {
        return rruOnTower;
    }

    public void setRruOnTower(Integer rruOnTower) {
        this.rruOnTower = rruOnTower;
    }

    public String getCtcBusinessConfirmNum() {
        return ctcBusinessConfirmNum;
    }

    public void setCtcBusinessConfirmNum(String ctcBusinessConfirmNum) {
        this.ctcBusinessConfirmNum = ctcBusinessConfirmNum == null ? null : ctcBusinessConfirmNum.trim();
    }

    public Integer getShareInformation() {
        return shareInformation;
    }

    public void setShareInformation(Integer shareInformation) {
        this.shareInformation = shareInformation;
    }

    public Integer getCtcMaintenceLevel() {
        return ctcMaintenceLevel;
    }

    public void setCtcMaintenceLevel(Integer ctcMaintenceLevel) {
        this.ctcMaintenceLevel = ctcMaintenceLevel;
    }

    public Integer getTowerState() {
        return towerState;
    }

    public void setTowerState(Integer towerState) {
        this.towerState = towerState;
    }

    public String getRelatedNeCidName() {
        return relatedNeCidName;
    }

    public void setRelatedNeCidName(String relatedNeCidName) {
        this.relatedNeCidName = relatedNeCidName;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((admissionDate == null) ? 0 : admissionDate.hashCode());
        result = prime * result + ((ctcBusinessConfirmNum == null) ? 0 : ctcBusinessConfirmNum.hashCode());
        result = prime * result + ((ctcMaintenceLevel == null) ? 0 : ctcMaintenceLevel.hashCode());
        result = prime * result + ((platformNumber == null) ? 0 : platformNumber.hashCode());
        result = prime * result + ((provinceCode == null) ? 0 : provinceCode.hashCode());
        result = prime * result + ((relatedNeCid == null) ? 0 : relatedNeCid.hashCode());
        result = prime * result + ((rruOnTower == null) ? 0 : rruOnTower.hashCode());
        result = prime * result + ((shareAttribute == null) ? 0 : shareAttribute.hashCode());
        result = prime * result + ((shareInformation == null) ? 0 : shareInformation.hashCode());
        result = prime * result + ((systemNumber == null) ? 0 : systemNumber.hashCode());
        result = prime * result + ((towerCategory == null) ? 0 : towerCategory.hashCode());
        result = prime * result + ((towerCid == null) ? 0 : towerCid.hashCode());
        result = prime * result + ((towerHeight == null) ? 0 : towerHeight.hashCode());
        result = prime * result + ((towerName == null) ? 0 : towerName.hashCode());
        result = prime * result + ((towerOwner == null) ? 0 : towerOwner.hashCode());
        result = prime * result + ((towerProperty == null) ? 0 : towerProperty.hashCode());
        result = prime * result + ((towerType == null) ? 0 : towerType.hashCode());
        result = prime * result + ((windPressure == null) ? 0 : windPressure.hashCode());
        return result;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;
        DatBasetower other = (DatBasetower) obj;
        if (admissionDate == null) {
            if (other.admissionDate != null) return false;
        } else if (!admissionDate.equals(other.admissionDate)) return false;
        if (ctcBusinessConfirmNum == null) {
            if (other.ctcBusinessConfirmNum != null) return false;
        } else if (!ctcBusinessConfirmNum.equals(other.ctcBusinessConfirmNum)) return false;
        if (ctcMaintenceLevel == null) {
            if (other.ctcMaintenceLevel != null) return false;
        } else if (!ctcMaintenceLevel.equals(other.ctcMaintenceLevel)) return false;
        if (platformNumber == null) {
            if (other.platformNumber != null) return false;
        } else if (!platformNumber.equals(other.platformNumber)) return false;
        if (provinceCode == null) {
            if (other.provinceCode != null) return false;
        } else if (!provinceCode.equals(other.provinceCode)) return false;
        if (relatedNeCid == null) {
            if (other.relatedNeCid != null) return false;
        } else if (!relatedNeCid.equals(other.relatedNeCid)) return false;
        if (rruOnTower == null) {
            if (other.rruOnTower != null) return false;
        } else if (!rruOnTower.equals(other.rruOnTower)) return false;
        if (shareAttribute == null) {
            if (other.shareAttribute != null) return false;
        } else if (!shareAttribute.equals(other.shareAttribute)) return false;
        if (shareInformation == null) {
            if (other.shareInformation != null) return false;
        } else if (!shareInformation.equals(other.shareInformation)) return false;
        if (systemNumber == null) {
            if (other.systemNumber != null) return false;
        } else if (!systemNumber.equals(other.systemNumber)) return false;
        if (towerCategory == null) {
            if (other.towerCategory != null) return false;
        } else if (!towerCategory.equals(other.towerCategory)) return false;
        if (towerCid == null) {
            if (other.towerCid != null) return false;
        } else if (!towerCid.equals(other.towerCid)) return false;
        if (towerHeight == null) {
            if (other.towerHeight != null) return false;
        } else if (!towerHeight.equals(other.towerHeight)) return false;
        if (towerName == null) {
            if (other.towerName != null) return false;
        } else if (!towerName.equals(other.towerName)) return false;
        if (towerOwner == null) {
            if (other.towerOwner != null) return false;
        } else if (!towerOwner.equals(other.towerOwner)) return false;
        if (towerProperty == null) {
            if (other.towerProperty != null) return false;
        } else if (!towerProperty.equals(other.towerProperty)) return false;
        if (towerType == null) {
            if (other.towerType != null) return false;
        } else if (!towerType.equals(other.towerType)) return false;
        if (windPressure == null) {
            if (other.windPressure != null) return false;
        } else if (!windPressure.equals(other.windPressure)) return false;
        return true;
    }

    public Integer getDataFrom() {
        return dataFrom;
    }

    public void setDataFrom(Integer dataFrom) {
        this.dataFrom = dataFrom;
    }

    public String getCreate_user() {
        return create_user;
    }

    public void setCreate_user(String create_user) {
        this.create_user = create_user;
    }

    public String getCreate_ip() {
        return create_ip;
    }

    public void setCreate_ip(String create_ip) {
        this.create_ip = create_ip;
    }

    public Date getCreate_time() {
        return create_time;
    }

    public void setCreate_time(Date create_time) {
        this.create_time = create_time;
    }

    public String getUpdate_user() {
        return update_user;
    }

    public void setUpdate_user(String update_user) {
        this.update_user = update_user;
    }

    public String getUpdate_ip() {
        return update_ip;
    }

    public void setUpdate_ip(String update_ip) {
        this.update_ip = update_ip;
    }

    public Date getUpdate_time() {
        return update_time;
    }

    public void setUpdate_time(Date update_time) {
        this.update_time = update_time;
    }

    public String getPrvId() {
        return prvId;
    }

    public void setPrvId(String prvId) {
        this.prvId = prvId;
    }

    public BigDecimal getTowerHeight() {
        return towerHeight;
    }

    public void setTowerHeight(BigDecimal towerHeight) {
        this.towerHeight = towerHeight;
    }

    public BigDecimal getWindPressure() {
        return windPressure;
    }

    public void setWindPressure(BigDecimal windPressure) {
        this.windPressure = windPressure;
    }

    public String getAuditState() {
        return auditState;
    }

    public void setAuditState(String auditState) {
        this.auditState = auditState;
    }

    public String getBelongDept() {
        return belongDept;
    }

    public void setBelongDept(String belongDept) {
        this.belongDept = belongDept;
    }

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public String getPrvSname() {
        return prvSname;
    }

    public void setPrvSname(String prvSname) {
        this.prvSname = prvSname;
    }

    public String getPregName() {
        return pregName;
    }

    public void setPregName(String pregName) {
        this.pregName = pregName;
    }

    public String getRegId() {
        return regId;
    }

    public void setRegId(String regId) {
        this.regId = regId;
    }

    public String getRegName() {
        return regName;
    }

    public void setRegName(String regName) {
        this.regName = regName;
    }

    public String getTowerSiteCode() {
        return towerSiteCode;
    }

    public void setTowerSiteCode(String towerSiteCode) {
        this.towerSiteCode = towerSiteCode;
    }

    public String getPregId() {
        return pregId;
    }

    public void setPregId(String pregId) {
        this.pregId = pregId;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getOperateUserId() {
        return operateUserId;
    }

    public void setOperateUserId(String operateUserId) {
        this.operateUserId = operateUserId;
    }

    public String getTowerNote() {
        return towerNote;
    }

    public void setTowerNote(String towerNote) {
        this.towerNote = towerNote;
    }

    public Date getRetireTime() {
        return retireTime;
    }

    public void setRetireTime(Date retireTime) {
        this.retireTime = retireTime;
    }
}