package com.xunge.comm.utils;

import java.util.Random;

/**
 * @ClassName PasswordUtil
 * <AUTHOR>
 * @Date 2020-07-20
 **/
public class PasswordUtil {
    public final static String[] LOWER_CASES = {"a", "b", "c", "d", "e", "f", "g", "h", "i", "j", "k", "l", "m", "n", "o", "p", "q", "r", "s", "t", "u", "v", "w", "x", "y", "z"};
    public final static String[] UPPER_CASES = {"A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z"};
    public final static String[] NUMS_LIST = {"0", "1", "2", "3", "4", "5", "6", "7", "8", "9"};
    public final static String[] SYMBOLS_ARRAY = {"!", "~", "_", "*", "@"};
    private static final Random random = new Random();
    /**
     * 创建随机密码
     *
     * @param pwd_len 密码长度
     * @return 密码
     * 密码组成：2分之一的小写字母，4分之一的大写字母，8分之一数字，8分之一的特殊字符。可以按情况自由调配
     */
    public static String genRandomPwd(int pwd_len) {
        if (pwd_len < 6 || pwd_len > 20) {
            return "";
        }
        int lower = 3;

        int upper = 1;

        int symbol = 1;

        int num = pwd_len - lower - upper - symbol;

        StringBuffer pwd = new StringBuffer();

        while ((lower + upper) > 0) {
            if (upper > 0) {
                pwd.append(UPPER_CASES[random.nextInt(UPPER_CASES.length)]);
                upper--;
            }
            if (lower > 0) {
                pwd.append(LOWER_CASES[random.nextInt(LOWER_CASES.length)]);
                lower--;
            }
        }
        while ((num + symbol) > 0) {
            if (num > 0) {
                pwd.append(NUMS_LIST[random.nextInt(NUMS_LIST.length)]);
                num--;
            }
            if (symbol>0) {
                pwd.append(SYMBOLS_ARRAY[random.nextInt(SYMBOLS_ARRAY.length)]);
                symbol--;
            }

        }
        return pwd.toString();
    }

}
