package com.xunge.dao.report.Impl;

import com.xunge.dao.AbstractBaseDao;
import com.xunge.dao.report.IRptRentMonthStatisticsDao;
import com.xunge.model.report.RptRentMonthStatisticsVO;

import java.util.List;
import java.util.Map;

/**
 * @Auther: LinFei <PERSON>
 * @Date: 2018/12/26 11:26
 * @Description:
 */
public class RptRentMonthStatisticsDaoImpl extends AbstractBaseDao implements IRptRentMonthStatisticsDao {

    final String Namespace = "com.xunge.mapping.report.RptRentMonthStatisticsVOMapper.";

    @Override
    public List<RptRentMonthStatisticsVO> queryRentMonthStatistics(Map<String, Object> map) {
        return this.getSqlSession().selectList(Namespace + "queryRentMonthStatistics", map);
    }

    @Override
    public List<RptRentMonthStatisticsVO> queryRentMonthStatisticsByPregId(Map<String, Object> map) {
        return this.getSqlSession().selectList(Namespace + "queryRentMonthStatisticsByPregId", map);
    }

    @Override
    public List<RptRentMonthStatisticsVO> queryRentMonthStatisticsByRegId(Map<String, Object> map) {
        return this.getSqlSession().selectList(Namespace + "queryRentMonthStatisticsByRegId", map);
    }

    @Override
    public List<RptRentMonthStatisticsVO> queryRentPayableMonthStatistics(Map<String, Object> map) {
        return this.getSqlSession().selectList(Namespace + "queryRentPayableMonthStatistics", map);
    }

    @Override
    public List<RptRentMonthStatisticsVO> queryRentPayableMonthStatisticsByPregId(Map<String, Object> map) {
        return this.getSqlSession().selectList(Namespace + "queryRentPayableMonthStatisticsByPregId", map);
    }

    @Override
    public List<RptRentMonthStatisticsVO> queryRentPayableMonthStatisticsByRegId(Map<String, Object> map) {
        return this.getSqlSession().selectList(Namespace + "queryRentPayableMonthStatisticsByRegId", map);
    }

    @Override
    public List<RptRentMonthStatisticsVO> queryRentMonthStatisticsByPregIdCount(Map<String, Object> map) {
        return this.getSqlSession().selectList(Namespace + "queryRentMonthStatisticsByPregIdCount", map);
    }

    @Override
    public List<RptRentMonthStatisticsVO> queryRentMonthStatisticsByRegIdCount(Map<String, Object> map) {
        return this.getSqlSession().selectList(Namespace + "queryRentMonthStatisticsByRegIdCount", map);
    }

    @Override
    public List<RptRentMonthStatisticsVO> queryRentMonthStatisticsByPrvIdCount(Map<String, Object> map) {
        return this.getSqlSession().selectList(Namespace + "queryRentMonthStatisticsByPrvIdCount", map);
    }

    @Override
    public List<RptRentMonthStatisticsVO> queryRentPayableMonthStatisticsByPrvIdCount(Map<String, Object> map) {
        return this.getSqlSession().selectList(Namespace + "queryRentPayableMonthStatisticsByPrvIdCount", map);
    }

    @Override
    public List<RptRentMonthStatisticsVO> queryRentPayableMonthStatisticsByPregIdCount(Map<String, Object> map) {
        return this.getSqlSession().selectList(Namespace + "queryRentPayableMonthStatisticsByPregIdCount", map);
    }

    @Override
    public List<RptRentMonthStatisticsVO> queryRentPayableMonthStatisticsByRegIdCount(Map<String, Object> map) {
        return this.getSqlSession().selectList(Namespace + "queryRentPayableMonthStatisticsByRegIdCount", map);
    }
}
