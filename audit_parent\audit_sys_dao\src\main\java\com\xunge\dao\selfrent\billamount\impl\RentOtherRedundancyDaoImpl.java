package com.xunge.dao.selfrent.billamount.impl;

import com.xunge.dao.AbstractBaseDao;
import com.xunge.dao.selfrent.billamount.RentOtherRedundancyDao;
import com.xunge.model.selfrent.billamount.RentOtherRedundancyFinance;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2017年6月27日 上午10:14:34
 */
public class RentOtherRedundancyDaoImpl extends AbstractBaseDao implements RentOtherRedundancyDao {

    final String Namespace = "com.xunge.dao.selfelec.RentOtherRedundancyFinanceMapper.";

    /**
     * 查询对应缴费单主键的所有其它费用集合
     *
     * @param billaccountpaymentdetailId 核销缴费单主键
     * @return 集合
     */
    public List<RentOtherRedundancyFinance> queryOtherAmountFinanceList(String billaccountpaymentdetailId) {
        return this.getSqlSession().selectList(Namespace + "queryOtherAmountFinanceList", billaccountpaymentdetailId);
    }

    public int insert(RentOtherRedundancyFinance rentOtherRedundancyFinance) {
        return this.getSqlSession().insert(Namespace + "insert", rentOtherRedundancyFinance);
    }

    /**
     * 根据汇总单ID删除明细信息
     *
     * @param billamountId
     */
    public int deleteByBillamountId(String billamountId) {
        return this.getSqlSession().delete(Namespace + "deleteByBillamountId", billamountId);
    }

    /**
     * 根据汇总单详情ID删除明细信息
     *
     * @param billamountDetailId
     */
    public int deleteByBillamountDetailId(String billamountDetailId) {
        return this.getSqlSession().delete(Namespace + "deleteByBillamountDetailId", billamountDetailId);
    }

    public void updateSecondBillamountById(String secondBillamountId,String otherId){
        Map<String,Object> map = new HashMap<>();
        map.put("secondBillamountId",secondBillamountId);
        map.put("otherId",otherId);
        this.getSqlSession().update(Namespace + "updateSecondBillamountById",map);
    }
}
