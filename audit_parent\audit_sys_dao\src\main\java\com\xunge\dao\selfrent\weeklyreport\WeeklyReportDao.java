package com.xunge.dao.selfrent.weeklyreport;

import com.xunge.model.selfrent.weeklyreport.*;

import java.util.List;
import java.util.Map;

public interface WeeklyReportDao {

    List<DateScopeRelationVO> queryDateScopeWeeklyReport(Map<String,Object> map);

    Map<String,Object> getPeroidAndUpdateTime(Map<String,Object> map);

    List<RentWeeklyReportVO> queryRentWeeklyReportListSum(Map<String,Object> map);

    List<RentWeeklySuspectedProblemVO> queryRentWeeklyProblemListSum(Map<String,Object> map);

    List<RentWeeklyReportVO> queryRentWeeklyReportList(Map<String,Object> map);

    List<RentWeeklySuspectedProblemVO> queryRentWeeklyProblemList(Map<String,Object> map);

    /**
     * 根据参数查询实际报账金额-系统计算报账金额＞1元明细
     * @param paraMap 参数
     * @return List<RentWeeklyAmountPassOneVo> 集合
     */
    List<RentWeeklyAmountPassOneVo> queryRentWeeklyAmountPassOneList(Map<String, Object> paraMap);

    /**
     * 根据参数查询特殊报账单明细
     * @param paraMap 参数
     * @return List<RentWeeklySpecialReimburseVo> 集合
     */
    List<RentWeeklySpecialReimburseVo> queryRentWeeklySpecialReimburseList(Map<String, Object> paraMap);

    /**
     * 根据参数查询资源含状态为工程或退网的稽核单明细
     * @param paraMap 参数
     * @return List<RentWeeklyAbnormalResourceVo> 集合
     */
    List<RentWeeklyAbnormalResourceVo> queryRentWeeklyAbnormalResourceList(Map<String, Object> paraMap);

    /**
     * 根据参数查询外部价格年标杆超标明细
     * @param paraMap 参数
     * @return List<RentWeeklyBenchmarkOverproofVo> 集合
     */
    List<RentWeeklyBenchmarkOverproofVo> queryRentWeeklyBenchmarkOverproofList(Map<String, Object> paraMap);
}
