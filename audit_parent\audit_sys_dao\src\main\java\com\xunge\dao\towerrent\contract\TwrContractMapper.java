package com.xunge.dao.towerrent.contract;

import com.xunge.model.towerrent.contract.TwrContract;

import java.util.List;

public interface TwrContractMapper {
    
    int deleteByPrimaryKey(String contractId);

    
    int insert(TwrContract record);

    
    int insertSelective(TwrContract record);

    
    TwrContract selectByPrimaryKey(String contractId);

    
    int updateByPrimaryKeySelective(TwrContract record);

    
    int updateByPrimaryKey(TwrContract record);

    List<TwrContract> findListByEntity(TwrContract twrContract);

    int deleteBatch(List<String> contractIds);

    List<TwrContract> qryOneReg(TwrContract twrContract);

    List<TwrContract> qryMoreReg(TwrContract twrContract);

    /**
     * 查询直辖市合同
     *
     * @param twrContract
     * @return
     */
    List<TwrContract> findRegContract(TwrContract twrContract);
}