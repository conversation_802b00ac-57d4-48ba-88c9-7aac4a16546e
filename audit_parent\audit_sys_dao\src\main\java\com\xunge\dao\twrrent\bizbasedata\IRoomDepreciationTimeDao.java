package com.xunge.dao.twrrent.bizbasedata;

import com.xunge.core.page.Page;
import com.xunge.model.towerrent.bizbasedata.RoomDepreciationTimeVO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2018年06月06日
 * @description 机房折旧年限
 */
public interface IRoomDepreciationTimeDao {
    /**
     * 分页查询所有机房折旧年限集合
     *
     * @param pageSize
     * @param pageNumber
     * @return
     */
    Page<List<RoomDepreciationTimeVO>> queryRoomDepreciationTimeVO(int pageSize, int pageNumber, Map<String, Object> paramMap);

    List<RoomDepreciationTimeVO> queryRoomDepreciationTime(Map<String, Object> paramMap);

    /**
     * 根据Id删除机房折旧年限对象
     *
     * @param ids
     * @return
     */
    String deleteRoomDepreciationTimeById(List<String> ids);

    /**
     * 修改所选机房折旧年限对象
     *
     * @param roomDepreciationTimeVO
     * @return
     */
    String updateRoomDepreciationTimeById(RoomDepreciationTimeVO roomDepreciationTimeVO);

    /**
     * 新增机房折旧年限对象
     *
     * @param roomDepreciationTimeVO
     * @return
     */
    String insertRoomDepreciationTimeById(RoomDepreciationTimeVO roomDepreciationTimeVO);

    /**
     * 启用或停用机房折旧年限状态
     *
     * @param ids
     * @param state
     * @return
     */
    String startOrStopRoomDepreciationTimeById(List<String> ids, String state);

}
