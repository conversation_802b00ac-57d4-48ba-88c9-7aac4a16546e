package com.xunge.dao.selfelec.payment;


import com.xunge.model.selfelec.payment.ElePaymentChangeRecord;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【ele_payment_change_record(电费修改记录表)】的数据库操作Mapper
* @createDate 2024-09-24 17:24:14
* @Entity generator.domain.ElePaymentChangeRecord
*/
public interface ElePaymentChangeRecordMapper {

    int deleteByPrimaryKey(Long id);

    int insert(ElePaymentChangeRecord record);

    int insertSelective(ElePaymentChangeRecord record);

    ElePaymentChangeRecord selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ElePaymentChangeRecord record);

    int updateByPrimaryKey(ElePaymentChangeRecord record);

    int insertForBatch(List<ElePaymentChangeRecord> record);

}
