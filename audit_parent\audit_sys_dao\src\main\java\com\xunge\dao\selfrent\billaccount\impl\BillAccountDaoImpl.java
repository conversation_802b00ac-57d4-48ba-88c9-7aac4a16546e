package com.xunge.dao.selfrent.billaccount.impl;

import com.xunge.core.page.Page;
import com.xunge.dao.AbstractBaseDao;
import com.xunge.dao.selfrent.billaccount.IBillAccountDao;
import com.xunge.filter.PageInterceptor;
import com.xunge.model.selfelec.EleResourceRelationHistory;
import com.xunge.model.selfelec.ElecontractRelationHistory;
import com.xunge.model.selfrent.billAccount.BillAccountVO;
import com.xunge.model.selfrent.billamount.RentBillAmountHisPayVo;
import com.xunge.model.selfrent.contract.RentWarningVO;
import com.xunge.model.towerrent.settlement.BillCheckPraceFeeRentVO;
import com.xunge.model.towerrent.settlement.BillCheckPraceFeeVO;

import java.util.List;
import java.util.Map;

public class BillAccountDaoImpl extends AbstractBaseDao implements IBillAccountDao {

    final String Namespace = "com.xunge.mapping.BillAccountVOMapper.";

    @Override
    public Page<BillAccountVO> queryBillAccountVO(Map<String, Object> map, int pageNumber, int pageSize) {
        PageInterceptor.startPage(pageNumber, pageSize);
        this.getSqlSession().selectList(Namespace + "queryBillAccountVO", map);
        return PageInterceptor.endPage();
    }

    @Override
    public Page<BillAccountVO> queryFinanceBillAccountVO(Map<String, Object> map, int pageNumber, int pageSize) {
        PageInterceptor.startPage(pageNumber, pageSize);
        this.getSqlSession().selectList(Namespace + "queryFinanceBillAccountVO", map);
        return PageInterceptor.endPage();
    }

    @Override
    public BillAccountVO queryBillAccountById(String billAccountId) {
        // TODO Auto-generated method stub
        return this.getSqlSession().selectOne(Namespace + "queryBillAccountById", billAccountId);
    }

    @Override
    public List<Map<String, Object>> queryBillaccountRelations(Map<String, Object> paraMap) {
        return this.getSqlSession().selectList(Namespace + "queryBillaccountRelations", paraMap);
    }

    @Override
    public Page<RentWarningVO> queryAllbillaccountWarning(Map<String, Object> paraMap, int pageNumber, int pageSize) {
        PageInterceptor.startPage(pageNumber, pageSize);
        this.getSqlSession().selectList(Namespace + "queryAllbillaccountWarning", paraMap);
        return PageInterceptor.endPage();
    }

    @Override
    public Map<String, Object> queryContractStateByBillaccountId(Map<String, Object> paraMap) {
        return this.getSqlSession().selectOne(Namespace + "queryContractStateByBillaccountId", paraMap);
    }

    /**
     * @description 查询所有租费缴费预警
     * <AUTHOR>
     * @date 创建时间：2017年11月2日
     */
    @Override
    public List<RentWarningVO> queryAllbillaccountWarning(Map<String, Object> map) {
        return this.getSqlSession().selectList(Namespace + "queryAllbillaccountWarning", map);
    }

    @Override
    public List<Map<String, Object>> queryResourceStateByBillaccountId(String billaccountId) {
        return this.getSqlSession().selectList(Namespace + "queryResourceStateByBillaccountId", billaccountId);
    }

    @Override
    public List<Map<String, Object>> queryTowerStateByBillaccountId(String billaccountId) {
        return this.getSqlSession().selectList(Namespace + "queryTowerStateByBillaccountId", billaccountId);
    }

    @Override
    public int updateRentBillPlanDate(Map<String, Object> map) {
        return this.getSqlSession().update(Namespace + "updateRentBillPlanDate", map);
    }

    @Override
    public void updateRentBillaccountPlanDate(Map<String, String> map) {
        this.getSqlSession().update(Namespace + "updateRentBillaccountPlanDate", map);
    }

    @Override
    public Map<String, Object> queryOldCS(Map<String, Object> map) {
        return this.getSqlSession().selectOne(Namespace + "queryOldCS", map);
    }

    @Override
    public Map<String, Object> queryCS(Map<String, Object> map) {
        return this.getSqlSession().selectOne(Namespace + "queryCS", map);
    }

    @Override
    public List<String> queryOldRes(Map<String, Object> map) {
        return this.getSqlSession().selectList(Namespace + "queryOldRes", map);
    }

    @Override
    public List<String> queryRes(Map<String, Object> map) {
        return this.getSqlSession().selectList(Namespace + "queryRes", map);
    }

    @Override
    public List<Map<String, Object>> queryRegIdAndNameByPrvId(String prvId) {
        return this.getSqlSession().selectList(Namespace + "queryRegIdAndNameByPrvId", prvId);
    }

    @Override
    public List<Map<String, Object>> queryUnusedContractByPrvId(String prvId) {
        return this.getSqlSession().selectList(Namespace + "queryUnusedContractByPrvId", prvId);
    }

    @Override
    public List<Map<String, Object>> queryUnusedResourceByPrvId(String prvId) {
        return this.getSqlSession().selectList(Namespace + "queryUnusedResourceByPrvId", prvId);
    }

    @Override
    public int insertImportBillAcount(Map<String, Object> map) {
        return this.getSqlSession().insert(Namespace + "insertImportBillAcount", map);
    }

    @Override
    public int insertImportBillcontract(Map<String, Object> map) {
        return this.getSqlSession().insert(Namespace + "insertImportBillcontract", map);
    }

    @Override
    public int insertImportBillresource(Map<String, Object> map) {
        return this.getSqlSession().insert(Namespace + "insertImportBillresource", map);
    }

    @Override
    public List<Map<String, Object>> queryBillaccountTowerById(String billaccountId) {
        return this.getSqlSession().selectList(Namespace + "queryBillaccountTowerById", billaccountId);
    }

    @Override
    public List<BillAccountVO> queryBillaccountByPaymentIds(Map<String, Object> map) {
        return this.getSqlSession().selectList(Namespace + "queryBillaccountByPaymentIds", map);
    }

    @Override
    public List<ElecontractRelationHistory> selContractHistory(Map<String, Object> map) {
        return this.getSqlSession().selectList(Namespace + "selContractHistory", map);
    }

    @Override
    public List<EleResourceRelationHistory> queryResourceHistory(Map<String, Object> map) {
        return this.getSqlSession().selectList(Namespace + "queryResourceHistory", map);
    }

    @Override
    public List<EleResourceRelationHistory> queryRentTowerHistory(Map<String, Object> map) {
        return this.getSqlSession().selectList(Namespace + "queryRentTowerHistory", map);
    }

    @Override
    public List<String> queryDatBaseResourceToTowerSiteCode(String billaccountId) {
        return this.getSqlSession().selectList(Namespace + "queryDatBaseResourceToTowerSiteCode", billaccountId);
    }

    @Override
    public List<String> queryDatBaseTowerToTowerSiteCode(String billaccountId) {
        return this.getSqlSession().selectList(Namespace + "queryDatBaseTowerToTowerSiteCode", billaccountId);
    }

    @Override
    public List<BillCheckPraceFeeRentVO> queryDatBaseResourceToTowerSite(String billaccountId) {
        return this.getSqlSession().selectList(Namespace + "queryDatBaseResourceToTowerSite", billaccountId);
    }

    @Override
    public List<BillCheckPraceFeeRentVO> queryDatBaseTowerToTowerSite(String billaccountId) {
        return this.getSqlSession().selectList(Namespace + "queryDatBaseTowerToTowerSite", billaccountId);
    }

    @Override
    public List<BillCheckPraceFeeRentVO> queryDatBaseResourceToRelationSource(Map<String, Object> map) {
        return this.getSqlSession().selectList(Namespace + "queryDatBaseResourceToRelationSource", map);
    }

    @Override
    public List<BillCheckPraceFeeRentVO> queryAllTowerSite(Map<String, Object> map) {
        return this.getSqlSession().selectList(Namespace + "queryAllTowerSite", map);
    }

    @Override
    public List<BillCheckPraceFeeVO> queryTowerSiteWithPraceFeeNotZero(Map<String, Object> map) {
        return this.getSqlSession().selectList(Namespace + "queryTowerSiteWithPraceFeeNotZero", map);
    }

    @Override
    public List<BillCheckPraceFeeVO> queryTowerBillCheckPraceFee(Map<String, Object> map) {
        return this.getSqlSession().selectList(Namespace + "queryTowerBillCheckPraceFee", map);
    }

    @Override
    public List<BillCheckPraceFeeVO> queryRoomBillCheckPraceFee(Map<String, Object> map) {
        return this.getSqlSession().selectList(Namespace + "queryRoomBillCheckPraceFee", map);
    }

    @Override
    public List<BillCheckPraceFeeVO> queryTinyBillCheckPraceFee(Map<String, Object> map) {
        return this.getSqlSession().selectList(Namespace + "queryTinyBillCheckPraceFee", map);
    }

    @Override
    public List<BillCheckPraceFeeVO> queryNonstandBillCheckPraceFee(Map<String, Object> map) {
        return this.getSqlSession().selectList(Namespace + "queryNonstandBillCheckPraceFee", map);
    }

    @Override
    public List<BillCheckPraceFeeVO> queryCheckPraceFeeUnionAll(Map<String, Object> map) {
        return this.getSqlSession().selectList(Namespace + "queryCheckPraceFeeUnionAll", map);
    }

    @Override
    public RentBillAmountHisPayVo queryHisPayedTotalAmount(Map<String, Object> map) {
        return this.getSqlSession().selectOne(Namespace + "queryHisPayedTotalAmount", map);
    }

    @Override
    public int checkPaymentStartTime(Map<String, Object> map){
        return this.getSqlSession().selectOne(Namespace + "checkPaymentStartTime", map);
    }

    @Override
    public RentBillAmountHisPayVo queryContractId(Map<String, Object> map) {
        return this.getSqlSession().selectOne(Namespace + "queryContractId", map);
    }

    @Override
    public int checkResourceDateOverLap(Map<String, Object> map) {
        return this.getSqlSession().selectOne(Namespace + "checkResourceDateOverLap", map);
    }

    @Override
    public Integer queryBillAccountIsMutilContract(String billAccountId) {
        return this.getSqlSession().selectOne(Namespace + "queryBillAccountIsMutilContract", billAccountId);
    }
}
