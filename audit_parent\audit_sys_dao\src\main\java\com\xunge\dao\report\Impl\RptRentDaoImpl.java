package com.xunge.dao.report.Impl;

import com.xunge.dao.AbstractBaseDao;
import com.xunge.dao.report.IRptRentDao;
import com.xunge.model.report.RptRentBillaccountVO;
import com.xunge.model.report.RptRentBillamountVO;
import com.xunge.model.report.RptRentPaymentVO;
import com.xunge.model.selfrent.RptPrvRentPaymentRedundancyMon;

import java.util.List;
import java.util.Map;

public class RptRentDaoImpl extends AbstractBaseDao implements IRptRentDao {

    final String RptRentBillaccountNamespace = "com.xunge.mapping.report.RptRentBillaccountMapper.";
    final String RptRentBillamountNamespace = "com.xunge.mapping.report.RptRentBillamountMapper.";
    final String RptRentPaymentNamespace = "com.xunge.mapping.report.RptRentPaymentMapper.";

    @Override
    public List<RptRentBillaccountVO> queryRptRentBillaccount(Map<String, Object> paraMap) {

        return this.getSqlSession().selectList(RptRentBillaccountNamespace + "queryRptRentBillaccount", paraMap);
    }

    @Override
    public List<RptRentBillaccountVO> queryRptRentBillaccountByPrvId(Map<String, Object> paraMap) {

        return this.getSqlSession().selectList(RptRentBillaccountNamespace + "queryRptRentBillaccountByPrvId", paraMap);
    }

    @Override
    public List<RptRentBillaccountVO> queryRptRentBillaccountByPregId(Map<String, Object> paraMap) {

        return this.getSqlSession().selectList(RptRentBillaccountNamespace + "queryRptRentBillaccountByPregId", paraMap);
    }

    @Override
    public List<RptRentBillamountVO> queryRptRentBillamount(Map<String, Object> paraMap) {
        return this.getSqlSession().selectList(RptRentBillamountNamespace + "queryRptRentBillamount", paraMap);
    }

    @Override
    public List<RptRentBillamountVO> queryRptRentBillamountByPrvId(Map<String, Object> paraMap) {
        return this.getSqlSession().selectList(RptRentBillamountNamespace + "queryRptRentBillamountByPrvId", paraMap);
    }

    @Override
    public List<RptRentBillamountVO> queryRptRentBillamountByPregId(Map<String, Object> paraMap) {
        return this.getSqlSession().selectList(RptRentBillamountNamespace + "queryRptRentBillamountByPregId", paraMap);
    }

    @Override
    public List<RptRentPaymentVO> queryRptRentPayment(Map<String, Object> paraMap) {
        return this.getSqlSession().selectList(RptRentPaymentNamespace + "queryRptRentPayment", paraMap);
    }

    @Override
    public List<RptRentPaymentVO> queryRptRentPaymentByPrvId(Map<String, Object> paraMap) {
        return this.getSqlSession().selectList(RptRentPaymentNamespace + "queryRptRentPaymentByPrvId", paraMap);
    }

    @Override
    public List<RptRentPaymentVO> queryRptRentPaymentByPregId(Map<String, Object> paraMap) {
        return this.getSqlSession().selectList(RptRentPaymentNamespace + "queryRptRentPaymentByPregId", paraMap);
    }

    @Override
    public List<RptPrvRentPaymentRedundancyMon> queryRedundancyById(Map<String, Object> paraMap) {
        return this.getSqlSession().selectList(RptRentBillamountNamespace + "queryRedundancyById", paraMap);
    }

}
