package com.xunge.dao.twrrent.towerReport;

import com.xunge.model.towerrent.monthlyReport.TwrTeleCmnReport;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Description:
 * @Author: dxd
 * @Date: 2022/9/13
 */
public interface TowerTeleCmnMapper {


    /**
     * 查询起租对比分析表
     * @param prvId 省份ID
     * @param accountPeroid 账期
     * @param dataType 1 摊销 2 应付
     * @param querySign 查询标识
     * @return
     */
    List<TwrTeleCmnReport> queryTeleCmnReport(@Param("prvId")String prvId,
                                              @Param("accountPeroid") String accountPeroid,
                                              @Param("dataType") Integer dataType,
                                              @Param("querySign") Integer querySign);

    List<TwrTeleCmnReport> getThreeTeleCmnReports(@Param("prvId")String prvId,
                                                  @Param("accountPeroid") String accountPeroid,
                                                  @Param("dataType") Integer dataType,
                                                  @Param("querySign") Integer querySign);

    TwrTeleCmnReport getTwrCountReports(@Param("prvId")String prvId,
                                        @Param("accountPeroid") String accountPeroid,
                                        @Param("dataType") Integer dataType,
                                        @Param("querySign") Integer querySign);
}
