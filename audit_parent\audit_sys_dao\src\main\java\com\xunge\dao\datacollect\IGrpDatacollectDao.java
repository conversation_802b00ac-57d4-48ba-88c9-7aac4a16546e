package com.xunge.dao.datacollect;

import com.xunge.core.page.Page;
import com.xunge.model.datacollect.GrpDatacollectVO;

import java.util.List;
import java.util.Map;

public interface IGrpDatacollectDao {
    /**
     * 根据条件查寻集团收集表
     *
     * @param paraMap
     * @return
     * <AUTHOR>
     */
    public Page<GrpDatacollectVO> queryGrpDataCollectVO(Map<String, Object> paraMap, int pageNumber, int pageSize);

    /**
     * 根据id删除集团收集表
     *
     * @param datacollectId
     * @return
     * <AUTHOR>
     */
    public int deleteByPrimaryKey(String datacollectId);

    /**
     * 新增集团收集表
     *
     * @param grpDatacollectVO
     * @return
     * <AUTHOR>
     */
    public int insertSelective(GrpDatacollectVO grpDatacollectVO);

    /**
     * 修改集团收集表
     *
     * @param grpDatacollectVO
     * @return
     * <AUTHOR>
     */
    public int updateByPrimaryKeySelective(GrpDatacollectVO grpDatacollectVO);

    /**
     * 根据id查询集团收集表信息
     *
     * @param paraMap
     * @return
     */
    public GrpDatacollectVO queryGrpDataCollectById(Map<String, Object> paraMap);

    /**
     * 集团收集表派发到省份
     *
     * @param paraMap
     * @return
     */
    public int updateStateById(Map<String, Object> paraMap);

    /**
     * 验证是否存在相同的标题
     *
     * @param datacollectTitle
     * @return
     */
    public String querySameThing(String datacollectTitle);

    /**
     * 根据集团收集id查询标题
     *
     * @param paraMap
     * @return
     */
    public GrpDatacollectVO queryTitleById(Map<String, Object> paraMap);

    /**
     * 根据集团收集id查询抄送用户
     *
     * @param paraMap
     * @return
     */
    public String queryCopyUserById(Map<String, Object> paraMap);

    /**
     * 修改集团收集状态为已完结
     *
     * @param paraMap
     * @return
     */
    public int updateDatacollectToFinish(Map<String, Object> paraMap);

    /**
     * 根据省公司上报信息编码查询集团工单派发时间
     *
     * @param map
     * @return
     */
    public String querySendDateByPrvId(Map<String, Object> map);

    /**
     * 31省数据收集统计分析
     *
     * @param datacollectId
     * @return
     */
    public List<Map<String, Object>> queryAnalysisReport(String datacollectId);
}
