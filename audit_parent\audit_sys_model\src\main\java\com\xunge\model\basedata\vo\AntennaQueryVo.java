package com.xunge.model.basedata.vo;

import java.io.Serializable;

public class AntennaQueryVo extends BaseDataVO implements Serializable {

    /**
     *
     */
    private static final long serialVersionUID = 1L;

    /**
     * 模糊查询
     */
    private String tennaReg;

    /**
     * 类型
     */
    private String antennaType;
    private String[] antennaIds;
    private String auditStatus;
    private String depId;
    private Integer dataFrom;
    private String dataFroms;
    private Integer ifTeleCmnServ;

    public void setIfTeleCmnServ(Integer ifTeleCmnServ) {
        this.ifTeleCmnServ = ifTeleCmnServ;
    }

    public Integer getIfTeleCmnServ() {
        return ifTeleCmnServ;
    }

    public String getDataFroms() {
        return dataFroms;
    }

    public void setDataFroms(String dataFroms) {
        this.dataFroms = dataFroms;
    }

    public Integer getDataFrom() {
        return dataFrom;
    }

    public void setDataFrom(Integer dataFrom) {
        this.dataFrom = dataFrom;
    }

    public String getTennaReg() {
        return tennaReg;
    }

    public void setTennaReg(String tennaReg) {
        this.tennaReg = tennaReg;
    }

    public String getAntennaType() {
        return antennaType;
    }

    public void setAntennaType(String antennaType) {
        this.antennaType = antennaType;
    }

    public String[] getAntennaIds() {
        return antennaIds;
    }

    public void setAntennaIds(String[] antennaIds) {
        this.antennaIds = antennaIds;
    }

    public String getAuditStatus() {
        return auditStatus;
    }

    public void setAuditStatus(String auditStatus) {
        this.auditStatus = auditStatus;
    }

    public String getDepId() {
        return depId;
    }

    public void setDepId(String depId) {
        this.depId = depId;
    }
}
