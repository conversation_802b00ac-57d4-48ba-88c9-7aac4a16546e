package com.xunge.dao.activity;

import com.xunge.model.activity.Act;
import com.xunge.model.activity.ActHiTaskinst;
import org.activiti.engine.impl.persistence.entity.CommentEntity;
import org.activiti.engine.impl.persistence.entity.HistoricActivityInstanceEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface ActHiTaskinstMapper {
    //根据流程实例id获取上级审核人对象
    ActHiTaskinst superiorAssigneeByInstId(String procInstId);

    //查询已办流程上级审核信息
    List<ActHiTaskinst> superiorAssignee(String procInstId);

    Act getbaseresource(String procInstId);

    Act getSite(String procInstId);

    Act getBaseantenna(String procInstId);

    Act getTower(String procInstId);

    Act getEleContract(String procInstId);

    Act getRentContract(String procInstId);

    Act getEleBillacount(String procInstId);
    Act getDatElectricmeter(@Param("key")String procInstId, @Param("auditType")Integer auditType);

    Act getDsBillacount(String procInstId);

    Act getRentBillacount(String procInstId);

    Act getElePayment(String procInstId);

    Act getDsElePayment(String procInstId);

    Act getRentPayment(String procInstId);

    Act getLoan(String procInstId);

    Act getVerification(String procInstId);

    /**
     * 根据流程实例id和上级审核人名称查询上级信息
     *
     * @param actHiTaskinst
     * @return
     */
    ActHiTaskinst querySuperiorByInstId(ActHiTaskinst actHiTaskinst);

    /**
     * 根据所有流程流程的key
     *
     * @proDefinId
     */
    ActHiTaskinst queryKeyByDefinId(String definitionId);

    /**
     * 根据用户登录名查询待办列表
     *
     * @param actHiTaskinst
     * @return
     */
    List<ActHiTaskinst> queryToDoActList(ActHiTaskinst actHiTaskinst);


    List<ActHiTaskinst> queryToDoAct(ActHiTaskinst actHiTaskinst);

    /**
     * 根据组名称查询待办列表
     *
     * @param groupNams
     * @return
     */
    List<ActHiTaskinst> queryToDoActListByGroup(List<String> groupNams);

    Act getRecoverPayment(String procInstId);

    Act getRentRentAnomalousWarning(String procInstId);

    List<Act> queryToDoActListByloginName(Map<String, String> map);

    Act queryToDoActTimeByloginName(Map<String, String> map);

    int queryToDoActNumByloginName(Map<String, String> map);

    int queryAlreadyActNumByloginName(Map<String, String> map);

    Act getDsDatContract(String key);

    Act getTeleDatContract(String key);

    Act getTeleBillacount(String key);

    Act getTeleElePayment(String key);

    Act getAppInspection(String procInstId);

    List<ActHiTaskinst> queryActExcution(Map<String, Object> emergencyDegree);

    List<ActHiTaskinst> queryActProcinst(Map<String, Object> cond);

    Act getAppInspectionNew(String procInstId);

    Act getEleAccrual(String key);

	Act getRentAccrual(String key);

	Act getRentAccrualTower(String key);

	String getHisTaskVarsTitle(String executionId);

    Act getEleRegionPrice(String key);

    Act getBudget(String key);

    Act getUser(String key);

    Act getTwrAccrualManual(String key);

    List<Act> queryRuBusinessId(Map<String, Object> map);

    int updateActNameByTaskId(Map<String, Object> map);

    Act getRentExpireAccrual(String key);

    Act getRentExpireAccrualTower(String key);

    HistoricActivityInstanceEntity queryActHiActinstByTaskId(String taskId);

    void updateActHiActinstById(@Param("historicActivityInstance") HistoricActivityInstanceEntity historicActivityInstance, @Param("newId") String newId);

    void insertActHiActinstById(HistoricActivityInstanceEntity historicActivityInstance);

    String getTurnNumByTaskId(String taskId);

    void insertComment(CommentEntity comment);
}
