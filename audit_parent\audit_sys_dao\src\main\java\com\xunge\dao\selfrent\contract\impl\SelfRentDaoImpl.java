package com.xunge.dao.selfrent.contract.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.xunge.core.page.Page;
import com.xunge.dao.AbstractBaseDao;
import com.xunge.dao.selfrent.contract.ISelfRentDao;
import com.xunge.filter.PageInterceptor;
import com.xunge.model.selfrent.common.AccrualInfoImportVO;
import com.xunge.model.selfrent.contract.*;
import com.xunge.model.selfrent.rebursepoint.RentBillaccountVO;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class SelfRentDaoImpl extends AbstractBaseDao implements ISelfRentDao {
    //主合同表
    final String DatContractNamespace = "com.xunge.mapping.DatContractVOMapper.";
    //房屋租赁合同表
    final String RentContractNamespace = "com.xunge.mapping.RentContractVOMapper.";
    //付款供应商表
    final String DatSupplierNamespace = "com.xunge.mapping.DatSupplierVOMapper.";
    //租赁平台合同表
    final String PlatformContractNamespace = "com.xunge.mapping.PlatformContractMapper.";

    @Override
    public int insertDatContractVO(DatContractVO datContractVO) {
        return this.getSqlSession().insert(DatContractNamespace + "insertDatContractVO", datContractVO);
    }

    @Override
    public int updateDatContractVO(DatContractVO datContractVO) {
        return this.getSqlSession().update(DatContractNamespace + "updateDatContractVO", datContractVO);
    }

    @Override
    public int insertRentContractVO(RentContractVO rentContractVO) {
        return this.getSqlSession().insert(RentContractNamespace + "insertRentContractVO", rentContractVO);
    }

    @Override
    public int updateRentContractVO(RentContractVO rentContractVO) {
        return this.getSqlSession().update(RentContractNamespace + "updateRentContractVO", rentContractVO);
    }

    @Override
    public int insertDatSupplierVO(DatSupplierVO datSupplierVO) {
        return this.getSqlSession().insert(DatSupplierNamespace + "insertDatSupplierVO", datSupplierVO);
    }

    @Override
    public int updateDatSupplierVO(DatSupplierVO datSupplierVO) {
        return this.getSqlSession().update(DatSupplierNamespace + "updateDatSupplierVO", datSupplierVO);
    }

    @SuppressWarnings("unchecked")
    @Override
    public Page<RentContractVO> queryRentContractVO(Map<String, Object> paraMap, int pageNumber, int pageSize) {
        PageInterceptor.startPage(pageNumber, pageSize);
        this.getSqlSession().selectList(RentContractNamespace + "queryRentContractVO", paraMap);
        return PageInterceptor.endPage();
    }
    @Override
    public List<RentContractVO> queryRentContractVoList(Map<String, Object> paraMap){
       return this.getSqlSession().selectList(RentContractNamespace + "queryRentContractVO", paraMap);
    }

    @Override
    public List<RentContractVO> queryRentContractVO(Map<String, Object> paraMap) {
        return this.getSqlSession().selectList(RentContractNamespace + "queryRentContractVO", paraMap);
    }

    @Override
    public Map<String, Object> queryTeleContract(String contractId) {
        return this.getSqlSession().selectOne(RentContractNamespace + "queryTeleContract", contractId);
    }

    @Override
    public Map<String, Object> queryTelePayment(String billaccountpaymentdetailId) {
        return this.getSqlSession().selectOne(RentContractNamespace + "queryTelePayment", billaccountpaymentdetailId);
    }

    @Override
    public Map<String, Object> queryTeleBillaccount(String billaccountId) {
        return this.getSqlSession().selectOne(RentContractNamespace + "queryTeleBillaccount", billaccountId);
    }

    @Override
    public DatSupplierVO queryDatSupplierById(Map<String, Object> paraMap) {
        return this.getSqlSession().selectOne(DatSupplierNamespace + "queryDatSupplierById", paraMap);
    }

    public List<DatSupplierVO> queryDatSupplierByPrvID(Map<String, Object> paraMap) {
        return this.getSqlSession().selectList(DatSupplierNamespace + "queryDatSupplierByPrvID", paraMap);
    }

    /**
     * 分页查询供应商信息
     *
     * @param rentcontractId
     * @return
     * <AUTHOR>
     */
    public Page<List<DatSupplierVO>> queryDatSupplierByPrvID(Map<String, Object> paraMap, int pageNumber, int pageSize) {
        PageInterceptor.startPage(pageNumber, pageSize);
        this.getSqlSession().selectList(DatSupplierNamespace + "queryDatSupplierByPrvID", paraMap);
        return PageInterceptor.endPage();
    }

    ;

    @Override
    public DatContractVO queryDatContractById(Map<String, Object> paraMap) {
        return this.getSqlSession().selectOne(DatContractNamespace + "queryDatContractById", paraMap);
    }

    @Override
    public RentContractVO queryRentContractById(Map<String, Object> paraMap) {
        return this.getSqlSession().selectOne(RentContractNamespace + "queryRentContractById", paraMap);
    }

    @Override
    public RentContractVO queryAllRentContractById(Map<String, Object> paraMap) {
        return this.getSqlSession().selectOne(RentContractNamespace + "queryAllRentContractById", paraMap);
    }

    @Override
    public int updateCommit(Map<String, Object> map) {
        return this.getSqlSession().update(RentContractNamespace + "commit", map);
    }

    @Override
    public List<DatContractVO> checkContractCode(Map<String, Object> map) {
        return this.getSqlSession().selectList(DatContractNamespace + "checkContractCode", map);
    }

    @Override
    public int stopContract(Map<String, Object> map) {
        return this.getSqlSession().update(DatContractNamespace + "stopContract", map);
    }

    @Override
    public int deleteContract(Map<String, Object> map) {
        return this.getSqlSession().update(DatContractNamespace + "deleteContract", map);
    }

    @Override
    public int deleteRentContract(Map<String, Object> parmMap) {
        return this.getSqlSession().update(RentContractNamespace + "deleteRentContract", parmMap);
    }

    @Override
    public RentContractVO queryRentContractByBillAccountId(String billAccountId) {
        return this.getSqlSession().selectOne(RentContractNamespace + "queryRentContractByBillAccountId", billAccountId);
    }

    @Override
    public RentContractVO queryRentFinanceContractByBillAccountId(String billAccountId) {
        return this.getSqlSession().selectOne(RentContractNamespace + "queryRentFinanceContractByBillAccountId", billAccountId);
    }

    @Override
    public List<String> queryRentContractEndDate(Map<String, Object> paraMap) {
        return this.getSqlSession().selectList(RentContractNamespace + "getPaymentEnddate", paraMap);
    }

    @Override
    public RentContractVO queryContractByContractId(Map<String, Object> paraMap, int pageNumber, int pageSize) {
        return this.getSqlSession().selectOne(RentContractNamespace + "queryContractByContractId", paraMap);
    }

    @Override
    public RentContractVO getPaymentPeriodDate(Map<String, Object> paraMap) {
        return this.getSqlSession().selectOne(RentContractNamespace + "getPaymentPerioddate", paraMap);
    }

    @Override
    public RentContractVO queryContractAndSupplier(Map<String, Object> paraMap) {
        return this.getSqlSession().selectOne(RentContractNamespace + "queryContractAndSupplier", paraMap);
    }

    @Override
    public RentContractVO queryContractAndSupplierFcontract(Map<String, Object> paraMap) {
        return this.getSqlSession().selectOne(RentContractNamespace + "queryContractAndSupplierFcontract", paraMap);
    }

    @Override
    public RentContractVO queryContAndSupByBillId(Map<String, Object> paraMap) {
        return this.getSqlSession().selectOne(RentContractNamespace + "queryContAndSupByBillId", paraMap);
    }

    @Override
    public RentContractVO queryContAndSupByBillIdDeleteAudit(Map<String, Object> paraMap) {
        return this.getSqlSession().selectOne(RentContractNamespace + "queryContAndSupByBillIdDeleteAudit", paraMap);
    }

    @Override
    public RentContractVO queryContAndSupByBillIdFcontract(Map<String, Object> paraMap) {
        return this.getSqlSession().selectOne(RentContractNamespace + "queryContAndSupByBillIdFcontract", paraMap);
    }

    @Override
    public RentContractVO queryContAndSupByBillIdFcontractDeleteAudit(Map<String, Object> paraMap) {
        return this.getSqlSession().selectOne(RentContractNamespace + "queryContAndSupByBillIdFcontractDeleteAudit", paraMap);
    }

    @Override
    public List<Map<String, String>> selectContractNumByCondition(Map<String, Object> paraMap) {
        return this.getSqlSession().selectList(RentContractNamespace + "selectContractNumByCondition", paraMap);
    }

    /**
     * @description 查询所有固化信息
     * <AUTHOR>
     * @date 创建时间：2017年11月1日
     */
    @Override
    public List<CuringRentContractVO> queryCuringRentContractVO(Map<String, Object> paraMap) {
        return this.getSqlSession().selectList(RentContractNamespace + "queryCuringRentContractVO", paraMap);
    }

    ;

    /**
     * @description 修改租费合同审核状态 租费专用
     * <AUTHOR>
     * @date 创建时间：2017年11月7日
     */
    @Override
    public int updateRentContractAuditState(Map<String, Object> maps) {
        return this.getSqlSession().update(RentContractNamespace + "updateRentContractAuditState", maps);
    }

    ;

    /**
     * @description 查询所有需要修改系统统一编码的固化信息
     * <AUTHOR>
     * @date 创建时间：2018年1月30日
     */
    public List<RentContractVO> queryContractBySysCode(Map<String, Object> paraMap) {
        return this.getSqlSession().selectList(RentContractNamespace + "queryContractBySysCode", paraMap);
    }

    /**
     * @description 修改系统统一编码的固化信息
     * <AUTHOR>
     * @date 创建时间：2018年1月30日
     */
    public int updateSysContractCode(Map<String, Object> maps) {
        return this.getSqlSession().update(DatContractNamespace + "updateSysContractCode", maps);
    }

    /**
     * @description 批量新增租费固化信息
     * <AUTHOR>
     * @date 创建时间：2018年1月30日
     */
    @Override
    public int insertRentContractInfoList(List<CuringRentContractVO> list) {
        Map<String, Object> paraMap = new HashMap<String, Object>();
        paraMap.put("contractList", list);
        return this.getSqlSession().insert(RentContractNamespace + "insertRentContractInfoList", paraMap);
    }

    /**
     * @description 批量修改租费固化信息
     * <AUTHOR>
     * @date 创建时间：2018年1月30日
     */
    @Override
    public int updateRentContractInfoList(List<CuringRentContractVO> list) {
        Map<String, Object> paraMap = new HashMap<String, Object>();
        paraMap.put("contractList", list);
        return this.getSqlSession().update(RentContractNamespace + "updateRentContractInfoList", paraMap);
    }

    /**
     * @description 批量新增主合同固化信息
     * <AUTHOR>
     * @date 创建时间：2018年1月30日
     */
    @Override
    public int insertContractInfoList(List<CuringDatContractVO> list, String flag) {
        Map<String, Object> paraMap = new HashMap<String, Object>();
        paraMap.put("contractList", list);
        paraMap.put("flag", flag);
        return this.getSqlSession().insert(DatContractNamespace + "insertContractInfoList", paraMap);
    }

    /**
     * @description 批量修改主合同固化信息
     * <AUTHOR>
     * @date 创建时间：2018年1月30日
     */
    @Override
    public int updateContractInfoList(List<CuringDatContractVO> list) {
        Map<String, Object> paraMap = new HashMap<String, Object>();
        paraMap.put("contractList", list);
        return this.getSqlSession().update(DatContractNamespace + "updateContractInfoList", paraMap);
    }

    /**
     * @description 根据省份id查询所有合同
     * <AUTHOR>
     * @date 创建时间：2018年1月30日
     */
    @Override
    public List<CuringRentContractVO> queryCuringRentContractVOByPrvId(Map<String, Object> paraMap) {
        return this.getSqlSession().selectList(RentContractNamespace + "queryCuringRentContractVOByPrvId", paraMap);
    }

    @Override
    public List<DatSupplierVO> queryAllDatSupplier(Map<String, Object> paraMap) {
        return this.getSqlSession().selectList(DatSupplierNamespace + "queryAllDatSupplier", paraMap);
    }

    @Override
    public DatContractVO queryDatContractByFlow(Map<String, Object> paraMap) {
        return this.getSqlSession().selectOne(DatContractNamespace + "queryDatContractByFlow", paraMap);
    }

    @Override
    public RentContractVO queryRentContractByDatId(Map<String, Object> paraMap) {
        return this.getSqlSession().selectOne(RentContractNamespace + "queryRentContractByDatId", paraMap);
    }

    @Override
    public int updatePushAndContractState(Map<String, Object> paramMap) {
        return this.getSqlSession().update(DatContractNamespace + "updatePushAndContractState", paramMap);
    }

    @Override
    public int updateByContractId(RentContractVO rentContractVO) {
        return this.getSqlSession().selectOne(RentContractNamespace + "updateByContractId", rentContractVO);
    }

    @Override
    public List<RentContractVO> queryRentContractList(Map<String, Object> paraMap) {
        return this.getSqlSession().selectList(RentContractNamespace + "queryRentContractList", paraMap);
    }

    @Override
    public int updateContractInfo(Map<String, Object> paraMap) {
        return this.getSqlSession().update(DatContractNamespace + "updateContractInfo", paraMap);
    }

    @Override
    public PageInfo<PlatformContractVO> queryNoLinkPlatformContract(Map<String, Object> map) {
        PageHelper.startPage(Integer.parseInt(map.get("pageNum").toString()), Integer.parseInt(map.get("pageSize").toString()));
        List<PlatformContractVO> list = this.getSqlSession().selectList(PlatformContractNamespace + "queryNoLinkPlatformContract", map);
        return new PageInfo<>(list);
    }

    public int addPlatformContractLink(Map<String, Object> map) {
        return this.getSqlSession().update(PlatformContractNamespace + "addPlatformContractLink", map);
    }

    public int cancelPlatformContractLink(Map<String, Object> map) {
        return this.getSqlSession().update(PlatformContractNamespace + "cancelPlatformContractLink", map);
    }

    public Integer selectContractTypeByRentContractId(String rentcontractId) {
        return this.getSqlSession().selectOne(PlatformContractNamespace + "selectContractTypeByRentContractId", rentcontractId);
    }

    public List<Integer> selectContractTypeByRentContractIdRel(String rentcontractId) {
        return this.getSqlSession().selectList(PlatformContractNamespace + "selectContractTypeByRentContractIdRel", rentcontractId);
    }

    @Override
    public RentBillaccountVO queryBilaccountByContract(String rentcontractId) {
        return this.getSqlSession().selectOne(RentContractNamespace + "queryBilaccountByContract", rentcontractId);
    }

    @Override
    public int updateSettledDate(Map<String, Object> paramMap) {
        return this.getSqlSession().update(RentContractNamespace + "updateSettledDate", paramMap);
    }

    @Override
    public String queryPlatformContractMaxDate() {
        return this.getSqlSession().selectOne(PlatformContractNamespace + "queryPlatformContractMaxDate");
    }

    @Override
    public List<AccrualInfoImportVO> queryAllCnontractForAccrual(String prvId) {
        return this.getSqlSession().selectList(RentContractNamespace+"queryAllCnontractForAccrual",prvId);
    }

    @Override
    public int updateRentAccrualInfoImport(List<AccrualInfoImportVO> list) {
        return this.getSqlSession().update(RentContractNamespace+"updateRentAccrualInfoImport",list);
    }

    @Override
    public List<PlatformContractVO> queryLastestPlatformContractByCode(Map<String, Object> paraMap){
        return this.getSqlSession().selectList(PlatformContractNamespace + "queryLastestPlatformContractByCode", paraMap);
    }

    @Override
    public int linkPlatformContractInfo(PlatformContractVO vo) {
        return this.getSqlSession().update(RentContractNamespace+"linkPlatformContractInfo",vo);
    }

    @Override
    public String queryProcessInstanceIdByBusinessKey(String businessKey) {
        return this.getSqlSession().selectOne(RentContractNamespace + "queryProcessInstanceIdByBusinessKey", businessKey);
    }

    @Override
    public ActLastAuditVO queryLastAuditTimeAndLastAuditUserByProcessInstanceId(String processInstanceId) {
        return this.getSqlSession().selectOne(RentContractNamespace + "queryLastAuditTimeAndLastAuditUserByProcessInstanceId", processInstanceId);
    }
}