package com.xunge.model.basedata.vo;

/**
 * 资源枚举类
 *
 * <AUTHOR>
 */
public class DatBaseresourceEnum {

    private Integer id;
    private String resourceField;
    private Integer fieldKey;
    private String fieldValue;
    private String version;
    private Integer isUse;
    private String fieldDesc;
    private Integer fieldOfType;

    public void setFieldOfType(Integer fieldOfType) { this.fieldOfType = fieldOfType; }

    public Integer getFieldOfType() { return fieldOfType; }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getResourceField() {
        return resourceField;
    }

    public void setResourceField(String resourceField) {
        this.resourceField = resourceField;
    }

    public Integer getFieldKey() {
        return fieldKey;
    }

    public void setFieldKey(Integer fieldKey) {
        this.fieldKey = fieldKey;
    }

    public String getFieldValue() {
        return fieldValue;
    }

    public void setFieldValue(String fieldValue) {
        this.fieldValue = fieldValue;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public Integer getIsUse() {
        return isUse;
    }

    public void setIsUse(Integer isUse) {
        this.isUse = isUse;
    }

    public String getFieldDesc() {
        return fieldDesc;
    }

    public void setFieldDesc(String fieldDesc) {
        this.fieldDesc = fieldDesc;
    }
}
