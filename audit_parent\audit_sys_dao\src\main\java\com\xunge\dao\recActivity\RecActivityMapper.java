package com.xunge.dao.recActivity;

import com.xunge.model.recActivity.RecActivity;
import com.xunge.model.recActivity.RecFinance;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface RecActivityMapper {

    List<RecActivity> selectListNoTransaction(RecActivity param);

    String queryActivityConfig(String prvId);

    String queryRentActivityConfig(String prvId);

    String queryTowerActivityConfig(String prvId);

    int checkMarketAndProdunctRequired(@Param("businessType") String businessType,@Param("businessId")  String businessId);

    List<RecActivity> selectAllListNoTransaction(@Param("activityCodeList") List<String> activityCodeList);

    List<RecActivity> selectNotNullMarketAndProdunct(@Param("businessType") String businessType,@Param("businessId")  String businessId);

    List<RecFinance> selectListNotLike(RecFinance param);
}