package com.xunge.model.basedata.power;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 卓望动环数据
 */
@Data
public class ZWPowerData {
    @Excel(name = "编码",orderNum ="1",replace = {"-_null"})
    private String id;
    @Excel(name = "数据日期",exportFormat = "yyyy-MM-dd",orderNum ="2",replace = {"-_null"})
    private Date dataDate;
    private String provinceCode;
    //@Excel(name = "省份",orderNum ="3",replace = {"-_null"})
    private String provinceName;
    private String cityCode;
    @Excel(name = "城市",orderNum ="3",replace = {"-_null"})
    private String cityName;
    @Excel(name = "站点代码",orderNum ="4",replace = {"-_null"})
    private String siteCode;
    @Excel(name = "站点名称",orderNum ="5",replace = {"-_null"})
    private String siteName;
    @Excel(name = "站点类型",orderNum ="6",replace = {"-_null"})
    private String siteTypeCode;
    @Excel(name = "总电量(Kw/h)",orderNum ="7",replace = {"-_null"})
    private BigDecimal totalElectric;
    @Excel(name = "IT设备电量(Kw/h)",orderNum ="8",replace = {"-_null"})
    private BigDecimal priDeviceElectric;
    @Excel(name = "空调电量(Kw/h)",orderNum ="9",replace = {"-_null"})
    private BigDecimal airCondiElectric;
    @Excel(name = "pue",orderNum ="10",replace = {"-_null"})
    private BigDecimal pue;
    private Date dataTransDate;
    @Excel(name = "报账点编码",orderNum ="11",replace = {"-_null"})
    private String billaccountCode;
    @Excel(name = "报账点名称",orderNum ="12",replace = {"-_null"})
    private String billaccountName;

}
