package com.xunge.model.budget;

import java.util.Date;

public class NationwideExplainVo {
    private String id;
    private String budgetCode;
    private String prvCode;
    private String prvName;
    private String expenseType;
    private String prvExplain;
    private String budgetTime;
    private String systemSource;
    private Date optTime;
    private String optUserId;
    private String optUserName;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getBudgetCode() {
        return budgetCode;
    }

    public void setBudgetCode(String budgetCode) {
        this.budgetCode = budgetCode;
    }

    public String getPrvCode() {
        return prvCode;
    }

    public void setPrvCode(String prvCode) {
        this.prvCode = prvCode;
    }

    public String getPrvName() {
        return prvName;
    }

    public void setPrvName(String prvName) {
        this.prvName = prvName;
    }

    public String getExpenseType() {
        return expenseType;
    }

    public void setExpenseType(String expenseType) {
        this.expenseType = expenseType;
    }

    public String getPrvExplain() {
        return prvExplain;
    }

    public void setPrvExplain(String prvExplain) {
        this.prvExplain = prvExplain;
    }

    public String getBudgetTime() {
        return budgetTime;
    }

    public void setBudgetTime(String budgetTime) {
        this.budgetTime = budgetTime;
    }

    public String getSystemSource() {
        return systemSource;
    }

    public void setSystemSource(String systemSource) {
        this.systemSource = systemSource;
    }

    public Date getOptTime() {
        return optTime;
    }

    public void setOptTime(Date optTime) {
        this.optTime = optTime;
    }

    public String getOptUserId() {
        return optUserId;
    }

    public void setOptUserId(String optUserId) {
        this.optUserId = optUserId;
    }

    public String getOptUserName() {
        return optUserName;
    }

    public void setOptUserName(String optUserName) {
        this.optUserName = optUserName;
    }
}
