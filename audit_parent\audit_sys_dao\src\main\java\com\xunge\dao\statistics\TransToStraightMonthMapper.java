/**
* @Title: TransToStraightMonthMapper.java 
* @Package com.xunge.dao.statistics 
* <AUTHOR>   
* @date 2022年5月12日 下午2:48:00 
* @version V1.0   
*/ 
package com.xunge.dao.statistics;

import java.util.List;
import java.util.Map;

import com.xunge.model.statistics.EleBillaccountTranstoStarightMonth;

/** 
* @ClassName: TransToStraightMonthMapper 
* @Description: 电费转改直改造完成月报统计
* @Author：tian
* @Date：2022年5月12日 
*/
public interface TransToStraightMonthMapper {

	/** 
	* @Description: TODO 电费转改直改造完成月报查询
	* <AUTHOR>   
	* @date 2022年5月12日 下午2:50:16 
	* @param params
	* @return  
	*/ 
	List<EleBillaccountTranstoStarightMonth> queryTransToStraightMonthList(Map<String, Object> params);

}
