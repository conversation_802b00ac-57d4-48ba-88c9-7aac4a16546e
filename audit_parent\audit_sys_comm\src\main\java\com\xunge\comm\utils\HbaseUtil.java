package com.xunge.comm.utils;

import lombok.extern.slf4j.Slf4j;

import java.io.IOException;

/**
 * hbase调用工具类
 *
 * <AUTHOR>
 */
@Slf4j
public class HbaseUtil {

	/*private static Logger logger = LoggerFactory.getLogger(HbaseUtil.class);
	private static PropertiesLoader loader = new PropertiesLoader(new String[]{"properties/benchmark.properties"});
	public static Configuration configuration;
	public static Connection connection;

	static {
		configuration = HBaseConfiguration.create();
		//configuration.set("hbase.zookeeper.property.clientPort", loader.getProperty("clientPort"));
		configuration.set("hbase.zookeeper.quorum", loader.getProperty("quorum"));
		//configuration.set("hbase.master", loader.getProperty("master"));
		configuration.set("zookeeper.znode.parent", "/hbase-unsecure");



		try {
			connection = ConnectionFactory.createConnection(configuration);
		} catch (IOException e) {
			log.error("HbaseUtil 出错",e);
		}
	}

	*//**
     * 创建表
     * @param tableName
     * @param familyColumnName
     *//*
	public static void createTable(String name, List<String> familyColumnName) {
		try {
			TableName tableName = TableName.valueOf(name);
			Admin hAdmin = HbaseUtil.connection.getAdmin();
			HTableDescriptor descripter = new HTableDescriptor(tableName);
			for (String familyName : familyColumnName) {
				descripter.addFamily(new HColumnDescriptor(familyName));
			}
			if (hAdmin.tableExists(tableName)) {
				hAdmin.disableTable(tableName);
				hAdmin.deleteTable(tableName);
				logger.info(tableName + "is exists...");
			}
			hAdmin.createTable(descripter);
			hAdmin.close();
		} catch (MasterNotRunningException e) {
			log.error("HbaseUtil 出错",e);
		} catch (ZooKeeperConnectionException e) {
			log.error("HbaseUtil 出错",e);
		} catch (IOException e) {
			log.error("HbaseUtil 出错",e);
		}
	}

	*//**
     * 往表里面添加数据
     * @param tableName
     * @param rowkey
     * @param columnValues
     * @return
     *//*
	public static int addDataForTable(String name, String rowkey,
									  Map<String, Map<String, String>> columnValues) {
		try {

			Put put = new Put(Bytes.toBytes(rowkey));
			TableName tableName = TableName.valueOf(name);
			Table htable = HbaseUtil.connection.getTable(tableName);
			HColumnDescriptor[] columnFamilies = htable.getTableDescriptor()
					.getColumnFamilies();// 获取所有的列名
			for (HColumnDescriptor hColumnDescriptor : columnFamilies) {
				String familyName = hColumnDescriptor.getNameAsString();
				Map<String, String> columnNameValueMap = columnValues
						.get(familyName);

				if (columnNameValueMap != null) {
					for (String columnName : columnNameValueMap.keySet()) {
						put.addColumn(Bytes.toBytes(familyName), Bytes
								.toBytes(columnName), Bytes
								.toBytes(columnNameValueMap.get(columnName)));
					}
				}
			}
			htable.put(put);
			htable.close();
			return put.size();
		} catch (IOException e) {
			log.error("HbaseUtil 出错",e);
		}
		return 0;
	}

	*//**
     * 批量添加数据
     * @param list
     *//*
	public static void insertDataList(List<HbaseDataEntity> list) {
		List<Put> puts = new ArrayList<Put>();
		Table table = null;
		Put put;
		try {
			for (HbaseDataEntity entity : list) {
				TableName tableName = TableName.valueOf(entity.getTableName());
				table = HbaseUtil.connection.getTable(tableName);
				put = new Put(entity.getMobileKey().getBytes());// 一个PUT代表一行数据，再NEW一个PUT表示第二行数据,每行一个唯一的ROWKEY
				for (String columnfamily : entity.getColumns().keySet()) {
					for (String column : entity.getColumns().get(columnfamily)
							.keySet()) {
						put.addColumn(
								columnfamily.getBytes(),
								column.getBytes(),
								entity.getColumns().get(columnfamily)
										.get(column).getBytes());
					}
				}
				puts.add(put);
			}
			table.put(puts);
			table.close();
		} catch (Exception e) {
			log.error("HbaseUtil 出错",e);
		} finally {

		}
	}

	*//**
     * 更新表中的一列
     * @param tableName
     * @param rowKey
     * @param familyName
     * @param columnName
     * @param value
     *//*
	public static void updateTable(String name, String rowKey,
								   String familyName, String columnName, String value) {
		try {
			TableName tableName = TableName.valueOf(name);
			Table table = HbaseUtil.connection.getTable(tableName);

			Put put = new Put(Bytes.toBytes(rowKey));
			put.addColumn(Bytes.toBytes(familyName), Bytes.toBytes(columnName),
					Bytes.toBytes(value));
			table.put(put);
			table.close();

		} catch (IOException e) {
			log.error("HbaseUtil 出错",e);
		}
	}

	*//**
     * 批量删除数据
     * @param list
     *//*
	public static void deleteDataList(List<HbaseDataEntity> list) {
		Table table = null;
		List<Delete> deletes = new ArrayList<Delete>();
		try {
			for (HbaseDataEntity entity : list) {
				TableName tableName = TableName.valueOf(entity.getTableName());
				table = HbaseUtil.connection.getTable(tableName);
				Delete delete = new Delete(Bytes.toBytes(entity.getMobileKey()));
				for (String columnfamily : entity.getColumns().keySet()) {
					for (String column : entity.getColumns().get(columnfamily)
							.keySet()) {
						delete.addColumn(columnfamily.getBytes(),
								column.getBytes());
					}
				}
				deletes.add(delete);
			}
			table.delete(deletes);
			table.close();
		} catch (Exception e) {
			log.error("HbaseUtil 出错",e);
		} finally {

		}
	}

	*//**
     * 删除指定的列
     * @param tableName
     * @param rowKey
     * @param familyName
     * @param columnName
     *//*
	public static void deleteColumn(String name, String rowKey,
									String familyName, String columnName) {
		try {

			TableName tableName = TableName.valueOf(name);
			Table table = HbaseUtil.connection.getTable(tableName);
			Delete delete = new Delete(Bytes.toBytes(rowKey));
			delete.addColumn(Bytes.toBytes(familyName),
					Bytes.toBytes(columnName));
			table.delete(delete);
			logger.info(familyName + ":" + columnName + "is delete");
			table.close();
		} catch (IOException e) {
			log.error("HbaseUtil 出错",e);
		}
	}

	*//**
     * 删除所有列
     * @param tableName
     * @param rowKey
     *//*
	public static void deleteAllColumns(String name, String rowKey) {
		try {
			TableName tableName = TableName.valueOf(name);
			Table table = HbaseUtil.connection.getTable(tableName);
			Delete delete = new Delete(Bytes.toBytes(rowKey));
			table.delete(delete);
			logger.info("all columns are deleted!");
			table.close();
		} catch (IOException e) {
			log.error("HbaseUtil 出错",e);
		}
	}

	*//**
     * 获取所有的数据
     * @param name
     * @param size
     * @return
     *//*
	public static List<HbaseDataEntity> getResultScans(String name, int size) {
		Scan scan = new Scan();
		ResultScanner resultScanner = null;
		List<HbaseDataEntity> list = new ArrayList<HbaseDataEntity>();
		try {
			TableName tableName = TableName.valueOf(name);
			Table table = HbaseUtil.connection.getTable(tableName);
			long beiginTime = System.currentTimeMillis();
			resultScanner = table.getScanner(scan);
			long endTime = System.currentTimeMillis();
			double spentTime = (endTime - beiginTime) / 1000.0;
			logger.info("cost:===" + spentTime + "s");
			for (Result result : resultScanner) {
				logger.info("获得到rowkey:" + new String(result.getRow()));
				HbaseDataEntity entity = new HbaseDataEntity();
				entity.setTableName(name);
				entity.setMobileKey(new String(result.getRow()));
				Map<String, Map<String, String>> familyMap = new HashMap<String, Map<String, String>>();
				for (Cell cell : result.rawCells()) {
					if (familyMap.get(new String(cell.getFamilyArray())) == null) {
						Map<String, String> columnsMap = new HashMap<String, String>();
						columnsMap.put(
								new String(cell.getQualifierArray(), cell
										.getQualifierOffset(), cell
										.getQualifierLength()),
								new String(cell.getValueArray(), cell
										.getValueOffset(), cell
										.getValueLength()));
						familyMap.put(
								new String(cell.getFamilyArray(), cell
										.getFamilyOffset(), cell
										.getFamilyLength()), columnsMap);
					} else {
						familyMap.get(
								new String(cell.getFamilyArray(), cell
										.getFamilyOffset(), cell
										.getFamilyLength())).put(
								new String(cell.getQualifierArray(),
										cell.getQualifierOffset(),
										cell.getQualifierLength()),
								new String(cell.getValueArray(), cell
										.getValueOffset(), cell
										.getValueLength()));
					}
					logger.info(
							"列：" + new String(cell.getFamilyArray(), cell.getFamilyOffset(), cell.getFamilyLength())
									+ "====值:"
									+ new String(cell.getValueArray(), cell.getValueOffset(), cell.getValueLength()));
				}
				entity.setColumns(familyMap);
				list.add(entity);
				if (size == list.size()) {
					break;
				}
			}
			table.close();
			return list;
		} catch (IOException e) {
			log.error("HbaseUtil 出错",e);
		} finally {
			resultScanner.close();
		}
		return null;
	}

	*//**
     * 根据RowKey获取数据
     * @param tableName 表名称
     * @param rowKey    RowKey名称
     * @param colFamily 列族名称
     * @param col       列名称
     * @throws IOException
     *//*
	public static void getData(String tableName, String rowKey, String colFamily, String col) throws IOException {
		Table table = connection.getTable(TableName.valueOf(tableName));
		Get get = new Get(Bytes.toBytes(rowKey));
		if (!colFamily.equals("")) {
			get.addFamily(Bytes.toBytes(colFamily));
		}
		if (!colFamily.equals("") && !col.equals("")) {
			get.addColumn(Bytes.toBytes(colFamily), Bytes.toBytes(col));
		}
		Result result = table.get(get);
		table.close();
	}

	*//**
     * 格式化输出
     * @param result
     *//*
	public static void showCell(Result result) {
		Cell[] cells = result.rawCells();
		for (Cell cell : cells) {
			System.out.println("RowName: " + new String(CellUtil.cloneRow(cell)) + " ");
			System.out.println("Timetamp: " + cell.getTimestamp() + " ");
			System.out.println("column Family: " + new String(CellUtil.cloneFamily(cell)) + " ");
			System.out.println("row Name: " + new String(CellUtil.cloneQualifier(cell)) + " ");
			System.out.println("value: " + new String(CellUtil.cloneValue(cell)) + " ");
		}
	}

	*//**
     * 查询rowkey在startRow和endRow之间的数据
     * @param tablename    表名
     * @param family 列族名
     * @param col 列明
     * @param startRow 开始行key 包含
     * @param endRow 结束行key 不包含
     * @param order 是否设置倒排序，start和end也要反过来
     * @throws Exception
     *//*
	public static List<Result> getBetweenRow(String tableName, String family, String col, String startRow, String stopRow, boolean order) {
		List<Result> list = new ArrayList<>();
		try {
			Table table = connection.getTable(TableName.valueOf(tableName));
			Scan scan = new Scan();
			scan.setBatch(1000);
			scan.setCaching(9);
			scan.setReversed(order);
			scan.addColumn(family.getBytes(), col.getBytes());
			scan.setStartRow(startRow.getBytes());
			scan.setStopRow(stopRow.getBytes());
			ResultScanner scanner = table.getScanner(scan);
			for(Result res : scanner){
				list.add(res);
			}
			scanner.close();
		} catch (IOException e) {
			log.error("HbaseUtil 出错",e);
		}
		return list;
	}

	*//**
     * 查询根据rowkey查询
     * @param tableName
     * @param rowkey
     * @return
     *//*
	public static HbaseDataEntity getResultData(String tableName, String rowkey) {
		Scan scan = new Scan();
		ResultScanner resultScanner = null;
		HbaseDataEntity entity = new HbaseDataEntity();
		try {
			TableName tableNameN = TableName.valueOf(tableName);
			Table table = HbaseUtil.connection.getTable(tableNameN);
			long beiginTime = System.currentTimeMillis();
			resultScanner = table.getScanner(scan);
			long endTime = System.currentTimeMillis();
			double spentTime = (endTime - beiginTime) / 1000.0;
			Get get = new Get(rowkey.getBytes());
			logger.info("cost:===" + spentTime + "s");
			Result res = table.get(get);
			if (res.getRow() == null) {
				return null;
			}
			entity.setTableName(tableName);
			entity.setMobileKey(new String(res.getRow()));
			Map<String, Map<String, String>> familyMap = new HashMap<String, Map<String, String>>();
			Map<String, String> columnsMap = new HashMap<String, String>();
			for (Cell cell : res.rawCells()) {
				//每条数据之间用&&&将Qualifier和Value进行区分
				if (familyMap.get(new String(cell.getFamilyArray())) == null) {
					columnsMap.put(
							new String(cell.getQualifierArray(), cell
									.getQualifierOffset(), cell
									.getQualifierLength()),
							new String(cell.getValueArray(), cell
									.getValueOffset(), cell
									.getValueLength()));
					familyMap.put(
							new String(cell.getFamilyArray(), cell
									.getFamilyOffset(), cell
									.getFamilyLength()), columnsMap);
				} else {
					familyMap.get(
							new String(cell.getFamilyArray(), cell
									.getFamilyOffset(), cell
									.getFamilyLength())).put(
							new String(cell.getQualifierArray(),
									cell.getQualifierOffset(),
									cell.getQualifierLength()),
							new String(cell.getValueArray(), cell
									.getValueOffset(), cell
									.getValueLength()));
				}
				logger.info(
						"列：" + new String(cell.getFamilyArray(), cell.getFamilyOffset(), cell.getFamilyLength())
								+ "====值:"
								+ new String(cell.getValueArray(), cell.getValueOffset(), cell.getValueLength()));
			}
			entity.setColumns(familyMap);
			table.close();
			return entity;
		} catch (IOException e) {
			log.error("HbaseUtil 出错",e);
		} finally {
			resultScanner.close();
		}
		return null;
	}

	*//**
     * 组合条件查询 and
     * @param nameSpace  命名空间
     * @param tableName  表名
     * @param parameters 格式是：columnFamily,columnName,columnValue
     *//*
	public static List<HbaseDataEntity> QueryDataByConditionsAnd(
			String nameSpace, String name, List<String> parameters) {
		ResultScanner rs = null;
		Table table = null;
		List<HbaseDataEntity> list = new ArrayList<HbaseDataEntity>();
		try {
			TableName tableName = TableName.valueOf(name);
			table = HbaseUtil.connection.getTable(tableName);
			// 参数的格式：columnFamily,columnName,columnValue
			List<Filter> filters = new ArrayList<Filter>();
			for (String parameter : parameters) {
				String[] columns = parameter.split(",");
				SingleColumnValueFilter filter = new SingleColumnValueFilter(
						Bytes.toBytes(columns[0]), Bytes.toBytes(columns[1]),
						CompareOp.valueOf(columns[2]),
						Bytes.toBytes(columns[3]));
				filter.setFilterIfMissing(true);
				filters.add(filter);
			}
			FilterList filterList = new FilterList(filters);
			Scan scan = new Scan();
			scan.setFilter(filterList);
			rs = table.getScanner(scan);
			for (Result r : rs) {
				logger.info("获得到rowkey:" + new String(r.getRow()));
				HbaseDataEntity entity = new HbaseDataEntity();
				entity.setNameSpace(nameSpace);
				entity.setTableName(name);
				entity.setMobileKey(new String(r.getRow()));
				Map<String, Map<String, String>> familyMap = new HashMap<String, Map<String, String>>();
				for (Cell cell : r.rawCells()) {
					if (familyMap.get(new String(cell.getFamilyArray(), cell
							.getFamilyOffset(), cell.getFamilyLength())) == null) {
						Map<String, String> columnsMap = new HashMap<String, String>();
						columnsMap.put(
								new String(cell.getQualifierArray(), cell
										.getQualifierOffset(), cell
										.getQualifierLength()),
								new String(cell.getValueArray(), cell
										.getValueOffset(), cell
										.getValueLength()));
						familyMap.put(
								new String(cell.getFamilyArray(), cell
										.getFamilyOffset(), cell
										.getFamilyLength()), columnsMap);
					} else {
						familyMap.get(
								new String(cell.getFamilyArray(), cell
										.getFamilyOffset(), cell
										.getFamilyLength())).put(
								new String(cell.getQualifierArray(),
										cell.getQualifierOffset(),
										cell.getQualifierLength()),
								new String(cell.getValueArray(), cell
										.getValueOffset(), cell
										.getValueLength()));
					}
				}
				entity.setColumns(familyMap);
				list.add(entity);
			}
			rs.close();
			table.close();
			return list;
		} catch (Exception e) {
			log.error("HbaseUtil 出错",e);
		}
		return null;
	}

	*//**
     * 组合条件查询 or
     * @param nameSpace  命名空间
     * @param tableName  表名
     * @param parameters 格式是：columnFamily,columnName,columnValue
     * @return
     *//*
	public static List<HbaseDataEntity> QueryDataByConditionsOr(
			String nameSpace, String name, List<String> parameters) {

		ResultScanner rs = null;
		List<HbaseDataEntity> list = new ArrayList<HbaseDataEntity>();

		try {

			TableName tableName = TableName.valueOf(name);
			Table table = HbaseUtil.connection.getTable(tableName);

			// 参数的额格式：columnFamily,columnName,columnValue
			List<Filter> filters = new ArrayList<Filter>();
			Scan scan = new Scan();

			byte[] columnFamily = null;
			byte[] columnName = null;

			for (String parameter : parameters) {
				String[] columns = parameter.split(",");
				columnFamily = Bytes.toBytes(columns[0]);
				columnName = Bytes.toBytes(columns[1]);
				SingleColumnValueFilter filter = new SingleColumnValueFilter(
						Bytes.toBytes(columns[0]), Bytes.toBytes(columns[1]),
						CompareOp.valueOf(columns[2]),
						Bytes.toBytes(columns[3]));
				filter.setFilterIfMissing(true);
				filters.add(filter);
			}

			FilterList filterList = new FilterList(
					Operator.MUST_PASS_ONE, filters);

			scan.setFilter(filterList);

			rs = table.getScanner(scan);
			for (Result r : rs) {
				if (r.containsColumn(columnFamily, columnName)) {
					logger.info("获得到rowkey:" + new String(r.getRow()));
					HbaseDataEntity entity = new HbaseDataEntity();
					entity.setNameSpace(nameSpace);
					entity.setTableName(name);
					entity.setMobileKey(new String(r.getRow()));
					Map<String, Map<String, String>> familyMap = new HashMap<String, Map<String, String>>();
					for (Cell cell : r.rawCells()) {
						if (familyMap
								.get(new String(cell.getFamilyArray(), cell
										.getFamilyOffset(), cell
										.getFamilyLength())) == null) {
							Map<String, String> columnsMap = new HashMap<String, String>();
							columnsMap.put(
									new String(cell.getQualifierArray(), cell
											.getQualifierOffset(), cell
											.getQualifierLength()),
									new String(cell.getValueArray(), cell
											.getValueOffset(), cell
											.getValueLength()));
							familyMap.put(
									new String(cell.getFamilyArray(), cell
											.getFamilyOffset(), cell
											.getFamilyLength()), columnsMap);
						} else {
							familyMap.get(
									new String(cell.getFamilyArray(), cell
											.getFamilyOffset(), cell
											.getFamilyLength())).put(
									new String(cell.getQualifierArray(),
											cell.getQualifierOffset(),
											cell.getQualifierLength()),
									new String(cell.getValueArray(), cell
											.getValueOffset(), cell
											.getValueLength()));
						}
					}

					entity.setColumns(familyMap);
					list.add(entity);
				}
			}

			table.close();
			rs.close();
			return list;
		} catch (Exception e) {
			log.error("HbaseUtil 出错",e);
		}
		return null;
	}

	*//**
     * 组合条件查询 or
     * @param nameSpace  命名空间
     * @param tableName  表名
     * @param parameters 格式是：columnFamily,columnName,columnValue
     * @return
     *//*
	public static List<HbaseDataEntity> QueryDataByConditions(String nameSpace,
															  String name, List<HbaseConditionEntity> hbaseConditions) {

		ResultScanner rs = null;
		List<HbaseDataEntity> list = new ArrayList<HbaseDataEntity>();

		try {

			TableName tableName = TableName.valueOf(name);
			Table table = HbaseUtil.connection.getTable(tableName);

			// 参数的额格式：columnFamily,columnName,columnValue
			// List<Filter> filters = new ArrayList<Filter>();
			Scan scan = new Scan();

			FilterList filterList = null;
			Operator operator = null;
			for (HbaseConditionEntity hbaseCondition : hbaseConditions) {

				SingleColumnValueFilter filter = new SingleColumnValueFilter(
						hbaseCondition.getFamilyColumn(),
						hbaseCondition.getColumn(),
						hbaseCondition.getCompareOp(),
						hbaseCondition.getValue());
				filter.setFilterIfMissing(true);

				if (hbaseCondition.getOperator() != null) {

					if (operator == null) {
						operator = hbaseCondition.getOperator();
						filterList = new FilterList(
								hbaseCondition.getOperator());
						filterList.addFilter(filter);
						logger.info("filterList==1" + filterList);
					} else if (operator.equals(hbaseCondition.getOperator())) {
						filterList.addFilter(filter);
					} else {
						filterList.addFilter(filter);
						logger.info("filterList==2" + filterList);
						FilterList addFilterList = new FilterList(
								hbaseCondition.getOperator());
						addFilterList.addFilter(filterList);
						logger.info("addFilterList==1" + addFilterList);
						filterList = addFilterList;
						logger.info("filterList==3" + filterList);
					}

				} else {
					if (filterList == null) {
						filterList = new FilterList(Operator.MUST_PASS_ALL);// 默认只有一个条件的时候
					}
					filterList.addFilter(filter);
				}

			}

			logger.info(filterList + ":filterList");

			scan.setFilter(filterList);

			rs = table.getScanner(scan);
			for (Result r : rs) {
				logger.info("获得到rowkey:" + new String(r.getRow()));
				HbaseDataEntity entity = new HbaseDataEntity();
				entity.setNameSpace(nameSpace);
				entity.setTableName(name);
				entity.setMobileKey(new String(r.getRow()));
				Map<String, Map<String, String>> familyMap = new HashMap<String, Map<String, String>>();
				for (Cell cell : r.rawCells()) {
					if (familyMap.get(new String(cell.getFamilyArray(), cell
							.getFamilyOffset(), cell.getFamilyLength())) == null) {
						Map<String, String> columnsMap = new HashMap<String, String>();
						columnsMap.put(
								new String(cell.getQualifierArray(), cell
										.getQualifierOffset(), cell
										.getQualifierLength()),
								new String(cell.getValueArray(), cell
										.getValueOffset(), cell
										.getValueLength()));
						familyMap.put(
								new String(cell.getFamilyArray(), cell
										.getFamilyOffset(), cell
										.getFamilyLength()), columnsMap);
					} else {
						familyMap.get(
								new String(cell.getFamilyArray(), cell
										.getFamilyOffset(), cell
										.getFamilyLength())).put(
								new String(cell.getQualifierArray(),
										cell.getQualifierOffset(),
										cell.getQualifierLength()),
								new String(cell.getValueArray(), cell
										.getValueOffset(), cell
										.getValueLength()));
					}
				}

				entity.setColumns(familyMap);
				list.add(entity);
			}

			table.close();
			rs.close();
			return list;
		} catch (Exception e) {
			log.error("HbaseUtil 出错",e);
		}
		return null;
	}


	*//**
     * 分页的复合条件查询
     * @param nameSpace       命名空间
     * @param name            表名
     * @param hbaseConditions 复合条件
     * @param pageSize        每页显示的数量
     * @param lastRow         当前页的最后一行
     * @return
     *//*
	public static List<HbaseDataEntity> QueryDataByConditionsAndPage(
			String nameSpace, String name,
			List<HbaseConditionEntity> hbaseConditions, int pageSize,
			byte[] lastRow) {
		final byte[] POSTFIX = new byte[]{0x00};

		ResultScanner rs = null;
		List<HbaseDataEntity> list = new ArrayList<HbaseDataEntity>();

		try {

			TableName tableName = TableName.valueOf(name);
			Table table = HbaseUtil.connection.getTable(tableName);

			Scan scan = new Scan();

			FilterList filterList = null;
			Operator operator = null;
			for (HbaseConditionEntity hbaseCondition : hbaseConditions) {

				SingleColumnValueFilter filter = new SingleColumnValueFilter(
						hbaseCondition.getFamilyColumn(),
						hbaseCondition.getColumn(),
						hbaseCondition.getCompareOp(),
						hbaseCondition.getValue());
				filter.setFilterIfMissing(true);

				if (hbaseCondition.getOperator() != null) {

					if (operator == null) {
						operator = hbaseCondition.getOperator();
						filterList = new FilterList(
								hbaseCondition.getOperator());
						filterList.addFilter(filter);
						logger.info("filterList==1" + filterList);
					} else if (operator.equals(hbaseCondition.getOperator())) {
						filterList.addFilter(filter);
					} else {
						filterList.addFilter(filter);
						logger.info("filterList==2" + filterList);
						FilterList addFilterList = new FilterList(
								hbaseCondition.getOperator());
						addFilterList.addFilter(filterList);
						logger.info("addFilterList==1" + addFilterList);
						filterList = addFilterList;
						logger.info("filterList==3" + filterList);
					}

				} else {
					if (filterList == null) {
						filterList = new FilterList(Operator.MUST_PASS_ALL);// 默认只有一个条件的时候
					}
					filterList.addFilter(filter);
				}

			}

			logger.info(filterList + ":filterList");

			FilterList pageFilterList = new FilterList(Operator.MUST_PASS_ALL);// 默认只有一个条件的时候
			Filter pageFilter = new PageFilter(pageSize);
			pageFilterList.addFilter(pageFilter);
			pageFilterList.addFilter(filterList);
			if (lastRow != null) {
				// 注意这里添加了POSTFIX操作，不然死循环了
				byte[] startRow = Bytes.add(lastRow, POSTFIX);
				scan.setStartRow(startRow);
			}

			logger.info(pageFilterList + ":pageFilterList");
			scan.setFilter(pageFilterList);

			rs = table.getScanner(scan);
			for (Result r : rs) {
				logger.info("获得到rowkey:" + new String(r.getRow()));
				HbaseDataEntity entity = new HbaseDataEntity();
				entity.setNameSpace(nameSpace);
				entity.setTableName(name);
				entity.setMobileKey(new String(r.getRow()));
				Map<String, Map<String, String>> familyMap = new HashMap<String, Map<String, String>>();
				for (Cell cell : r.rawCells()) {
					if (familyMap.get(new String(cell.getFamilyArray(), cell
							.getFamilyOffset(), cell.getFamilyLength())) == null) {
						Map<String, String> columnsMap = new HashMap<String, String>();
						columnsMap.put(
								new String(cell.getQualifierArray(), cell
										.getQualifierOffset(), cell
										.getQualifierLength()),
								new String(cell.getValueArray(), cell
										.getValueOffset(), cell
										.getValueLength()));
						familyMap.put(
								new String(cell.getFamilyArray(), cell
										.getFamilyOffset(), cell
										.getFamilyLength()), columnsMap);
					} else {
						familyMap.get(
								new String(cell.getFamilyArray(), cell
										.getFamilyOffset(), cell
										.getFamilyLength())).put(
								new String(cell.getQualifierArray(),
										cell.getQualifierOffset(),
										cell.getQualifierLength()),
								new String(cell.getValueArray(), cell
										.getValueOffset(), cell
										.getValueLength()));
					}
				}

				entity.setColumns(familyMap);
				list.add(entity);
			}
			table.close();
			rs.close();
			return list;
		} catch (Exception e) {
			log.error("HbaseUtil 出错",e);
		}
		return null;
	}

	*//**
     * 复合条件分页查询
     * @param name
     * @param pageSize
     * @param lastRow
     * @return
     *//*
	public static List<HbaseDataEntity> getHbaseDatasByPage(String name,
															int pageSize, byte[] lastRow) {
		final byte[] POSTFIX = new byte[]{0x00};
		Scan scan = new Scan();
		ResultScanner resultScanner = null;
		Table table = null;
		List<HbaseDataEntity> list = new ArrayList<HbaseDataEntity>();
		try {
			TableName tableName = TableName.valueOf(name);
			table = HbaseUtil.connection.getTable(tableName);
			Filter filter = new PageFilter(pageSize);
			scan.setFilter(filter);
			if (lastRow != null) {
				// 注意这里添加了POSTFIX操作，不然死循环了
				byte[] startRow = Bytes.add(lastRow, POSTFIX);
				scan.setStartRow(startRow);
			}
			resultScanner = table.getScanner(scan);
			for (Result result : resultScanner) {
				HbaseDataEntity entity = new HbaseDataEntity();
				entity.setTableName(name);
				entity.setMobileKey(new String(result.getRow()));
				Map<String, Map<String, String>> familyMap = new HashMap<String, Map<String, String>>();
				for (Cell cell : result.rawCells()) {
					if (familyMap.get(new String(cell.getFamilyArray(), cell
							.getFamilyOffset(), cell.getFamilyLength())) == null) {
						Map<String, String> columnsMap = new HashMap<String, String>();
						columnsMap.put(
								new String(cell.getQualifierArray(), cell
										.getQualifierOffset(), cell
										.getQualifierLength()),
								new String(cell.getValueArray(), cell
										.getValueOffset(), cell
										.getValueLength()));
						familyMap.put(
								new String(cell.getFamilyArray(), cell
										.getFamilyOffset(), cell
										.getFamilyLength()), columnsMap);
					} else {
						familyMap.get(
								new String(cell.getFamilyArray(), cell
										.getFamilyOffset(), cell
										.getFamilyLength())).put(
								new String(cell.getQualifierArray(),
										cell.getQualifierOffset(),
										cell.getQualifierLength()),
								new String(cell.getValueArray(), cell
										.getValueOffset(), cell
										.getValueLength()));
					}
				}
				entity.setColumns(familyMap);
				list.add(entity);
			}
			table.close();
			return list;
		} catch (IOException e) {
			log.error("HbaseUtil 出错",e);
		} finally {
			resultScanner.close();
		}
		return null;
	}

	public static int getDataByPage(String name, int pageSize) {

		final byte[] POSTFIX = new byte[]{0x00};
		TableName tableName = TableName.valueOf(name);
		Table table;
		int totalRows = 0;
		try {
			table = HbaseUtil.connection.getTable(tableName);
			Filter filter = new PageFilter(pageSize);
			byte[] lastRow = null;

			while (true) {
				Scan scan = new Scan();
				scan.setFilter(filter);
				if (lastRow != null) {
					// 注意这里添加了POSTFIX操作，不然死循环了
					byte[] startRow = Bytes.add(lastRow, POSTFIX);
					scan.setStartRow(startRow);
				}
				ResultScanner scanner = table.getScanner(scan);
				int localRows = 0;
				Result result;
				while ((result = scanner.next()) != null) {
					logger.info(localRows++ + ":" + result);
					totalRows++;
					lastRow = result.getRow();
				}
				scanner.close();
				if (localRows == 0)
					break;
			}
		} catch (IOException e) {
			log.error("HbaseUtil 出错",e);
		}
		logger.info("total rows:" + totalRows);
		return totalRows;
	}*/

}
