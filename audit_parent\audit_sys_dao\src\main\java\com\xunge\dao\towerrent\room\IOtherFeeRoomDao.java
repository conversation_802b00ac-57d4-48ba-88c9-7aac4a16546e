package com.xunge.dao.towerrent.room;

import com.xunge.core.page.Page;
import com.xunge.model.towerrent.settlement.OtherFeeRoomVO;
import com.xunge.model.towerrent.settlement.TowerAndMobileBillRoomConfirmVO;
import com.xunge.model.towerrent.settlement.TowerBillBalanceRoomVO;

import java.util.List;
import java.util.Map;

/**
 * TODO: 接口描述
 *
 * <AUTHOR>
 * @date 2019/4/23 14:59
 */
public interface IOtherFeeRoomDao {
    List<TowerBillBalanceRoomVO> queryRoomBill(Map<String, Object> paramMap);

    List<TowerAndMobileBillRoomConfirmVO> queryAccountedRoomBill(Map<String, Object> paramMap);

    //撤销汇总
    int updateRoomFeeSumcodeToNull(Map<String, Object> map);

    int updateOtherFeeRoomSumcodeToNull(Map<String, Object> map);

    //汇总后改状态
    int updateRoomBillSetSumcode(TowerBillBalanceRoomVO towerBillBalanceRoomVO);

    //室分其他费用，汇总后改状态
    String updateRoomOtherById(OtherFeeRoomVO otherFeeRoomVO);

    Page<TowerAndMobileBillRoomConfirmVO> queryAccountedRoomBillByPage(Map<String, Object> paraMap, int pageNumber, int pageSize);
}
