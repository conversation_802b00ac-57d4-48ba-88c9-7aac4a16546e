package com.xunge.model.contract;

import java.io.Serializable;

public class SynConfig implements Serializable {

    private static final long serialVersionUID = -6209057979898144588L;
    private Integer pageSize;
    private String lastUpdateTime;
    private Integer days;
    private String retroactive;//补充协议配置

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public String getLastUpdateTime() {
        return lastUpdateTime;
    }

    public void setLastUpdateTime(String lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    public Integer getDays() {
        return days;
    }

    public void setDays(Integer days) {
        this.days = days;
    }

    public String getRetroactive() {
        return retroactive;
    }

    public void setRetroactive(String retroactive) {
        this.retroactive = retroactive;
    }


}
