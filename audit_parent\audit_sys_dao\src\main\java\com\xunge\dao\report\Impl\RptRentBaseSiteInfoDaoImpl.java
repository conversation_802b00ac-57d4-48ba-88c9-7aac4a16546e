package com.xunge.dao.report.Impl;

import com.xunge.dao.AbstractBaseDao;
import com.xunge.dao.report.IRptRentBaseSiteInfoDao;
import com.xunge.model.report.RptRentBaseSiteInfoVO;
import com.xunge.model.report.RptRentSiteInfoNewVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * @创建人 LiangCheng
 * @创建时间 2019/11/29 0029
 * @描述：
 */
@Repository("rptRentBaseSiteInfoDao")
@Slf4j
public class RptRentBaseSiteInfoDaoImpl extends AbstractBaseDao implements IRptRentBaseSiteInfoDao {

    final String Namespace = "com.xunge.mapping.report.RptRentBaseSiteInfoMapper.";

    @Override
    public List<RptRentSiteInfoNewVO> queryRptRentBaseSiteInfoByPrvId(Map<String, Object> param) {
        List<RptRentBaseSiteInfoVO> list = this.getSqlSession().selectList(Namespace + "queryRptRentBaseSiteInfoByPrvId", param);
        list = MultiplyRadio(param, list);
        return transpose(list, 1);
    }

    @Override
    public List<RptRentSiteInfoNewVO> queryRptRentBaseSiteInfoByPregId(Map<String, Object> param) {
        List<RptRentBaseSiteInfoVO> list = this.getSqlSession().selectList(Namespace + "queryRptRentBaseSiteInfoByPregId", param);
        list = MultiplyRadio(param, list);
        return transpose(list, 2);
    }

    @Override
    public List<RptRentSiteInfoNewVO> queryRptRentBaseSiteInfoByRegId(Map<String, Object> param) {
        List<RptRentBaseSiteInfoVO> list = this.getSqlSession().selectList(Namespace + "queryRptRentBaseSiteInfoByRegId", param);
        list = MultiplyRadio(param, list);
        return transpose(list, 3);
    }

    @Override
    public List<RptRentSiteInfoNewVO> queryRptRentBaseSitePayableInfoByPrvId(Map<String, Object> param) {
        List<RptRentBaseSiteInfoVO> list = this.getSqlSession().selectList(Namespace + "queryRptRentBaseSitePayableInfoByPrvId", param);
        list = MultiplyRadio(param, list);
        return transpose(list, 1);
    }

    @Override
    public List<RptRentSiteInfoNewVO> queryRptRentBaseSitePayableInfoByPregId(Map<String, Object> param) {
        List<RptRentBaseSiteInfoVO> list = this.getSqlSession().selectList(Namespace + "queryRptRentBaseSitePayableInfoByPregId", param);
        list = MultiplyRadio(param, list);
        return transpose(list, 2);
    }

    @Override
    public List<RptRentSiteInfoNewVO> queryRptRentBaseSitePayableInfoByRegId(Map<String, Object> param) {
        List<RptRentBaseSiteInfoVO> list = this.getSqlSession().selectList(Namespace + "queryRptRentBaseSitePayableInfoByRegId", param);
        list = MultiplyRadio(param, list);
        return transpose(list, 3);
    }

    @Override
    public List<RptRentBaseSiteInfoVO> queryRptRentBaseSiteInfoByPrvIdCount(Map<String, Object> param) {
        List<RptRentBaseSiteInfoVO> list = this.getSqlSession().selectList(Namespace + "queryRptRentBaseSiteInfoByPrvIdCount", param);
        list = MultiplyRadio(param, list);
        return list;
    }

    @Override
    public List<RptRentBaseSiteInfoVO> queryRptRentBaseSiteInfoByPregIdCount(Map<String, Object> param) {
        List<RptRentBaseSiteInfoVO> list = this.getSqlSession().selectList(Namespace + "queryRptRentBaseSiteInfoByPregIdCount", param);
        list = MultiplyRadio(param, list);
        return list;
    }

    @Override
    public List<RptRentBaseSiteInfoVO> queryRptRentBaseSiteInfoByRegIdCount(Map<String, Object> param) {
        List<RptRentBaseSiteInfoVO> list = this.getSqlSession().selectList(Namespace + "queryRptRentBaseSiteInfoByRegIdCount", param);
        list = MultiplyRadio(param, list);
        return list;
    }

    @Override
    public List<RptRentBaseSiteInfoVO> queryRptRentBaseSitePayableInfoByPrvIdCount(Map<String, Object> param) {
        List<RptRentBaseSiteInfoVO> list = this.getSqlSession().selectList(Namespace + "queryRptRentBaseSitePayableInfoByPrvIdCount", param);
        list = MultiplyRadio(param, list);
        return list;
    }

    @Override
    public List<RptRentBaseSiteInfoVO> queryRptRentBaseSitePayableInfoByPregIdCount(Map<String, Object> param) {
        List<RptRentBaseSiteInfoVO> list = this.getSqlSession().selectList(Namespace + "queryRptRentBaseSitePayableInfoByPregIdCount", param);
        list = MultiplyRadio(param, list);
        return list;
    }

    @Override
    public List<RptRentBaseSiteInfoVO> queryRptRentBaseSitePayableInfoByRegIdCount(Map<String, Object> param) {
        List<RptRentBaseSiteInfoVO> list = this.getSqlSession().selectList(Namespace + "queryRptRentBaseSitePayableInfoByRegIdCount", param);
        list = MultiplyRadio(param, list);
        return list;
    }

    public List<RptRentBaseSiteInfoVO> MultiplyRadio(Map<String, Object> param, List<RptRentBaseSiteInfoVO> list) {
        try {
            BigDecimal radio = BigDecimal.valueOf(30.416667);
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
            String onYear = param.get("onYear").toString();
            String onMonth = param.get("onMonth").toString();
            String dateStr = onYear + onMonth + "01";
            Date date = sdf.parse(dateStr);
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(date);
            int now_day = calendar.getActualMaximum(Calendar.DAY_OF_MONTH);
            calendar.set(Calendar.MONTH, calendar.get(Calendar.MONTH) - 1);
            int up_day = calendar.getActualMaximum(Calendar.DAY_OF_MONTH);
            for (RptRentBaseSiteInfoVO v : list) {
                //月平均单站租赁费及变化幅度
                BigDecimal monthAvg = v.getMonthAvg().divide(new BigDecimal(now_day), 6, BigDecimal.ROUND_HALF_UP);
                monthAvg = monthAvg.multiply(radio);
                v.setMonthAvg(monthAvg);
                BigDecimal lastMonthAvg = BigDecimal.ZERO;
                if (monthAvg.compareTo(BigDecimal.ZERO) != 0) {
                    lastMonthAvg = v.getLastMonthAvg().divide(new BigDecimal(up_day), 6, BigDecimal.ROUND_HALF_UP);
                    lastMonthAvg = lastMonthAvg.multiply(radio);
                    BigDecimal lastMonthAvgSubNow = (monthAvg).subtract(lastMonthAvg);
                    if (lastMonthAvg.compareTo(BigDecimal.ZERO) != 0) {
                        lastMonthAvg = lastMonthAvgSubNow.divide(lastMonthAvg, 6, BigDecimal.ROUND_HALF_UP);
                    } else {
                        lastMonthAvg = BigDecimal.ZERO;
                    }
                }
                v.setLastMonthAvg(lastMonthAvg);
                //月平均单站外部参考价格及变化幅度
                BigDecimal benchmarkAvg = v.getBenchmarkPriceAvg().divide(new BigDecimal(now_day), 6, BigDecimal.ROUND_HALF_UP);
                benchmarkAvg = benchmarkAvg.multiply(radio);
                v.setBenchmarkPriceAvg(benchmarkAvg);
                BigDecimal lastBenchmarkAvg = BigDecimal.ZERO;
                if (benchmarkAvg.compareTo(BigDecimal.ZERO) != 0) {
                    lastBenchmarkAvg = v.getLastBenchmarkPriceAvg().divide(new BigDecimal(up_day), 6, BigDecimal.ROUND_HALF_UP);
                    lastBenchmarkAvg = lastBenchmarkAvg.multiply(radio);
                    BigDecimal lastBenchmarkAvgSubNow = (benchmarkAvg).subtract(lastBenchmarkAvg);
                    if (lastBenchmarkAvg.compareTo(BigDecimal.ZERO) != 0) {
                        lastBenchmarkAvg = lastBenchmarkAvgSubNow.divide(lastBenchmarkAvg, 6, BigDecimal.ROUND_HALF_UP);
                    } else {
                        lastBenchmarkAvg = BigDecimal.ZERO;
                    }
                }
                v.setLastBenchmarkPriceAvg(lastBenchmarkAvg);
                //月平均单面积租赁费及变化幅度
                if (v.getMonthAmountAvgArea() == null) {
                    v.setLastMonthAmountAvgArea(null);
                } else {
                    BigDecimal monthAvgArea = v.getMonthAmountAvgArea().divide(new BigDecimal(now_day), 6, BigDecimal.ROUND_HALF_UP);
                    monthAvgArea = monthAvgArea.multiply(radio);
                    v.setMonthAmountAvgArea(monthAvgArea);
                    BigDecimal lastMonthAvgArea = null;
                    if (v.getLastMonthAmountAvgArea() != null) {
                        if (monthAvgArea.compareTo(BigDecimal.ZERO) != 0) {
                            lastMonthAvgArea = v.getLastMonthAmountAvgArea().divide(new BigDecimal(up_day), 6, BigDecimal.ROUND_HALF_UP);
                            lastMonthAvgArea = lastMonthAvgArea.multiply(radio);
                            BigDecimal lastMonthAvgAreaSubNow = (monthAvgArea).subtract(lastMonthAvgArea);
                            if (lastMonthAvgArea.compareTo(BigDecimal.ZERO) != 0) {
                                lastMonthAvgArea = lastMonthAvgAreaSubNow.divide(lastMonthAvgArea, 6, BigDecimal.ROUND_HALF_UP);
                            } else {
                                lastMonthAvgArea = BigDecimal.ZERO;
                            }
                        }
                    }
                    v.setLastMonthAmountAvgArea(lastMonthAvgArea);
                }
                //月平均单面积外部参考价格及变化幅度
                if (v.getBenchmarkPriceAvgArea() == null) {
                    v.setLastBenchmarkPriceAvgArea(null);
                } else {
                    BigDecimal benchmarkAvgArea = v.getBenchmarkPriceAvgArea().divide(new BigDecimal(now_day), 6, BigDecimal.ROUND_HALF_UP);
                    benchmarkAvgArea = benchmarkAvgArea.multiply(radio);
                    v.setBenchmarkPriceAvgArea(benchmarkAvgArea);
                    BigDecimal lastBenchmarkAvgArea = null;
                    if (v.getLastBenchmarkPriceAvgArea() != null) {
                        if (benchmarkAvgArea.compareTo(BigDecimal.ZERO) != 0) {
                            lastBenchmarkAvgArea = v.getLastBenchmarkPriceAvgArea().divide(new BigDecimal(up_day), 6, BigDecimal.ROUND_HALF_UP);
                            lastBenchmarkAvgArea = lastBenchmarkAvgArea.multiply(radio);
                            BigDecimal lastBenchmarkAvgAreaSubNow = (benchmarkAvgArea).subtract(lastBenchmarkAvgArea);
                            if (lastBenchmarkAvgArea.compareTo(BigDecimal.ZERO) != 0) {
                                lastBenchmarkAvgArea = lastBenchmarkAvgAreaSubNow.divide(lastBenchmarkAvgArea, 6, BigDecimal.ROUND_HALF_UP);
                            } else {
                                lastBenchmarkAvgArea = BigDecimal.ZERO;
                            }
                        }
                    }
                    v.setLastBenchmarkPriceAvgArea(lastBenchmarkAvgArea);
                }
            }
        } catch (ParseException e) {
            log.error("RptRentBaseSiteInfoDaoImpl 出错", e);
        }
        return list;
    }

    private List<RptRentSiteInfoNewVO> transpose(List<RptRentBaseSiteInfoVO> result, int type) {
        List<RptRentSiteInfoNewVO> newResult = new ArrayList<>();
        Map<String, RptRentBaseSiteInfoVO> regMap = new HashMap<>();
        String key = "";
        //过滤原结果集，以省份或地市或区县维度生成新结果集基础数据
        for (RptRentBaseSiteInfoVO vo : result) {
            switch (type) {
                case 1:
                    key = vo.getPrvId();
                    break;
                case 2:
                    key = vo.getPrvId() + vo.getPregId();
                    break;
                case 3:
                    key = vo.getPrvId() + vo.getPregId() + vo.getRegId();
                    break;
            }
            if (regMap.containsKey(key)) {
                continue;
            } else {
                regMap.put(key, vo);
                RptRentSiteInfoNewVO vo0 = new RptRentSiteInfoNewVO();
                vo0.setAnalyticIndex("0");
                vo0.setOnYear(vo.getOnYear());
                vo0.setPrvId(vo.getPrvId());
                vo0.setPregId(vo.getPregId());
                vo0.setRegId(vo.getRegId());
                vo0.setPrvName(vo.getPrvName());
                vo0.setPregName(vo.getPregName());
                vo0.setRegName(vo.getRegName());
                newResult.add(vo0);//月平均单站租赁费
                RptRentSiteInfoNewVO vo1 = (RptRentSiteInfoNewVO) vo0.clone();
                vo1.setAnalyticIndex("1");
                newResult.add(vo1);//月平均单站租赁费变化幅度
                RptRentSiteInfoNewVO vo2 = (RptRentSiteInfoNewVO) vo0.clone();
                vo2.setAnalyticIndex("2");
                newResult.add(vo2);//月平均单站租赁费变化原因
                RptRentSiteInfoNewVO vo3 = (RptRentSiteInfoNewVO) vo0.clone();
                vo3.setAnalyticIndex("3");
                newResult.add(vo3);//月平均单面积租赁费
                RptRentSiteInfoNewVO vo4 = (RptRentSiteInfoNewVO) vo0.clone();
                vo4.setAnalyticIndex("4");
                newResult.add(vo4);//月平均单面积租赁费变化幅度
                /*RptRentSiteInfoNewVO vo5 = (RptRentSiteInfoNewVO) vo0.clone();
                vo5.setAnalyticIndex("5");
                newResult.add(vo5);//月平均单站外部参考价格
                RptRentSiteInfoNewVO vo6 = (RptRentSiteInfoNewVO) vo0.clone();
                vo6.setAnalyticIndex("6");
                newResult.add(vo6);//月平均单站外部参考价格变化幅度
                RptRentSiteInfoNewVO vo7 = (RptRentSiteInfoNewVO) vo0.clone();
                vo7.setAnalyticIndex("7");
                newResult.add(vo7);//月平均单面积外部参考价格
                RptRentSiteInfoNewVO vo8 = (RptRentSiteInfoNewVO) vo0.clone();
                vo8.setAnalyticIndex("8");
                newResult.add(vo8);//月平均单面积外部参考价格变化幅度*/
            }
        }
        //赋值
        for (RptRentSiteInfoNewVO newVo : newResult) {
            for (RptRentBaseSiteInfoVO vo : result) {
                boolean flag = false;
                switch (type) {
                    case 1:
                        flag = newVo.getPrvId().equals(vo.getPrvId());
                        break;
                    case 2:
                        flag = newVo.getPrvId().equals(vo.getPrvId()) && newVo.getPregId().equals(vo.getPregId());
                        break;
                    case 3:
                        flag = newVo.getPrvId().equals(vo.getPrvId()) && newVo.getPregId().equals(vo.getPregId()) && newVo.getRegId().equals(vo.getRegId());
                        break;
                }
                if (flag) {
                    //单站租赁费
                    if (newVo.getAnalyticIndex().equals("0")) {
                        switch (vo.getSiteType()) {
                            case 0:
                                newVo.setTotal(vo.getMonthAvg());
                                break;
                            case 1:
                                newVo.setCoreBuilding(vo.getMonthAvg());
                                break;
                            case 2:
                                newVo.setTransSite(vo.getMonthAvg());
                                break;
                            case 3:
                                newVo.setTransPosition(vo.getMonthAvg());
                                break;
                            case 4:
                                newVo.setBaseSite(vo.getMonthAvg());
                                break;
                            case 5:
                                newVo.setRoomAndWlan(vo.getMonthAvg());
                                break;
                            case 6:
                                newVo.setHakkaJike(vo.getMonthAvg());
                                break;
                            case 7:
                                newVo.setIdc(vo.getMonthAvg());
                                break;
                            case 8:
                                newVo.setBase(vo.getMonthAvg());
                                break;
                            case 9:
                                newVo.setComprehensivePosition(vo.getMonthAvg());
                                break;
                            case 10:
                                newVo.setOther(vo.getMonthAvg());
                                break;
                        }
                    }
                    //变化幅度
                    if (newVo.getAnalyticIndex().equals("1")) {
                        BigDecimal lastMonthAvg = vo.getLastMonthAvg() == null
                                ? null : vo.getLastMonthAvg().multiply(new BigDecimal(100));
                        switch (vo.getSiteType()) {
                            case 0:
                                newVo.setTotal(lastMonthAvg);
                                break;
                            case 1:
                                newVo.setCoreBuilding(lastMonthAvg);
                                break;
                            case 2:
                                newVo.setTransSite(lastMonthAvg);
                                break;
                            case 3:
                                newVo.setTransPosition(lastMonthAvg);
                                break;
                            case 4:
                                newVo.setBaseSite(lastMonthAvg);
                                break;
                            case 5:
                                newVo.setRoomAndWlan(lastMonthAvg);
                                break;
                            case 6:
                                newVo.setHakkaJike(lastMonthAvg);
                                break;
                            case 7:
                                newVo.setIdc(lastMonthAvg);
                                break;
                            case 8:
                                newVo.setBase(lastMonthAvg);
                                break;
                            case 9:
                                newVo.setComprehensivePosition(lastMonthAvg);
                                break;
                            case 10:
                                newVo.setOther(lastMonthAvg);
                                break;
                        }
                    }
                    //变化原因
                    if (newVo.getAnalyticIndex().equals("2")) {
                        switch (vo.getSiteType()) {
                            case 1:
                                newVo.setCoreBuildingNote(vo.getSingeAmountNote());
                                break;
                            case 2:
                                newVo.setTransSiteNote(vo.getSingeAmountNote());
                                break;
                            case 3:
                                newVo.setTransPositionNote(vo.getSingeAmountNote());
                                break;
                            case 4:
                                newVo.setBaseSiteNote(vo.getSingeAmountNote());
                                break;
                            case 5:
                                newVo.setRoomAndWlanNote(vo.getSingeAmountNote());
                                break;
                            case 6:
                                newVo.setHakkaJikeNote(vo.getSingeAmountNote());
                                break;
                            case 7:
                                newVo.setIdcNote(vo.getSingeAmountNote());
                                break;
                            case 8:
                                newVo.setBaseNote(vo.getSingeAmountNote());
                                break;
                            case 9:
                                newVo.setComprehensivePositionNote(vo.getSingeAmountNote());
                                break;
                            case 10:
                                newVo.setOtherNote(vo.getSingeAmountNote());
                                break;
                        }
                    }
                    //月平均单面积租赁费
                    if (newVo.getAnalyticIndex().equals("3")) {
                        switch (vo.getSiteType()) {
                            case 0:
                                newVo.setTotal(vo.getMonthAmountAvgArea());
                                break;
                            case 1:
                                newVo.setCoreBuilding(vo.getMonthAmountAvgArea());
                                break;
                            case 2:
                                newVo.setTransSite(vo.getMonthAmountAvgArea());
                                break;
                            case 3:
                                newVo.setTransPosition(vo.getMonthAmountAvgArea());
                                break;
                            case 4:
                                newVo.setBaseSite(vo.getMonthAmountAvgArea());
                                break;
                            case 5:
                                newVo.setRoomAndWlan(vo.getMonthAmountAvgArea());
                                break;
                            case 6:
                                newVo.setHakkaJike(vo.getMonthAmountAvgArea());
                                break;
                            case 7:
                                newVo.setIdc(vo.getMonthAmountAvgArea());
                                break;
                            case 8:
                                newVo.setBase(vo.getMonthAmountAvgArea());
                                break;
                            case 9:
                                newVo.setComprehensivePosition(vo.getMonthAmountAvgArea());
                                break;
                            case 10:
                                newVo.setOther(vo.getMonthAmountAvgArea());
                                break;
                        }
                    }
                    //月平均单面积租赁费变化幅度
                    if (newVo.getAnalyticIndex().equals("4")) {
                        BigDecimal lastMonthAmountAvgArea = vo.getLastMonthAmountAvgArea() == null
                                ? null : vo.getLastMonthAmountAvgArea().multiply(new BigDecimal(100));
                        switch (vo.getSiteType()) {
                            case 0:
                                newVo.setTotal(lastMonthAmountAvgArea);
                                break;
                            case 1:
                                newVo.setCoreBuilding(lastMonthAmountAvgArea);
                                break;
                            case 2:
                                newVo.setTransSite(lastMonthAmountAvgArea);
                                break;
                            case 3:
                                newVo.setTransPosition(lastMonthAmountAvgArea);
                                break;
                            case 4:
                                newVo.setBaseSite(lastMonthAmountAvgArea);
                                break;
                            case 5:
                                newVo.setRoomAndWlan(lastMonthAmountAvgArea);
                                break;
                            case 6:
                                newVo.setHakkaJike(lastMonthAmountAvgArea);
                                break;
                            case 7:
                                newVo.setIdc(lastMonthAmountAvgArea);
                                break;
                            case 8:
                                newVo.setBase(lastMonthAmountAvgArea);
                                break;
                            case 9:
                                newVo.setComprehensivePosition(lastMonthAmountAvgArea);
                                break;
                            case 10:
                                newVo.setOther(lastMonthAmountAvgArea);
                                break;
                        }
                    }
                    //月平均单站外部参考价格
                    if (newVo.getAnalyticIndex().equals("5")) {
                        switch (vo.getSiteType()) {
                            case 0:
                                newVo.setTotal(vo.getBenchmarkPriceAvg());
                                break;
                            case 1:
                                newVo.setCoreBuilding(vo.getBenchmarkPriceAvg());
                                break;
                            case 2:
                                newVo.setTransSite(vo.getBenchmarkPriceAvg());
                                break;
                            case 3:
                                newVo.setTransPosition(vo.getBenchmarkPriceAvg());
                                break;
                            case 4:
                                newVo.setBaseSite(vo.getBenchmarkPriceAvg());
                                break;
                            case 5:
                                newVo.setRoomAndWlan(vo.getBenchmarkPriceAvg());
                                break;
                            case 6:
                                newVo.setHakkaJike(vo.getBenchmarkPriceAvg());
                                break;
                            case 7:
                                newVo.setIdc(vo.getBenchmarkPriceAvg());
                                break;
                            case 8:
                                newVo.setBase(vo.getBenchmarkPriceAvg());
                                break;
                            case 9:
                                newVo.setComprehensivePosition(vo.getBenchmarkPriceAvg());
                                break;
                            case 10:
                                newVo.setOther(vo.getBenchmarkPriceAvg());
                                break;
                        }
                    }
                    //月平均单站外部参考价格变化幅度
                    if (newVo.getAnalyticIndex().equals("6")) {
                        BigDecimal lastBenchmarkPriceAvg = vo.getLastBenchmarkPriceAvg() == null
                                ? null : vo.getLastBenchmarkPriceAvg().multiply(new BigDecimal(100));
                        switch (vo.getSiteType()) {
                            case 0:
                                newVo.setTotal(lastBenchmarkPriceAvg);
                                break;
                            case 1:
                                newVo.setCoreBuilding(lastBenchmarkPriceAvg);
                                break;
                            case 2:
                                newVo.setTransSite(lastBenchmarkPriceAvg);
                                break;
                            case 3:
                                newVo.setTransPosition(lastBenchmarkPriceAvg);
                                break;
                            case 4:
                                newVo.setBaseSite(lastBenchmarkPriceAvg);
                                break;
                            case 5:
                                newVo.setRoomAndWlan(lastBenchmarkPriceAvg);
                                break;
                            case 6:
                                newVo.setHakkaJike(lastBenchmarkPriceAvg);
                                break;
                            case 7:
                                newVo.setIdc(lastBenchmarkPriceAvg);
                                break;
                            case 8:
                                newVo.setBase(lastBenchmarkPriceAvg);
                                break;
                            case 9:
                                newVo.setComprehensivePosition(lastBenchmarkPriceAvg);
                                break;
                            case 10:
                                newVo.setOther(lastBenchmarkPriceAvg);
                                break;
                        }
                    }
                    //月平均单面积外部参考价格
                    if (newVo.getAnalyticIndex().equals("7")) {
                        switch (vo.getSiteType()) {
                            case 0:
                                newVo.setTotal(vo.getBenchmarkPriceAvgArea());
                                break;
                            case 1:
                                newVo.setCoreBuilding(vo.getBenchmarkPriceAvgArea());
                                break;
                            case 2:
                                newVo.setTransSite(vo.getBenchmarkPriceAvgArea());
                                break;
                            case 3:
                                newVo.setTransPosition(vo.getBenchmarkPriceAvgArea());
                                break;
                            case 4:
                                newVo.setBaseSite(vo.getBenchmarkPriceAvgArea());
                                break;
                            case 5:
                                newVo.setRoomAndWlan(vo.getBenchmarkPriceAvgArea());
                                break;
                            case 6:
                                newVo.setHakkaJike(vo.getBenchmarkPriceAvgArea());
                                break;
                            case 7:
                                newVo.setIdc(vo.getBenchmarkPriceAvgArea());
                                break;
                            case 8:
                                newVo.setBase(vo.getBenchmarkPriceAvgArea());
                                break;
                            case 9:
                                newVo.setComprehensivePosition(vo.getBenchmarkPriceAvgArea());
                                break;
                            case 10:
                                newVo.setOther(vo.getBenchmarkPriceAvgArea());
                                break;
                        }
                    }
                    //月平均单面积外部参考价格变化幅度
                    if (newVo.getAnalyticIndex().equals("8")) {
                        BigDecimal lastBenchmarkPriceAvgArea = vo.getLastBenchmarkPriceAvgArea() == null
                                ? null : vo.getLastBenchmarkPriceAvgArea().multiply(new BigDecimal(100));
                        switch (vo.getSiteType()) {
                            case 0:
                                newVo.setTotal(lastBenchmarkPriceAvgArea);
                                break;
                            case 1:
                                newVo.setCoreBuilding(lastBenchmarkPriceAvgArea);
                                break;
                            case 2:
                                newVo.setTransSite(lastBenchmarkPriceAvgArea);
                                break;
                            case 3:
                                newVo.setTransPosition(lastBenchmarkPriceAvgArea);
                                break;
                            case 4:
                                newVo.setBaseSite(lastBenchmarkPriceAvgArea);
                                break;
                            case 5:
                                newVo.setRoomAndWlan(lastBenchmarkPriceAvgArea);
                                break;
                            case 6:
                                newVo.setHakkaJike(lastBenchmarkPriceAvgArea);
                                break;
                            case 7:
                                newVo.setIdc(lastBenchmarkPriceAvgArea);
                                break;
                            case 8:
                                newVo.setBase(lastBenchmarkPriceAvgArea);
                                break;
                            case 9:
                                newVo.setComprehensivePosition(lastBenchmarkPriceAvgArea);
                                break;
                            case 10:
                                newVo.setOther(lastBenchmarkPriceAvgArea);
                                break;
                        }
                    }
                }
            }
        }
        return newResult;
    }
}
