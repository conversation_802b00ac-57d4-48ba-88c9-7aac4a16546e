package com.xunge.dao.selfelec;

import com.xunge.model.selfelec.EleBillamountdetail;
import com.xunge.model.selfelec.EleBillamountdetailExample;
import com.xunge.model.selfelec.EleBillamountdetailFinance;
import com.xunge.model.selfelec.EleBillamountdetailFinanceNew;
import com.xunge.model.selfelec.verification.EleVerification;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.cursor.Cursor;

import java.util.List;
import java.util.Map;

public interface EleBillamountdetailMapper {
    int countByExample(EleBillamountdetailExample example);

    int deleteByExample(EleBillamountdetailExample example);

    int deleteByPrimaryKey(String billamountdetailId);

    int insert(EleBillamountdetail record);

    int insertSelective(EleBillamountdetail record);

    List<EleBillamountdetail> selectByExample(EleBillamountdetailExample example);

    EleBillamountdetail selectByPrimaryKey(String billamountdetailId);

    int updateByExampleSelective(@Param("record") EleBillamountdetail record, @Param("example") EleBillamountdetailExample example);

    int updateByExample(@Param("record") EleBillamountdetail record, @Param("example") EleBillamountdetailExample example);

    int updateByPrimaryKeySelective(EleBillamountdetail record);

    int updateByPrimaryKey(EleBillamountdetail record);

    List<EleBillamountdetail> queryelePaymentBySupplierId(EleBillamountdetail eleBillamountdetail);

    List<EleBillamountdetail> queryFinanceelePaymentBySupplierId(EleBillamountdetail eleBillamountdetail);

    List<EleBillamountdetail> queryBillamountPayment(Map<String, Object> maps);

    List<EleVerification> selectElePayment(@Param("billamountId") String billamountId);

    List<EleBillamountdetail> queryEleBillamountDetail(Map<String, Object> maps);

    List<EleBillamountdetail> queryRentEleBillamountDetail(Map<String, Object> maps);

    List<EleBillamountdetailFinance> queryEleBillamountDetailFinance(Map<String, Object> maps);

    List<EleBillamountdetailFinanceNew> queryEleBillamountDetailFinanceNew(Map<String, Object> maps);


    List<EleBillamountdetail> querySupplierInfo(@Param("billamountId") String billamountId);

    List<EleBillamountdetail> queryContractInfo(@Param("billamountId") String billamountId);

    void deleteByBillamountId(@Param("billamountId") String billamountId);

    void updateBillamountdetailAdjustById(EleBillamountdetail param);


    /**
     * @param @param supplierId
     * @param @param supplierIds    设定文件
     * @return void    返回类型
     * @throws
     * @Title: updateSupplierInfo
     * @Description: TODO(这里用一句话描述这个方法的作用)
     */

    void updateSupplierInfo(@Param("supplierId") String supplierId, @Param("supplierIds") List<String> supplierIds);


    /**
     * @param @param contractId
     * @param @param contractIds    设定文件
     * @return void    返回类型
     * @throws
     * @Title: updateComtractInfo
     * @Description: TODO(这里用一句话描述这个方法的作用)
     */

    void updateComtractInfo(@Param("contractId") String contractId, @Param("contractIds") List<String> contractIds);

    int updateSecondBillamountId(EleBillamountdetail amountDetail);

    int updateSecondBillamountIdByPayment(EleBillamountdetail amountDetail);

    List<EleBillamountdetail> queryEleBillamountDetailLeftJoinOther(Map<String, Object> maps);

    List<EleBillamountdetail> queryElePaymentInfo(@Param("billamountId")String billamountId);

    List<EleBillamountdetail> selectListByBillaccountPaymentDetailId(@Param("billaccountPaymentDetailId")String billaccountPaymentDetailId);

	/** 
	* @Description: 汇总生成，修改查询导出缴费单
	* <AUTHOR>   
	* @date 2025年7月11日 上午11:00:05 
	* @param eleBillamountdetail
	* @return  
	*/ 
	Cursor<EleBillamountdetail> queryelePaymentBySupplierIdV1(EleBillamountdetail eleBillamountdetail);

	/** 
	* @Description: 汇总生成，修改查询导出缴费单
	* <AUTHOR>   
	* @date 2025年7月11日 下午4:55:26 
	* @param eleBillamountdetail
	* @return  
	*/ 
	Cursor<EleBillamountdetail> queryFinanceelePaymentBySupplierIdV1(EleBillamountdetail eleBillamountdetail);

}