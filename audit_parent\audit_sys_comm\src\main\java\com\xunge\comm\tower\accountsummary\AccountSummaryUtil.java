package com.xunge.comm.tower.accountsummary;

import com.google.common.collect.BiMap;
import com.google.common.collect.HashBiMap;

import java.util.Set;

/**
 * @Description:
 * @Author: dxd
 * @Date: 2023/5/15 9:56
 */
public class AccountSummaryUtil {

    public static final String FINANCE_CONFIG = "\\properties\\financeConfig.properties";

    public static final String SYS_CONFIG = "\\properties\\sysConfig.properties";

    public static final String IMAGE_CHECK_URL = "imageSimilarCheckUrl";

    public static final String IMAGE_DELETE_URL = "imageSimilarDeleteImagesUrl";

    public static final String UPLOAD_URLS= "UploadUrls";

    public static final String[] fileTypes = new String[]{"PNG","JPG","BMP"};

    public static final String MAJOR_CODE = "04";

    public static final String  MAJOR_CODE_RENT = "02";

    public static final String SYSTEM_CODE = "HQ_NCMS";


    public static final String TOWER_BUSINESS_TYPE = "twr_accountsummary";

    //图片查重返回码定义
    private static final BiMap<String,String> defineMap = HashBiMap.create(6);


    static {
        defineMap.put("10000","请求成功");
        defineMap.put("20000","参数错误");
        defineMap.put("30000","系统异常");
        defineMap.put("30001","图片格式有误");
        defineMap.put("30002","base64编码解码有误");
        defineMap.put("30003","请求方式错误");
    }

    public static String getReturnMsg(String code) {
        Set<String> returnCodes = defineMap.keySet();
        if (!returnCodes.contains(code)) {
            return "";
        }
        return defineMap.get(code);
    }

}
