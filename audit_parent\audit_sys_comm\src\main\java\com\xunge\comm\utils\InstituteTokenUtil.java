package com.xunge.comm.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xunge.core.util.PropertiesLoader;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;


/**
 * @author: LiangCheng
 * Date: 2023/1/4 8:53
 * Description: 研究院token工具类
 */
@Slf4j
public class InstituteTokenUtil {
    /**
     * 平台ID
     */
    public static final String PlatformId= "02";
    /**
     * 公司ID
     */
    public static final String CompanyId= "zygs-wlwgs";
    //
    /**
     * 用户ID
     */
    public static final String UserId= "pengdengyin2022";

    public static String instituteToken(Integer type){
        String accessToken="";
        try {
            String appid="";
            String secretKey="";
            PropertiesLoader prop = new PropertiesLoader("\\properties\\financeConfig.properties");
            String getTokenUrl = prop.getProperty("getTokenUrl");
            if (type==0){
                appid = prop.getProperty("AIPSAppid");
                secretKey = prop.getProperty("AIPSSecretKey");
            }else if (type==1){
                appid = prop.getProperty("imageSimilarAppid");
                secretKey = prop.getProperty("imageSimilarSecretKey");
            }else if (type==2){
                appid = prop.getProperty("OCRAppid");
                secretKey = prop.getProperty("OCRSecretKey");
            }else if (type==3){
                appid = prop.getProperty("predictionAppId");
                secretKey = prop.getProperty("predictionSecret");
            }
            //获取token
            if (StringUtils.isNotEmpty(getTokenUrl)
                    &&StringUtils.isNotEmpty(appid)&&StringUtils.isNotEmpty(secretKey)){
                InstituteTokenUtil demo = new InstituteTokenUtil();
                accessToken = demo.getAccessToken(getTokenUrl,appid, secretKey);
            }else{
                log.error("获取token参数不全：TokenUrl:"+getTokenUrl
                        +" appid:"+appid+" secretKey:"+secretKey);
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("获取token异常:",e);
        }
        return accessToken;
    }


    /**
     * 输入自己的appId跟秘钥，获取的安全密令字符串
     *
     * @return
     */
    public String getAccessToken(String address,String appId, String secret) {
        HttpUtil httpUtil = new HttpUtil();
        String url = address+"?appId="
                + appId + "&secret=" + secret;
        String accessToken = null;
        try {
            String responseContent = httpUtil.sendHttpRequest(url, "GET", null);
            //转化成json对象然后返回accessToken属性的值
            JSONObject demoJson = JSON.parseObject(responseContent);
            accessToken = demoJson.getJSONObject("body").getString("token");
            if (StringUtils.isEmpty(accessToken)){
                log.error("研究院token获取失败：返回内容："+demoJson);
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("获取token异常:",e);
        }
        return accessToken;
    }

    public String invokeDemo(String address,String acessToken, String methodType) {
        HttpUtil httpUtil = new HttpUtil();
        //此处为调用实际业务url，url后拼接accessToken即可
        String bizUrl = address+"?accessToken=" + acessToken;
        try {
            String httpResponse = httpUtil.sendHttpRequest(bizUrl, methodType, null);
            return httpResponse;
        } catch (Exception e) {
            e.printStackTrace();
            log.error("获取token异常:",e);
        }
        return null;
    }

    public static void main(String[] args) throws IOException {
        InstituteTokenUtil demo = new InstituteTokenUtil();
        String appId = "e0a915841fb5ff6d5bc3fc9c3105b744";
        String secret = "0b3217359409fc48e94ff19faa3028a6";
        String accessToken = demo.getAccessToken("http://**************:30001/api/v1/ability_sub/external/getToken",appId, secret);
        System.out.println("token is:" + accessToken);
        String responseContent = demo.invokeDemo("http://**************:30001/app-06y6rkkxo67k7n-store/api/v1/ab/demo",accessToken, "GET");
        System.out.println(responseContent);
        JSONObject demoJson = JSON.parseObject(responseContent);
        //token过期判断
        if (demoJson != null && "ERROR".equalsIgnoreCase(demoJson.getString("state")) && "-98402".equals(demoJson.get("resultCode"))) {
            //再次获取token
            accessToken = demo.getAccessToken("http://**************:30001/api/v1/ability_sub/external/getToken",appId, secret);
            //再次调用业务
            responseContent = demo.invokeDemo("http://**************:30001/app-06y6rkkxo67k7n-store/api/v1/ab/demo",accessToken, "GET");
            demoJson = JSON.parseObject(responseContent);

        }
        System.out.println(demoJson);
    }

}
