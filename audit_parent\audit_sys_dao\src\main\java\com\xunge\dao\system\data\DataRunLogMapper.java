package com.xunge.dao.system.data;

import com.xunge.model.system.data.DataRunLog;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * @Author: huang<PERSON><PERSON>
 * @Data:2024/4/9 9:22
 */
public interface DataRunLogMapper {
    /**
     * 按条件查询任务运行记录
     * @param map
     * @return
     */
    List<DataRunLog> queryDataRunLogList(Map<String, Object> map);
    DataRunLog queryDataRunLogById(int exeId);
}
