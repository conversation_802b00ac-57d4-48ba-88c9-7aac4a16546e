package com.xunge.model.basedata;

import java.util.Date;

/**
 * @ClassName:    DelApprovedDatAttachment
 * @Description:  ${description}
 * @Date:         2022/3/1
 * @Version:      1.0
 */
public class DelApprovedDatAttachment {
    /**
    * 附件编码
    */
    private String attachmentId;

    /**
    * 附件类型
    */
    private Integer attachmentType;

    /**
    * 附件名称
    */
    private String attachmentName;

    /**
    * 附件地址
    */
    private String attachmentUrl;

    /**
    * 附件说明
    */
    private String attachmentNote;

    /**
    * 业务类型
    */
    private String businessType;

    /**
    * 业务Id
    */
    private String businessId;

    private String prvId;

    /**
    * 付款id
    */
    private String paymentId;

    /**
    * 创建人
    */
    private String createUser;

    /**
    * 创建时间
    */
    private Date createTime;

    /**
    * 附件对应起租月份
    */
    private String yearmonth;

    private Integer id;

    /**
    * 文件md5值
    */
    private String md5Num;

    /**
    * 通过附件是否删除
    */
    private Integer state;

    /**
    * 更新附件的用户
    */
    private String updateUser;

    /**
    * 更新附件时间
    */
    private Date updateTime;

    private String businessIds;

    private boolean isPs;//判断图片是否被处理过

    private boolean isImage;//判断是否图片

    public String getAttachmentId() {
        return attachmentId;
    }

    public void setAttachmentId(String attachmentId) {
        this.attachmentId = attachmentId;
    }

    public Integer getAttachmentType() {
        return attachmentType;
    }

    public void setAttachmentType(Integer attachmentType) {
        this.attachmentType = attachmentType;
    }

    public String getAttachmentName() {
        return attachmentName;
    }

    public void setAttachmentName(String attachmentName) {
        this.attachmentName = attachmentName;
    }

    public String getAttachmentUrl() {
        return attachmentUrl;
    }

    public void setAttachmentUrl(String attachmentUrl) {
        this.attachmentUrl = attachmentUrl;
    }

    public String getAttachmentNote() {
        return attachmentNote;
    }

    public void setAttachmentNote(String attachmentNote) {
        this.attachmentNote = attachmentNote;
    }

    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    public String getBusinessId() {
        return businessId;
    }

    public void setBusinessId(String businessId) {
        this.businessId = businessId;
    }

    public String getPrvId() {
        return prvId;
    }

    public void setPrvId(String prvId) {
        this.prvId = prvId;
    }

    public String getPaymentId() {
        return paymentId;
    }

    public void setPaymentId(String paymentId) {
        this.paymentId = paymentId;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getYearmonth() {
        return yearmonth;
    }

    public void setYearmonth(String yearmonth) {
        this.yearmonth = yearmonth;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getMd5Num() {
        return md5Num;
    }

    public void setMd5Num(String md5Num) {
        this.md5Num = md5Num;
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getBusinessIds() {
        return businessIds;
    }

    public void setBusinessIds(String businessIds) {
        this.businessIds = businessIds;
    }

    public boolean isPs() {
        return isPs;
    }

    public void setPs(boolean ps) {
        isPs = ps;
    }

    public boolean isImage() {
        return isImage;
    }

    public void setImage(boolean image) {
        isImage = image;
    }
}
