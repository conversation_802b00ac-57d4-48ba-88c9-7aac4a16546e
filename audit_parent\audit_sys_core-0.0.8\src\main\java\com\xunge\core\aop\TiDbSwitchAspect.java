package com.xunge.core.aop;

import com.xunge.core.Interceptor.TenantContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.After;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;

import java.util.concurrent.atomic.AtomicBoolean;

/**
 * @ClassName TiDbSwitchAspect
 **/

@Aspect
@Component
@Slf4j
public class TiDbSwitchAspect {
    private final AtomicBoolean active = new AtomicBoolean(false);

    @Pointcut("@annotation(com.xunge.core.annotaion.tidb.TiDbSwitch)")
    public void tiDbSwitchCut() {
    }

    @Before("tiDbSwitchCut()")
    public void addTiDbFlag(JoinPoint joinPoint) {
        String tiDbCode = "";
        String tiDbFlag = "_TI";
        String prvCode = TenantContextHolder.getTenant();
        log.info("获取当前省标识：{}", prvCode);
        if (StringUtils.isNotBlank(prvCode) && !prvCode.contains("_TI")) {
            tiDbCode = prvCode.concat(tiDbFlag);
            active.set(true);
        }
        log.info("设置TiDB库省标识：{}", tiDbCode);
        TenantContextHolder.remove();
        TenantContextHolder.setTenant(tiDbCode);

    }

    @After("tiDbSwitchCut()")
    public void rmTiDbFlag(JoinPoint joinPoint) {
        String prvCode = "";
        String tiDbCode = TenantContextHolder.getTenant();
        log.info("获取当前TiDB库省标识：{}", tiDbCode);
        if (StringUtils.isNotBlank(tiDbCode) && active.get()) {
            prvCode = tiDbCode.split("_")[0];
        }
        log.info("设置省标识：{}", prvCode);
        TenantContextHolder.remove();
        TenantContextHolder.setTenant(prvCode);
    }
}
