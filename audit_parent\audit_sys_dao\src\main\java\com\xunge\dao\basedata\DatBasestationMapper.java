package com.xunge.dao.basedata;

import com.xunge.model.basedata.DatBasestation;
import com.xunge.model.basedata.DatBasestationExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface DatBasestationMapper {
    
    int countByExample(DatBasestationExample example);

    
    int deleteByExample(DatBasestationExample example);

    
    int deleteByPrimaryKey(String basestationId);

    
    int insert(DatBasestation record);

    
    int insertSelective(DatBasestation record);

    
    List<DatBasestation> selectByExample(DatBasestationExample example);

    
    DatBasestation selectByPrimaryKey(String basestationId);

    
    int updateByExampleSelective(@Param("record") DatBasestation record, @Param("example") DatBasestationExample example);

    
    int updateByExample(@Param("record") DatBasestation record, @Param("example") DatBasestationExample example);

    
    int updateByPrimaryKeySelective(DatBasestation record);

    
    int updateByPrimaryKey(DatBasestation record);
}