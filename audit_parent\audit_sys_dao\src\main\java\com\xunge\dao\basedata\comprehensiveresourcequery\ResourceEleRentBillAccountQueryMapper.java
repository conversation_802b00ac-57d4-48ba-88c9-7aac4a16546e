package com.xunge.dao.basedata.comprehensiveresourcequery;

import com.github.pagehelper.Page;
import com.xunge.dto.selfelec.AuthorityUser;
import com.xunge.model.basedata.BillaccountInfo;
import com.xunge.model.basedata.ResourceEleRentBillAccount;
import com.xunge.model.basedata.ResourceEleRentBillAccountEasyExcel;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 查询综资与租电报账点
 */
public interface ResourceEleRentBillAccountQueryMapper {

    List<ResourceEleRentBillAccountEasyExcel> queryResourceEleRentBillAccount(@Param("baseResourceIds") List<String> baseResourceIds);

    Page<String> queryResourceEleRentBillAccountV1(@Param("user") AuthorityUser user, @Param("regId") String regId, @Param("pregId") String pregId
            , @Param("stationOrSite") String stationOrSite, @Param("billAccountCodeOrName") String billAccountCodeOrName, @Param("supplierCodeOrName") String supplierCodeOrName) ;


    BillaccountInfo queryRentBillAccountByRes(String baseResourceId);

    List<BillaccountInfo> queryEleBillAccountByRes(String baseResourceId);

    //关联合同查报账点合同等信息
    BillaccountInfo queryTeleBillAccountByRes(String baseResourceId);

    //关联固话查报账点合同等信息
    BillaccountInfo queryTeleBillAccountByResCuring(String baseResourceId);

    ResourceEleRentBillAccountEasyExcel queryResourceById(@Param("resId") String resId);
}
