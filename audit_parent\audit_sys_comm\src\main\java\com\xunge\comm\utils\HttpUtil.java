package com.xunge.comm.utils;

import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.*;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;

import java.io.IOException;

/**
 * @version: 1.0
 * @author: liyongchao
 * @date: 2021/9/8 15:12
 * @desc:
 */
public class HttpUtil {


    private static final int DEFAULT_TIME_OUT = 6000;
    CloseableHttpClient client = HttpClientBuilder.create().build();

    public String sendHttpRequest(String url, String methodType, String content) {

        System.out.println("request url is:" + url + "\nmethodType:" + methodType
                + "\ncontent:" + content);
        HttpRequestBase method = null;
        CloseableHttpResponse httpResponse = null;

        try {
            int timeout = DEFAULT_TIME_OUT;
            RequestConfig requestConfig = RequestConfig.custom().setSocketTimeout(timeout).setConnectTimeout(timeout)
                    .setConnectionRequestTimeout(timeout).build();
            if ("POST".equalsIgnoreCase(methodType)) {
                HttpPost httpPost = new HttpPost(url);
                httpPost.setConfig(requestConfig);
                httpPost.setEntity(new StringEntity(content, ContentType.APPLICATION_JSON));
                method = httpPost;
            } else if ("PUT".equalsIgnoreCase(methodType)) {
                HttpPut httpPut = new HttpPut(url);
                httpPut.setConfig(requestConfig);
                httpPut.setEntity(new StringEntity(content, ContentType.APPLICATION_JSON));
                method = httpPut;
            } else if ("GET".equalsIgnoreCase(methodType)) {
                HttpGet httpGet = new HttpGet(url);
                httpGet.setConfig(requestConfig);
                method = httpGet;
            } else if ("DELETE".equalsIgnoreCase(methodType)) {
                HttpDelete httpDelete = new HttpDelete(url);
                httpDelete.setConfig(requestConfig);
                method = httpDelete;
            }

            httpResponse = client.execute(method);
            String responseContent = EntityUtils.toString(httpResponse.getEntity(), "UTF-8");
            return responseContent;
        } catch (Exception e) {
            e.printStackTrace();

        } finally {
            if(client!=null){
                try {
                    ((CloseableHttpClient) client).close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            if(httpResponse!=null){
                try {
                    httpResponse.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return null;
    }

}
