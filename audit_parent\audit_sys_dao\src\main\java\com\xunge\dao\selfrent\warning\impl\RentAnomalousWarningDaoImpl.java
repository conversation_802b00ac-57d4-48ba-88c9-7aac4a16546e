package com.xunge.dao.selfrent.warning.impl;

import com.xunge.core.page.Page;
import com.xunge.dao.AbstractBaseDao;
import com.xunge.dao.selfrent.warning.RentAnomalousWarningDao;
import com.xunge.filter.PageInterceptor;
import com.xunge.model.basedata.DatAttachment;
import com.xunge.model.basedata.EleAnomalousWarningVo;
import com.xunge.model.contract.DatContractVO;
import com.xunge.model.selfrent.billAccount.RentPaymentVO;
import com.xunge.model.selfrent.billAccount.VPaymentVO;
import com.xunge.model.selfrent.warning.RentAnomalousResourceVO;
import com.xunge.model.selfrent.warning.RentAnomalousWarningDetailVo;
import com.xunge.model.selfrent.warning.RentAnomalousWarningRecoveryExport;
import com.xunge.model.selfrent.warning.RentAnomalousWarningVO;
import com.xunge.model.system.region.RegionVO;

import java.util.List;
import java.util.Map;

/**
 * @创建人 LiangCheng
 * @创建时间 2019/10/8 0008
 * @描述：
 */
public class RentAnomalousWarningDaoImpl extends AbstractBaseDao implements RentAnomalousWarningDao {

    final String Namespace = "com.xunge.mapping.RentAnomalousWarningVOMapper.";

    @Override
    public Page<RentAnomalousWarningVO> queryRentAnomalousWarningList(Map<String, Object> map, int pageNumber, int pageSize) {
        PageInterceptor.startPage(pageNumber, pageSize);
        this.getSqlSession().selectList(Namespace + "queryRentAnomalousWarningList", map);
        return PageInterceptor.endPage();
    }

    @Override
    public Page<RentAnomalousWarningVO> queryRentAnomalousWarningList2(Map<String, Object> map, int pageNumber, int pageSize) {
        PageInterceptor.startPage(pageNumber, pageSize);
        this.getSqlSession().selectList(Namespace + "queryRentAnomalousWarningList2", map);
        return PageInterceptor.endPage();
    }

    @Override
    public VPaymentVO queryPayment(Map<String, Object> map) {
        return this.getSqlSession().selectOne(Namespace + "queryPayment", map);
    }

    @Override
    public void saveRevocery(RentAnomalousWarningVO rentAnomalousWarningVO) {
        this.getSqlSession().update(Namespace + "saveRevocery", rentAnomalousWarningVO);
    }

    @Override
    public RentAnomalousWarningVO queryRentAnomalousWarning(RentAnomalousWarningVO rentAnomalousWarningVO) {
        return this.getSqlSession().selectOne(Namespace + "queryRentAnomalousWarning", rentAnomalousWarningVO);
    }

    @Override
    public RentAnomalousWarningVO queryRentAnomalousWarning1(Map<String, Object> map) {
        return this.getSqlSession().selectOne(Namespace + "queryRentAnomalousWarning1", map);
    }

    @Override
    public void submitAudit(RentAnomalousWarningVO rentAnomalousWarningVO) {
        this.getSqlSession().update(Namespace + "submitAudit", rentAnomalousWarningVO);
    }

    @Override
    public void editRevocery(RentAnomalousWarningVO rentAnomalousWarningVO) {
        this.getSqlSession().update(Namespace + "editRevocery", rentAnomalousWarningVO);
    }

    @Override
    public Page<RentAnomalousWarningVO> queryRentAnomalousWarningCheckList(Map<String, Object> map, int pageNumber, int pageSize) {
        PageInterceptor.startPage(pageNumber, pageSize);
        this.getSqlSession().selectList(Namespace + "queryRentAnomalousWarningCheckList", map);
        return PageInterceptor.endPage();
    }

    @Override
    public int rentAnomalousWarningSubmitAudit(Map<String, Object> map) {
        return this.getSqlSession().update(Namespace + "rentAnomalousWarningSubmitAudit", map);
    }

    @Override
    public Page<RentAnomalousWarningVO> queryRentAnomalousWarningListGroup(Map<String, Object> map, int pageNumber, int pageSize) {
        PageInterceptor.startPage(pageNumber, pageSize);
        this.getSqlSession().selectList(Namespace + "queryRentAnomalousWarningListGroup", map);
        return PageInterceptor.endPage();
    }

    @Override
    public List<EleAnomalousWarningVo> selectRentAnomalousWarning(Map<String, Object> map) {
        return getSqlSession().selectList(Namespace + "selectRentAnomalousWarning", map);
    }

    @Override
    public List<EleAnomalousWarningVo> selectRentAnomalousWarningToDo(Map<String, Object> map) {
        return getSqlSession().selectList(Namespace + "selectRentAnomalousWarningToDo", map);
    }

    @Override
    public Page<RentAnomalousWarningVO> queryRentRecoveryRecordListGroup(Map<String, Object> map, int pageNumber, int pageSize) {
        PageInterceptor.startPage(pageNumber, pageSize);
        this.getSqlSession().selectList(Namespace + "queryRentRecoveryRecordListGroup", map);
        return PageInterceptor.endPage();
    }

    @Override
    public List<RegionVO> queryPrv(Map<String, Object> map) {
        return getSqlSession().selectList(Namespace + "queryPrv", map);
    }

    @Override
    public List<RegionVO> queryPreg(Map<String, Object> map) {
        return getSqlSession().selectList(Namespace + "queryPreg", map);
    }

    @Override
    public List<RegionVO> queryReg(Map<String, Object> map) {
        return getSqlSession().selectList(Namespace + "queryReg", map);
    }

    @Override
    public int delWarningRecord(RentAnomalousWarningVO rentAnomalousWarningVO) {
        return getSqlSession().update(Namespace + "delWarningRecord", rentAnomalousWarningVO);
    }

    @Override
    public int delWarningTime(Map<String, String> delMap) {
        return getSqlSession().delete(Namespace + "delWarningTime", delMap);
    }

    @Override
    public List<DatAttachment> queryDatAttachmentByParam(DatAttachment record) {
        return getSqlSession().selectList(Namespace + "queryDatAttachmentByParam", record);
    }

    @Override
    public int deleteFile(DatAttachment datAttachment) {
        return getSqlSession().delete(Namespace + "deleteFile", datAttachment);
    }

    @Override
    public List<DatContractVO> queryDatContract(Map<String, Object> map) {
        return this.getSqlSession().selectList(Namespace + "queryDatContract", map);
    }

    @Override
    public void updateRentAnomalousWarningById(RentAnomalousWarningVO warningVO) {
        this.getSqlSession().update(Namespace + "updateRentAnomalousWarningById", warningVO);
    }

    @Override
    public List<RentAnomalousWarningVO> queryRentAnomalousHangUpRecordList(Map<String, Object> map) {
        return this.getSqlSession().selectList(Namespace + "queryRentAnomalousHangUpRecordList", map);
    }

    @Override
    public List<RentAnomalousWarningVO> queryRentAnomalousWarningAll() {
        return this.getSqlSession().selectList(Namespace + "queryRentAnomalousWarningAll");
    }

    @Override
    public void delRentAnomalousWarningAll(String prvId) {
        this.getSqlSession().delete(Namespace + "delRentAnomalousWarningAll", prvId);
    }

    @Override
    public void insertRentAnomalousWarning(List prvdatas) {
        this.getSqlSession().insert(Namespace + "insertRentAnomalousWarning", prvdatas);
    }

    @Override
    public List<RentAnomalousWarningDetailVo> queryRentAnomalousWarningDetailList(Map<String, Object> map) {
        return this.getSqlSession().selectList(Namespace + "queryRentAnomalousWarningDetailList", map);
    }

    @Override
    public RentAnomalousWarningVO queryRentAnomalousWarningByStatus(RentAnomalousWarningVO nomalous) {
        return this.getSqlSession().selectOne(Namespace + "queryRentAnomalousWarningByStatus", nomalous);
    }

    @Override
    public RentPaymentVO queryRentPaymentByPaymentId(String paymentId) {
        return this.getSqlSession().selectOne(Namespace + "queryRentPaymentByPaymentId", paymentId);
    }

    @Override
    public RentPaymentVO queryRentPaymentByBillAccountCode(String billaccountCode) {
        return this.getSqlSession().selectOne(Namespace + "queryRentPaymentByBillAccountCode", billaccountCode);
    }
}
