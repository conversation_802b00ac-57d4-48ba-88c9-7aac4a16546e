package com.xunge.dao.system.data;

import com.xunge.model.system.data.dataExtractionVO;
import com.xunge.model.system.province.SysProvinceVO;
import com.xunge.model.system.user.SysUserVO;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;
import java.util.Set;

public interface DataExtractionMapper {
    /**
     * 按名称、编码或者省份查询数据集列表
     * @param map
     * @return
     */

    List<dataExtractionVO> queryData (Map<String,Object> map);

    /**
     * 按名称、编码或者省份查询数据集列表
     * @param map
     * @return
     */
   int addData(Map<String,Object> map);

    /**
     * 按名称、编码或者省份查询数据集列表
     * @param map
     * @return
     */
    int updateData(Map<String,Object> map);

    /**
     * 改变任务状态启动还是停止
     * @param map
     * @return
     */
    int changeState(Map<String,Object> map);

    /**
     * 查询DataCollect表中的data名称和id
     * @param
     * @return
     */
    List<dataExtractionVO> queryDataCollect();

    /**
     * 查询datajob表中的任务描述和cron表达式
     * @param map
     * @return
     */
    List<dataExtractionVO> queryDataJobById(Map<String,Object> map);

    List<SysProvinceVO> queryAllProvinceList();

    String queryPrvId(String s);

    String queryPrvCode(String s);
//    查询省测联系人
    @MapKey("userId")
Map<String, Map<String,Object>> queryRoles(Map<String, Object> params);
//    查询角色名称
@MapKey("userId")
    Map<String,Map<String,String>> queryUsers(@Param("allUserIds")Set<String> userIds);
}
