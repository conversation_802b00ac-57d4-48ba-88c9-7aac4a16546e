package com.xunge.model.budget.twr;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2022/7/28
 * @description 本年1-9月存量订单在新年度的费用
 */
@Data
public class BudgetTwrStockOrderFeeVO {

    /**
     * 省份ID
     */
    private String prvId;

    /**
     * 省份名称
     */
    private String prvName;
    /**
     * 年份
     */
    private Integer onYear;
    /**
     * 业务类型({塔类,1}, {室分,2}, {微站,3}, {传输,4}, {非标,5}, {合计,6})
     */
    private Integer productType;

    /**
     * 最新账期累计摊销值
     */
    private BigDecimal stockOrderAmoritzeFee;

    /**
     * 最新账期计提金额
     */
    private BigDecimal stockOrderAccrualFee;
    /**
     * 剩余月份应付费用
     */
    private BigDecimal stockOrderCopeFee;

    /**
     * 本年账期年份1月-账期月份存量订单在新年度的费用
     */
    private BigDecimal stockOrderNewFee;

    /**
     * 本年账期年份1月-账期月份存量订单在新年度的费用-预算金额
     */
    private BigDecimal stockOrderBudgetFee;

    /**
     * 本年账期年份1月-账期月份存量订单在新年度的费用-核减金额
     */
    private BigDecimal stockOrderSubtractFee;

    /**
     * 调整金额
     */
    private BigDecimal stockOrderAdjustFee;
    private BigDecimal stockOrderAdjustFeeAfter;

    /**
     * 备注
     */
    private String stockOrderRemark;


}
