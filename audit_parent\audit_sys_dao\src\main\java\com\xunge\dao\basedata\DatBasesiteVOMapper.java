package com.xunge.dao.basedata;

import com.xunge.model.basedata.DatBasesiteVO;
import com.xunge.model.basedata.DatBasesiteVOExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface DatBasesiteVOMapper {
    int countByExample(DatBasesiteVOExample example);

    int deleteByExample(DatBasesiteVOExample example);

    int deleteByPrimaryKey(Map<String, Object> map);

    int insert(DatBasesiteVO record);

    int insertSelective(DatBasesiteVO record);

    List<DatBasesiteVO> selectByExample(DatBasesiteVOExample example);

    DatBasesiteVO selectByPrimaryKey(Map<String, Object> map);

    int updateByExampleSelective(@Param("record") DatBasesiteVO record, @Param("example") DatBasesiteVOExample example);

    int updateByExample(@Param("record") DatBasesiteVO record, @Param("example") DatBasesiteVOExample example);

    int updateByPrimaryKeySelective(DatBasesiteVO record);

    int updateByPrimaryKey(DatBasesiteVO record);

    boolean batchInsert(List<DatBasesiteVO> datas);

    boolean delByCuidsAndPrvid(Map<String, Object> map);

    /**
     * 查询站点信息审核页面
     *
     * @param hashMaps
     * @return
     * <AUTHOR>
     */
    public List<DatBasesiteVO> querySiteInfo(Map<String, Object> hashMaps);

    /**
     * 编码校验
     *
     * @param map
     * @return
     * <AUTHOR>
     */
    public List<DatBasesiteVO> checkByCode(Map<String, Object> map);

    public int setRecordDeptId(Map<String, String> map);

    public String selectMaxSiteCuid(String param);

    DatBasesiteVO queryBeanById(Map<String, Object> map);
}