package com.xunge.dto.selfelec.verification;

import com.xunge.dto.selfelec.PageInfo;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.List;

/**
 * 报账点电费查询条件
 *
 * <AUTHOR>
 * @date 2021/11/9 10:13
 */
@Getter
@Setter
@ToString
public class VerificationQueryDto extends PageInfo implements Serializable {
    private static final long serialVersionUID = 1093604960800399564L;
    String billaccountCodeOrName;

    String verificationCode;
    String contractCodeOrName;
    String pregId;
    String regId;
    String auditingState;
    String startOpen;
    String startClose;

    String endOpen;
    String endClose;

    Integer supplyMethod;

    Integer submitState;
    String userCodeOrName;
    String billamountDateOpen;
    String overLoss;
    String billamountDateClose;

    String financeDateOpen;

    String financeDateClose;

    String isFinance;

    String payType;
    String newFlag;
    String billamountState;

    String dateSort;
    String searchKeywords;
    String overFlowType;

    String overProof;

    String billaccountCode;

    String billaccountName;
    String stayAuditingUser;

    String imageaiResult;

    String auditingUserId;

    String sourcePage;

    /**
     * 0：普通报账点 1：特殊报账点 ，默认为0
     */
    Integer isSpecial = 0;
    /**
     * 导出选中缴费单id
     */
    List<String> billaccountpaymentdetailIds;

//    String lowerAuditor; 废弃

    // 普服资源
    Integer ifTeleCmnServ;
    // 5G标识
    Integer fivegFlag;

    String meterCode;

    Integer ratioParam;

    Integer overflow;

    /**
     * 当前审核节点名称
     */
    private String auditNodeName;

    private String isAttachmentFlag;

    private String baseresourceCode;

    public boolean isDefaultQuery() {
        return
                 StringUtils.isBlank(billaccountCodeOrName) && StringUtils.isBlank(verificationCode) && StringUtils.isBlank(contractCodeOrName)
                && StringUtils.isBlank(pregId) && StringUtils.isBlank(regId) && StringUtils.isBlank(auditingState)
                && StringUtils.isBlank(startOpen) && StringUtils.isBlank(overLoss)
                && StringUtils.isBlank(startClose) && StringUtils.isBlank(endOpen)
                && StringUtils.isBlank(endClose) && null == supplyMethod
                && null == submitState && StringUtils.isBlank(payType)
                && StringUtils.isBlank(userCodeOrName)&& overflow!=null
                && StringUtils.isBlank(billamountDateOpen) && StringUtils.isBlank(newFlag)
                && StringUtils.isBlank(billamountState) && StringUtils.isBlank(searchKeywords)
                && StringUtils.isBlank(overFlowType) && StringUtils.isBlank(billamountDateClose)
                && StringUtils.isBlank(meterCode) && StringUtils.isBlank(baseresourceCode)
                && StringUtils.isBlank(stayAuditingUser);
    }
}
