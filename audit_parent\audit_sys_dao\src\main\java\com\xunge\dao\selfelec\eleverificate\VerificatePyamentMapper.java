package com.xunge.dao.selfelec.eleverificate;

import com.xunge.model.selfelec.eleverificate.VEleBillaccountVerificateInfo;
import com.xunge.model.selfelec.eleverificate.VerificationPayment;
import com.xunge.model.selfelec.vo.CommonMeterDataVo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;
import java.util.Map;

public interface VerificatePyamentMapper {

    String querypaymentId = null;

    public void insertSelective(VerificationPayment verificatePyament);

    public List<String> queryList(@Param("eleVerificateList") List<VEleBillaccountVerificateInfo> eleVerificateList);

    public void deleteList(Map<String, Object> map);

    public List<String> querypaymentId(@Param("verificationId") String verificationId);

    public void deleteById(@Param("paymentId") String paymentId);

    /**
     * 最近一个报账点缴费电表数据
     *
     * @param vo
     * @return 结果
     */
    List<CommonMeterDataVo> getLastOneMeterData(CommonMeterDataVo vo);

    /**
     * 最近一个报账点缴费电表数据
     *
     * @param vo
     * @return 结果
     */
    List<CommonMeterDataVo> getIcLastOneMeterData(CommonMeterDataVo vo);

    @Select("select meter_id from ele_ic_verificationdetail where verification_id=#{billaccountpaymentdetailId}")
    List<String> getIcMeterIds(String billaccountpaymentdetailId);

    @Select("select meter_id from ele_verificationdetail where verification_id=#{billaccountpaymentdetailId}")
    List<String> getMeterIds(String billaccountpaymentdetailId);

    @Update("update ele_verification set meter_change=#{meterChange} where verification_code=#{paymentCode}")
    void updateMeterChange(@Param("paymentCode") String paymentCode, @Param("meterChange") String meterChange);
}