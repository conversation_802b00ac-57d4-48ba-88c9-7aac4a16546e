package com.xunge.dao.statistics;

import com.xunge.model.statistics.PreferentialPolicies;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface PreferentialPoliciesMapper {

    List<PreferentialPolicies> queryList(Map<String, Object> param);

    PreferentialPolicies queryDetail(@Param("prvId") String prvId, @Param("fileNum") String fileNum);

    Integer insert(PreferentialPolicies policy);

    Integer update(PreferentialPolicies policy);

    Integer delPolicy(Map<String, Object> map);

    void insertHqData(List<PreferentialPolicies> tmp);

    List<PreferentialPolicies> reportMonth(@Param("reportMonth") String reportMonth, @Param("prvId") String prvId);

}
