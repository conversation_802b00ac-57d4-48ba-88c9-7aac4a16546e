package com.xunge.dao.towerrent.nonstand.impl;

import com.xunge.core.page.Page;
import com.xunge.dao.AbstractBaseDao;
import com.xunge.dao.towerrent.nonstand.INonstandFeeDao;
import com.xunge.filter.PageInterceptor;
import com.xunge.model.towerrent.settlement.*;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service("iNonstandFeeDao")
public class NonstandFeeDaoImpl extends AbstractBaseDao implements INonstandFeeDao {

    final String NonstandFee = "com.xunge.mapping.TowerSideBillNonstandVOMapper.";
    final String OtherFeeNonstand = "com.xunge.mapping.OtherFeeNonstandVOMapper.";

    @Override
    public List<TowerBillBalanceNonstandVO> queryNonstandBill(Map<String, Object> paramMap) {
        return this.getSqlSession().selectList(NonstandFee + "queryNonstandBill", paramMap);
    }

    @Override
    public int updateNonstandFeeSumcodeToNull(Map<String, Object> paramMap) {
        return this.getSqlSession().update(NonstandFee + "updateNonstandFeeSumcodeToNull", paramMap);
    }

    @Override
    public int updateRoomNonstandFeeSumcodeToNull(Map<String, Object> paramMap) {
        return this.getSqlSession().update(NonstandFee + "updateRoomNonstandFeeSumcodeToNull", paramMap);
    }

    @Override
    public int updateNonstandBillSetSumcode(TowerBillBalanceNonstandVO nonstandVO) {
        return this.getSqlSession().update(NonstandFee + "updateNonstandBillSetSumcode", nonstandVO);
    }

    @Override
    public List<TowerAndMobileBillNonstandConfirmVO> queryAccountedNonstandBill(Map<String, Object> paramMap) {
        return this.getSqlSession().selectList(NonstandFee + "queryAccountedNonstandBill", paramMap);
    }

    @Override
    public Page<List<TowerAndMobileBillNonstandConfirmVO>> queryAccountedNonstandBillByPage(Map<String, Object> paraMap, int pageNumber, int pageSize) {
        PageInterceptor.startPage(pageNumber, pageSize);
        this.getSqlSession().selectList(NonstandFee + "queryAccountedNonstandBill", paraMap);
        return PageInterceptor.endPage();
    }

    @Override
    public Page<List<TowerAndMobileBillRoomNonstandConfirmVO>> queryAccountedRoomNonstandBillByPage(Map<String, Object> paraMap, int pageNumber, int pageSize){
        PageInterceptor.startPage(pageNumber, pageSize);
        this.getSqlSession().selectList(NonstandFee + "queryAccountedRoomNonstandBill", paraMap);
        return PageInterceptor.endPage();
    }

    @Override
    public List<TowerAndMobileBillRoomNonstandConfirmVO> queryAccountedRoomNonstandBill(Map<String, Object> paraMap){
        return this.getSqlSession().selectList(NonstandFee + "queryAccountedRoomNonstandBill", paraMap);
    }
    @Override
    public List<OtherFeeNonstandVO> queryAccountedOtherFeeNonstand(Map<String, Object> paramMap) {
        return this.getSqlSession().selectList(OtherFeeNonstand + "queryAccountedOtherFeeNonstand", paramMap);
    }

    @Override
    public Page<List<OtherFeeNonstandVO>> queryAccountedOtherFeeNonstandByPage(Map<String, Object> paraMap, int pageNumber, int pageSize) {
        PageInterceptor.startPage(pageNumber, pageSize);
        this.getSqlSession().selectList(OtherFeeNonstand + "queryAccountedOtherFeeNonstand", paraMap);
        return PageInterceptor.endPage();
    }

    @Override
    public int queryRoomNonstandCtlCount(String yearmonth) {
        return this.getSqlSession().selectOne(NonstandFee + "queryRoomNonstandCtlCount", yearmonth);
    }

    @Override
    public List<TowerBillBalanceRoomNonstandVO> queryRoomNonstandBill(Map<String, Object> paramMap){
        return this.getSqlSession().selectList(NonstandFee + "queryRoomNonstandBill", paramMap);
    }

    @Override
    public int updateRoomNonstandBillSetSumcode(TowerBillBalanceRoomNonstandVO vo){
        return this.getSqlSession().update(NonstandFee + "updateRoomNonstandBillSetSumcode", vo);
    }
}
