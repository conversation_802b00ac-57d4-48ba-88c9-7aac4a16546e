package com.xunge.dao.report;

import com.xunge.model.report.RptRentBaseSiteInfoVO;
import com.xunge.model.report.RptRentSiteInfoNewVO;

import java.util.List;
import java.util.Map;

/**
 * @创建人 LiangCheng
 * @创建时间 2019/11/29 0029
 * @描述：
 */
public interface IRptRentBaseSiteInfoDao {

    List<RptRentSiteInfoNewVO> queryRptRentBaseSiteInfoByPrvId(Map<String, Object> param);

    List<RptRentSiteInfoNewVO> queryRptRentBaseSiteInfoByPregId(Map<String, Object> param);

    List<RptRentSiteInfoNewVO> queryRptRentBaseSiteInfoByRegId(Map<String, Object> param);

    List<RptRentSiteInfoNewVO> queryRptRentBaseSitePayableInfoByPrvId(Map<String, Object> param);

    List<RptRentSiteInfoNewVO> queryRptRentBaseSitePayableInfoByPregId(Map<String, Object> param);

    List<RptRentSiteInfoNewVO> queryRptRentBaseSitePayableInfoByRegId(Map<String, Object> param);

    List<RptRentBaseSiteInfoVO> queryRptRentBaseSiteInfoByPrvIdCount(Map<String, Object> param);

    List<RptRentBaseSiteInfoVO> queryRptRentBaseSiteInfoByPregIdCount(Map<String, Object> param);

    List<RptRentBaseSiteInfoVO> queryRptRentBaseSiteInfoByRegIdCount(Map<String, Object> param);

    List<RptRentBaseSiteInfoVO> queryRptRentBaseSitePayableInfoByPrvIdCount(Map<String, Object> param);

    List<RptRentBaseSiteInfoVO> queryRptRentBaseSitePayableInfoByPregIdCount(Map<String, Object> param);

    List<RptRentBaseSiteInfoVO> queryRptRentBaseSitePayableInfoByRegIdCount(Map<String, Object> param);

}
