package com.xunge.dao.budget.ele;

import com.xunge.model.budget.ele.BudgetEleCountVO;
import com.xunge.model.budget.ele.BudgetEleParamVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @author: LiangCheng
 * Date: 2022/7/5 16:54
 * Description: 预算总表
 */
public interface BudgetEleCountMapper {

    /**
     * 保存预算总表
     * @param eleCountList 预算总表集合
     * @param nodeType 节点
     */
    void saveEleCount(BudgetEleParamVO budgetEleParamVO);

    List<BudgetEleCountVO> getEleCount(BudgetEleParamVO budgetEleParamVO);

    void editEleCount(BudgetEleCountVO budgetEleCountVO);

    void delBudgetEleData(BudgetEleParamVO budgetEleParamVO);


}
