package com.xunge.dao.report;

import com.xunge.model.report.RptEleBillaccountVO;
import com.xunge.model.report.RptEleBillamountVO;
import com.xunge.model.report.RptElePaymentVO;
import com.xunge.model.selfelec.RptPrvElePaymentRedundancyMon;

import java.util.List;
import java.util.Map;

public interface IRptSelfelecDao {

    public List<RptEleBillaccountVO> queryRptEleBillaccount(Map<String, Object> paraMap);

    /**
     * 根据省份编号查询电费报账点统计数据
     *
     * @param paraMap
     * @return
     * <AUTHOR>
     */
    public List<RptEleBillaccountVO> queryRptEleBillaccountByPrvId(Map<String, Object> paraMap);

    /**
     * 根据地市编号查询电费报账点统计数据
     *
     * @param paraMap
     * @return
     * <AUTHOR>
     */
    public List<RptEleBillaccountVO> queryRptEleBillaccountByPregId(Map<String, Object> paraMap);

    public List<RptEleBillamountVO> queryRptEleBillamount(Map<String, Object> paraMap);

    /**
     * 根据省份编号查询电费报账数据
     *
     * @param paraMap
     * @return
     * <AUTHOR>
     */
    public List<RptEleBillamountVO> queryRptEleBillamountByPrvId(Map<String, Object> paraMap);

    /**
     * 根据地市编号查询电费报账数据
     *
     * @param paraMap
     * @return
     * <AUTHOR>
     */
    public List<RptEleBillamountVO> queryRptEleBillamountByPregId(Map<String, Object> paraMap);

    public List<RptElePaymentVO> queryRptElePayment(Map<String, Object> paraMap);

    /**
     * 根据省份编号查询电费缴费稽核数据
     *
     * @param paraMap
     * @return
     * <AUTHOR>
     */
    public List<RptElePaymentVO> queryRptElePaymentByPrvId(Map<String, Object> paraMap);

    /**
     * 根据地市编号查询电费缴费稽核数据
     *
     * @param paraMap
     * @return
     * <AUTHOR>
     */
    public List<RptElePaymentVO> queryRptElePaymentByPregId(Map<String, Object> paraMap);

    public List<RptPrvElePaymentRedundancyMon> queryRedundancyById(Map<String, Object> paraMap);

}