package com.xunge.dao.basedata;

import com.xunge.model.basedata.DatBaseresource;
import com.xunge.model.basedata.DatBaseresourceExample;
import com.xunge.model.basedata.colletion.SiteTypeMapping;

import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface DatBaseresourceMapper {
    
    int countByExample(DatBaseresourceExample example);

    
    int deleteByExample(DatBaseresourceExample example);

    
    int deleteByPrimaryKey(String baseresourceId);

    
    int insert(DatBaseresource record);

    
    int insertSelective(DatBaseresource record);

    
    List<DatBaseresource> selectByExample(DatBaseresourceExample example);

    
    DatBaseresource selectByPrimaryKey(String baseresourceId);

    
    int updateByExampleSelective(@Param("record") DatBaseresource record, @Param("example") DatBaseresourceExample example);

    
    int updateByExample(@Param("record") DatBaseresource record, @Param("example") DatBaseresourceExample example);

    
    int updateByPrimaryKeySelective(DatBaseresource record);

    
    int updateByPrimaryKey(DatBaseresource record);


	List<SiteTypeMapping> getAllCompatibleSiteType(@Param("resourceType") int resourceType);
}