package com.xunge.dao.report;

import com.xunge.model.report.RptMeterLedgerVO;

import java.util.List;

public interface IrptMeterLedgerDao {

    /**
     * 查询计量电表信息
     *
     * @param rptMeterLedgerVO
     * @return
     */
    List<RptMeterLedgerVO> queryMeterLedger(RptMeterLedgerVO rptMeterLedgerVO);

    /**
     * queryMeterLedgerNew
     *
     * @param rptMeterLedgerVO
     * @return List<RptMeterLedgerVO> 返回类型
     * @description << 查询计量电表信息,通过存储过程汇总的数据 >>
     * <AUTHOR>
     * @version V1.0
     * @date 2018年8月1日
     * @email <EMAIL>
     */
    List<RptMeterLedgerVO> queryMeterLedgerNew(RptMeterLedgerVO rptMeterLedgerVO);

    /**
     * 查询计量电表信息总数
     *
     * @param rptMeterLedgerVO
     * @return
     */
    int queryMeterLedgerCount(RptMeterLedgerVO rptMeterLedgerVO);

    /**
     * queryMeterLedgerCountNew
     *
     * @param rptMeterLedgerVO
     * @return int 返回类型
     * @description << 查询计量电表信息,通过存储过程汇总的数据 >>
     * <AUTHOR>
     * @version V1.0
     * @date 2018年8月1日
     * @email <EMAIL>
     */
    int queryMeterLedgerCountNew(RptMeterLedgerVO rptMeterLedgerVO);

}