package com.xunge.dao.contract;

import com.xunge.model.basedata.DatSupplierVO;
import com.xunge.model.basedata.colletion.TaskHistoryInfoVO;
import com.xunge.model.contract.*;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface BigContractSynServiceDao {

    List<Map<String, String>> selePrvInfo();

    List<DatSupplierVO> seleSupplierIds(@Param("prvId") String prvId);

    List<String> seleDatContractCodes(@Param("prvId") String prvId);

    void insertDatSupplier(List<DatSupplierVO> data);

    void insertDatContract(List<DatContractVO> data);

    void insertEleContract(List<EleContract> data);

    void insertRentContract(List<RentContract> data);

    void updateDatSupplier(DatSupplierVO datSupplierVO);

    void updateDatContract(DatContractVO datContractVO);

    void updateEleContract(EleContract eleContract);

    void updateRentContract(RentContract rentContractVO);

    void insertTaskInfo(TaskHistoryInfoVO taskHistory);

    List<String> seleDatContractSysIds(@Param("prvId") String prvId);

    List<DatContractVO> seleContractCodeAndDatId(@Param("prvId") String prvId);

    List<DatContractVO> seleContractSysAndDatId(@Param("prvId") String prvId);

    void insertDat(DatContractVO data);

    void insertEle(EleContract data);

    void insertRent(RentContract data);

    List<Map<String, String>> seleEleContractSupplier(@Param("prvId") String prvId);

    List<Map<String, String>> seleRentContractSupplier(@Param("prvId") String prvId);

    List<String> seleEleContractNoIds(@Param("prvId") String prvId);

    List<String> seleRentContractNoIds(@Param("prvId") String prvId);

    void updateLastSynTime(@Param("updateTime") String updateTime, @Param("incExpType") String incExpType);

    SynConfig getSnyConfig(@Param("incExpType") String incExpType);

    void updateDatContractAuditState(@Param("data") List<String> data);

    DatContractVO getDatContractByCode(@Param("prvId") String prvId, @Param("contractCode") String contractCode);

    /**
     * 获取多供应商合同关联关系
     *
     * @param contractId
     * @return
     */
    List<DatContractSupplier> getDatContractSuppliers(@Param("prvId") String prvId, @Param("contractId") String contractId);

    void insertDatContractSupplier(List<DatContractSupplier> adddsList);

    void updateDatContractSupplier(DatContractSupplier upddsList);

    List<Map<String, Object>> queryEleSupplierIdByContractCode(@Param("prvId") String prvId, @Param("contractCode") String contractCode);

    List<Map<String, Object>> queryRentSupplierIdByContractCode(@Param("prvId") String prvId, @Param("contractCode") String contractCode);

    void eleContractConnectWithSupplier(@Param("prvId") String prvId, @Param("contractCode") String contractCode, @Param("masterSupid") String masterSupid);

    void rentContractConnectWithSupplier(@Param("prvId") String prvId, @Param("contractCode") String contractCode, @Param("masterSupid") String masterSupid);

    List<String> queryContractIdIdByContractCode(BigDatContractVo d);
    void updateEleContractClaimAmount(BigDatContractVo d);

    void updateEleContractTotalAmount(BigDatContractVo d);

	void updateRentContractClaimAmount(BigDatContractVo d);

	List<DatContractVO> queryEleContracts(@Param("codes")List<String> contractCodes, @Param("ids")List<String> ids);

	List<DatContractVO> queryRentContracts(@Param("codes")List<String> contractCodes, @Param("ids")List<String> ids);
}