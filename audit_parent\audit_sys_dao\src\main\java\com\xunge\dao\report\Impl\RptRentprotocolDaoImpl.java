package com.xunge.dao.report.Impl;

import com.xunge.dao.AbstractBaseDao;
import com.xunge.dao.report.IRptRentprotocolDao;

import java.util.List;
import java.util.Map;

public class RptRentprotocolDaoImpl extends AbstractBaseDao implements IRptRentprotocolDao {

    final String Namespace = "com.xunge.mapping.RptRentprotocolVOMapper.";

    @Override
    public List<Map<String, Object>> queryRptRentprotocolByPrvid(Map<String, Object> map) {
        return this.getSqlSession().selectList(Namespace + "queryRptRentprotocolByPrvid", map);
    }

    @Override
    public List<Map<String, Object>> queryRptRentprotocolByPregid(Map<String, Object> map) {
        return this.getSqlSession().selectList(Namespace + "queryRptRentprotocolByPregid", map);
    }

    @Override
    public List<Map<String, Object>> queryRptRentprotocol(Map<String, Object> map) {
        return this.getSqlSession().selectList(Namespace + "queryRptRentprotocol", map);
    }

}
