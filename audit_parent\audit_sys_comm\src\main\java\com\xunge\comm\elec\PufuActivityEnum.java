package com.xunge.comm.elec;

/**
 * 普服业务活动枚举
 */
public enum PufuActivityEnum {

    /**
     * AN180421
     */
    AN180421("AN180451", "网络电费-铁塔公司站点-基站-直供电费-公益性业务"),

    /**
     * AN180422
     */
    AN180422("AN180452", "网络电费-铁塔公司站点-基站-转供电费-公益性业务"),

    /**
     * AN180423
     */
    AN180423("AN180453", "网络电费-非铁塔公司站点-基站-直供电费-公益性业务"),
    /**
     * AN180424
     */
    AN180424("AN180454", "网络电费-非铁塔公司站点-基站-转供电费-公益性业务");

    public final String code;
    public final String text;

    PufuActivityEnum(String code, String text) {
        this.code = code;
        this.text = text;
    }

    /**
     * 获取枚举信息
     *
     * @param code 状态码
     * @return 结果
     */
    public static PufuActivityEnum getPufuActivityEnum(String code) {
        PufuActivityEnum temp = null;
        for (PufuActivityEnum pufuActivityEnum : PufuActivityEnum.values()) {
            if (pufuActivityEnum.getCode().equals(code)) {
                temp = pufuActivityEnum;
                break;
            }
        }
        return temp;
    }

    public String getCode() {
        return code;
    }

    public String getText() {
        return text;
    }

}
