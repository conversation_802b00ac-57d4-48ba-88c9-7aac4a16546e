package com.xunge.dao.account.impl;

import com.xunge.dao.AbstractBaseDao;
import com.xunge.dao.account.IAccountStateDao;
import com.xunge.model.system.log.TaskHistoryInfoVO;

import java.util.List;
import java.util.Map;

public class AccountStateDaoImpl extends AbstractBaseDao implements IAccountStateDao {

    final String Namespace = "com.xunge.dao.account.AccountStateDao.";

    @Override
    public List<Map<String, Object>> getAccrualBillamount(String prvId) {
        return this.getSqlSession().selectList(Namespace + "getAccrualBillamount", prvId);
    }

    @Override
    public List<Map<String, Object>> getEleBillaMount(String prvId) {
        return this.getSqlSession().selectList(Namespace + "getEleBillaMount", prvId);
    }

    @Override
    public List<Map<String, Object>> getTeleBillaMount(String prvId) {
        return this.getSqlSession().selectList(Namespace + "getTeleBillaMount", prvId);
    }

    @Override
    public List<Map<String, Object>> getRentBillamount(String prvId) {
        return this.getSqlSession().selectList(Namespace + "getRentBillamount", prvId);
    }

    @Override
    public List<Map<String, Object>> getEleVerificationBillamount(String prvId) {
        return this.getSqlSession().selectList(Namespace + "getEleVerificationBillamount", prvId);
    }

    @Override
    public List<Map<String, Object>> getEleLoanBillamount(String prvId) {
        return this.getSqlSession().selectList(Namespace + "getEleLoanBillamount", prvId);
    }

    @Override
    public List<Map<String, Object>> getDsEleBillaMount(String prvId) {
        return this.getSqlSession().selectList(Namespace + "getDsEleBillaMount", prvId);
    }

    @Override
    public void upEleBillaMount(Map<String, Object> map) {
        this.getSqlSession().update(Namespace + "upEleBillaMount", map);
    }

    @Override
    public void upTeleBillaMount(Map<String, Object> map) {
        this.getSqlSession().update(Namespace + "upTeleBillaMount", map);
    }

    @Override
    public void upRentBillamount(Map<String, Object> map) {
        this.getSqlSession().update(Namespace + "upRentBillamount", map);
    }

    @Override
    public void upEleVerificationBillamount(Map<String, Object> map) {
        this.getSqlSession().update(Namespace + "upEleVerificationBillamount", map);
    }

    @Override
    public void upEleLoanBillamount(Map<String, Object> map) {
        this.getSqlSession().update(Namespace + "upEleLoanBillamount", map);
    }

    @Override
    public void upDsEleBillaMount(Map<String, Object> map) {
        this.getSqlSession().update(Namespace + "upDsEleBillaMount", map);
    }

    @Override
    public void upStatus(String taskId) {
        this.getSqlSession().update(Namespace + "upTaskStatus", taskId);
    }

    @Override
    public void inLog(TaskHistoryInfoVO taskHistory) {
        this.getSqlSession().insert(Namespace + "inLog", taskHistory);
    }

    @Override
    public void editPayment(Map<String, Object> map) {
        this.getSqlSession().update(Namespace + "editPayment", map);
    }
}
