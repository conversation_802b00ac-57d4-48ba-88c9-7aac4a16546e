package com.xunge.dao.towerrent.room.impI;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2019/7/24.
 */
public class TwrRoomAppDaoImpl {
    /*final String towerRoomSpace = "com.xunge.dao.towerrent.TowerRentRoomMapper.";
    final String towerRoomChangeSpace = "com.xunge.dao.towerrent.TowerRentRoomChangeMapper.";
    final String mobileRoomSpace = "com.xunge.dao.towerrent.MobileRentRoomMapper.";
    final String mobileRoomChangeSpace = "com.xunge.dao.towerrent.MobileRentRoomChangeMapper.";
    final String nameSpace = "com.xunge.dao.towerrent.TowerBaseMapper.";


    @Override
    public TowerRentRoomVO getRwrRentRoomInfoById(String rentInformationTowerRoomId) {
        return this.getSqlSession().selectOne(towerRoomSpace + "getRwrRentRoomInfoById", rentInformationTowerRoomId);
    }

    @Override
    public TowerRentRoomVO queryBeanById(Map<String, Object> map) {
        return this.getSqlSession().selectOne(towerRoomSpace + "queryBeanById", map);
    }

    @Override
    public int updateRentRoomChangeCheckState(Map<String, String> map) {
        return this.getSqlSession().update(towerRoomChangeSpace + "updateRentRoomChangeCheckState",map);
    }

    @Override
    public int updateRentRoomCheckState(Map<String, String> map) {
        return this.getSqlSession().update(towerRoomSpace + "updateRentRoomCheckState",map);
    }

    @Override
    public MobileRentRoomVO getMobileRentRoomById(Map<String, String> paramMap) {
        return this.getSqlSession().selectOne(mobileRoomSpace + "getMobileRentRoomById",paramMap);
    }

    @Override
    public int deleteChangeByBussId(Map<String, String> map) {
        return this.getSqlSession().delete(mobileRoomChangeSpace + "deleteChangeByBussId",map);
    }

    @Override
    public int deleteByBussId(Map<String, String> map) {
        return this.getSqlSession().delete(mobileRoomSpace +"deleteByBussId",map);
    }

    @Override
    public TowerLinkNew getResLink(String towerStationCode) {
        return this.getSqlSession().selectOne(nameSpace+"getResLink",towerStationCode);
    }

    @Override
    public int insertRoomAsTower(Map<String, String> map) {
        return this.getSqlSession().insert(mobileRoomSpace+"insertRoomAsTower",map);
    }

    @Override
    public int insertRoomChangeAsTower(Map<String, String> map) {
        return this.getSqlSession().insert(mobileRoomChangeSpace+"insertRoomChangeAsTower",map);
    }

    @Override
    public List<TowerRentRoomChange> getTwrRoomUpdateHistory(Map<String, Object> paraMap) {
        return this.getSqlSession().selectList(towerRoomChangeSpace + "getTwrRoomUpdateHistory", paraMap);
    }

    @Override
    public int updateMobileRentRoomByParam(Map<String, Object> paraMap) {
        return this.getSqlSession().update(mobileRoomSpace+"updateMobileRentRoomByParam",paraMap);
    }

    @Override
    public int insertBatchByTwrRentChange(Map<String, Object> paraMap) {
        return this.getSqlSession().insert(mobileRoomChangeSpace+"insertBatchByTwrRentChange",paraMap);
    }

    @Override
    public MobileRentRoomVO getMobileRentRoomInfoById(Map<String, Object> param) {
        return this.getSqlSession().selectOne(mobileRoomSpace + "getMobileRentRoomInfoById", param);
    }

    @Override
    public MobileRentRoomVO queryMobileBeanById(Map<String, Object> param) {
        return this.getSqlSession().selectOne(mobileRoomSpace + "queryMobileBeanById", param);
    }

    @Override
    public List<MobileRentRoomChangeVO> getMobileChangeHistory(Map<String, String> auditMap) {
        return this.getSqlSession().selectList(mobileRoomChangeSpace+"getMobileChangeHistory",auditMap);
    }

    @Override
    public int mobileChangeCheckStateUpdate(Map<String, String> auditMap) {
        return this.getSqlSession().update(mobileRoomChangeSpace + "mobileChangeCheckStateUpdate",auditMap);
    }

    @Override
    public int mobileRoomCheckStateUpdate(Map<String, String> auditMap) {
        return this.getSqlSession().update(mobileRoomSpace+"mobileRoomCheckStateUpdate",auditMap);
    }

    @Override
    public Page<MobileRentRoomChangeVO> getMobileChangeHistory(Map<String, Object> map, int pageNum, int pageSize) {
        PageInterceptor.startPage(pageNum,pageSize);
        this.getSqlSession().selectList(mobileRoomChangeSpace+"getMobileChangeHistory",map);
        return PageInterceptor.endPage();
    }

    @Override
    public Page<TowerRentRoomChange> getTwrRoomUpdateHistory(Map<String, Object> paraMap, int pageNum, int pageSize) {
        PageInterceptor.startPage(pageNum,pageSize);
        this.getSqlSession().selectList(towerRoomChangeSpace + "getTwrRoomUpdateHistory", paraMap);
        return PageInterceptor.endPage();
    }*/
}
