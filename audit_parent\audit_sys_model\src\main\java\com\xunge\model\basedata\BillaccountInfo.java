package com.xunge.model.basedata;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2024/8/28 16:42
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
public class BillaccountInfo {
    private String billaccountName;
    private String billaccountCode;
    private String supplierCode;
    private String supplierName;
    private Integer isSpecial;
    private String billaccountId;
    private String supplierId;
    private String billaccountType;
    private String supplyMethod;
    private String isIncludeAll;
    private String auditingState;
    private String billaccountNote;
    private String contractCode;
    private String contractName;
    private String contractStartdate;
    private String contractEnddate;
    private String costCenter;
    private String costCenterName;
}
