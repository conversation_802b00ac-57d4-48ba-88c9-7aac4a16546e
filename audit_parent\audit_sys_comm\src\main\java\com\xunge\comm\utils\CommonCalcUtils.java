package com.xunge.comm.utils;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;


/**
 * 提供常用数值对象比较的工具类
 */
public class CommonCalcUtils {

    /**
     * 比较两个BigDecimal对象是否相等
     *
     * @param cs1 第一个BigDecimal对象
     * @param cs2 第二个BigDecimal对象
     * @return 如果两个BigDecimal对象相等返回true，否则返回false
     */
    public static boolean bigDecimalEquals(BigDecimal cs1, BigDecimal cs2) {
        // 使用Objects.equals进行空安全的equals检查
        return Objects.equals(cs1, cs2) ? (cs1 == null || cs1.compareTo(cs2) == 0) : false;
    }

    /**
     * 比较两个Integer对象是否相等
     *
     * @param cs1 第一个Integer对象
     * @param cs2 第二个Integer对象
     * @return 如果两个Integer对象相等返回true，否则返回false
     */
    public static boolean integerEquals(Integer cs1, Integer cs2) {
        // 使用Objects.equals进行空安全的equals检查
        return Objects.equals(cs1, cs2);
    }

    /**
     * 比较两个Date对象是否相等
     *
     * @param cs1 第一个Date对象
     * @param cs2 第二个Date对象
     * @return 如果两个Date对象相等返回true，否则返回false
     */
    public static boolean dateEquals(Date cs1, Date cs2) {
        // 使用Objects.equals进行空安全的equals检查
        return Objects.equals(cs1, cs2);
    }
}
