package com.xunge.dao.report;

import java.util.List;
import java.util.Map;

public interface IrptRoomDao {
    /**
     * 查询机房报表信息通过省份
     *
     * @return
     * <AUTHOR>
     */
    public List<Map<String, Object>> queryRoomByPrv(Map<String, Object> map);

    /**
     * 查询机房报表信息通过地市
     *
     * @return
     * <AUTHOR>
     */
    public List<Map<String, Object>> queryRoomByPreg(Map<String, Object> map);

    public List<Map<String, Object>> queryRoom(Map<String, Object> map);
}