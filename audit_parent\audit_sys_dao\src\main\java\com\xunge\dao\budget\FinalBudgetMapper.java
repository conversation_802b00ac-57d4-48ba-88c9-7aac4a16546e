package com.xunge.dao.budget;

import com.xunge.model.budget.FinalBudgetVO;
import com.xunge.model.budget.ThreeTowerBudgetReport;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface FinalBudgetMapper {

    int insertFianlBudget(@Param("list") List<FinalBudgetVO> list);

    List<FinalBudgetVO> queryFianlBudget(@Param("workOrderId") String workOrderId,@Param("prvId") String prvId);

    int delFianlBudget(@Param("workOrderId") String workOrderId);
}
