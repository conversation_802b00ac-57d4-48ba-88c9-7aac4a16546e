package com.xunge.dao.system.preferenceset;

import com.xunge.model.system.preferenceset.SysPreferenceSet;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface SysPreferenceSetDao {
    int deleteByPrimaryKey(Integer id);

    int insert(SysPreferenceSet record);

    int insertSelective(SysPreferenceSet record);

    SysPreferenceSet selectByPrimaryKey(Integer id);

    List<SysPreferenceSet> selectByUserId(@Param("userId")String userId,@Param("functionModel")String functionModel);

    int updateByPrimaryKeySelective(SysPreferenceSet record);

    int updateByPrimaryKey(SysPreferenceSet record);

    int updateByUserIdAndModel(SysPreferenceSet record);
}