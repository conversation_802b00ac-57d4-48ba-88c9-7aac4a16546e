package com.xunge.model.basedata.power;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 描述：smart_meter表对应java bean
 * Created on 2019/6/13.
 * <p>Title:</p>
 * <p>Copyright:Copyright (c) 2017</p>
 * <p>Company:科大国创软件股份有限公司</p>
 * <p>Department:NBC</p>
 *
 * <AUTHOR> <EMAIL>
 * @version 1.0
 * @update
 */
public class SmartMeter implements Serializable {


    private static final long serialVersionUID = 8205867106290190062L;

    private String smartMeterId;

    private String prvId;

    private String prvName;

    private String pregId;

    private String pregName;

    private String regId;

    private String regName;

    /**
     * @description 当前用户管辖地市IDlist
     * <AUTHOR>
     * @date 创建时间：2017年8月3日
     */
    private List<String> pregIds;

    /**
     * @description 当前用户管辖区县IDlist
     * <AUTHOR>
     * @date 创建时间：2017年8月3日
     */
    private List<String> regIds;


    private String cuid;

    private String meterCode;

    private Date startTime;

    private Date endTime;

    private Date createTime;

    private BigDecimal rate;

    private Integer deviceProperty;

    private Integer valueType;

    private Integer deviceType;

    private BigDecimal meterUpper;

    private BigDecimal currentDegree;

    private BigDecimal usedDegree;

    private String queryDate;

    private String proprietorEquipmentCode;

    //private BigDecimal dayQuantity;

    private String installAddress;

    private Integer enabledStatus;

    private String billAccountCode;

    private BigDecimal collectDegree1;

    private Date collectTime1;

    private BigDecimal collectDegree2;

    private Date collectTime2;

    private BigDecimal collectDegree3;

    private Date collectTime3;

    private BigDecimal collectDegree4;

    private Date collectTime4;

    private BigDecimal collectDegree5;

    private Date collectTime5;

    private BigDecimal collectDegree6;

    private Date collectTime6;

    private BigDecimal collectDegree7;

    private Date collectTime7;

    private BigDecimal collectDegree8;

    private Date collectTime8;

    private BigDecimal collectDegree9;

    private Date collectTime9;

    private BigDecimal collectDegree10;

    private Date collectTime10;

    private BigDecimal collectDegree11;

    private Date collectTime11;

    private BigDecimal collectDegree12;

    private Date collectTime12;

    private BigDecimal collectDegree13;

    private Date collectTime13;

    private BigDecimal collectDegree14;

    private Date collectTime14;

    private BigDecimal collectDegree15;

    private Date collectTime15;

    private BigDecimal collectDegree16;

    private Date collectTime16;

    private BigDecimal collectDegree17;

    private Date collectTime17;

    private BigDecimal collectDegree18;

    private Date collectTime18;

    private BigDecimal collectDegree19;

    private Date collectTime19;

    private BigDecimal collectDegree20;

    private Date collectTime20;

    private BigDecimal collectDegree21;

    private Date collectTime21;

    private BigDecimal collectDegree22;

    private Date collectTime22;

    private BigDecimal collectDegree23;

    private Date collectTime23;

    private BigDecimal collectDegree24;

    private Date collectTime24;

    public String getSmartMeterId() {
        return smartMeterId;
    }

    public void setSmartMeterId(String smartMeterId) {
        this.smartMeterId = smartMeterId;
    }

    public String getPrvId() {
        return prvId;
    }

    public void setPrvId(String prvId) {
        this.prvId = prvId;
    }

    public String getPrvName() {
        return prvName;
    }

    public void setPrvName(String prvName) {
        this.prvName = prvName;
    }

    public String getPregId() {
        return pregId;
    }

    public void setPregId(String pregId) {
        this.pregId = pregId;
    }

    public String getPregName() {
        return pregName;
    }

    public void setPregName(String pregName) {
        this.pregName = pregName;
    }

    public String getRegId() {
        return regId;
    }

    public void setRegId(String regId) {
        this.regId = regId;
    }

    public String getRegName() {
        return regName;
    }

    public void setRegName(String regName) {
        this.regName = regName;
    }

    public String getCuid() {
        return cuid;
    }

    public void setCuid(String cuid) {
        this.cuid = cuid;
    }

    public String getMeterCode() {
        return meterCode;
    }

    public void setMeterCode(String meterCode) {
        this.meterCode = meterCode;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public BigDecimal getRate() {
        if (rate == null) {
            rate = new BigDecimal('0');
        }
        return rate;
    }

    public void setRate(BigDecimal rate) {
        this.rate = rate;
    }

    public Integer getDeviceProperty() {
        return deviceProperty;
    }

    public void setDeviceProperty(Integer deviceProperty) {
        this.deviceProperty = deviceProperty;
    }

    public Integer getValueType() {
        return valueType;
    }

    public void setValueType(Integer valueType) {
        this.valueType = valueType;
    }

    public Integer getDeviceType() {
        return deviceType;
    }

    public void setDeviceType(Integer deviceType) {
        this.deviceType = deviceType;
    }

    public BigDecimal getMeterUpper() {
        if (meterUpper == null) {
            meterUpper = new BigDecimal('0');
        }
        return meterUpper;
    }

    public void setMeterUpper(BigDecimal meterUpper) {
        this.meterUpper = meterUpper;
    }

    public BigDecimal getCurrentDegree() {
        return currentDegree;
    }

    public void setCurrentDegree(BigDecimal currentDegree) {
        this.currentDegree = currentDegree;
    }

    public BigDecimal getUsedDegree() {
        return usedDegree;
    }

    public void setUsedDegree(BigDecimal usedDegree) {
        this.usedDegree = usedDegree;
    }

    public String getQueryDate() {
        return queryDate;
    }

    public void setQueryDate(String queryDate) {
        this.queryDate = queryDate;
    }

    public String getProprietorEquipmentCode() {
        return proprietorEquipmentCode;
    }

    public void setProprietorEquipmentCode(String proprietorEquipmentCode) {
        this.proprietorEquipmentCode = proprietorEquipmentCode;
    }

    /*public BigDecimal getDayQuantity() {
        return dayQuantity;
    }

    public void setDayQuantity(BigDecimal dayQuantity) {
        this.dayQuantity = dayQuantity;
    }*/

    public String getInstallAddress() {
        return installAddress;
    }

    public void setInstallAddress(String installAddress) {
        this.installAddress = installAddress;
    }

    public Integer getEnabledStatus() {
        return enabledStatus;
    }

    public void setEnabledStatus(Integer enabledStatus) {
        this.enabledStatus = enabledStatus;
    }

    public String getBillAccountCode() {
        return billAccountCode;
    }

    public void setBillAccountCode(String billAccountCode) {
        this.billAccountCode = billAccountCode;
    }

    public BigDecimal getCollectDegree1() {
        return collectDegree1;
    }

    public void setCollectDegree1(BigDecimal collectDegree1) {
        this.collectDegree1 = collectDegree1;
    }

    public Date getCollectTime1() {
        return collectTime1;
    }

    public void setCollectTime1(Date collectTime1) {
        this.collectTime1 = collectTime1;
    }

    public BigDecimal getCollectDegree2() {
        return collectDegree2;
    }

    public void setCollectDegree2(BigDecimal collectDegree2) {
        this.collectDegree2 = collectDegree2;
    }

    public Date getCollectTime2() {
        return collectTime2;
    }

    public void setCollectTime2(Date collectTime2) {
        this.collectTime2 = collectTime2;
    }

    public BigDecimal getCollectDegree3() {
        return collectDegree3;
    }

    public void setCollectDegree3(BigDecimal collectDegree3) {
        this.collectDegree3 = collectDegree3;
    }

    public Date getCollectTime3() {
        return collectTime3;
    }

    public void setCollectTime3(Date collectTime3) {
        this.collectTime3 = collectTime3;
    }

    public BigDecimal getCollectDegree4() {
        return collectDegree4;
    }

    public void setCollectDegree4(BigDecimal collectDegree4) {
        this.collectDegree4 = collectDegree4;
    }

    public Date getCollectTime4() {
        return collectTime4;
    }

    public void setCollectTime4(Date collectTime4) {
        this.collectTime4 = collectTime4;
    }

    public BigDecimal getCollectDegree5() {
        return collectDegree5;
    }

    public void setCollectDegree5(BigDecimal collectDegree5) {
        this.collectDegree5 = collectDegree5;
    }

    public Date getCollectTime5() {
        return collectTime5;
    }

    public void setCollectTime5(Date collectTime5) {
        this.collectTime5 = collectTime5;
    }

    public BigDecimal getCollectDegree6() {
        return collectDegree6;
    }

    public void setCollectDegree6(BigDecimal collectDegree6) {
        this.collectDegree6 = collectDegree6;
    }

    public Date getCollectTime6() {
        return collectTime6;
    }

    public void setCollectTime6(Date collectTime6) {
        this.collectTime6 = collectTime6;
    }

    public BigDecimal getCollectDegree7() {
        return collectDegree7;
    }

    public void setCollectDegree7(BigDecimal collectDegree7) {
        this.collectDegree7 = collectDegree7;
    }

    public Date getCollectTime7() {
        return collectTime7;
    }

    public void setCollectTime7(Date collectTime7) {
        this.collectTime7 = collectTime7;
    }

    public BigDecimal getCollectDegree8() {
        return collectDegree8;
    }

    public void setCollectDegree8(BigDecimal collectDegree8) {
        this.collectDegree8 = collectDegree8;
    }

    public Date getCollectTime8() {
        return collectTime8;
    }

    public void setCollectTime8(Date collectTime8) {
        this.collectTime8 = collectTime8;
    }

    public BigDecimal getCollectDegree9() {
        return collectDegree9;
    }

    public void setCollectDegree9(BigDecimal collectDegree9) {
        this.collectDegree9 = collectDegree9;
    }

    public Date getCollectTime9() {
        return collectTime9;
    }

    public void setCollectTime9(Date collectTime9) {
        this.collectTime9 = collectTime9;
    }

    public BigDecimal getCollectDegree10() {
        return collectDegree10;
    }

    public void setCollectDegree10(BigDecimal collectDegree10) {
        this.collectDegree10 = collectDegree10;
    }

    public Date getCollectTime10() {
        return collectTime10;
    }

    public void setCollectTime10(Date collectTime10) {
        this.collectTime10 = collectTime10;
    }

    public BigDecimal getCollectDegree11() {
        return collectDegree11;
    }

    public void setCollectDegree11(BigDecimal collectDegree11) {
        this.collectDegree11 = collectDegree11;
    }

    public Date getCollectTime11() {
        return collectTime11;
    }

    public void setCollectTime11(Date collectTime11) {
        this.collectTime11 = collectTime11;
    }

    public BigDecimal getCollectDegree12() {
        return collectDegree12;
    }

    public void setCollectDegree12(BigDecimal collectDegree12) {
        this.collectDegree12 = collectDegree12;
    }

    public Date getCollectTime12() {
        return collectTime12;
    }

    public void setCollectTime12(Date collectTime12) {
        this.collectTime12 = collectTime12;
    }

    public BigDecimal getCollectDegree13() {
        return collectDegree13;
    }

    public void setCollectDegree13(BigDecimal collectDegree13) {
        this.collectDegree13 = collectDegree13;
    }

    public Date getCollectTime13() {
        return collectTime13;
    }

    public void setCollectTime13(Date collectTime13) {
        this.collectTime13 = collectTime13;
    }

    public BigDecimal getCollectDegree14() {
        return collectDegree14;
    }

    public void setCollectDegree14(BigDecimal collectDegree14) {
        this.collectDegree14 = collectDegree14;
    }

    public Date getCollectTime14() {
        return collectTime14;
    }

    public void setCollectTime14(Date collectTime14) {
        this.collectTime14 = collectTime14;
    }

    public BigDecimal getCollectDegree15() {
        return collectDegree15;
    }

    public void setCollectDegree15(BigDecimal collectDegree15) {
        this.collectDegree15 = collectDegree15;
    }

    public Date getCollectTime15() {
        return collectTime15;
    }

    public void setCollectTime15(Date collectTime15) {
        this.collectTime15 = collectTime15;
    }

    public BigDecimal getCollectDegree16() {
        return collectDegree16;
    }

    public void setCollectDegree16(BigDecimal collectDegree16) {
        this.collectDegree16 = collectDegree16;
    }

    public Date getCollectTime16() {
        return collectTime16;
    }

    public void setCollectTime16(Date collectTime16) {
        this.collectTime16 = collectTime16;
    }

    public BigDecimal getCollectDegree17() {
        return collectDegree17;
    }

    public void setCollectDegree17(BigDecimal collectDegree17) {
        this.collectDegree17 = collectDegree17;
    }

    public Date getCollectTime17() {
        return collectTime17;
    }

    public void setCollectTime17(Date collectTime17) {
        this.collectTime17 = collectTime17;
    }

    public BigDecimal getCollectDegree18() {
        return collectDegree18;
    }

    public void setCollectDegree18(BigDecimal collectDegree18) {
        this.collectDegree18 = collectDegree18;
    }

    public Date getCollectTime18() {
        return collectTime18;
    }

    public void setCollectTime18(Date collectTime18) {
        this.collectTime18 = collectTime18;
    }

    public BigDecimal getCollectDegree19() {
        return collectDegree19;
    }

    public void setCollectDegree19(BigDecimal collectDegree19) {
        this.collectDegree19 = collectDegree19;
    }

    public Date getCollectTime19() {
        return collectTime19;
    }

    public void setCollectTime19(Date collectTime19) {
        this.collectTime19 = collectTime19;
    }

    public BigDecimal getCollectDegree20() {
        return collectDegree20;
    }

    public void setCollectDegree20(BigDecimal collectDegree20) {
        this.collectDegree20 = collectDegree20;
    }

    public Date getCollectTime20() {
        return collectTime20;
    }

    public void setCollectTime20(Date collectTime20) {
        this.collectTime20 = collectTime20;
    }

    public BigDecimal getCollectDegree21() {
        return collectDegree21;
    }

    public void setCollectDegree21(BigDecimal collectDegree21) {
        this.collectDegree21 = collectDegree21;
    }

    public Date getCollectTime21() {
        return collectTime21;
    }

    public void setCollectTime21(Date collectTime21) {
        this.collectTime21 = collectTime21;
    }

    public BigDecimal getCollectDegree22() {
        return collectDegree22;
    }

    public void setCollectDegree22(BigDecimal collectDegree22) {
        this.collectDegree22 = collectDegree22;
    }

    public Date getCollectTime22() {
        return collectTime22;
    }

    public void setCollectTime22(Date collectTime22) {
        this.collectTime22 = collectTime22;
    }

    public BigDecimal getCollectDegree23() {
        return collectDegree23;
    }

    public void setCollectDegree23(BigDecimal collectDegree23) {
        this.collectDegree23 = collectDegree23;
    }

    public Date getCollectTime23() {
        return collectTime23;
    }

    public void setCollectTime23(Date collectTime23) {
        this.collectTime23 = collectTime23;
    }

    public BigDecimal getCollectDegree24() {
        return collectDegree24;
    }

    public void setCollectDegree24(BigDecimal collectDegree24) {
        this.collectDegree24 = collectDegree24;
    }

    public Date getCollectTime24() {
        return collectTime24;
    }

    public void setCollectTime24(Date collectTime24) {
        this.collectTime24 = collectTime24;
    }

    public List<String> getPregIds() {
        return pregIds;
    }

    public void setPregIds(List<String> pregIds) {
        this.pregIds = pregIds;
    }

    public List<String> getRegIds() {
        return regIds;
    }

    public void setRegIds(List<String> regIds) {
        this.regIds = regIds;
    }

    @Override
    public String toString() {
        return "SmartMeter{" +
                "smartMeterId='" + smartMeterId + '\'' +
                ", prvId='" + prvId + '\'' +
                ", pregId='" + pregId + '\'' +
                ", pregName='" + pregName + '\'' +
                ", regId='" + regId + '\'' +
                ", regName='" + regName + '\'' +
                ", cuid='" + cuid + '\'' +
                ", meterCode='" + meterCode + '\'' +
                ", startTime=" + startTime +
                ", endTime=" + endTime +
                ", createTime=" + createTime +
                ", rate=" + rate +
                ", deviceProperty=" + deviceProperty +
                ", valueType=" + valueType +
                ", deviceType=" + deviceType +
                ", meterUpper=" + meterUpper +
                ", currentDegree=" + currentDegree +
                ", usedDegree=" + usedDegree +
                ", queryDate='" + queryDate + '\'' +
                ", proprietorEquipmentCode='" + proprietorEquipmentCode + '\'' +
                ", installAddress='" + installAddress + '\'' +
                ", enabledStatus=" + enabledStatus +
                ", billAccountCode='" + billAccountCode + '\'' +
                ", collectDegree1=" + collectDegree1 +
                ", collectTime1=" + collectTime1 +
                ", collectDegree2=" + collectDegree2 +
                ", collectTime2=" + collectTime2 +
                ", collectDegree3=" + collectDegree3 +
                ", collectTime3=" + collectTime3 +
                ", collectDegree4=" + collectDegree4 +
                ", collectTime4=" + collectTime4 +
                ", collectDegree5=" + collectDegree5 +
                ", collectTime5=" + collectTime5 +
                ", collectDegree6=" + collectDegree6 +
                ", collectTime6=" + collectTime6 +
                ", collectDegree7=" + collectDegree7 +
                ", collectTime7=" + collectTime7 +
                ", collectDegree8=" + collectDegree8 +
                ", collectTime8=" + collectTime8 +
                ", collectDegree9=" + collectDegree9 +
                ", collectTime9=" + collectTime9 +
                ", collectDegree10=" + collectDegree10 +
                ", collectTime10=" + collectTime10 +
                ", collectDegree11=" + collectDegree11 +
                ", collectTime11=" + collectTime11 +
                ", collectDegree12=" + collectDegree12 +
                ", collectTime12=" + collectTime12 +
                ", collectDegree13=" + collectDegree13 +
                ", collectTime13=" + collectTime13 +
                ", collectDegree14=" + collectDegree14 +
                ", collectTime14=" + collectTime14 +
                ", collectDegree15=" + collectDegree15 +
                ", collectTime15=" + collectTime15 +
                ", collectDegree16=" + collectDegree16 +
                ", collectTime16=" + collectTime16 +
                ", collectDegree17=" + collectDegree17 +
                ", collectTime17=" + collectTime17 +
                ", collectDegree18=" + collectDegree18 +
                ", collectTime18=" + collectTime18 +
                ", collectDegree19=" + collectDegree19 +
                ", collectTime19=" + collectTime19 +
                ", collectDegree20=" + collectDegree20 +
                ", collectTime20=" + collectTime20 +
                ", collectDegree21=" + collectDegree21 +
                ", collectTime21=" + collectTime21 +
                ", collectDegree22=" + collectDegree22 +
                ", collectTime22=" + collectTime22 +
                ", collectDegree23=" + collectDegree23 +
                ", collectTime23=" + collectTime23 +
                ", collectDegree24=" + collectDegree24 +
                ", collectTime24=" + collectTime24 +
                '}';
    }
}
