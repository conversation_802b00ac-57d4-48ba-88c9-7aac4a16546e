package com.xunge.model.finance.ext.accClaim.accrual;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 计提汇总信息
 *
 * <AUTHOR>
 * @date 2020-07-31
 */
@Data
public class EleAccrualBillamount {

    /**
     * 计提汇总id
     */
    private String billamountId;

    /**
     * 计提汇总单编码
     */
    private String billamountCode;

    /**
     * 计提汇总摘要
     */
    private String billamountNote;

    /**
     * 报账单类型编码
     */
    private String claimTypeCode;

    /**
     * 计提总金额
     */
    private BigDecimal totalAmount;

    private Integer accrualType;

    private String yearMonth;
    /**
     * 财务报账人公司编码
     */
    private String oprCompanyCode;
    /**
     * 财务报账组织编码
     */
    private String oprOrganizationCode;
    /**
     * 财务报账SMAP
     */
    private String oprSmapId;
    private String oprUserId;

}
