package com.xunge.dao.basedata;

import com.xunge.model.basedata.DatAttachment;
import com.xunge.model.basedata.DatAttachmentCompress;
import com.xunge.model.basedata.DatAttachmentExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface DatAttachmentMapper {
    
    int countByExample(DatAttachmentExample example);

    
    int deleteByExample(DatAttachmentExample example);

    int deleteByExampleNew(DatAttachment dat);

    int deleteByBusinessIdAndUrl(DatAttachment dat);

    int deleteByBusinessId(DatAttachment dat);
    
    int deleteByPrimaryKey(String attachmentId);

    /**
     * 根据附件路径查询附件表是否存在。
     *
     * @param filePath 附件路径
     * @return
     */
    Integer queryAttachExists(@Param("filePath") String filePath, @Param("businessId") String businessId);

    
    int insert(DatAttachment record);

    
    int insertSelective(DatAttachment record);

    
    List<DatAttachment> selectByExample(DatAttachmentExample example);

    List<DatAttachment> selectByExampleNew(DatAttachment example);

    
    DatAttachment selectByPrimaryKey(String attachmentId);

    
    int updateByExampleSelective(@Param("record") DatAttachment record, @Param("example") DatAttachmentExample example);

    
    int updateByExample(@Param("record") DatAttachment record, @Param("example") DatAttachmentExample example);

    
    int updateByPrimaryKeySelective(DatAttachment record);

    
    int updateByPrimaryKey(DatAttachment record);

    /**
     * 缴费查询附件使用
     *
     * @param paramMap
     * @return
     */
    List<DatAttachment> queryDatAttachmentByParam(Map<String, Object> paramMap);

    /**
     * 根据附件Id查询附件
     *
     * @param paramMap
     * @return
     */
    List<DatAttachment> queryDatAttachmentByAttachmentIds(@Param("attachmentIds") List<String> attachmentIds);


    /**
     * 缴费查询附件使用
     *
     * @param paramMap
     * @return
     */
    List<DatAttachment> queryDatAttachmentByBussId(Map<String, Object> paramMap);

    /**
     * 根据缴费单ID和电表ID查询
     *
     * @param paramMap
     * @return
     */
    List<DatAttachment> queryAppDatAttachment(Map<String, Object> paramMap);


    /**
     * 查询补充附件
     *
     * @param paramMap
     * @return
     */
    List<DatAttachment> queryRemarkDatAttachment(Map<String, Object> paramMap);

    /**
     * 批量插入补充附件
     *
     * @param list
     * @return
     */
    int insertAdditionalAttachRecord(List<DatAttachment> list);

    DatAttachment queryByMd5(@Param("md5") String md5);

    void updateNewUrlByMd5(@Param("path") String path, @Param("md5") String md5);

    void updateUniqUrlByMd5(@Param("path") String path, @Param("md5") String md5);

    void updateAppNewUrlByMd5(@Param("path") String path, @Param("md5") String md5);
    void updateDownloadListNewUrlByMd5(@Param("path") String path, @Param("md5") String md5);

    List<DatAttachment> queryFileByMd5(String md5Num);

    Integer countByMd5(String md5Num);

    Integer appFileCountByMd5(String md5Num);

    Integer downloadListCountByMd5(String md5Num);

    List<DatAttachment> getCompressFile(@Param("businessType") String businessType, @Param("businessId") String businessId);

    int insertCompress(List<DatAttachmentCompress> list);

    List<DatAttachment> getDeCompressFiles(@Param("businessId") String businessId);

    void delDeCompressFiles(@Param("businessId") String businessId);

    int updateAttachmentTypeByAttachmentId(@Param("attachmentId") String attachmentId, @Param("attachmentType") Integer attachmentType);

    List<DatAttachment> getTowerCompressFile(@Param("businessId") String businessId,@Param("idList") List<String> idList);

    List<DatAttachment> queryDatAttachmentInfoByParam(DatAttachment datAttachment);

    int updateAttachmentTypeByParams(@Param("attachmentId") String attachmentId,@Param("attachmentType") Integer attachmentType,@Param("createUser") String createUser);

    List<Integer> selectAttachmentTypeByBusinessId(@Param("businessId") String primaryId);

    List<DatAttachment> queryDatAttachmentByBusinessId(@Param("businessId") String businessId);
}