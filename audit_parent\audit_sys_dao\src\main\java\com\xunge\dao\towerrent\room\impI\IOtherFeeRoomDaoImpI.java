package com.xunge.dao.towerrent.room.impI;

import com.xunge.comm.system.RESULT;
import com.xunge.core.page.Page;
import com.xunge.dao.AbstractBaseDao;
import com.xunge.dao.towerrent.room.IOtherFeeRoomDao;
import com.xunge.filter.PageInterceptor;
import com.xunge.model.towerrent.settlement.OtherFeeRoomVO;
import com.xunge.model.towerrent.settlement.TowerAndMobileBillRoomConfirmVO;
import com.xunge.model.towerrent.settlement.TowerBillBalanceRoomVO;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * TODO: 类描述
 *
 * <AUTHOR>
 * @date 2019/4/23 15:04
 */
@Service("iOtherFeeRoomDao")
public class IOtherFeeRoomDaoImpI extends AbstractBaseDao implements IOtherFeeRoomDao {
    final String RoomFee = "com.xunge.mapping.TowerSideBillRoomVOMapper.";
    final String RoomOtherFee = "com.xunge.mapping.OtherFeeRoomVOMapper.";
    final String RoomTowerAndMobiler = "com.xunge.mapping.TowerAndMobilerBillRoomConfirmVOMapper.";


    @Override
    public List<TowerBillBalanceRoomVO> queryRoomBill(Map<String, Object> paramMap) {
        return this.getSqlSession().selectList(RoomFee + "queryRoomBill", paramMap);
    }

    @Override
    public List<TowerAndMobileBillRoomConfirmVO> queryAccountedRoomBill(Map<String, Object> paramMap) {
        return this.getSqlSession().selectList(RoomTowerAndMobiler + "queryAccountedRoomBill", paramMap);
    }

    @Override
    public int updateRoomFeeSumcodeToNull(Map<String, Object> map) {
        return this.getSqlSession().update(RoomFee + "updateRoomFeeSumcodeToNull", map);
    }

    @Override
    public int updateOtherFeeRoomSumcodeToNull(Map<String, Object> map) {
        return this.getSqlSession().update(RoomOtherFee + "updateOtherFeeRoomSumcodeToNull", map);
    }

    @Override
    public int updateRoomBillSetSumcode(TowerBillBalanceRoomVO towerBillBalanceRoomVO) {
        return this.getSqlSession().update(RoomFee + "updateRoomBillSetSumcode", towerBillBalanceRoomVO);
    }

    @Override
    public String updateRoomOtherById(OtherFeeRoomVO otherFeeRoomVO) {
        int result = this.getSqlSession().update(RoomOtherFee + "updateRoomOtherById", otherFeeRoomVO);
        return (result == 0) ? RESULT.FAIL_0 : RESULT.SUCCESS_1;
    }

    @Override
    public Page<TowerAndMobileBillRoomConfirmVO> queryAccountedRoomBillByPage(
            Map<String, Object> paraMap, int pageNumber, int pageSize) {
        PageInterceptor.startPage(pageNumber, pageSize);
        this.getSqlSession().selectList(RoomTowerAndMobiler + "queryAccountedRoomBillByPage", paraMap);
        return PageInterceptor.endPage();
    }
}
