package com.xunge.dao.report.Impl;

import com.xunge.dao.AbstractBaseDao;
import com.xunge.dao.report.IrptResourcesDao;
import com.xunge.model.selfrent.resource.ZeroFlowResourceVO;

import java.util.List;
import java.util.Map;

public class RptResourcesDaoImpl extends AbstractBaseDao implements IrptResourcesDao {

    final String Namespace = "com.xunge.mapping.RptResourcesVOMapper.";

    @Override
    public List<Map<String, Object>> queryResourcesByPrv(Map<String, Object> map) {
        return this.getSqlSession().selectList(Namespace + "queryResourcesByPrv", map);
    }

    @Override
    public List<Map<String, Object>> queryResourcesByPreg(Map<String, Object> map) {
        return this.getSqlSession().selectList(Namespace + "queryResourcesByPreg", map);
    }

    @Override
    public List<Map<String, Object>> queryResources(Map<String, Object> map) {
        return this.getSqlSession().selectList(Namespace + "queryResources", map);
    }

    @Override
    public List<Map<String, Object>> queryResPointByPrv(Map<String, Object> map) {
        return this.getSqlSession().selectList(Namespace + "queryResPointByPrv", map);
    }

    @Override
    public List<Map<String, Object>> queryResPointByPreg(Map<String, Object> map) {
        return this.getSqlSession().selectList(Namespace + "queryResPointByPreg", map);
    }

    @Override
    public List<Map<String, Object>> queryResPoint(Map<String, Object> map) {
        return this.getSqlSession().selectList(Namespace + "queryResPoint", map);
    }

    @Override
    public List<ZeroFlowResourceVO> queryRentZeroFlowResource(Map<String, Object> map) {
        return this.getSqlSession().selectList(Namespace + "queryRentZeroFlowResource",map);
    }

    @Override
    public int updateZeroNote(ZeroFlowResourceVO vo) {
        return this.getSqlSession().update(Namespace + "updateZeroNote",vo);
    }

}