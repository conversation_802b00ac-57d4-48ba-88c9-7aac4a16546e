package com.xunge.model.converter.ele;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;

//电损计算方式:"电损按度数_0", "电损按金额_1"
public class LossTypeConverter implements Converter<Integer> {
    @Override
    public Integer convertToJavaData(ReadCellData<?> cellData, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        String value = cellData.getStringValue();
        if (value.equals("电损按金额")){
            return 1;
        }else if (value.equals("电损按度数")){
            return 0;
        }
        return null;
    }

    @Override
    public WriteCellData<?> convertToExcelData(Integer value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        if (value == null){
            return new WriteCellData<>("");
        }else if (value == 1){
            return new WriteCellData<>("电损按金额");
        }else if (value == 0){
            return new WriteCellData<>("电损按度数");
        }
        return new  WriteCellData<>("");
    }
}
