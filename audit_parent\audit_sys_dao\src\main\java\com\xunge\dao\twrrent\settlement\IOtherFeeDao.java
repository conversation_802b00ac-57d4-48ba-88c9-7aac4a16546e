package com.xunge.dao.twrrent.settlement;

import com.xunge.core.page.Page;
import com.xunge.model.towerrent.settlement.*;

import java.util.List;
import java.util.Map;

public interface IOtherFeeDao {
    /*
     * 查询
     * */
    Page<List<OtherFeeVO>> queryOtherFee(Map<String, Object> paramMap, int pageSize, int pageNum);

    OtherFeeVO queryOtherFeeList(Map<String, Object> paramMap);

    /**
     * 返回List
     *
     * @param paramMap
     * @return
     */
    List<OtherFeeVO> queryOtherFeeMapList(Map<String, Object> paramMap);

    List<OtherFeeRoomVO> queryOtherFeeRoomMapList(Map<String, Object> paramMap);

    List<OtherFeeTransVO> queryOtherFeeTransMapList(Map<String, Object> paramMap);

    List<OtherFeeTinyVO> queryOtherFeeTinyMapList(Map<String, Object> paramMap);

    List<OtherFeeNonstandVO> queryOtherFeeNonstandMapList(Map<String, Object> paramMap);


    List<OtherFeeRoomVO> queryAccountedOtherFeeRoom(Map<String, Object> paramMap);

    Page<List<OtherFeeRoomVO>> queryAccountedOtherFeeRoomByPage(Map<String, Object> paraMap, int pageNumber, int pageSize);

    List<OtherFeeTransVO> queryAccountedOtherFeeTrans(Map<String, Object> paramMap);

    Page<List<OtherFeeTransVO>> queryAccountedOtherFeeTransByPage(Map<String, Object> paraMap, int pageNumber, int pageSize);

    OtherFeeVO queryOtherFeeVOById(String id);

    /*删除
     * */
    String deleteOtherById(List<String> ids);

    /*修改*/
    String updateOtherById(OtherFeeVO otherFee);

    /*添加*/
    String insertOtherById(OtherFeeVO otherFee);

    /*导出*/
    List<OtherFeeVO> queryExportList(Map<String, Object> map);

    /**
     * 删除汇总报账数据，清除已汇总的账单数据的汇总编码字段
     *
     * @param map
     * @return
     */
    int updateOtherFeeSumcodeToNull(Map<String, Object> map);

    /**
     * 其他费用导出查询
     *
     * @param paramMap
     * @return
     */
    List<OtherFeeVO> exportOtherFee(Map<String, Object> paramMap);


    int updateTinyOtherById(OtherFeeTinyVO tinyVO);

    int updateNonstandOtherById(OtherFeeNonstandVO nonstandVO);

    int updateTinyOtherFeeSumcodeToNull(Map<String, Object> map);

    int updateNonstandOtherFeeSumcodeToNull(Map<String, Object> map);

}