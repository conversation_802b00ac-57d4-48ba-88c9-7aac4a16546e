package com.xunge.dao.selfelec.elecaudit.impl;

import com.xunge.comm.system.RESULT;
import com.xunge.core.page.Page;
import com.xunge.dao.AbstractBaseDao;
import com.xunge.dao.selfelec.elecaudit.IModelParamSetDao;
import com.xunge.filter.PageInterceptor;
import com.xunge.model.selfelec.dto.NoteConfigBean;
import com.xunge.model.selfelec.vo.EleBenchmarkParaConfigVO;
import com.xunge.model.selfelec.vo.ModelParamSetVO;
import com.xunge.model.selfelec.vo.ModelParamSetVONew;

import java.util.List;
import java.util.Map;

public class ModelParamSetDaoImpl extends AbstractBaseDao implements IModelParamSetDao {

    final String ModelParamSetNamespace = "com.xunge.mapping.ModelParamSetVOMapper.";

    final String EleBenchmarkParaConfigNamespace = "com.xunge.dao.eleBenchmarkParaConfigVOMapper.";

    @Override
    public Page<ModelParamSetVO> queryAll(Map<String, Object> paramMap) {
        int pageNumber = Integer.parseInt(paramMap.get("pageNumber").toString());
        int pageSize = Integer.parseInt(paramMap.get("pageSize").toString());
        PageInterceptor.startPage(pageNumber, pageSize);
        this.getSqlSession().selectList(ModelParamSetNamespace + "queryAllModel", paramMap);
        return PageInterceptor.endPage();
    }

    @Override
    public List<ModelParamSetVO> queryAllNoPage(Map<String, Object> paramMap) {
        return this.getSqlSession().selectList(ModelParamSetNamespace + "queryAllModel", paramMap);
    }

    @Override
    public List<EleBenchmarkParaConfigVO> queryBenchmarkParaConfig(Map<String, Object> paramMap) {
        return this.getSqlSession().selectList(EleBenchmarkParaConfigNamespace + "queryBenchmarkParaConfig", paramMap);
    }

    @Override
    public String updateById(Map<String, Object> paramMap) {
        int result = this.getSqlSession().update(ModelParamSetNamespace + "updateModelParam", paramMap);
        return result == 1 ? RESULT.SUCCESS_1 : RESULT.FAIL_0;
    }

    @Override
    public void export() {

    }

    @Override
    public String insertModel(Map<String, Object> paramMap) {
        int result = this.getSqlSession().insert(ModelParamSetNamespace + "insertModel", paramMap);
        return result == 1 ? RESULT.SUCCESS_1 : RESULT.FAIL_0;
    }

    @Override
    public int insertBenchmarkParaConfig(EleBenchmarkParaConfigVO eleBenchmarkParaConfigVO) {
        return this.getSqlSession().insert(EleBenchmarkParaConfigNamespace + "insertBenchmarkParaConfig", eleBenchmarkParaConfigVO);
    }

    @Override
    public int updateBenchmarkParaConfig(EleBenchmarkParaConfigVO eleBenchmarkParaConfigVO) {
        return this.getSqlSession().update(EleBenchmarkParaConfigNamespace + "updateBenchmarkParaConfig", eleBenchmarkParaConfigVO);
    }

    @Override
    public int deleteAll() {
        return this.getSqlSession().insert(EleBenchmarkParaConfigNamespace + "deleteAll");
    }

    @Override
    public int insert(Map<String, Object> map) {
        return this.getSqlSession().insert(EleBenchmarkParaConfigNamespace + "insert", map);
    }

    @Override
    public List<ModelParamSetVONew> queryAllNoPageNew(Map<String, Object> paramMap) {
        return this.getSqlSession().selectList(ModelParamSetNamespace + "queryAllNoPageNew", paramMap);
    }

    @Override
    public int insertModelNew(ModelParamSetVONew m) {
        return this.getSqlSession().insert(ModelParamSetNamespace + "insertModelNew", m);
    }

    @Override
    public int deleteAllByregIdAndMonth(Map<String, Object> param) {
        return this.getSqlSession().delete(ModelParamSetNamespace + "deleteAllByregIdAndMonth", param);
    }

    @Override
    public List<ModelParamSetVONew> queryAllNoPageNewexport(Map<String, Object> map) {
        return this.getSqlSession().selectList(ModelParamSetNamespace + "queryAllNoPageNewexport", map);
    }

    @Override
    public int insertLog(Map<String, Object> map) {
        return this.getSqlSession().insert(EleBenchmarkParaConfigNamespace + "insertLog", map);
    }

    @Override
    public List<Map<String, Object>> selectLog() {
        return this.getSqlSession().selectList(EleBenchmarkParaConfigNamespace + "selectLog");
    }

    @Override
    public Map<String, Object> selectELeConfig() {
        return this.getSqlSession().selectOne(EleBenchmarkParaConfigNamespace + "selectELeConfig");
    }

    @Override
    public Map<String, Object> selectBenchmarkChangeLog(Map<String, Object> map) {
        return this.getSqlSession().selectOne(EleBenchmarkParaConfigNamespace + "selectBenchmarkChangeLog", map);
    }

    @Override
    public List<NoteConfigBean> selectNoteConfig(Map<String, Object> param) {
        return this.getSqlSession().selectList(ModelParamSetNamespace + "selectNoteConfig", param);
    }

    @Override
    public int saveNoteConfig(NoteConfigBean noteConfigBean) {
        return this.getSqlSession().insert(ModelParamSetNamespace + "saveNoteConfig", noteConfigBean);
    }

    @Override
    public int deleteNoteConfig(String[] idArray) {
        return this.getSqlSession().delete(ModelParamSetNamespace + "deleteNoteConfig", idArray);
    }

    @Override
    public int updateNoteConfigState(Map<String, Object> paramMap) {
        return this.getSqlSession().update(ModelParamSetNamespace + "updateNoteConfigState", paramMap);
    }

    @Override
    public int updateNoteConfig(NoteConfigBean noteConfigBean) {
        return this.getSqlSession().update(ModelParamSetNamespace + "updateNoteConfig", noteConfigBean);
    }
}
