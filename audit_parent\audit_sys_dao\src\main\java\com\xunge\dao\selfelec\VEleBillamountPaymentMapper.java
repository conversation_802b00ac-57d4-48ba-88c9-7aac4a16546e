package com.xunge.dao.selfelec;

import com.xunge.model.selfelec.VEleBillamountPayment;
import com.xunge.model.selfelec.VEleBillamountPaymentExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface VEleBillamountPaymentMapper {
    
    int countByExample(VEleBillamountPaymentExample example);

    
    int deleteByExample(VEleBillamountPaymentExample example);

    
    int insert(VEleBillamountPayment record);

    
    int insertSelective(VEleBillamountPayment record);

    
    List<VEleBillamountPayment> selectByExample(VEleBillamountPaymentExample example);

    /**
     * 查询电费汇总
     *
     * @param paraMap
     * @return
     */
    public List<VEleBillamountPayment> queryBillamountPayment(Map<String, Object> paraMap);

    
    int updateByExampleSelective(@Param("record") VEleBillamountPayment record, @Param("example") VEleBillamountPaymentExample example);

    
    int updateByExample(@Param("record") VEleBillamountPayment record, @Param("example") VEleBillamountPaymentExample example);

    /**
     * 根据汇总id批量将字段Billamountid和billamountdetailId置null
     *
     * @param ids
     */
    void updateBillamountidIsNull(List<String> ids);

    /**
     * 根据汇总明细id批量将字段Billamountid和billamountdetailId置null
     *
     * @param ids
     */
    void updateBillamountDetailidIsNull(List<String> ids);

    /**
     * 查询符合条件的供应商数据
     *
     * @param paraMap
     * @return
     */
    public List<String> queryPayMentBySupplierMap(Map<String, Object> paraMap);
}