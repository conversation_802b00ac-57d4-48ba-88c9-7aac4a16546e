package com.xunge.dao.selfelec;

import com.xunge.model.selfelec.VDatElectricmeter;
import com.xunge.model.selfelec.VEleBillaccountbaseresource;
import com.xunge.model.selfelec.VEleBillaccountcontract;

import java.util.List;
import java.util.Map;

public interface VDatElectricmeterExpMapper {
    /**
     * 根据报账点id 获取 电表列表
     *
     * @param billaccountId
     * @return
     */
    List<VDatElectricmeter> getVDatElectricmeterBybillaccountId(String billaccountId);

    /**
     * 缴费录入查询电表状态
     *
     * @param billaccountId
     * @return
     */
    List<VDatElectricmeter> queryMeterStateByBillaccountId(String billaccountId);

    /**
     * 根据报账点id 获取 电表列表
     *
     * @param billaccountId
     * @return
     */
    List<VDatElectricmeter> getVDatElectricmeterBybillaccountIdShow(String billaccountId);

    int countPaymentByBillAccountIdForRefresh(Map<String, Object> map);

    public List<VDatElectricmeter> getElectricmeterByConditionsShow(Map<String, Object> param);

    /**
     * 根据报账点获取合同信息
     *
     * @param billaccountId
     * @return
     */
    List<VEleBillaccountcontract> getContractBybillaccountId(String billaccountId);

    /**
     * 根据报账点获取合同信息
     *
     * @param billaccountId
     * @return
     */
    List<VEleBillaccountcontract> queryContractStateById(String billaccountId);


    /**
     * 根据报账点获取合同信息(合同大集中)
     *
     * @param billaccountId
     * @return
     */
    List<VEleBillaccountcontract> queryFinanceContractStateById(String billaccountId);

    /**
     * 根据报账点集合获取合同信息
     *
     * @param billaccountId
     * @return
     */
    List<VEleBillaccountcontract> queryContractStateByIds(List<String> billaccountId);

    /**
     * 根据报账点id获取资源信息
     *
     * @param billaccountId
     * @return
     */
    public List<VEleBillaccountbaseresource> queryResourceStateByBillaccountId(String billaccountId);

    List<VEleBillaccountbaseresource> queryResourceStateByBillaccountIdForCheck(String billaccountId);

    /**
     * 计算标杆信息
     *
     * @param param
     * @return
     */
    void getBenchmark(Map<String, Object> param);

    /**
     * 根据电表编码查询对应资源点，合同，报账点，供应商信息
     *
     * @param meterCode
     * @return
     */
    public Map<String, Object> selectInfoByMeterCode(Map<String, Object> map);

    /**
     * 根据报账点id 获取 电表列表
     *
     * @param billaccountId
     * @return
     */
    public List<VDatElectricmeter> getDatElectricmeterBybillaccountIdShow(String billaccountId);


    /**
     * 根据报账点获取合同信息(合同大集中)
     *
     * @param verificationId
     * @return
     */
    List<VEleBillaccountcontract> queryFinanceContractStateByIdNew(String verificationId);

    /**
     * 根据报账点获取合同信息
     *
     * @param verificationId
     * @return
     */
    List<VEleBillaccountcontract> queryContractStateByIdNew(String verificationId);

}