package com.xunge.model.budget.twr;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2022/7/28
 * @description 本年起租订单在新年补足费用
 */
@Data
public class BudgetTwrComplementVo {

    /**
     * 省份ID
     */
    private String prvId;

    /**
     * 省份名称
     */
    private String prvName;
    /**
     * 业务类型({塔类,1}, {室分,2}, {微站,3}, {传输,4}, {非标,5}, {合计,6})
     */
    private Integer productType;

    /**
     * 本年起租订单在新年补足费用
     */
    private BigDecimal complementNewRentFee;
    /**
     * 本年起租订单在新年补足费用-预算金额
     */
    private BigDecimal complementNewBudgetFee;

    /**
     * 本年起租订单在新年补足费用-核减金额
     */
    private BigDecimal complementNewSubtractFee;

    /**
     * 本年起租订单在新年补足费用
     */
    private BigDecimal complementNewFee;

    /**
     * 调整金额
     */
    private BigDecimal complementNewAdjustFee;
    private BigDecimal complementNewAdjustFeeAfter;


    /**
     * 备注
     */
    private String complementNewRemark;

}
