package com.xunge.dao.report;

import com.xunge.core.page.Page;
import com.xunge.model.report.SupplyMethodChangeCountVO;
import com.xunge.model.report.SupplyMethodChangeVO;

import java.util.List;
import java.util.Map;

/**
 * @创建人 LiangCheng
 * @创建时间 2020/1/19 0019
 * @描述：
 */
public interface SupplyMethodChangeDao {
    //导入明细
    public void saveImportSupplyChangeData(List<SupplyMethodChangeVO> list);

    //分页查询明细
    public Page<List<SupplyMethodChangeVO>> queryAllSupplyChange(Map<String, Object> map, int pageNumber, int pageSize);

    //导出明细
    public List<SupplyMethodChangeVO> queryAllSupplyChangeList(Map<String, Object> map);

    //分页查询统计概况
    public Page<List<SupplyMethodChangeCountVO>> queryAllSupplyChangeCount(Map<String, Object> map, int pageNumber, int pageSize);

    //导出明细统计概况
    public List<SupplyMethodChangeCountVO> queryAllSupplyChangeCountList(Map<String, Object> map);


    //分页查询明细
    public Page<List<SupplyMethodChangeVO>> queryAllSupplyChangeJT(Map<String, Object> map, int pageNumber, int pageSize);

    //导出明细
    public List<SupplyMethodChangeVO> queryAllSupplyChangeListJT(Map<String, Object> map);

    //分页查询统计概况
    public Page<List<SupplyMethodChangeCountVO>> queryAllSupplyChangeCountJT(Map<String, Object> map, int pageNumber, int pageSize);

    //导出明细统计概况
    public List<SupplyMethodChangeCountVO> queryAllSupplyChangeCountListJT(Map<String, Object> map);


}
