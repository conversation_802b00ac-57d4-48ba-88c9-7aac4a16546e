package com.xunge.comm.elec;

/**
 * @Description 缴费信息相关type
 * <AUTHOR>
 * @Date 2021/3/5 17:04
 * @modifier ZXX
 * @date 2021/3/5 17:04
 * @Version 1.0
 **/
public class PaymentMessageType {

    /**
     * 数据来源类型(0:手工录入,2:接口采集)
     */
    public final static int data_from_type_0 = 0; //手工录入
    public final static int data_from_type_2 = 2; //接口采集
    public final static int data_from_type_3 = 3; //批量导入接口
    /**
     * 缴费信息是否完整(1:是，0:否)
     */
    public final static Integer is_complete_type_0 = 0; //否
    public final static Integer is_complete_type_1 = 1; //是


}
