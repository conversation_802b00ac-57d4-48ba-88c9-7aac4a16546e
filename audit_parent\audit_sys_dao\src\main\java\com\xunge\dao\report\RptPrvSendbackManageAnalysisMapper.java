package com.xunge.dao.report;


import com.xunge.model.report.RptPrvSendbackManageAnalysis;
import com.xunge.model.system.user.SysUserVO;
import java.util.List;
import java.util.Map;

public interface RptPrvSendbackManageAnalysisMapper {

    int insert(Map<String, Object> map);
    List<SysUserVO> querySearch(String submitUserName);

    List<RptPrvSendbackManageAnalysis> querySendbackManageDetail(RptPrvSendbackManageAnalysis rptPrvSendbackManageAnalysis);

    List<RptPrvSendbackManageAnalysis> querySendbackByCondition(Map<String, Object> param);



}