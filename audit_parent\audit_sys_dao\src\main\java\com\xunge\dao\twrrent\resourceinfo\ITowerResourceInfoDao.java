package com.xunge.dao.twrrent.resourceinfo;

import com.xunge.core.page.Page;
import com.xunge.model.towerrent.mobile.TowerRruDisCompare;
import com.xunge.model.towerrent.mobile.TowerRruDisConfig;
import com.xunge.model.towerrent.rentmanager.StartRentCompareDetailVo;
import com.xunge.model.towerrent.rentmanager.StartRentCompareStatisticsVo;
import com.xunge.model.towerrent.rentmanager.TowerResourceInfoVO;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 起租管理 铁塔资源信息dao接口
 *
 * <AUTHOR>
 */
public interface ITowerResourceInfoDao {

    //环比变动审核需求,所有的逻辑。binbin.wang 2018/07/17
    // 1. 在审核前，查询移动侧有没有和铁塔侧相对应的数据，返回int值
    String selectInformation(Map<String, Object> paraMap);

    // 2. 审核完成,修改informationtower表的审核状态和完成时间
    int updateTowerCheckState(Map<String, Object> paraMap);

    // 4. 新增：审核通过后，根据rentinformationtowerId，查询铁塔侧新增的批量起租insert到移动侧
    int insetMoble(Map<String, Object> rentinformationtowerMap);

    // 6. 修改: 审核通过后，移动侧information表
    int updateRentinfirmation(Map<String, Object> map);


    int deleteinformation(Map<String, Object> paraMap);

    // 9. 审核不通过,修改informationtower表的审核状态和确认状态
    int updateTowerConfirmState(Map<String, Object> paraMap);

    TowerResourceInfoVO queryByRentinformationtowerId(String id);

    /**
     * 查询铁塔资源信息 yuefy
     */
    public Page<TowerResourceInfoVO> queryTowerResourceInfo(Map<String, Object> paraMap, int pageNumber, int pageSize);

    /**
     * 根据铁塔资源id查询铁塔起租信息
     */
    public TowerResourceInfoVO queryTowerResourceInfoVOById(Map<String, Object> paraMap);

    TowerResourceInfoVO queryByRentinformationId(String id);

    /**
     * 审核完成后修改状态
     */
    public int updateCommit(Map<String, Object> paraMap);

    public int updateCommitMobile(Map<String, Object> paraMap);

    /**
     * 查询铁塔资源信息 yuefy
     */
    List<TowerResourceInfoVO> queryTowerResourceInfo(Map<String, Object> paraMap);

    List<TowerResourceInfoVO> queryTowerResourceAllInfoByPrvId(String prvId);

    /**
     * 根据省份id获取该省所有的铁塔起租单
     *
     * @param prvId
     * @return
     */
    List<String> queryTowerResourceInfoByPrvId(String prvId);

    /**
     * 根据省份id获取该省所有的移动侧起租单主键id
     *
     * @param prvId
     * @return
     */
    List<String> queryMobileResourceInfoByPrvId(String prvId);

    /**
     * 新增铁塔资源信息 yuefy
     */
    int insertTowerResourceInfo(TowerResourceInfoVO towerResourceInfoVO);

    /**
     * 批量新增铁塔资源信息 yuefy
     */
    public int insertTowerResourceInfoList(List<TowerResourceInfoVO> list);

    /**
     * 修改铁塔资源信息 yuefy
     */
    public int updateTowerResourceInfo(TowerResourceInfoVO towerResourceInfoVO);

    /**
     * 将铁塔侧起租单状态从已确认改为未确认
     *
     * @param param
     * @return
     */
    int comfirmStateUpdate(Map<String, Object> param);

    int comfirmStateUpdateMobile(Map<String, Object> param);

    /**
     * 完成铁塔侧环比移动侧审核
     */
    int completeMobileChange(Map<String, Object> param);


    /**
     * 铁塔侧未通过的数据状态初始化
     *
     * @param rentInformationTowerIds
     * @return
     */
    int checkStatusInitializeByPrimary(List<String> rentInformationTowerIds);

    /**
     * 批量修改铁塔资源信息 yuefy
     */
    public int updateTowerResourceInfoList(List<TowerResourceInfoVO> updateTowerResourceInfoList);

    /**
     * 修改移动侧铁塔资源信息
     *
     * @param updateTowerResourceInfoList
     * @return
     */
    int updateMobileResourceInfoBatch(List<TowerResourceInfoVO> updateTowerResourceInfoList);


    /**
     * 查询铁塔起租单非标准建造成本
     *
     * @param param
     * @return
     */
    List<TowerResourceInfoVO> queryStandardBuild(Map<String, Object> param);

    List<TowerResourceInfoVO> queryHisStandardBuild(Map<String, Object> param);

    /**
     * 根据铁塔起租单id集合查询数据
     *
     * @param commonData
     * @return
     */
    List<TowerResourceInfoVO> queryTowerResourceInfoByIds(Map<String, Set<String>> commonData) throws Exception;

    int deleteTowerRentInformationByRentIds(List<String> rentinformationIds);

    Page<TowerResourceInfoVO> queryMobileChangeInfo(Map<String, Object> paraMap, int pageNumber, int pageSize);

    int mobileInfoHistoryBackup(Map<String, Object> param);

    int towerResourceInfoCountById(String rentinformationtowerId);

    Map<String, Object> queryBeanById(Map<String, Object> map);

    /**
     * 移动侧室分数据备份
     *
     * @param param
     * @return
     */
    int mobileRoomBackup(Map<String, Object> param);

    /**
     * 移动侧微站数据备份
     *
     * @param param
     * @return
     */
    int mobileTinyBackup(Map<String, Object> param);

    /**
     * 移动侧传输数据备份
     *
     * @param param
     * @return
     */
    int mobileTransBackup(Map<String, Object> param);

    /**
     * 移动侧非标数据备份
     *
     * @param param
     * @return
     */
    int mobileNonstandBackup(Map<String, Object> param);

    /**
     * 移动侧起租单数据备份后，增加日志数据
     *
     * @param param
     * @return
     */
    int mobileChangeCreateByBackup(Map<String, Object> param);

    /**
     * 针对移动侧起租单“机房产品”字段为“RRU拉远”，且“是否拉远方”为“是”的起租单,
     * 将起租单中的“配套共享信息”、“拉远方共享运营商数量”、“被拉远方共享运营商数量”带入公式，得到“RRU折扣”中的值，并将其赋值给移动侧起租单中的“配套当前共享折扣”
     *
     * @param param
     * @return
     */
    int updatePtShareDis(Map<String, Object> param);

    /**
     * 针对移动侧起租单“机房产品”字段为“RRU拉远”，且“是否拉远方”为“是”的起租单,
     * 将起租单中的“机房共享信息”、“拉远方共享运营商数量”、“被拉远方共享运营商数量”带入公式，得到“RRU折扣”中的值，并将其赋值给移动侧起租单中的“机房当前共享折扣”；
     *
     * @param param
     * @return
     */
    int updateRoomShareDis(Map<String, Object> param);

    /**
     * 针对塔类起租单“机房产品”字段非“RRU拉远”，且“是否被拉远方”为“是”的起租单,
     * 将起租单中的“配套共享信息”、“拉远方共享运营商数量”、“被拉远方共享运营商数量”带入公式，得到“RRU折扣”中的值，记录为“折扣2”
     *
     * @param param
     * @return
     */
    int updatePtShareDis3(Map<String, Object> param);

    /**
     * 针对塔类起租单“机房产品”字段非“RRU拉远”，且“是否被拉远方”为“是”的起租单,
     * 将起租单中的“机房共享信息”、“拉远方共享运营商数量”、“被拉远方共享运营商数量”带入公式，得到“RRU折扣”中的值，记录为“折扣1”。
     *
     * @param param
     * @return
     */
    int updateRoomShareDis3(Map<String, Object> param);

    /**
     * 获取RRU折扣计算公式
     *
     * @param paraMap
     * @return
     */
    List<TowerRruDisConfig> getTwrRruDisInfo(Map<String, Object> paraMap);

    List<TowerRruDisConfig> getTwrRruDisInfoWithoutName(Map<String, Object> paramMap);

    /**
     * 获取折扣对比信息
     *
     * @param paraMap
     * @return
     */
    List<TowerRruDisCompare> getTwrRruDisCompareInfo(Map<String, Object> paraMap);

    int refreshResource(Map<String, Object> paramMap);

    Page<StartRentCompareDetailVo> queryStartRentCompareDetail(Map<String, Object> paraMap, int pageNumber, int pageSize);


    Page<StartRentCompareStatisticsVo> queryStartRentCompareStatistics(Map<String, Object> paraMap, int pageNumber, int pageSize);


    List<StartRentCompareStatisticsVo> queryStartRentCompareStatisticsExport(Map<String, Object> paraMap);


    List<StartRentCompareStatisticsVo> queryStartRentCompareStatisticsRentNumber(Map<String, Object> paraMap);

    List<StartRentCompareDetailVo> queryStartRentCompareDetail(Map<String, Object> paraMap);

    String getMaxUpdateTime();

    List<String> getAccountPeriod();
}
