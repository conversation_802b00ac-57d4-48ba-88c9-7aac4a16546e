package com.xunge.dao.twrrent.settlement.impl;

import com.xunge.comm.system.RESULT;
import com.xunge.core.page.Page;
import com.xunge.dao.AbstractBaseDao;
import com.xunge.dao.twrrent.settlement.IOtherFeeDao;
import com.xunge.filter.PageInterceptor;
import com.xunge.model.towerrent.settlement.*;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service("iOtherFeeDao")
public class OtherFeeDaoImpI extends AbstractBaseDao implements IOtherFeeDao {
    final String OtherFee = "com.xunge.mapping.OtherFeeVOMapper.";
    final String OtherFeeRoom = "com.xunge.mapping.OtherFeeRoomVOMapper.";
    final String OtherFeeTrans = "com.xunge.mapping.OtherFeeTransVOMapper.";
    final String OtherFeeTiny = "com.xunge.mapping.OtherFeeTinyVOMapper.";
    final String OtherFeeNonstand = "com.xunge.mapping.OtherFeeNonstandVOMapper.";

    @SuppressWarnings("unchecked")
    @Override

    public Page<List<OtherFeeVO>> queryOtherFee(Map<String, Object> paramMap, int pageSize, int pageNum) {
        PageInterceptor.startPage(pageNum, pageSize);
        this.getSqlSession().selectList(OtherFee + "queryPageOtherFee", paramMap);
        return PageInterceptor.endPage();
    }

    @Override
    public OtherFeeVO queryOtherFeeList(Map<String, Object> paramMap) {
        return this.getSqlSession().selectOne(OtherFee + "selectOtherFeeList", paramMap);
    }

    @Override
    public List<OtherFeeVO> queryOtherFeeMapList(Map<String, Object> paramMap) {
        return this.getSqlSession().selectList(OtherFee + "selectOtherFeeMapList", paramMap);
    }

    @Override
    public List<OtherFeeRoomVO> queryOtherFeeRoomMapList(Map<String, Object> paramMap) {
        return this.getSqlSession().selectList(OtherFeeRoom + "queryOtherFeeRoomMapList", paramMap);
    }

    @Override
    public List<OtherFeeTransVO> queryOtherFeeTransMapList(Map<String, Object> paramMap) {
        return this.getSqlSession().selectList(OtherFeeTrans + "queryOtherFeeTransMapList", paramMap);
    }

    @Override
    public List<OtherFeeTinyVO> queryOtherFeeTinyMapList(Map<String, Object> paramMap) {
        return this.getSqlSession().selectList(OtherFeeTiny + "queryOtherFeeTinyMapList", paramMap);
    }

    @Override
    public List<OtherFeeNonstandVO> queryOtherFeeNonstandMapList(Map<String, Object> paramMap) {
        return this.getSqlSession().selectList(OtherFeeNonstand + "queryOtherFeeNonstandMapList", paramMap);
    }

    @Override
    public List<OtherFeeRoomVO> queryAccountedOtherFeeRoom(Map<String, Object> paramMap) {
        return this.getSqlSession().selectList(OtherFeeRoom + "queryAccountedOtherFeeRoom", paramMap);
    }

    @Override
    public Page<List<OtherFeeRoomVO>> queryAccountedOtherFeeRoomByPage(
            Map<String, Object> paraMap, int pageNumber, int pageSize) {
        PageInterceptor.startPage(pageNumber, pageSize);
        this.getSqlSession().selectList(OtherFeeRoom + "queryAccountedOtherFeeRoomByPage", paraMap);
        return PageInterceptor.endPage();
    }

    @Override
    public Page<List<OtherFeeTransVO>> queryAccountedOtherFeeTransByPage(
            Map<String, Object> paraMap, int pageNumber, int pageSize) {
        PageInterceptor.startPage(pageNumber, pageSize);
        this.getSqlSession().selectList(OtherFeeTrans + "queryAccountedOtherFeeTransByPage", paraMap);
        return PageInterceptor.endPage();
    }

    @Override
    public List<OtherFeeTransVO> queryAccountedOtherFeeTrans(Map<String, Object> paramMap) {
        return this.getSqlSession().selectList(OtherFeeTrans + "queryAccountedOtherFeeTrans", paramMap);
    }

    @Override
    public OtherFeeVO queryOtherFeeVOById(String checkId) {
        return this.getSqlSession().selectOne(OtherFee + "selectByPrimaryKey", checkId);
    }

    @Override
    public String deleteOtherById(List<String> prvCheckIdList) {
        int result = this.getSqlSession().delete(OtherFee + "deletePrvCheck", prvCheckIdList);
        return (result == 0) ? RESULT.FAIL_0 : RESULT.SUCCESS_1;
    }

    @Override
    public String updateOtherById(OtherFeeVO otherFee) {
        int result = this.getSqlSession().update(OtherFee + "updateOther", otherFee);
        return (result == 0) ? RESULT.FAIL_0 : RESULT.SUCCESS_1;
    }

    @Override
    public String insertOtherById(OtherFeeVO otherFee) {
        int result = this.getSqlSession().insert(OtherFee + "addPrvCheck", otherFee);
        return (result == 0) ? RESULT.FAIL_0 : RESULT.SUCCESS_1;
    }

    @Override
    public List<OtherFeeVO> queryExportList(Map<String, Object> map) {
        // TODO Auto-generated method stub
        return this.getSqlSession().selectList(OtherFee + "selectOtherFeeList", map);
    }

    @Override
    public int updateOtherFeeSumcodeToNull(Map<String, Object> map) {
        return this.getSqlSession().update(OtherFee + "updateOtherFeeSumcodeToNull", map);
    }

    @Override
    public List<OtherFeeVO> exportOtherFee(Map<String, Object> paramMap) {
        return this.getSqlSession().selectList(OtherFee + "queryPageOtherFee", paramMap);
    }

    @Override
    public int updateTinyOtherById(OtherFeeTinyVO tinyVO) {
        return this.getSqlSession().update(OtherFeeTiny + "updateTinyOtherById", tinyVO);
    }

    @Override
    public int updateNonstandOtherById(OtherFeeNonstandVO nonstandVO) {
        return this.getSqlSession().update(OtherFeeNonstand + "updateNonstandOtherById", nonstandVO);
    }

    @Override
    public int updateTinyOtherFeeSumcodeToNull(Map<String, Object> map) {
        return this.getSqlSession().update(OtherFeeTiny + "updateTinyOtherFeeSumcodeToNull", map);
    }

    @Override
    public int updateNonstandOtherFeeSumcodeToNull(Map<String, Object> map) {
        return this.getSqlSession().update(OtherFeeNonstand + "updateNonstandOtherFeeSumcodeToNull", map);
    }
}
