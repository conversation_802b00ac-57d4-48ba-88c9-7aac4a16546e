package com.xunge.dao.selfrent.billamount;

import com.xunge.core.page.Page;
import com.xunge.model.selfrent.billamount.RentBillamountPaymentVO;
import com.xunge.model.selfrent.billamount.RentBillamountVO;
import com.xunge.model.selfrent.billamount.RentBillamountVerificationDetail;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 2017-06-26
 * @description 租费报账汇总DAO接口
 */
public interface RentBillamountDao<T> {
    /**
     * @param map
     * @return
     * @description 查询列表数据
     */
    public Page<List<T>> queryRentBillamountPage(Map<String, Object> map);

    /**
     * @param map
     * @return
     * @description 查询列表数据
     */
    public Page<List<T>> queryRentBillamountPage1(Map<String, Object> map);

    /**
     * @param map
     * @return
     * @description 汇总明细查询/勾选导出
     */
    public List<RentBillamountPaymentVO> queryRentBillamountPage2(Map<String, Object> map);

    /**
     * @param rentBillamountVO
     * @return
     * @description 插入租费报账汇总
     */
    public int insertRentBillamountVO(RentBillamountVO rentBillamountVO);

    /**
     * @param rentBillamountVO
     * @return
     * @description 更新汇总单状态
     */
    public int updateRentBillamountState(RentBillamountVO rentBillamountVO);

    public int updateByPrimaryKeySelective(RentBillamountVO rentBillamountVO);

    /**
     * @param map
     * @param map billamountId 租费报账汇总Id
     * @return
     * @description 查询租费报账汇总数据
     */
    public RentBillamountVO queryRentBillamountById(Map<String, Object> map);

    /**
     * @param map
     * @param map List<String> billamountId 租费报账汇总Id数组
     * @return
     * @description 查询租费报账汇总数据列表
     */
    public List<RentBillamountVO> queryRentBillamountByIds(Map<String, Object> map);

    /**
     * 删除汇总单
     *
     * @param billamountId 汇总单ID
     * @return
     */

    public int deleteBybillamountId(String billamountId);

    /**
     * @param RentBillamountVO
     * @return
     * @description 更新租费报账汇总信息状态
     */
    public int updateByPrimaryKey(RentBillamountVO rentBillamount);

    public RentBillamountVO selectRentBillamountById(Map<String, Object> map);

    /**
     * 根据汇总单code查询billamount
     *
     * @param billamountCode
     * @return
     */
    public RentBillamountVO selectBillamountByCode(String billamountCode);

    /**
     * 定时器查询payment补充信息对应缴费单编码
     *
     * @return
     */
    public List<String> queryInquiryClaimPmtBillamount(Map<String, Object> map);


    /**
     * @param @param paraMap    设定文件
     * @return void    返回类型
     * @throws
     * @Title: updateSupplierInfo
     * @Description: TODO(这里用一句话描述这个方法的作用)
     */

    public int updateSupplierInfo(Map<String, Object> paraMap);


    /**
     * @param @param paraMap    设定文件
     * @return void    返回类型
     * @throws
     * @Title: updateComtractInfo
     * @Description: TODO(这里用一句话描述这个方法的作用)
     */

    public int updateComtractInfo(Map<String, Object> paraMap);

    int updateBillamountAdjustById(RentBillamountVO rentBillamountVO);

    List<RentBillamountVerificationDetail> exportVerificationDetails(Map paramMap);

    T queryRentBillamountByBillamountId(Map<String, Object> paraMap);

    void editPayment(RentBillamountVO rentBillamountVO);
}