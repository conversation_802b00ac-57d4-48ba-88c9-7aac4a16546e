package com.xunge.dao.report;


import com.xunge.model.report.RptPrvAuditType;

public interface RptPrvAuditTypeMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(RptPrvAuditType record);

    int insertSelective(RptPrvAuditType record);

    RptPrvAuditType selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(RptPrvAuditType record);

    int updateByPrimaryKey(RptPrvAuditType record);
}