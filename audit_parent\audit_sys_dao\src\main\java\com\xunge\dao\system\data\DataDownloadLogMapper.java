package com.xunge.dao.system.data;

import com.xunge.model.system.data.DataDownloadLog;
import com.xunge.model.system.data.DataRunLog;

import java.util.List;
import java.util.Map;

/**
 * @Author: h<PERSON><PERSON><PERSON>
 * @Data:2024/4/9 9:22
 */
public interface DataDownloadLogMapper {
    /**
     * 按条件查询下载记录
     * @param map
     * @return
     */
    List<DataDownloadLog> queryDataDownloadLogList(Map<String, Object> map);

    int insertDataDownloadLog(DataDownloadLog dataDownloadLog);

}
