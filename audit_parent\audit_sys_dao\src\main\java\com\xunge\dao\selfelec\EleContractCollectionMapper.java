package com.xunge.dao.selfelec;

import com.xunge.model.selfelec.EleContractCollection;
import com.xunge.model.selfelec.EleContractCollectionExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface EleContractCollectionMapper {
    
    int countByExample(EleContractCollectionExample example);

    
    int deleteByExample(EleContractCollectionExample example);

    
    int deleteByPrimaryKey(String elecontractId);

    
    int insert(EleContractCollection record);

    
    int insertSelective(EleContractCollection record);

    
    List<EleContractCollection> selectByExample(EleContractCollectionExample example);

    
    EleContractCollection selectByPrimaryKey(String elecontractId);

    
    int updateByExampleSelective(@Param("record") EleContractCollection record, @Param("example") EleContractCollectionExample example);

    
    int updateByExample(@Param("record") EleContractCollection record, @Param("example") EleContractCollectionExample example);

    
    int updateByPrimaryKeySelective(EleContractCollection record);

    
    int updateByPrimaryKey(EleContractCollection record);
}