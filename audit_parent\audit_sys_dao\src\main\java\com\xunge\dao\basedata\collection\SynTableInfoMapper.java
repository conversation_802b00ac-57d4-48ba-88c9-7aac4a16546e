package com.xunge.dao.basedata.collection;

import com.xunge.model.basedata.colletion.SynTableInfo;

import java.util.List;

public interface SynTableInfoMapper {
    
    int deleteByPrimaryKey(String guid);

    
    int insert(SynTableInfo record);

    
    int insertSelective(SynTableInfo record);

    
    SynTableInfo selectByPrimaryKey(String guid);

    
    int updateByPrimaryKeySelective(SynTableInfo record);

    
    int updateByPrimaryKey(SynTableInfo record);

    List<SynTableInfo> findListByEntity(SynTableInfo synTableInfo);
}