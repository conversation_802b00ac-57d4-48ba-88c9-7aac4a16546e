package com.xunge.dao.basedata.collection;

import com.xunge.model.basedata.colletion.TaskHistoryInfoVO;

import java.util.List;
import java.util.Map;

public interface TaskHistoryInfoVOMapper {
    List<TaskHistoryInfoVO> queryAllHistoryLogVO(Map<String, Object> paramMap);

    int deleteHistoryLogVO(Map<String, Object> paramMap);

    List<TaskHistoryInfoVO> queryTaskRunningHistoryLog(Map<String, Object> map);
}
