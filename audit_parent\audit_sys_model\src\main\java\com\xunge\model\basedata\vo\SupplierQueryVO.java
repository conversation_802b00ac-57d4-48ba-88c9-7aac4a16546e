package com.xunge.model.basedata.vo;

import com.xunge.core.model.UserLoginInfo;

import java.io.Serializable;
import java.util.List;

/**
 * 供应商查询 VO
 * <p>
 * Title: SupplierQueryVO
 *
 * <AUTHOR>
 */
public class SupplierQueryVO extends BaseDataVO implements Serializable {

    private static final long serialVersionUID = 4779790854632941595L;
    private String prvId;
    private String city;
    private String region;
    private String supplierReg;
    private Integer state;
    private Integer queryType;
    private Integer DataFrom;
    private Integer innerFlag;
    private List<String> ids;
    // 登录用户信息
    private UserLoginInfo loginUser;

    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    public Integer getInnerFlag() {
        return innerFlag;
    }

    public void setInnerFlag(Integer innerFlag) {
        this.innerFlag = innerFlag;
    }

    public Integer getDataFrom() {
        return DataFrom;
    }

    public void setDataFrom(Integer dataFrom) {
        DataFrom = dataFrom;
    }

    public String getPrvId() {
        return prvId;
    }

    public void setPrvId(String prvId) {
        this.prvId = prvId;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getRegion() {
        return region;
    }

    public void setRegion(String region) {
        this.region = region;
    }

    public String getSupplierReg() {
        return supplierReg;
    }

    public void setSupplierReg(String supplierReg) {
        this.supplierReg = supplierReg;
    }

    public Integer getQueryType() {
        return queryType;
    }

    public void setQueryType(Integer queryType) {
        this.queryType = queryType;
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public UserLoginInfo getLoginUser() {
        return loginUser;
    }

    public void setLoginUser(UserLoginInfo loginUser) {
        this.loginUser = loginUser;
    }

    public List<String> getIds() {
        return ids;
    }

    public void setIds(List<String> ids) {
        this.ids = ids;
    }

}
