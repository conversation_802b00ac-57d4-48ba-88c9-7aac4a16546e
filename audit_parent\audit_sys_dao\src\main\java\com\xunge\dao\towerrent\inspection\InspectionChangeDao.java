package com.xunge.dao.towerrent.inspection;

import com.xunge.model.towerrent.inspection.InspectionChange;

import java.util.List;

/**
 * TODO: 描述信息
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2019/5/10 15:28
 */
public interface InspectionChangeDao {

    int deleteByPrimaryKey(String inspectionChangeId);

    int insert(InspectionChange record);

    int insertSelective(InspectionChange record);

    InspectionChange selectByPrimaryKey(String inspectionChangeId);

    int updateByPrimaryKeySelective(InspectionChange record);

    int updateByPrimaryKey(InspectionChange record);

    int bachInsertSelective(List<InspectionChange> changes);

    int updateChangeInfo(List<InspectionChange> changes);

    int updateCompareResult(InspectionChange change);

    List<InspectionChange> selectByInspectionId(String inspectionInfoId);
}
