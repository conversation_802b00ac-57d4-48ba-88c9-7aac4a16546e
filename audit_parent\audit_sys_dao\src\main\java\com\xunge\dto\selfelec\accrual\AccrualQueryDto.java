package com.xunge.dto.selfelec.accrual;

import com.xunge.dto.selfelec.PageInfo;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/2/21 15:07
 */
@Getter
@Setter
public class AccrualQueryDto extends PageInfo implements Serializable {

    private static final long serialVersionUID = 7645924367724417004L;
    private String billaccountCode;
    private String billaccountName;
    private String pregId;
    private String regId;
    private List<String> regIds;
    private String resourceCode;
    private String resourceName;
    private String contractCode;
    private String contractName;
    private String meterCode;
    private String billaccountType;
    private String supplyMethod;
    private String accrualCode;
    private String billaccountState;
    private String auditingState;
    private String pushState;
    private String userInfo;
    private String accrualAmountCode;
    private String erpAccrualCode;
    private String accrualStart1;
    private String accrualStart2;
    private String accrualEnd1;
    private String accrualEnd2;
    private String costCenter;
    private String dataTable;
    private String processNode;
    private String accrualKey;
    private String accrualDate1;
    private String accrualDate2;
    private String className;
    private String classSmName;
    private String activityName;
    private String contractout;
    private String isout;
    private String comprehensiveParams;
    private String outAmount;
//    private String billaccountName;
//    private String billaccountName;
//    private String billaccountName;
//    private String billaccountName;
}
