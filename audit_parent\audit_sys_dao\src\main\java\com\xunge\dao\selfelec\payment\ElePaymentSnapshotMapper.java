package com.xunge.dao.selfelec.payment;


import com.xunge.model.selfelec.electricmeter.DatElectricmeterApprovedSnapshot;
import com.xunge.model.selfelec.payment.ElePaymentSnapshot;

/**
* <AUTHOR>
* @description 针对表【ele_payment_snapshot(电费快照表)】的数据库操作Mapper
* @createDate 2024-09-24 17:24:37
* @Entity generator.domain.ElePaymentSnapshot
*/
public interface ElePaymentSnapshotMapper {

    int deleteByPrimaryKey(Long id);
    int deleteByPaymentId(String id);

    int insert(ElePaymentSnapshot record);

    int insertSelective(ElePaymentSnapshot record);

    ElePaymentSnapshot selectByPrimaryKey(Long id);

    ElePaymentSnapshot selectByPaymentId(String id);

    int updateByPrimaryKeySelective(ElePaymentSnapshot record);

    int updateByPrimaryKey(ElePaymentSnapshot record);

}
