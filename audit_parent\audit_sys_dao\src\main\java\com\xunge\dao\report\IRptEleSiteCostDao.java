package com.xunge.dao.report;

import com.xunge.model.report.RptPrvEleBasesiteVo;

import java.util.List;
import java.util.Map;

public interface IRptEleSiteCostDao {
    /**
     * 查询省份站点月费数据
     *
     * @param map
     * @return
     */
    List<RptPrvEleBasesiteVo> querySiteCostAll(Map<String, Object> map);

    /**
     * 查询地市站点月费数据
     *
     * @param map
     * @return
     */
    List<RptPrvEleBasesiteVo> querySiteCostByPrvId(Map<String, Object> map);

    /**
     * 查询区县站点月费数据
     *
     * @param map
     * @return
     */
    List<RptPrvEleBasesiteVo> querySiteCostByPregId(Map<String, Object> map);


    List<RptPrvEleBasesiteVo> querySiteCostAllTotal(Map<String, Object> map);


    List<RptPrvEleBasesiteVo> querySiteCostByPrvIdTotal(Map<String, Object> map);

    List<RptPrvEleBasesiteVo> querySiteCostByPregIdTotal(Map<String, Object> map);

	int exportProvisionDetailsCount(Map<String, Object> map);

	int getAmortizeDetailsProCount(Map<String, Object> map);


    List<RptPrvEleBasesiteVo> querySiteCostByOnMonths(Map<String, Object> map);

}
