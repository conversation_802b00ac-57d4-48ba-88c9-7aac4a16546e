package com.xunge.dao.report;

import java.util.List;
import java.util.Map;

public interface IRptElecontractDao {
    /**
     * 根据省份id查询各地市电费合同数据
     *
     * @return
     * <AUTHOR>
     */
    public List<Map<String, Object>> queryRptElecontractByPrvid(Map<String, Object> map);

    /**
     * 根据地市id查询区县电费合同数据
     *
     * @return
     * <AUTHOR>
     */
    public List<Map<String, Object>> queryRptElecontractByPregid(Map<String, Object> map);

    public List<Map<String, Object>> queryRptElecontract(Map<String, Object> map);
}
