package com.xunge.model.app.basenum;

/**
 * @Auther: hxh
 * @Date: 2018/9/2 19:09
 * @Description:
 */
public enum PromptMessage {

    /**
     * No-cache
     */
    NO_CACHE("No-cache", 0),
    /**
     * image/jpeg
     */
    CONTENT_TYPE_IMAGE_JPEG("image/jpeg", 0),
    /**
     * 图片宽w:134
     */
    IMAGE_W("图片宽w:134", 134),
    /**
     * 图片高h:40
     */
    IMAGE_H("图片高h:40", 40);


    private String strValue;
    private int intValue;

    PromptMessage(String strValue, int intValue) {
        this.strValue = strValue;
        this.intValue = intValue;
    }

    public String getStrValue() {
        return strValue;
    }

    public void setStrValue(String strValue) {
        this.strValue = strValue;
    }

    public int getIntValue() {
        return intValue;
    }

    public void setIntValue(int intValue) {
        this.intValue = intValue;
    }
}
