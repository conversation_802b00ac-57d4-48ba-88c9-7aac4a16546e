package com.xunge.filter;

import com.xunge.core.Interceptor.AreaToDatabaseUtil;
import com.xunge.core.Interceptor.HistoryDataLocalHolder;
import com.xunge.core.Interceptor.ReflectHelper;
import com.xunge.core.page.Page;
import com.xunge.core.util.Reflections;
import com.xunge.core.util.SpringContextUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.executor.parameter.ParameterHandler;
import org.apache.ibatis.executor.resultset.ResultSetHandler;
import org.apache.ibatis.executor.statement.StatementHandler;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.plugin.*;
import org.apache.ibatis.reflection.MetaObject;
import org.apache.ibatis.reflection.SystemMetaObject;
import org.apache.ibatis.scripting.defaults.DefaultParameterHandler;
import org.mybatis.spring.SqlSessionTemplate;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;
import java.util.Properties;

@Intercepts({@Signature(type = StatementHandler.class, method = "prepare", args = {Connection.class, Integer.class}),
        @Signature(type = StatementHandler.class, method = "query", args = {java.sql.Statement.class, org.apache.ibatis.session.ResultHandler.class})})
@Slf4j
public class PageInterceptor implements Interceptor {

    public static final ThreadLocal<Page> localPage = new ThreadLocal<>();
    private static final String schamestart = "/*!mycat:schema=";
    private static final String schameend = " */";

    public static void startPage(int pageNum, int pageSize) {
        localPage.set(new Page(pageNum >= 0 ? pageNum - 1 : pageNum, pageSize));
    }

    public static Page endPage() {
        Page page = localPage.get();
        localPage.remove();
        return page;
    }

    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        try {
            String tenant = AreaToDatabaseUtil.getDatabaseByArea();
            if ((invocation.getTarget() instanceof StatementHandler)) {
                String methodName = invocation.getMethod().getName();
                if ("prepare".equals(methodName)) {
                    StatementHandler statementHandler = (StatementHandler) invocation.getTarget();
                    MetaObject metaStatementHandler = SystemMetaObject.forObject(statementHandler);

                    while (metaStatementHandler.hasGetter("h")) {
                        Object object = metaStatementHandler.getValue("h");
                        metaStatementHandler = SystemMetaObject.forObject(object);
                    }

                    while (metaStatementHandler.hasGetter("target")) {
                        Object object = metaStatementHandler.getValue("target");
                        metaStatementHandler = SystemMetaObject.forObject(object);
                    }
                    MappedStatement mappedStatement = (MappedStatement) metaStatementHandler.getValue("delegate.mappedStatement");

                    BoundSql boundSql = (BoundSql) metaStatementHandler.getValue("delegate.boundSql");
                    Connection connection = (Connection) invocation.getArgs()[0];
                    String sql = boundSql.getSql();

                    if (HistoryDataLocalHolder.queryHistoryData(HistoryDataLocalHolder.HISTORY_ACTIVITY_TABLE)) {
                        sql = sql.replaceAll("\\sACT_HI_ACTINST", " ACT_HI_ACTINST_STATIC");
                        sql = sql.replaceAll("\\sACT_HI_ATTACHMENT", " ACT_HI_ATTACHMENT_STATIC");
                        sql = sql.replaceAll("\\sACT_HI_COMMENT", " ACT_HI_COMMENT_STATIC");
                        sql = sql.replaceAll("\\sACT_HI_DETAIL", " ACT_HI_DETAIL_STATIC");
                        sql = sql.replaceAll("\\sACT_HI_IDENTITYLINK", " ACT_HI_IDENTITYLINK_STATIC");
                        sql = sql.replaceAll("\\sACT_HI_PROCINST", " ACT_HI_PROCINST_STATIC");
                        sql = sql.replaceAll("\\sACT_HI_TASKINST", " ACT_HI_TASKINST_STATIC");
                        sql = sql.replaceAll("\\sACT_HI_VARINST", " ACT_HI_VARINST_STATIC");
                    }
                    //需要分页处理
                    Page page = localPage.get();
                    if (page == null) {
                        sql = schamestart + tenant + schameend + sql;
                        ReflectHelper.setFieldValue(boundSql, "sql", sql);
                        return invocation.proceed();
                    }
                    String pageSql = buildPageSql(sql, page, tenant);

                    metaStatementHandler.setValue("delegate.boundSql.sql", pageSql);

                    //对总数查询进行改造
                    setPageParameter(sql, connection, mappedStatement, boundSql, page, invocation, tenant);
                    return invocation.proceed();
                } else if ("query".equals(methodName)) {
                    Object result = invocation.proceed();
                    Page page = localPage.get();
                    if (page != null) {
                        page.setResult((List) result);
                    }
                    return result;
                }
            }
            return null;
        } catch (Exception e) {
            //避免异常污染线程
            localPage.remove();
            throw e;
        }
    }

    @Override
    public Object plugin(Object target) {
        // TODO Auto-generated method stub
        if (((target instanceof StatementHandler)) || ((target instanceof ResultSetHandler))) {
            return Plugin.wrap(target, this);
        }
        return target;
    }

    @Override
    public void setProperties(Properties properties) {
    }

    private String buildPageSql(String sql, Page page, String tenant) {
        StringBuilder pageSql = new StringBuilder(200);
        // 拼接mycat多数据源
        pageSql.append(schamestart + tenant + schameend);
        pageSql.append("select /*+READ_CONSISTENCY(WEAK)*/ temp.* from ( ");
        pageSql.append(sql);
        pageSql.append(" ) temp limit ").append(page.getPageNum() * page.getPageSize());
        pageSql.append(",").append(page.getPageSize());
        return pageSql.toString();
    }

    private void setPageParameter(String sql, Connection connection, MappedStatement mappedStatement, BoundSql boundSql, Page page,
                                  Invocation invocation, String tenant) throws SQLException {
        //拼接mycat 多数据源
        String countSql = schamestart + tenant + schameend + "select /*+READ_CONSISTENCY(WEAK)*/ count(0) from (" + sql + ") as t";
        PreparedStatement countStmt = null;
        ResultSet rs = null;
        try {
            countStmt = connection.prepareStatement(countSql);
            SqlSessionTemplate sqlsessionTemplate = (SqlSessionTemplate) SpringContextUtil.getBean("sqlsessionTemplate");
            MappedStatement mt = sqlsessionTemplate.getSqlSessionFactory().getConfiguration().getMappedStatement(mappedStatement.getId());
            BoundSql countBS = mt.getBoundSql(boundSql.getParameterObject());//new BoundSql(mt.getConfiguration(), countSql, boundSql.getParameterMappings(),
            // boundSql.getParameterObject());

            if (Reflections.getFieldValue(boundSql, "metaParameters") != null) {
                MetaObject mo = (MetaObject) Reflections.getFieldValue(boundSql, "metaParameters");
                Reflections.setFieldValue(countBS, "metaParameters", mo);
            }
            setParameters(countStmt, mt, countBS, boundSql.getParameterObject());
            rs = countStmt.executeQuery();
            int totalCount = 0;
            if (rs.next()) {
                totalCount = rs.getInt(1);
            }
            page.setTotal(totalCount);
            int totalPage = totalCount / page.getPageSize() + (totalCount % page.getPageSize() == 0 ? 0 : 1);
            page.setPages(totalPage);
        } finally {
            try {
                if (rs != null) {
                    rs.close();
                }
            } catch (SQLException e) {
                log.error("PageInterceptor 出错", e);
            }
            try {
                if (countStmt != null) {
                    countStmt.close();
                }
            } catch (SQLException e) {
                log.error("PageInterceptor 出错", e);
            }
        }
    }

    private void setParameters(PreparedStatement ps, MappedStatement mappedStatement, BoundSql boundSql, Object parameterObject) throws SQLException {
        ParameterHandler parameterHandler = new DefaultParameterHandler(mappedStatement, parameterObject, boundSql);
        parameterHandler.setParameters(ps);
    }

}
