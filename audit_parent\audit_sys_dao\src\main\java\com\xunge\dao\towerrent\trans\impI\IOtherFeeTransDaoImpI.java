package com.xunge.dao.towerrent.trans.impI;

import com.xunge.comm.system.RESULT;
import com.xunge.core.page.Page;
import com.xunge.dao.AbstractBaseDao;
import com.xunge.dao.towerrent.trans.IOtherFeeTransDao;
import com.xunge.filter.PageInterceptor;
import com.xunge.model.towerrent.settlement.OtherFeeTransVO;
import com.xunge.model.towerrent.settlement.TowerAndMobileBillTransConfirmVO;
import com.xunge.model.towerrent.settlement.TowerBillBalanceTransVO;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * TODO: 类描述
 *
 * <AUTHOR>
 * @date 2019/4/23 15:04
 */
@Service("iOtherFeeTransDao")
public class IOtherFeeTransDaoImpI extends AbstractBaseDao implements IOtherFeeTransDao {

    final String TransFee = "com.xunge.mapping.TowerSideBillTransVOMapper.";
    final String TransOtherFee = "com.xunge.mapping.OtherFeeTransVOMapper.";
    final String TransTowerAndMobiler = "com.xunge.mapping.TowerAndMobilerBillTransConfirmVOMapper.";

    @Override
    public List<TowerBillBalanceTransVO> queryTransBill(Map<String, Object> paramMap) {
        return this.getSqlSession().selectList(TransFee + "queryTransBill", paramMap);
    }

    @Override
    public List<TowerAndMobileBillTransConfirmVO> queryAccountedTransBill(Map<String, Object> paramMap) {
        return this.getSqlSession().selectList(TransTowerAndMobiler + "queryAccountedTransBill", paramMap);
    }

    @Override
    public Page<List<TowerAndMobileBillTransConfirmVO>> queryAccountedTransBillByPage(
            Map<String, Object> paraMap, int pageNumber, int pageSize) {
        PageInterceptor.startPage(pageNumber, pageSize);
        this.getSqlSession().selectList(TransTowerAndMobiler + "queryAccountedTransBillByPage", paraMap);
        return PageInterceptor.endPage();
    }

    @Override
    public int updateTransFeeSumcodeToNull(Map<String, Object> map) {
        return this.getSqlSession().update(TransFee + "updateTransFeeSumcodeToNull", map);
    }

    @Override
    public int updateOtherFeeTransSumcodeToNull(Map<String, Object> map) {
        return this.getSqlSession().update(TransOtherFee + "updateOtherFeeTransSumcodeToNull", map);
    }

    @Override
    public int updateTransBillSetSumcode(TowerBillBalanceTransVO towerBillBalanceTransVO) {
        return this.getSqlSession().update(TransFee + "updateTransBillSetSumcode", towerBillBalanceTransVO);
    }

    @Override
    public String updateTransOtherById(OtherFeeTransVO otherFeeTransVO) {
        int result = this.getSqlSession().update(TransOtherFee + "updateTransOtherById", otherFeeTransVO);
        return (result == 0) ? RESULT.FAIL_0 : RESULT.SUCCESS_1;
    }

}
