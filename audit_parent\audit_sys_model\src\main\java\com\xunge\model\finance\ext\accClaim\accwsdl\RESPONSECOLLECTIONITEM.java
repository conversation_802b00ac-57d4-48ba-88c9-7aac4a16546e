package com.xunge.model.finance.ext.accClaim.accwsdl;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * &lt;p&gt;RESPONSECOLLECTION_ITEM complex type的 Java 类。
 * <p>
 * &lt;p&gt;以下模式片段指定包含在此类中的预期内容。
 * <p>
 * &lt;pre&gt;
 * &amp;lt;complexType name="RESPONSECOLLECTION_ITEM"&amp;gt;
 * &amp;lt;complexContent&amp;gt;
 * &amp;lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&amp;gt;
 * &amp;lt;sequence&amp;gt;
 * &amp;lt;element name="REQUEST_ID" type="{http://www.w3.org/2001/XMLSchema}string"/&amp;gt;
 * &amp;lt;element name="PRI_KEY" type="{http://www.w3.org/2001/XMLSchema}string"/&amp;gt;
 * &amp;lt;element name="CLAIM_NUM" type="{http://www.w3.org/2001/XMLSchema}string"/&amp;gt;
 * &amp;lt;element name="RESERVED_1" type="{http://www.w3.org/2001/XMLSchema}string"/&amp;gt;
 * &amp;lt;element name="RESERVED_2" type="{http://www.w3.org/2001/XMLSchema}string"/&amp;gt;
 * &amp;lt;element name="RESP_EXT" type="{http://www.w3.org/2001/XMLSchema}string"/&amp;gt;
 * &amp;lt;/sequence&amp;gt;
 * &amp;lt;/restriction&amp;gt;
 * &amp;lt;/complexContent&amp;gt;
 * &amp;lt;/complexType&amp;gt;
 * &lt;/pre&gt;
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "RESPONSECOLLECTION_ITEM", propOrder = {
        "requestid",
        "prikey",
        "claimnum",
        "reserved1",
        "reserved2",
        "respext"
})
public class RESPONSECOLLECTIONITEM {

    @XmlElement(name = "REQUEST_ID", required = true, nillable = true)
    protected String requestid;
    @XmlElement(name = "PRI_KEY", required = true, nillable = true)
    protected String prikey;
    @XmlElement(name = "CLAIM_NUM", required = true, nillable = true)
    protected String claimnum;
    @XmlElement(name = "RESERVED_1", required = true, nillable = true)
    protected String reserved1;
    @XmlElement(name = "RESERVED_2", required = true, nillable = true)
    protected String reserved2;
    @XmlElement(name = "RESP_EXT", required = true, nillable = true)
    protected String respext;

    /**
     * 获取requestid属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getREQUESTID() {
        return requestid;
    }

    /**
     * 设置requestid属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setREQUESTID(String value) {
        this.requestid = value;
    }

    /**
     * 获取prikey属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getPRIKEY() {
        return prikey;
    }

    /**
     * 设置prikey属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setPRIKEY(String value) {
        this.prikey = value;
    }

    /**
     * 获取claimnum属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getCLAIMNUM() {
        return claimnum;
    }

    /**
     * 设置claimnum属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setCLAIMNUM(String value) {
        this.claimnum = value;
    }

    /**
     * 获取reserved1属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getRESERVED1() {
        return reserved1;
    }

    /**
     * 设置reserved1属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setRESERVED1(String value) {
        this.reserved1 = value;
    }

    /**
     * 获取reserved2属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getRESERVED2() {
        return reserved2;
    }

    /**
     * 设置reserved2属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setRESERVED2(String value) {
        this.reserved2 = value;
    }

    /**
     * 获取respext属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getRESPEXT() {
        return respext;
    }

    /**
     * 设置respext属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setRESPEXT(String value) {
        this.respext = value;
    }

}
