package com.xunge.core.util;

import com.alibaba.fastjson.JSONObject;
import com.github.junrar.Archive;
import com.github.junrar.rarfile.FileHeader;
import lombok.Cleanup;
import lombok.extern.slf4j.Slf4j;
import net.sf.sevenzipjbinding.IInArchive;
import net.sf.sevenzipjbinding.SevenZip;
import net.sf.sevenzipjbinding.SevenZipException;
import net.sf.sevenzipjbinding.impl.RandomAccessFileInStream;
import org.springframework.stereotype.Component;

import java.io.*;
import java.nio.charset.Charset;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Enumeration;
import java.util.Iterator;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import java.util.zip.*;

/**
 *  ZIP,RAR解压工具类(不支持rar5)
 *  @className: DeCompressUtil
 *  @author: Liang<PERSON>heng
 *  @date: 2023/2/22 9:13
 *  @version: 1.0
 */
@Component
@Slf4j
public class DeCompressUtil {

    /**
     * 根据原始压缩包路径，解压到指定文件夹下
     *
     * @param compressPath       原始路径+name
     * @param deCompressPath 解压到的文件夹
     */
    public static boolean deCompressFile(String compressPath,String deCompressPath){
        //ZIP
        if (compressPath.toLowerCase().endsWith(".zip")){
            return unzip(compressPath,deCompressPath);
        }
        //RAR
        if (compressPath.toLowerCase().endsWith(".rar")){
            return unRar5(compressPath,deCompressPath);
        }
        return false;
    }

    /**
     * 根据原始zip路径，解压到指定文件夹下
     *
     * @param zippath       原始zip路径+name
     * @param resourcepath 解压到的文件夹
     */
    public static boolean unzip(String zippath,String resourcepath){
        File pathFile=new File(resourcepath);
        // 目标目录不存在时，创建该文件夹
        if(!pathFile.exists()){
            pathFile.mkdirs();
        }
        try{
            //
            @Cleanup ZipFile zp=new ZipFile(zippath,Charset.forName("gbk"));
            //遍历里面的文件及文件夹
            Enumeration entries=zp.entries();
            while(entries.hasMoreElements()){
                ZipEntry entry= (ZipEntry) entries.nextElement();
                String zipEntryName=entry.getName();

                @Cleanup InputStream in=zp.getInputStream(entry);// NOSONAR
                String outpath=(resourcepath+File.separator+zipEntryName.trim()).replace("/",File.separator);
                //判断路径是否存在，不存在则创建文件路径
                File file = new  File(outpath.substring(0,outpath.lastIndexOf(File.separator)));
                if(!file.exists()){
                    file.mkdirs();
                }
                //判断文件全路径是否为文件夹,如果是,不需要解压
                if(new File(outpath).isDirectory()){
                    continue;
                }
                @Cleanup OutputStream out=new FileOutputStream(outpath);// NOSONAR
                byte[] bf=new byte[2048];
                int len;
                while ((len=in.read(bf))>0){
                    out.write(bf,0,len);
                }
            }
        }catch ( Exception e){
            e.printStackTrace();
            log.error("zip file decompression failed",e);
            return false;
        }
        return true;
    }

    /**
     * 根据原始rar路径，解压到指定文件夹下
     * 这种方法只能解压rar 5.0版本以下的,5.0及其以上的无法解决
     *
     * @param srcRarPath       原始rar路径+name
     * @param dstDirectoryPath 解压到的文件夹
     */
    public static boolean unrar(String srcRarPath, String dstDirectoryPath){
        File dstDiretory = new File(dstDirectoryPath);
        // 目标目录不存在时，创建该文件夹
        if (!dstDiretory.exists()) {
            dstDiretory.mkdirs();
        }
        try {
            @Cleanup Archive archive = new Archive(new FileInputStream(new File(srcRarPath)));
            if (archive != null) {
                // 打印文件信息
                archive.getMainHeader().print();
                FileHeader fileHeader = archive.nextFileHeader();
                while (fileHeader != null) {
                    // 解决中文乱码问题【压缩文件中文乱码】
                    String fileName = fileHeader.getFileNameW().isEmpty() ? fileHeader.getFileNameString() : fileHeader.getFileNameW();
                    // 文件夹
                    if (fileHeader.isDirectory()) {
                        File fol = new File(dstDirectoryPath + File.separator + fileName.trim());
                        fol.mkdirs();
                    } else { // 文件
                        // 解决linux系统中\分隔符无法识别问题
                        String[] fileParts = fileName.split("\\\\");
                        StringBuilder filePath = new StringBuilder();
                        for (String filePart : fileParts) {
                            filePath.append(filePart).append(File.separator);
                        }
                        fileName = filePath.substring(0, filePath.length() - 1);
                        File out = new File(dstDirectoryPath + File.separator + fileName.trim());
                        if (!out.exists()) {
                            // 相对路径可能多级，可能需要创建父目录.
                            if (!out.getParentFile().exists()) {
                                System.out.println(out.getParentFile());
                                out.getParentFile().mkdirs();
                            }
                            out.createNewFile();
                        }
                        @Cleanup FileOutputStream os = new FileOutputStream(out);
                        archive.extractFile(fileHeader, os);
                    }
                    fileHeader = archive.nextFileHeader();
                }
            } else {
                log.info("rar file decompression failed , archive is null");
                return false;
            }
        }catch (Exception e) {
            e.printStackTrace();
            log.error("rar file decompression failed(unsupportedRarArchive异常忽略，rar5暂不支持)",e);
            return false;
        }
        return true;
    }

    /**
     * 根据原始rar路径，解压到指定文件夹下
     * rar解压，兼容rar5
     *
     * @param rarDir       原始rar路径+name
     * @param outDir 解压到的文件夹
     */
    public static boolean unRar5(String rarDir, String outDir){
        RandomAccessFile randomAccessFile = null;
        IInArchive inArchive = null;
        try {
            // 第一个参数是需要解压的压缩包路径，第二个参数参考JdkAPI文档的RandomAccessFile
            randomAccessFile = new RandomAccessFile(rarDir, "r");
            inArchive = SevenZip.openInArchive(null, new RandomAccessFileInStream(randomAccessFile));

            int[] in = new int[inArchive.getNumberOfItems()];
            for (int i = 0; i < in.length; i++) {
                in[i] = i;
            }
            inArchive.extract(in, false, new ExtractCallback(inArchive, "366", outDir));
        } catch (FileNotFoundException | SevenZipException e) {
            e.printStackTrace();
        } finally {
            if (inArchive != null) {
                try {
                    inArchive.close();
                } catch (SevenZipException e) {
                    e.printStackTrace();
                    log.error("rar file decompression failed",e);
                }
            }
            if (randomAccessFile != null) {
                try {
                    randomAccessFile.close();
                } catch (IOException e) {
                    e.printStackTrace();
                    log.error("rar file decompression failed",e);
                }
            }
        }
        return true;
    }

    public static void main(String[] args) throws IOException {
        String a = "C:\\work\\测试文件夹\\RAR\\RAR.rar";
        String b = "C:\\work\\测试文件夹\\RAR\\RAR";
        String c = "C:\\work\\测试文件夹\\ZIP\\ZIP.zip";
        String d = "C:\\work\\测试文件夹\\ZIP\\ZIP";
        String rar5 = "C:\\work\\测试文件夹\\RAR\\RAR\\测试图片.rar";
        String rar5s = "C:\\work\\测试文件夹\\RAR\\RAR\\rar5解压文件";
        System.out.println(deCompressFile(rar5, rar5s));
        /*System.out.println(deCompressFile(c, d));
        String[] arr = new String[]{"PNG"};
        List<String> list = FileUtils.getDirectoryFileTypes(b,arr);
        list.forEach(System.out::println);*/
        /*String e = "C:/work";
        Path path = Paths.get(a);
        Path uppath = Paths.get(e);
        Path resultPath = Paths.get(path.toString().replace(uppath.toString(),""));
        System.out.println(resultPath.toString());*/
    }

}