package com.xunge.dao.activity;

import java.util.List;

import com.xunge.model.activity.SysAuditingCheckUser;

/** 
* @ClassName: SysAuditingCheckUserMapper 
* @Description: 流程审批人处理
* @Author：tian
* @Date：2025年5月22日 
*/
public interface SysAuditingCheckUserMapper {

	/** 
	* @Description: 查询流程审批人信息
	* <AUTHOR>   
	* @date 2025年5月22日 上午10:21:11 
	* @param auditUser
	* @return  
	*/ 
	List<SysAuditingCheckUser> queryAuditCheckUser(SysAuditingCheckUser auditUser);

	/** 
	* @Description: 保存流程审批人
	* <AUTHOR>   
	* @date 2025年5月22日 上午11:00:23 
	* @param auditUser  
	*/ 
	void insertAuditCheckUser(SysAuditingCheckUser auditUser);

	/** 
	* @Description: 更新流程审批人
	* <AUTHOR>   
	* @date 2025年5月22日 下午3:52:02 
	* @param auditUser  
	*/ 
	void updateAuditCheckUser(SysAuditingCheckUser auditUser);

}
