package com.xunge.dao.basedata;

import com.xunge.model.basedata.DatBasetower;
import com.xunge.model.basedata.ResourceDisplayInfoForTowerVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

@Mapper
public interface DatBasetowerVOMapper {
    /**
     * 查询数据库所有铁塔信息
     *
     * @param prvId
     * @return
     */
    List<DatBasetower> queryAllTower(String prvId);

    /**
     * 将需要删除的铁塔信息状态置为-1
     *
     * @param map
     * @return
     */
    int delByCuidsAndPrvid(Map<String, Object> map);

    /**
     * 新增铁塔数据入库
     *
     * @param list
     * @return
     */
    int insertTower(List<DatBasetower> list);

    /**
     * 根据条件查询铁塔的信息集合
     *
     * @param map
     * @return
     */
    public List<DatBasetower> queryDatBasetowerList(Map<String, Object> map);

    /**
     * 根据条件查询铁塔的单个信息
     *
     * @param map
     * @return
     */
    public DatBasetower queryDatBasetowerInfoByParam(Map<String, Object> map);

    public DatBasetower queryDatBasetowerInfoByParam1(Map<String, Object> map);

    /**
     * 新增铁塔信息
     *
     * @param datBasetower
     * @return
     */
    int insertSelective(DatBasetower datBasetower);

    /**
     * 更新铁塔信息
     *
     * @param datBasetower
     * @return
     */
    int updateByPrimaryKeySelective(DatBasetower datBasetower);

    /**
     * 删除铁塔
     *
     * @param map
     * @return
     */
    int deleteByPrimaryKey(Map<String, Object> map);

    /**
     * 查询未被关联的铁塔数据
     *
     * @param map
     * @return
     */
    List<Map<String, Object>> queryTowerNoUsed(Map<String, Object> map);

    /**
     * 查询未被关联的铁塔数据
     *
     * @param map
     * @return
     */
    List<Map<String, Object>> queryTowerNoUsedNew(Map<String, Object> map);


    /**
     * 根据铁塔id集合查询铁塔数据
     *
     * @param map
     * @return
     */
    List<Map<String, Object>> queryTowerByTowerIds(Map<String, Object> map);

    /**
     * 查询当前报账点关联的铁塔id
     *
     * @param billaccountId
     * @return
     */
    List<String> queryTowerIdUsed(String billaccountId);

    /**
     * 修改报账点和铁塔关系为停用
     *
     * @param map
     * @return
     */
    int updateStateToStop(Map<String, Object> map);

    /**
     * 新增铁塔和报账点关系
     *
     * @param map
     * @return
     */
    int insertBillTower(Map<String, Object> map);

    /**
     * 根据报账点id查找当前关联的铁塔数据
     *
     * @param billaccountId
     * @return
     */
    List<Map<String, Object>> queryTowerUsed(String billaccountId, String prvId);

    /**
     * 根据报账点id查找当前关联的铁塔数据
     *
     * @param billaccountId
     * @return
     */
    List<Map<String, Object>> queryTowerUsedDeleteAudit(String billaccountId, String prvId);

    /**
     * 查询当当前报账点需要缴费的铁塔信息
     *
     * @param billaccountId
     * @return
     */
    List<Map<String, Object>> queryTowerNeedFee(String billaccountId);

    /**
     * 通过铁塔id查询铁塔信息
     *
     * @param map
     * @return
     */
    DatBasetower getTowerById(Map<String, Object> map);

    /**
     * 查询最大的铁塔cid
     *
     * @param map
     * @return
     */
    String queryMaxTowerCuid(Map<String, Object> map);

    /**
     * 修改报账点和铁塔关系为停用 批量
     *
     * @param map
     * @return
     */
    int updateAllStateToStop(Map<String, Object> map);

    /**
     * 修改报账点和铁塔关系为 删除审核通过=4 批量
     *
     * @param map
     * @return
     */
    int updateAllStateToStopDeleteAudit(Map<String, Object> map);

    DatBasetower queryBeanById(Map<String, Object> map);

    List<DatBasetower> queryNormalServiceTower(List<String> towerIds);

    List<Map<String, Object>> queryTowerNoUsedNewRebuild(Map<String, Object> map);


    /**
     * 查询铁塔可选的机房\位置点
     * @param prvId
     * @param resourceCid
     * @param resourceName
     * @return
     */
    List<ResourceDisplayInfoForTowerVo> queryResourceIdsTowerCanChoose(@Param("prvId") String prvId, @Param("resourceCid") String resourceCid, @Param("resourceName") String resourceName);


    List<ResourceDisplayInfoForTowerVo> queryResourceInfoByIdList(@Param("list") List<String> resourceIdList);
}
