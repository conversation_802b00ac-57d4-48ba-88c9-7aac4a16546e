package com.xunge.dao.twrrent.handleReport;

import com.xunge.core.page.Page;
import com.xunge.model.towerrent.monthlyReport.*;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 月报查询
 */
public interface IHandleReportSearchDao {
    /**
     * 查询铁塔站址月报--省份
     *
     * @param param
     * @param pageSize
     * @param pageNum
     * @return
     */
    Page<RptMonthlyTwrSite> qyeryPageTwrSiteReportPrv(Map<String, Object> param, int pageSize, int pageNum);

    List<RptMonthlyTwrSite> queryListTwrSiteReportPrv(Map<String, Object> param);

    /**
     * 查询铁塔站址月报--地市
     *
     * @param param
     * @param pageSize
     * @param pageNum
     * @return
     */
    Page<RptMonthlyTwrSite> qyeryPageTwrSiteReportCity(Map<String, Object> param, int pageSize, int pageNum);

    List<RptMonthlyTwrSite> queryListTwrSiteReportCity(Map<String, Object> param);

    /**
     * 查询铁塔站址月报--区县
     *
     * @param param
     * @param pageSize
     * @param pageNum
     * @return
     */
    Page<RptMonthlyTwrSite> qyeryPageTwrSiteReportRegion(Map<String, Object> param, int pageSize, int pageNum);

    List<RptMonthlyTwrSite> queryListTwrSiteReportRegion(Map<String, Object> param);

    /**
     * 获取铁塔站址“塔类产品服务费本月未考核处罚前的金额（元，不含税）”与塔类服务费的“塔类产品服务费本月未考核处罚前的金额（元，不含税）”金额的比较信息，省份
     *
     * @param param
     * @return
     */
    List<TwrSiteMoneyCompareWithService> getCompareInfoPrv(Map<String, Object> param);

    /**
     * 获取铁塔站址“塔类产品服务费本月未考核处罚前的金额（元，不含税）”与塔类服务费的“塔类产品服务费本月未考核处罚前的金额（元，不含税）”金额的比较信息，地市
     *
     * @param param
     * @return
     */
    List<TwrSiteMoneyCompareWithService> getCompareInfoCity(Map<String, Object> param);

    /**
     * 获取铁塔站址“塔类产品服务费本月未考核处罚前的金额（元，不含税）”与塔类服务费的“塔类产品服务费本月未考核处罚前的金额（元，不含税）”金额的比较信息，区县
     *
     * @param param
     * @return
     */
    List<TwrSiteMoneyCompareWithService> getCompareInfoRegion(Map<String, Object> param);

    List<RptMonthlyBudgetAndProgress> querybudgetAndProgressPrv(Map<String, Object> map);

    List<RptMonthlyBudgetAndProgress> querybudgetAndProgressCity(Map<String, Object> map);

    List<RptMonthlyBudgetAndProgress> querybudgetAndProgressReg(Map<String, Object> map);

    /**
     * 查询某账期的推送总金额
     *
     * @param map 账期
     * @return
     */
    BigDecimal queryTotalAmount(Map<String, Object> map);

    /**
     * 查询某账期的推送账单包含总铁塔数
     *
     * @param map 账期
     * @return
     */
    Integer queryNumAmount(Map<String, Object> map);


    List<RptMonthlyIconTowerCost> queryIronTowerReportSearchPrv(Map<String, Object> map);

    List<RptMonthlyIconTowerCost> queryIronTowerReportSearchPreg(Map<String, Object> map);

    List<RptMonthlyIconTowerCost> queryIronTowerReportSearchReg(Map<String, Object> map);

    List<RptMonthlyBudgetAndProgressNew> getBudgetAndProgressCopeDataWhole(Map<String, Object> paraMap);

    List<RptMonthlyBudgetAndProgressNew> getBudgetAndProgressCopeDataPrv(Map<String, Object> paraMap);

    List<RptMonthlyBudgetAndProgressNew> getBudgetAndProgressCopeDataPreg(Map<String, Object> paraMap);

    List<RptMonthlyIconTowerCost> queryTowerServiceFeeSearchPrv(Map<String, Object> map);

    List<RptMonthlyIconTowerCost> queryTowerServiceFeeSearchPreg(Map<String, Object> map);

    List<RoomCostReportVo> queryRoomCostReport(Map<String, Object> map);

    List<RoomCostReportVo> queryRoomCostReportDetail(Map<String, Object> map);

    List<ProductStructureFor5g> query5gAnalysisReport(Map<String, Object> map);

    List<TwrOilPowerFee> queryTwrOilPowerFeeListPrv(Map<String, Object> paraMap);

    List<TwrOilPowerFee> queryTwrOilPowerFeeListPreg(Map<String, Object> paraMap);

    List<TwrOilPowerFee> queryTwrOilPowerFeeListReg(Map<String, Object> paraMap);
}
