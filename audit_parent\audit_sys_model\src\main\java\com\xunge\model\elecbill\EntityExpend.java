package com.xunge.model.elecbill;

import java.io.Serializable;
import java.math.BigDecimal;

public class EntityExpend implements Serializable {

    private static final long serialVersionUID = 6702812400588221251L;

    private Integer tier2_payment_type;

    private Integer tier3_payment_type;

    private BigDecimal price;

    private BigDecimal count;

    private BigDecimal line_amount;

    private BigDecimal tax_rate;

    private BigDecimal document_line_amount;

    private BigDecimal tax_line_amount;

    private String amount_date_begin;

    private String amount_date_end;

    private String contract_code;

    private Integer contract_type;

    private String check_result;

    private String belong_room;
    private BigDecimal other_price;
    private BigDecimal other_tax_rate;
    private BigDecimal other_tax;
    private BigDecimal other_no_deduction;

    public Integer getTier2_payment_type() {
        return tier2_payment_type;
    }

    public void setTier2_payment_type(Integer tier2_payment_type) {
        this.tier2_payment_type = tier2_payment_type;
    }

    public Integer getTier3_payment_type() {
        return tier3_payment_type;
    }

    public void setTier3_payment_type(Integer tier3_payment_type) {
        this.tier3_payment_type = tier3_payment_type;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public BigDecimal getCount() {
        return count;
    }

    public void setCount(BigDecimal count) {
        this.count = count;
    }

    public BigDecimal getLine_amount() {
        return line_amount;
    }

    public void setLine_amount(BigDecimal line_amount) {
        this.line_amount = line_amount;
    }

    public BigDecimal getTax_rate() {
        return tax_rate;
    }

    public void setTax_rate(BigDecimal tax_rate) {
        this.tax_rate = tax_rate;
    }

    public BigDecimal getDocument_line_amount() {
        return document_line_amount;
    }

    public void setDocument_line_amount(BigDecimal document_line_amount) {
        this.document_line_amount = document_line_amount;
    }

    public BigDecimal getTax_line_amount() {
        return tax_line_amount;
    }

    public void setTax_line_amount(BigDecimal tax_line_amount) {
        this.tax_line_amount = tax_line_amount;
    }

    public String getAmount_date_begin() {
        return amount_date_begin;
    }

    public void setAmount_date_begin(String amount_date_begin) {
        this.amount_date_begin = amount_date_begin;
    }

    public String getAmount_date_end() {
        return amount_date_end;
    }

    public void setAmount_date_end(String amount_date_end) {
        this.amount_date_end = amount_date_end;
    }

    public String getContract_code() {
        return contract_code;
    }

    public void setContract_code(String contract_code) {
        this.contract_code = contract_code;
    }

    public Integer getContract_type() {
        return contract_type;
    }

    public void setContract_type(Integer contract_type) {
        this.contract_type = contract_type;
    }

    public String getCheck_result() {
        return check_result;
    }

    public void setCheck_result(String check_result) {
        this.check_result = check_result;
    }

    public String getBelong_room() {
        return belong_room;
    }

    public void setBelong_room(String belong_room) {
        this.belong_room = belong_room;
    }

    public BigDecimal getOther_price() {
        return other_price;
    }

    public void setOther_price(BigDecimal other_price) {
        this.other_price = other_price;
    }

    public BigDecimal getOther_tax_rate() {
        return other_tax_rate;
    }

    public void setOther_tax_rate(BigDecimal other_tax_rate) {
        this.other_tax_rate = other_tax_rate;
    }

    public BigDecimal getOther_tax() {
        return other_tax;
    }

    public void setOther_tax(BigDecimal other_tax) {
        this.other_tax = other_tax;
    }

    public BigDecimal getOther_no_deduction() {
        return other_no_deduction;
    }

    public void setOther_no_deduction(BigDecimal other_no_deduction) {
        this.other_no_deduction = other_no_deduction;
    }

}
