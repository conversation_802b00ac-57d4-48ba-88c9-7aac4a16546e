package com.xunge.dao.selfelec;

import com.xunge.model.basedata.DatBaseresource;
import com.xunge.model.report.RptBillPayLedgerVO;
import com.xunge.model.selfelec.ElePaymentBenchmark;
import com.xunge.model.selfelec.VEleBillaccountInfo;
import com.xunge.model.selfelec.VEleBillaccountPaymentInfo;
import com.xunge.model.selfelec.VEleBillaccountPaymentInfoExample;
import com.xunge.model.selfelec.dto.PaymentInfoDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface VEleBillaccountPaymentInfoMapper {

    int countByExample(VEleBillaccountPaymentInfoExample example);


    int deleteByExample(VEleBillaccountPaymentInfoExample example);


    int insert(VEleBillaccountPaymentInfo record);


    int insertSelective(VEleBillaccountPaymentInfo record);


    List<VEleBillaccountPaymentInfo> selectByExample(VEleBillaccountPaymentInfoExample example);

    List<VEleBillaccountPaymentInfo> selectByExample(Map<String, Object> map);


    int updateByExampleSelective(@Param("record") VEleBillaccountPaymentInfo record, @Param("example") VEleBillaccountPaymentInfoExample example);


    int updateByExample(@Param("record") VEleBillaccountPaymentInfo record, @Param("example") VEleBillaccountPaymentInfoExample example);

    /**
     * 查询报账点审核页面
     *
     * @param hashMaps
     * @return
     * <AUTHOR>
     */
    public List<VEleBillaccountPaymentInfo> queryEleBillaccountPayment(Map<String, Object> hashMaps);

    /**
     * 删除明细账单
     *
     * @param map
     * @return
     * <AUTHOR>
     */
    int deletePaymentDetail(Map<String, Object> map);

    /**
     * 删除历史标杆表
     *
     * @param map
     * @return
     * <AUTHOR>
     */
    int deletePaymentBenchmark(Map<String, Object> map);

    /**
     * 删除账单主表
     *
     * @param map
     * @return
     * <AUTHOR>
     */
    int deletePayment(Map<String, Object> map);

    /**
     * @description 查询报账点及报账点缴费详细信息
     * <AUTHOR>
     * @date 创建时间：2017年9月21日
     */
    public List<VEleBillaccountPaymentInfo> selectBillamountPaymentDetails(Map<String, Object> map);


    /**
     * @description 查询报账点及报账点缴费详细信息
     * <AUTHOR>
     * @date 创建时间：2018年12月7日
     */
    public List<VEleBillaccountPaymentInfo> selectBillaccountPaymentDetails(Map<String, Object> map);
    /**
     * @description 查询报账点及报账点缴费详细信息(新)
     * <AUTHOR>
     * @date 创建时间：2024年10月17日
     */
    List<VEleBillaccountPaymentInfo> queryBillaccountPaymentDetails(Map<String, Object> map);
    /**
     * @description 查询报账点Id(新)
     * <AUTHOR>
     * @date 创建时间：2024年10月31日
     */
    List<String> queryBillaccountIds();

    /**
     * 缴费记录详情查询
     *
     * @param map
     * @return
     */
    public VEleBillaccountPaymentInfo queryEleBillaccountPaymentDetail(Map<String, Object> map);

    public VEleBillaccountPaymentInfo queryFinanceEleBillaccountPaymentDetail(Map<String, Object> map);

    /**
     * 根据报账点编码查询此最大缴费期终的信息
     *
     * @param billaccountId
     * @return
     */
    public VEleBillaccountPaymentInfo queryMaxBillAccountEnded(String billaccountId);

    public List<VEleBillaccountPaymentInfo> queryBillaccountPayMentByCondition(Map<String, Object> map);

    List<RptBillPayLedgerVO> queryAllEleBillaccountPayment(Map<String, Object> map);

    public List<VEleBillaccountPaymentInfo> queryFinanceBillaccountPayMentByCondition(Map<String, Object> map);

    public List<VEleBillaccountInfo> queryBillaccountPayMentByCondition2(Map<String, Object> map);

    public List<VEleBillaccountPaymentInfo> queryValidateStatus(Map<String, Object> map);

    /**
     * 查询缴费数据周期重复数据
     */
    public List<VEleBillaccountPaymentInfo> queryIntersection();

    /**
     * 更加电费缴费id查询缴费明细中资源列表
     *
     * @param map
     * @return
     */
    public List<VEleBillaccountPaymentInfo> queryBaseresourceByPaymentId(Map<String, Object> map);

    public List<VEleBillaccountPaymentInfo> querySpecialBillaccountPaymentByCondition(Map<String, Object> map);

    public List<VEleBillaccountPaymentInfo> queryFinanceSpecialBillaccountPaymentByCondition(Map<String, Object> map);

    public void deletePaymentDetailById(@Param("paymentId") String paymentId);

    public void deletePaymentBenchmarkById(@Param("paymentId") String paymentId);

    public void deletePaymentById(@Param("paymentId") String paymentId);

    /**
     * 新能源查历史同期缴费
     * @param param
     * @return
     */
    List<VEleBillaccountPaymentInfo> queryHistoryPaymentNewEnergy(Map<String, Object> param);

    /**
     * 新能源查历史同期缴费 - 用资源匹配
     *
     * @param param
     * @return
     */
    List<VEleBillaccountPaymentInfo> queryHistoryPaymentNewEnergyOther(Map<String, Object> param);

    /**
     * 非新能源查历史同期缴费
     * @param param
     * @return
     */
    List<VEleBillaccountPaymentInfo> queryHistoryPayment(Map<String, Object> param);

    /**
     * 非新能源查历史同期缴费 - 用资源匹配
     *
     * @param param
     * @return
     */
    List<VEleBillaccountPaymentInfo> queryHistoryPaymentOther(Map<String, Object> param);

    /**
     * 新能源缴费查上笔
     * @param param
     * @return
     */
    VEleBillaccountPaymentInfo queryLastPaymentNewEnergy(Map<String, Object> param);

    /**
     * 新能源电费查另一路缴费上笔列表
     * @param param
     * @return
     */
    List<VEleBillaccountPaymentInfo> queryLastPaymentOther(Map<String, Object> param);

    /**
     * 非新能源电费查另一路上笔列表
     * @param param
     * @return
     */
    List<VEleBillaccountPaymentInfo> queryLastPaymentNewEnergyOther(Map<String, Object> param);

    /**
     * 根据缴费单编码查资源idList
     * @param paymentCode
     * @return
     */
    List<String> selectBaseresourceIds(String paymentCode);

    VEleBillaccountPaymentInfo queryLastPayment(Map<String, Object> param);

    List<VEleBillaccountPaymentInfo> queryLastThreePayment(Map<String, Object> param);

    /**
     * 根据缴费单id查询附件逻辑bussinessId
     *
     * @param billaccountpaymentdetailId
     * @return
     */
    public VEleBillaccountPaymentInfo selectAttachementBusinessId(String billaccountpaymentdetailId);

    public VEleBillaccountPaymentInfo selectTeleAttachementBusinessId(String billaccountpaymentdetailId);

    /**
     * 根据ID获取统计数量
     *
     * @param info
     * @return
     */
    int countById(VEleBillaccountPaymentInfo info);

    /**
     * 根据ID获取数据
     *
     * @param paraMap
     * @return
     */
    VEleBillaccountPaymentInfo queryBeanById(Map<String, Object> paraMap);

    List<String> queryBaseresourceByPaymentMeterId(Map<String, Object> map);

    List<String> queryListResourceByPaymentId(Map<String, Object> map);

    String selectAppDegreeNum(Map<String, Object> map);

    List<Map<String, Object>> selectDegreeNum(String billaccountpaymentdetailId);

    List<Map<String, Object>> selectMeterId(String billaccountpaymentdetailId);

    Map<String, Object> selectOneElePaymentBybillamountdetailId(Map<String, Object> map);

    Map<String, Object> selectOneElePaymentBybillamountdetailIdToTele(Map<String, Object> map);

    int updateIsBack(Map<String, Object> map);

    List<VEleBillaccountPaymentInfo> queryPaymentWaringPaymentInfo(Map<String, Object> map);

    void deleteElePaymentBenchmark(Map<String, Object> paraMap);

    VEleBillaccountPaymentInfo queryElePaymentByDetailId(Map<String, Object> map);

    void updatePaymentProvince(Map<String, Object> cond);

    List<VEleBillaccountPaymentInfo> queryFinanceBillaccountPayMent(Map<String, Object> params);

    List<VEleBillaccountPaymentInfo> exportPaymentOldData(Map<String, Object> params);

    /**
     * 报账点电费查询
     */
    List<VEleBillaccountPaymentInfo> queryBillAccountPayment(Map<String, Object> params);


    List<VEleBillaccountPaymentInfo> queryEleBillaccountPaymentOptimize(Map<String, Object> map);

    /**
     * 查询审核中的待办
     * 本人审核
     */
    List<VEleBillaccountPaymentInfo> queryAuditingBillAccountPayment(Map<String, Object> map);

    /**
     * 查询审核通过缴费单
     * 用户权限范围内审核通过，不一定是本人审核
     */
    List<VEleBillaccountPaymentInfo> queryAuditedBillAccountPayment(Map<String, Object> map);

    /**查询审核通过缴费单的缴费期终
     *
     * @param params
     * @return
     */
    List<VEleBillaccountPaymentInfo> queryAuditedBillAccountbyId(Map<String, Object> map);

    List<VEleBillaccountPaymentInfo> querySpecialBillaccountPayment(Map<String, Object> params);

    List<VEleBillaccountInfo> queryBillaccountPayMentOptimize(Map<String, Object> map);

    List<VEleBillaccountInfo> queryPaymentMeterInfos(@Param("idList") List<String> idList);

    List<VEleBillaccountPaymentInfo> queryFinanceBillaccountPayMentBetter(Map<String, Object> params);

    List<VEleBillaccountInfo> queryBillaccountPayMentOptimize2(Map<String, Object> map);

    Integer selectElePaymentVersionCode(@Param("id") String id);

    Integer selectRentPaymentVersionCode(@Param("id") String id);

    Integer selectTelePaymentVersionCode(@Param("id") String id);

    Integer selectElecontractVersionCode(@Param("id") String id);

    Integer selectTelecontractVersionCode(@Param("id") String id);

    Integer selectRentcontractVersionCode(@Param("id") String id);

    Integer selectRentcontractGHVersionCode(@Param("id") String id);

    Integer selectVerificationVersionCode(@Param("id") String id);

    Integer selectTwrOilSummaryVersionCode(@Param("id") String id);

    Integer selectLoanVersionCode(@Param("id") String id);

    int checkElePaymentNextAuditUser(@Param("id") String id, @Param("nextAuditUser") String nextAuditUser);

    int checkTelePaymentNextAuditUser(@Param("id") String id, @Param("nextAuditUser") String nextAuditUser);

    int checkRentPaymentNextAuditUser(@Param("id") String id, @Param("nextAuditUser") String nextAuditUser);

    int checkElecontractNextAuditUser(@Param("id") String id, @Param("nextAuditUser") String nextAuditUser);

    int checkTElecontractNextAuditUser(@Param("id") String id, @Param("nextAuditUser") String nextAuditUser);

    int checkRentcontractNextAuditUser(@Param("id") String id, @Param("nextAuditUser") String nextAuditUser);

    int checkRentcontractGHNextAuditUser(@Param("id") String id, @Param("nextAuditUser") String nextAuditUser);

    int checkEleVerificationNextAuditUser(@Param("id") String id, @Param("nextAuditUser") String nextAuditUser);

    int checkEleLoanNextAuditUser(@Param("id") String id, @Param("nextAuditUser") String nextAuditUser);

    int checkTwrAccountSummaryNextAuditUser(@Param("id") String id, @Param("nextAuditUser") String nextAuditUser);

    int checkTwrOilSummaryNextAuditUser(@Param("id") String id, @Param("nextAuditUser") String nextAuditUser);

    int updateElePaymentVersionCode(@Param("billaccountpaymentdetailId") String billaccountpaymentdetailId);

    int updateRentPaymentVersionCode(@Param("paymentId") String paymentId);

    int updateElecontractVersionCode(@Param("contractId") String contractId);

    int updateRentcontractVersionCode(@Param("contractId") String contractId);

    int updateVerificationVersionCode(@Param("verificationId") String verificationId);

    int updateTwrOilSummaryVersionCode(@Param("billSummaryId") String verificationId);

    int updateLoanVersionCode(@Param("loanId") String loanId);

    List<String> queryUserLoginNameByRoleCodes(List<String> list);

    String setlectContractIdByRentContractId(@Param("rentcontractId") String rentcontractId);

    String selectPregNameByUserId(@Param("userId") String userId);

    List<String> selectAttachmentIds(String businessId);

    List<ElePaymentBenchmark> queryPaybenchmark(String billaccountpaymentdetailId);

    List<DatBaseresource> queryBaseresourceState(Map<String,Object> param);

    List<PaymentInfoDto> queryPaymentInfoById(@Param("billaccountpaymentdetailId") String billaccountpaymentdetailId);

    List<PaymentInfoDto> queryVerificationInfoById(@Param("billaccountpaymentdetailId") String billaccountpaymentdetailId);

    /**
     * 根据缴费单id查分摊比例
     *
     * @param billaccountpaymentdetailIdList
     * @return
     */
    List<VEleBillaccountPaymentInfo> queryCmccRatioByBillaccountpaymentdetailIds(@Param("billaccountpaymentdetailIdList") List<String> billaccountpaymentdetailIdList);

    Long queryCountByBaseresourceName(String baseresourceCode);
    List<String> queryPaymentCodeByBaseresourceName(String baseresourceCode);
}