package com.xunge.dao.system.data;

import com.xunge.model.system.data.DataCollect;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * @Author: huang<PERSON><PERSON>
 * @Data:2024/3/12 10:29
 */
public interface DataCollectMapper {
    /**
     * 按名称、编码查询数据集列表
     * @param map
     * @return
     */
    List<DataCollect>queryDataCollectList(Map<String, Object> map);
    List<DataCollect>queryDataCollectDataNameList(Map<String, Object> map);
    DataCollect queryDataCollectById(String dataId);

    /**
     * 添加数据集
     */
    int insertDataCollectList(Map<String, Object> map);

    /**
     * 根据主键删除数据集
     */
    int deleteDataCollectById(Map<String, Object> map);

    /**
     * 根据数据集id修改数据集信息
     */
    int updateDataCollect(Map<String, Object> map);

    /**
     * 根据数据集id修改状态
     */
    int updateDataCollectStatusById(@Param("status")int status,@Param("dataIds")List<String>dataIds);

    Map<String, Object> queryMaxId(Map<String, Object> param);

}
