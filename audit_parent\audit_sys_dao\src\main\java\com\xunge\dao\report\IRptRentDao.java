package com.xunge.dao.report;

import com.xunge.model.report.RptRentBillaccountVO;
import com.xunge.model.report.RptRentBillamountVO;
import com.xunge.model.report.RptRentPaymentVO;
import com.xunge.model.selfrent.RptPrvRentPaymentRedundancyMon;

import java.util.List;
import java.util.Map;

public interface IRptRentDao {

    public List<RptRentBillaccountVO> queryRptRentBillaccount(Map<String, Object> paraMap);

    public List<RptRentBillaccountVO> queryRptRentBillaccountByPrvId(Map<String, Object> paraMap);

    public List<RptRentBillaccountVO> queryRptRentBillaccountByPregId(Map<String, Object> paraMap);

    public List<RptRentBillamountVO> queryRptRentBillamount(Map<String, Object> paraMap);

    public List<RptRentBillamountVO> queryRptRentBillamountByPrvId(Map<String, Object> paraMap);

    public List<RptRentBillamountVO> queryRptRentBillamountByPregId(Map<String, Object> paraMap);

    public List<RptRentPaymentVO> queryRptRentPayment(Map<String, Object> paraMap);

    public List<RptRentPaymentVO> queryRptRentPaymentByPrvId(Map<String, Object> paraMap);

    public List<RptRentPaymentVO> queryRptRentPaymentByPregId(Map<String, Object> paraMap);

    public List<RptPrvRentPaymentRedundancyMon> queryRedundancyById(Map<String, Object> paraMap);

}
