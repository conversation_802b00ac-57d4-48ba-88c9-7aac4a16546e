package com.xunge.dao.towerrent.accountsummary.impl;

import com.xunge.core.page.Page;
import com.xunge.dao.AbstractBaseDao;
import com.xunge.dao.towerrent.accountsummary.ITwrAccountsummaryVODao;
import com.xunge.filter.PageInterceptor;
import com.xunge.model.costcenter.CostCenterVO;
import com.xunge.model.costcenter.SmapUserOrgVO;
import com.xunge.model.selfrent.billamount.BillamountLogVO;
import com.xunge.model.system.user.SysUserVO;
import com.xunge.model.towerrent.accountsummary.*;
import com.xunge.model.towerrent.contract.TwrContract;
import com.xunge.model.towerrent.settlement.BillCheckPraceFeeRentExportVO;
import com.xunge.model.towerrent.settlement.TowerAndMobileBillConfirmVO;
import com.xunge.model.towerrent.settlement.TowerPaymentBillVO;
import com.xunge.model.towerrent.settlement.TwrImageCheckMeterVO;

import java.util.List;
import java.util.Map;

public class TwrAccountsummaryDaoImpl extends AbstractBaseDao implements ITwrAccountsummaryVODao {

    final String TwrAccountsummaryNamespace = "com.xunge.dao.towerrent.accountsummary.TwrAccountsummaryVODao.";
    final String TowerMobilerBillbalanceConfirmVOMapper = "com.xunge.dao.TowerMobilerBillbalanceConfirmVOMapper.";
    final String TwrSupplierVoMapper = "com.xunge.dao.towerrent.supplier.TwrSupplierVoMapper.";

    @Override
    public int deleteByPrimaryKey(String accountsummaryId) {
        return 0;
    }

    @Override
    public int insert(TwrAccountsummaryVO record) {
        // TODO Auto-generated method stub
        return 0;
    }

    @Override
    public int insertSelective(TwrAccountsummaryVO record) {
        return this.getSqlSession().insert(TwrAccountsummaryNamespace + "insertSelective", record);
    }

    @Override
    public TwrAccountsummaryVO selectByPrimaryKey(String accountsummaryId) {
        return this.getSqlSession().selectOne(TwrAccountsummaryNamespace + "selectByPrimaryKey", accountsummaryId);
    }

    @Override
    public int updateByPrimaryKeySelective(TwrAccountsummaryVO record) {
        // TODO Auto-generated method stub
        return this.getSqlSession().update(TwrAccountsummaryNamespace + "updateByPrimaryKeySelective", record);
    }

    @Override
    public int updateByPrimaryKey(TwrAccountsummaryVO record) {
        // TODO Auto-generated method stub
        return this.getSqlSession().update(TwrAccountsummaryNamespace + "updateByPrimaryKey", record);
    }

    @Override
    public List<Map<String, Object>> queryTwrAccountsummaryMapListByCondition(Map<String, Object> params) {
        return this.getSqlSession().selectList(TwrAccountsummaryNamespace + "queryTwrAccountsummeryMapListByCondition", params);
    }

    @Override
    public Page<Map<String, Object>> queryTwrAccountsummaryMapPage(Map<String, Object> paramMap) {
        PageInterceptor.startPage(Integer.parseInt(paramMap.get("pageNumber").toString()), Integer.parseInt(paramMap.get("pageSize").toString()));
        this.getSqlSession().selectList(TwrAccountsummaryNamespace + "queryTwrAccountsummeryMapListByCondition", paramMap);
        return PageInterceptor.endPage();
    }

    @Override
    public Page<List<Map<String, Object>>> queryTowerSupplierName(Map<String, Object> paramMap) {
        PageInterceptor.startPage(Integer.parseInt(paramMap.get("pageNumber").toString()), Integer.parseInt(paramMap.get("pageSize").toString()));
        this.getSqlSession().selectList(TwrSupplierVoMapper + "queryTowerSupplierName", paramMap);
        return PageInterceptor.endPage();
    }

    @Override
    public int deleteTwrAccountsummaryByCondition(Map<String, Object> params) {
        return this.getSqlSession().delete(TwrAccountsummaryNamespace + "deleteTwrAccountsummaryByCondition", params);
    }

    @Override
    public int selectSubmitedAccountsummaryCountByCondition(Map<String, Object> params) {
        return this.getSqlSession().selectOne(TwrAccountsummaryNamespace + "selectSubmitedAccountsummaryCountByCondition", params);
    }

    @Override
    public List<Map<String, Object>> queryTwrAccountsummeryMapListByCondition1(Map<String, Object> params) {
        return this.getSqlSession().selectList(TwrAccountsummaryNamespace + "queryTwrAccountsummeryMapListByCondition1", params);
    }

    @SuppressWarnings("unchecked")
    @Override
    public Page<TwrAccountsummaryVO> queryPageAccountsummery(Map<String, Object> params) {
        PageInterceptor.startPage(Integer.parseInt(params.get("pageNumber").toString()), Integer.parseInt(params.get("pageSize").toString()));
        this.getSqlSession().selectList(TwrAccountsummaryNamespace + "queryPageTwrAccountsummery", params);
        return PageInterceptor.endPage();
    }

    @Override
    public TwrAccountsummaryVO selectByAccountId(Map<String, Object> params) {
        return this.getSqlSession().selectOne(TwrAccountsummaryNamespace + "queryAccountsummeryById", params);
    }

    @Override
    public List<TwrAccountsummaryPushVO> queryAccountsummaryByIds(Map<String, Object> map) {
        // TODO Auto-generated method stub
        return this.getSqlSession().selectList(TwrAccountsummaryNamespace + "queryAccountsummaryByIds", map);
    }

    @Override
    public List<TwrAccountsummaryPushVO> queryAccountsummaryByIdsTwr(Map<String, Object> map) throws Exception {
        // TODO Auto-generated method stub
        return this.getSqlSession().selectList(TwrAccountsummaryNamespace + "queryAccountsummaryByIdsTwr", map);
    }

    @Override
    public List<TowerAndMobileBillConfirmVO> selectTowerAndMobileConfirmBill(Map<String, Object> paraMap) {
        return this.getSqlSession().selectList(TowerMobilerBillbalanceConfirmVOMapper + "selectTowerAndMobileConfirm", paraMap);
    }

    @Override
    public List<BillCheckPraceFeeRentExportVO> queryRentPaymentByDate(Map<String, Object> map) {
        return this.getSqlSession().selectList(TowerMobilerBillbalanceConfirmVOMapper + "queryRentPaymentByDate", map);
    }

    @Override
    public int updatePushdState(Map<String, Object> map) {
        return this.getSqlSession().update(TwrAccountsummaryNamespace + "updatePushdState", map);
    }

    @Override
    public int insertSysLog(BillamountLogVO vo) {
        // TODO Auto-generated method stub
        return this.getSqlSession().update(TwrAccountsummaryNamespace + "installSysLog", vo);
    }

    @Override
    public int updateAccountsummaryByCondition(Map<String, Object> params) {
        return this.getSqlSession().delete(TwrAccountsummaryNamespace + "updateAccountsummaryByCondition", params);
    }

    @Override
    public List<TwrAccountsummaryPushVO> queryAccountsummaryByCondition(Map<String, Object> map) throws Exception {
        return this.getSqlSession().selectList(TwrAccountsummaryNamespace + "queryAccountsummaryByCondition", map);
    }

    @Override
    public List<TwrAccountsummaryPushVO> queryAccountsummaryPage(Map<String, Object> map) throws Exception {
        return this.getSqlSession().selectList(TwrAccountsummaryNamespace + "queryAccountsummaryPage", map);
    }

    @Override
    public int queryAccountsummaryByConditionCount(Map<String, Object> map) throws Exception {
        return this.getSqlSession().selectOne(TwrAccountsummaryNamespace + "queryAccountsummaryByConditionCount", map);
    }

    @Override
    public TwrAccountsummaryVO queryBeanById(Map<String, Object> params) {
        return this.getSqlSession().selectOne(TwrAccountsummaryNamespace + "queryBeanById", params);
    }

    @Override
    public int queryBeanCountById(Map<String, Object> params) {
        return this.getSqlSession().selectOne(TwrAccountsummaryNamespace + "queryBeanCountById", params);
    }

    @Override
    public String selectOrgCodeBySmapId(Map<String, Object> params) {
        return this.getSqlSession().selectOne(TwrAccountsummaryNamespace + "selectOrgCodeBySmapId", params);
    }

    @Override
    public List<SmapUserOrgVO> selectCostCenterByOrgCode(Map<String, Object> params) {
        return this.getSqlSession().selectList(TwrAccountsummaryNamespace + "selectCostCenterByOrgCode", params);
    }

    @Override
    public Page<CostCenterVO> selectCostCenterPage(Map<String, Object> params) {
        PageInterceptor.startPage(Integer.parseInt(params.get("pageNumber").toString()), Integer.parseInt(params.get("pageSize").toString()));
        this.getSqlSession().selectList(TwrAccountsummaryNamespace + "selectCostCenterPage", params);
        return PageInterceptor.endPage();
    }

    @Override
    public List<SysUserVO> querySysUserByRole(Map<String, Object> params) {
        return this.getSqlSession().selectList(TwrAccountsummaryNamespace + "querySysUserByRole", params);
    }

    @Override
    public List<TowerPaymentBillVO> selectTowerPaymentBill(Map<String, Object> map) {
        return this.getSqlSession().selectList(TowerMobilerBillbalanceConfirmVOMapper + "selectTowerPaymentBill", map);
    }

    @Override
    public String qryMaxPushDateCompanyType(Map<String, Object> map) {
        return this.getSqlSession().selectOne(TwrAccountsummaryNamespace + "qryMaxPushDateCompanyType", map);
    }

    @Override
    public Page<TwrContract> findListByEntity(TwrContract twrContract, Map<String, Object> params) {
        PageInterceptor.startPage(Integer.parseInt(params.get("pageNumber").toString()), Integer.parseInt(params.get("pageSize").toString()));
        this.getSqlSession().selectList("com.xunge.dao.towerrent.contract.TwrContractMapper." + "findListByEntity", params);
        return PageInterceptor.endPage();
    }

    @Override
    public int updatePushdStateByPushCode(Map<String, Object> map) {
        return this.getSqlSession().update(TwrAccountsummaryNamespace + "updatePushdStateByPushCode", map);
    }

    @Override
    public int insertPunshDetails(List<TwrPunshDetails> pds) {
        return this.getSqlSession().insert(TwrAccountsummaryNamespace + "insertPunshDetails", pds);
    }

    @Override
    public int updatePunshDetails(Map<String, Object> map) {
        return this.getSqlSession().update(TwrAccountsummaryNamespace + "updatePunshDetails", map);
    }

    @Override
    public int deletePunshDetails(Map<String, Object> map) {
        return this.getSqlSession().delete(TwrAccountsummaryNamespace + "deletePunshDetails", map);
    }

    @Override
    public List<TwrPunshDetails> queryPunshDetails(Map<String, Object> map) {
        return this.getSqlSession().selectList(TwrAccountsummaryNamespace + "queryPunshDetails", map);
    }

    @Override
    public TwrPunshDetails queryAdjustDetail(Map<String, Object> map) {
        return this.getSqlSession().selectOne(TwrAccountsummaryNamespace + "queryAdjustDetail", map);
    }

    @Override
    public int queryTowerAbstractLogo(String accountSummaryId) {
        return this.getSqlSession().selectOne(TwrAccountsummaryNamespace + "queryTowerAbstractLogo", accountSummaryId);
    }

    @Override
    public int queryRoomAbstractLogo(String accountSummaryId) {
        return this.getSqlSession().selectOne(TwrAccountsummaryNamespace + "queryRoomAbstractLogo", accountSummaryId);
    }

    @Override
    public int queryTinyAbstractLogo(String accountSummaryId) {
        return this.getSqlSession().selectOne(TwrAccountsummaryNamespace + "queryTinyAbstractLogo", accountSummaryId);
    }

    @Override
    public int queryNonstandAbstractLogo(String accountSummaryId) {
        return this.getSqlSession().selectOne(TwrAccountsummaryNamespace + "queryNonstandAbstractLogo", accountSummaryId);
    }

    @Override
    public int insertTwrRefinanceList(List<TwrAccountsummaryRefinance> twrAccountsummaryRefinances) {
        return this.getSqlSession().insert(TwrAccountsummaryNamespace + "insertTwrRefinanceList", twrAccountsummaryRefinances);
    }

    @Override
    public int deleteTwrRefinance(Map<String, Object> map) {
        return this.getSqlSession().delete(TwrAccountsummaryNamespace + "deleteTwrRefinance", map);
    }

    @Override
    public List<TwrAccountsummaryRefinance> selectTwrRefinanceList(String accountsummaryId) {
        return this.getSqlSession().selectList(TwrAccountsummaryNamespace + "selectTwrRefinanceList", accountsummaryId);
    }

    @Override
    public int checkSecondSummaryRecfinance(List<String> ids) {
        return this.getSqlSession().selectOne(TwrAccountsummaryNamespace + "checkSecondSummaryRecfinance", ids);
    }

    @Override
    public int selectRecFinaceNum(List<String> ids) {
        return this.getSqlSession().selectOne(TwrAccountsummaryNamespace + "selectRecFinaceNum", ids);
    }

    @Override
    public int checkActivityCodes(List<String> codeList) {
        return this.getSqlSession().selectOne(TwrAccountsummaryNamespace + "checkActivityCodes", codeList);
    }

    @Override
    public List<TwrImageCheckMeterVO> queryTowerImageCheckList(TwrImageCheckMeterVO imageCheckMeterVO) {
        return this.getSqlSession().selectList(TwrAccountsummaryNamespace + "queryTowerImageCheckList", imageCheckMeterVO);
    }

    @Override
    public List<TwrOilSummaryPushDetailVO> querySummaryPushDetailBySummaryCode(String accountSummaryCode) {
        return this.getSqlSession().selectList(TwrAccountsummaryNamespace + "querySummaryPushDetailBySummaryCode", accountSummaryCode);
    }

    @Override
    public int insertTwrOilSummaryPushDetail(TwrOilSummaryPushDetailVO twrOilSummaryPushDetailVo) {
        return this.getSqlSession().insert(TwrAccountsummaryNamespace + "insertTwrOilSummaryPushDetail", twrOilSummaryPushDetailVo);
    }

    @Override
    public List<TwrAccountsummaryPushVO> queryTwrAccountSummaryListByPushCode(String pushCode) {
        return this.getSqlSession().selectList(TwrAccountsummaryNamespace + "queryTwrAccountSummaryListByPushCode", pushCode);
    }

    @Override
    public TwrOilSummaryPushDetailVO querySummaryPushDetailByExecuteLink(String twrAccountSummaryCode) {
        return this.getSqlSession().selectOne(TwrAccountsummaryNamespace + "querySummaryPushDetailByExecuteLink", twrAccountSummaryCode);
    }

    @Override
    public TwrOilSummaryPushDetailVO querySummaryPushDetailByReturnInfo(String twrAccountSummaryCode) {
        return this.getSqlSession().selectOne(TwrAccountsummaryNamespace + "querySummaryPushDetailByReturnInfo", twrAccountSummaryCode);
    }

    @Override
    public int updatePushDetailsState(String accountsummaryCode) {
        return this.getSqlSession().update(TwrAccountsummaryNamespace + "updatePushDetailsState", accountsummaryCode);
    }

    @Override
    public int deleteTwrAccountSummaryActRuTask(String businessKey) {
        return this.getSqlSession().delete(TwrAccountsummaryNamespace + "deleteTwrAccountSummaryActRuTask", businessKey);
    }
}
