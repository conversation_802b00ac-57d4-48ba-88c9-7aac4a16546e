package com.xunge.dao.selfelec;

import com.xunge.model.selfelec.*;
import com.xunge.model.selfelec.billaccount.BillaccountQueryDto;
import com.xunge.model.selfelec.vo.EleBillUnpaidVO;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.cursor.Cursor;

import java.util.List;
import java.util.Map;

public interface VEleBillaccountMapper {

    int countByExample(VEleBillaccountExample example);


    int deleteByExample(VEleBillaccountExample example);


    int insert(VEleBillaccount record);


    int insertSelective(VEleBillaccount record);


    List<VEleBillaccount> selectByExample(VEleBillaccountExample example);

    List<VEleBillaccount> selectByExample(Map<String, Object> map);

    /**
     * 根据报账点/合同/资源/电表信息查询
     *
     * @param map
     * @return
     * <AUTHOR>
     */
    List<VEleBillaccount> queryBillaccountByConditions(Map<String, Object> map);

    List<VEleBillaccount> queryBillaccount(BillaccountQueryDto billaccountQueryDto);

    /**
     * 报账点电费录入页面查询
     */
    List<VEleBillaccount> queryBillaccountForPayment(Map<String, Object> map);


    List<VEleBillaccount> queryBillaccountByCondition(Map<String, Object> map);

    /**
     * 根据ID查询
     *
     * @param vEleBillaccount
     * @return
     */
    VEleBillaccount queryBillaccountByBeanId(VEleBillaccount vEleBillaccount);


    int updateByExampleSelective(@Param("record") VEleBillaccount record, @Param("example") VEleBillaccountExample example);


    int updateByExample(@Param("record") VEleBillaccount record, @Param("example") VEleBillaccountExample example);

    /**
     * 批量插入
     *
     * @param list
     * @return
     * <AUTHOR>
     */
    int batchInsert(List<VEleBillaccount> list);

    /**
     * 查询数据库中最大数值的code
     *
     * @param param
     * @return
     * <AUTHOR>
     */
    Map<String, Object> queryMaxCode(Map<String, Object> param);

    /**
     * id更新审核状态
     *
     * @param map
     * <AUTHOR>
     */
    void updateById(Map<String, Object> map);

    /**
     * 查询报账点总数
     *
     * @param map
     * <AUTHOR>
     */
    int selectBillaccountNum(Map<String, Object> map);

    Map<String, Object> queryContractMaxCode(Map<String, Object> param);

    /**
     * 查询电费报账点信息及与之相关联的合同、资源点、电表信息（导出）
     *
     * @param example
     * @return
     */
    List<VEleBillaccount> selectBillaccountForExport(Map<String, Object> map);

    List<VEleBillaccount> queryBillaccountForExport(Map<String, Object> map);
    List<String> queryBillaccountWithMeterCode(Map<String, Object> map);


    /**
     *
     *通过资源编码和名称和5G和普服标识查询特殊报账点编码来导出，其他的条件通过导出的sql来过滤
     * @param map
     * @return
     */
    List<String> querySpecialBillaccountCodes(Map<String, Object> map);

    /**
     * 导出报账点信息作为缴费模板
     *
     * @param map
     * @return
     */
    List<VEleBillaccount> queryBillaccountByIds(Map<String, Object> map);

    /**
     * 根据报账点、合同、资源信息查询
     *
     * @param paraMap
     * @return
     * <AUTHOR>
     */
    List<VEleBillaccount> querySpecialBillaccountByConditions(Map<String, Object> paraMap);
    List<VEleBillaccount> queryNewEnergyBillaccountByConditions(Map<String, Object> paraMap);

    /**
     * 报账点查询，报账点维护页面
     */
    List<VEleBillaccount> querySpecialBillaccount(BillaccountQueryDto billaccountQueryDto);

    /**
     * 报账点电费录入页面
     */
    List<VEleBillaccount> querySpecialBillAccountForPayment(Map<String, Object> paraMap);

    /**
     * 查询电费特殊报账点信息及与之相关联的合同、资源点信息（导出）
     *
     * @param map
     * @return
     */
    List<VEleBillaccount> querySpecialBillaccountForExport(Map<String, Object> map);
    List<VEleBillaccount> exportSpecialBillAccount(Map<String, Object> map);

    List<VEleBillaccount> getRegion(Map<String, Object> map);

    List<VEleBillaccount> getExistBillaccount(Map<String, Object> map);

    void insertData(VEleBillaccount billaccount);

    VEleBillaccount queryBillaccountTypeByIds(Map<String, Object> paraMap);

    List<ElecontractRelationHistory> selContractHistory(Map<String, Object> map);

    List<EleResourceRelationHistory> queryResourceHistory(Map<String, Object> map);

    List<EleMeterRelationHistory> queryEleMeterHistory(Map<String, Object> map);

    List<EleResourceMeterRelationHistory> queryEleResourceMeterHistory(Map<String, Object> map);

    /**
     * 根据报账点/合同/资源/电表信息查询报账点id
     *
     * @param map
     * @return
     * <AUTHOR>
     */
    List<String> queryBillAccountIdByConditions(Map<String, Object> map);

    /**
     * 根据报账点id查询需要导出的报账点信息和关联合同资源信息
     *
     * @param idList
     * @return
     */
    List<EleBillUnpaidVO> queryBillAccountById(@Param("idList") List<String> idList, @Param("isSpecial") Integer isSpecial);

    /**
     * 根据成本中心值查询
     *
     * @param map
     * @return
     */
    List<VEleBillaccount> getExistCostCenter(Map<String, Object> map);

    /**
     * 通过报账点的id查询动环站点的站点编码、站点名称、站点类型编码
     * @param map
     * @return
     */
    List<EleBillaccountpowerdata> queryBillaccountpowerdataWithBillaccountId(Map<String, Object> map);


	/** 
	* @Description: TODO (这里用一句话描述这个类的作用)
	* <AUTHOR>   
	* @date 2025年6月12日 下午4:03:16 
	* @param map
	*/ 
    Cursor<VEleBillaccount> queryBillaccountForExportV1(Map<String, Object> map);

}
