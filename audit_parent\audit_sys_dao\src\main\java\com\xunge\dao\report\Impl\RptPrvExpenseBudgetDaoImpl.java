package com.xunge.dao.report.Impl;

import com.xunge.core.page.Page;
import com.xunge.dao.AbstractBaseDao;
import com.xunge.dao.report.IRptPrvExpenseBudgetDao;
import com.xunge.filter.PageInterceptor;
import com.xunge.model.report.RptPrvExpenseBudgetApplyLogVO;
import com.xunge.model.report.RptPrvExpenseBudgetApplyVO;
import com.xunge.model.report.RptPrvExpenseBudgetStateVO;
import com.xunge.model.report.RptPrvExpenseBudgetVO;

import java.util.List;
import java.util.Map;

/**
 * @创建人 LiangCheng
 * @创建时间 2018/12/12 0012
 * @描述：
 */
public class RptPrvExpenseBudgetDaoImpl extends AbstractBaseDao implements IRptPrvExpenseBudgetDao {

    final String Namespace = "com.xunge.mapping.report.RptPrvExpenseBudgetVOMapper.";

    @Override
    public List<RptPrvExpenseBudgetVO> queryExpenseBudget(RptPrvExpenseBudgetVO rptPrvExpenseBudgetVO) {
        return this.getSqlSession().selectList(Namespace + "queryExpenseBudget", rptPrvExpenseBudgetVO);
    }

    @Override
    public List<RptPrvExpenseBudgetVO> queryExpenseBudgetTotal(RptPrvExpenseBudgetVO rptPrvExpenseBudgetVO) {
        return this.getSqlSession().selectList(Namespace + "queryExpenseBudgetTotal", rptPrvExpenseBudgetVO);
    }

    @Override
    public void saveExpenseBudget(RptPrvExpenseBudgetVO rptPrvExpenseBudgetVO) {
        this.getSqlSession().insert(Namespace + "saveExpenseBudget", rptPrvExpenseBudgetVO);
    }

    @Override
    public void editExpenseBudget(RptPrvExpenseBudgetVO rptPrvExpenseBudgetVO) {
        this.getSqlSession().update(Namespace + "editExpenseBudget", rptPrvExpenseBudgetVO);
    }

    @Override
    public List<RptPrvExpenseBudgetVO> queryPregExpenseBudget(RptPrvExpenseBudgetVO rptPrvExpenseBudgetVO) {
        return this.getSqlSession().selectList(Namespace + "queryPregExpenseBudget", rptPrvExpenseBudgetVO);
    }

    @Override
    public List<RptPrvExpenseBudgetVO> queryPregExpenseBudgetTotal(RptPrvExpenseBudgetVO rptPrvExpenseBudgetVO) {
        return this.getSqlSession().selectList(Namespace + "queryPregExpenseBudgetTotal", rptPrvExpenseBudgetVO);
    }

    @Override
    public List<RptPrvExpenseBudgetVO> queryRegExpenseBudget(RptPrvExpenseBudgetVO rptPrvExpenseBudgetVO) {
        return this.getSqlSession().selectList(Namespace + "queryRegExpenseBudget", rptPrvExpenseBudgetVO);
    }

    @Override
    public List<RptPrvExpenseBudgetVO> queryRegExpenseBudgetTotal(RptPrvExpenseBudgetVO rptPrvExpenseBudgetVO) {
        return this.getSqlSession().selectList(Namespace + "queryRegExpenseBudgetTotal", rptPrvExpenseBudgetVO);
    }

    @Override
    public List<RptPrvExpenseBudgetStateVO> queryExpenseBudgetProgressGroup(Map<String, Object> map) {
        return this.getSqlSession().selectList(Namespace + "queryExpenseBudgetProgressGroup", map);
    }

    @Override
    public List<RptPrvExpenseBudgetStateVO> queryExpenseBudgetProgressGroupTotal(Map<String, Object> map) {
        return this.getSqlSession().selectList(Namespace + "queryExpenseBudgetProgressGroupTotal", map);
    }

    @Override
    public List<RptPrvExpenseBudgetStateVO> queryExpenseBudgetProgressProvince(Map<String, Object> map) {
        return this.getSqlSession().selectList(Namespace + "queryExpenseBudgetProgressProvince", map);
    }

    @Override
    public List<RptPrvExpenseBudgetStateVO> queryExpenseBudgetProgressProvinceTotal(Map<String, Object> map) {
        return this.getSqlSession().selectList(Namespace + "queryExpenseBudgetProgressProvinceTotal", map);
    }

    @Override
    public List<RptPrvExpenseBudgetStateVO> queryExpenseBudgetProgressCity(Map<String, Object> map) {
        return this.getSqlSession().selectList(Namespace + "queryExpenseBudgetProgressCity", map);
    }

    @Override
    public List<RptPrvExpenseBudgetStateVO> queryExpenseBudgetProgressCityTotal(Map<String, Object> map) {
        return this.getSqlSession().selectList(Namespace + "queryExpenseBudgetProgressCityTotal", map);
    }

    @Override
    public void saveExpenseBudgetApply(Map<String, Object> map) {
        this.getSqlSession().insert(Namespace + "saveExpenseBudgetApply", map);
    }

    @Override
    public void editExpenseState(Map<String, Object> map) {
        this.getSqlSession().update(Namespace + "editExpenseState", map);
    }

    @Override
    public void editExpenseApplyState(Map<String, Object> map) {
        this.getSqlSession().update(Namespace + "editExpenseApplyState", map);
    }

    @Override
    public List<RptPrvExpenseBudgetApplyVO> queryExpenseBudgetApply(Map<String, Object> map) {
        return this.getSqlSession().selectList(Namespace + "queryExpenseBudgetApply", map);
    }

    @Override
    public void saveExpenseBudgetApplyLog(Map<String, Object> map) {
        this.getSqlSession().insert(Namespace + "saveExpenseBudgetApplyLog", map);
    }

    @Override
    public List<RptPrvExpenseBudgetApplyLogVO> queryExpenseBudgetApplyHistoryList(Map<String, Object> map) {
        return this.getSqlSession().selectList(Namespace + "queryExpenseBudgetApplyHistoryList", map);
    }

    @Override
    public Page<RptPrvExpenseBudgetApplyVO> queryExpenseBudgetApplyList(Map<String, Object> map, int pageNumber, int pageSize) {
        PageInterceptor.startPage(pageNumber, pageSize);
        this.getSqlSession().selectList(Namespace + "queryExpenseBudgetApplyList", map);
        return PageInterceptor.endPage();
    }

    @Override
    public String queryApplyCount(Map<String, Object> map) {
        return this.getSqlSession().selectOne(Namespace + "queryApplyCount", map);
    }

}
