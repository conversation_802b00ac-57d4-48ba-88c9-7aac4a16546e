package com.xunge.model.finance.ext.accClaim.accwsdl;

import javax.xml.bind.annotation.*;


/**
 * &lt;p&gt;OutputParameters complex type的 Java 类。
 * <p>
 * &lt;p&gt;以下模式片段指定包含在此类中的预期内容。
 * <p>
 * &lt;pre&gt;
 * &amp;lt;complexType name="OutputParameters"&amp;gt;
 * &amp;lt;complexContent&amp;gt;
 * &amp;lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&amp;gt;
 * &amp;lt;sequence&amp;gt;
 * &amp;lt;element name="ESB_FLAG" type="{http://www.w3.org/2001/XMLSchema}string"/&amp;gt;
 * &amp;lt;element name="ESB_RETURN_CODE" type="{http://www.w3.org/2001/XMLSchema}string"/&amp;gt;
 * &amp;lt;element name="ESB_RETURN_MESSAGE" type="{http://www.w3.org/2001/XMLSchema}string"/&amp;gt;
 * &amp;lt;element name="BIZ_SERVICE_FLAG" type="{http://www.w3.org/2001/XMLSchema}string"/&amp;gt;
 * &amp;lt;element name="BIZ_RETURN_CODE" type="{http://www.w3.org/2001/XMLSchema}string"/&amp;gt;
 * &amp;lt;element name="BIZ_RETURN_MESSAGE" type="{http://www.w3.org/2001/XMLSchema}string"/&amp;gt;
 * &amp;lt;element name="INSTANCE_ID" type="{http://www.w3.org/2001/XMLSchema}string"/&amp;gt;
 * &amp;lt;element name="ERRORCOLLECTION" type="{http://soa.cmcc.com/OSB_RBS_CMF_HQ_ImportBatchAccruedClaimDocSrv}ERRORCOLLECTION"/&amp;gt;
 * &amp;lt;element name="RESPONSECOLLECTION" type="{http://soa.cmcc.com/OSB_RBS_CMF_HQ_ImportBatchAccruedClaimDocSrv}RESPONSECOLLECTION"/&amp;gt;
 * &amp;lt;/sequence&amp;gt;
 * &amp;lt;/restriction&amp;gt;
 * &amp;lt;/complexContent&amp;gt;
 * &amp;lt;/complexType&amp;gt;
 * &lt;/pre&gt;
 */
@XmlRootElement(name = "OutputParameters", namespace = "")
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "OutputParameters", propOrder = {
        "esbflag",
        "esbreturncode",
        "esbreturnmessage",
        "bizserviceflag",
        "bizreturncode",
        "bizreturnmessage",
        "instanceid",
        "errorcollection",
        "responsecollection"
})
public class OutputParameters {

    @XmlElement(name = "ESB_FLAG", required = true, nillable = true)
    protected String esbflag;
    @XmlElement(name = "ESB_RETURN_CODE", required = true, nillable = true)
    protected String esbreturncode;
    @XmlElement(name = "ESB_RETURN_MESSAGE", required = true, nillable = true)
    protected String esbreturnmessage;
    @XmlElement(name = "BIZ_SERVICE_FLAG", required = true, nillable = true)
    protected String bizserviceflag;
    @XmlElement(name = "BIZ_RETURN_CODE", required = true, nillable = true)
    protected String bizreturncode;
    @XmlElement(name = "BIZ_RETURN_MESSAGE", required = true, nillable = true)
    protected String bizreturnmessage;
    @XmlElement(name = "INSTANCE_ID", required = true, nillable = true)
    protected String instanceid;
    @XmlElement(name = "ERRORCOLLECTION", required = true)
    protected ERRORCOLLECTION errorcollection;
    @XmlElement(name = "RESPONSECOLLECTION", required = true)
    protected RESPONSECOLLECTION responsecollection;

    /**
     * 获取esbflag属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getESBFLAG() {
        return esbflag;
    }

    /**
     * 设置esbflag属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setESBFLAG(String value) {
        this.esbflag = value;
    }

    /**
     * 获取esbreturncode属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getESBRETURNCODE() {
        return esbreturncode;
    }

    /**
     * 设置esbreturncode属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setESBRETURNCODE(String value) {
        this.esbreturncode = value;
    }

    /**
     * 获取esbreturnmessage属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getESBRETURNMESSAGE() {
        return esbreturnmessage;
    }

    /**
     * 设置esbreturnmessage属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setESBRETURNMESSAGE(String value) {
        this.esbreturnmessage = value;
    }

    /**
     * 获取bizserviceflag属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getBIZSERVICEFLAG() {
        return bizserviceflag;
    }

    /**
     * 设置bizserviceflag属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setBIZSERVICEFLAG(String value) {
        this.bizserviceflag = value;
    }

    /**
     * 获取bizreturncode属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getBIZRETURNCODE() {
        return bizreturncode;
    }

    /**
     * 设置bizreturncode属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setBIZRETURNCODE(String value) {
        this.bizreturncode = value;
    }

    /**
     * 获取bizreturnmessage属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getBIZRETURNMESSAGE() {
        return bizreturnmessage;
    }

    /**
     * 设置bizreturnmessage属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setBIZRETURNMESSAGE(String value) {
        this.bizreturnmessage = value;
    }

    /**
     * 获取instanceid属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getINSTANCEID() {
        return instanceid;
    }

    /**
     * 设置instanceid属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setINSTANCEID(String value) {
        this.instanceid = value;
    }

    /**
     * 获取errorcollection属性的值。
     *
     * @return possible object is
     * {@link ERRORCOLLECTION }
     */
    public ERRORCOLLECTION getERRORCOLLECTION() {
        return errorcollection;
    }

    /**
     * 设置errorcollection属性的值。
     *
     * @param value allowed object is
     *              {@link ERRORCOLLECTION }
     */
    public void setERRORCOLLECTION(ERRORCOLLECTION value) {
        this.errorcollection = value;
    }

    /**
     * 获取responsecollection属性的值。
     *
     * @return possible object is
     * {@link RESPONSECOLLECTION }
     */
    public RESPONSECOLLECTION getRESPONSECOLLECTION() {
        return responsecollection;
    }

    /**
     * 设置responsecollection属性的值。
     *
     * @param value allowed object is
     *              {@link RESPONSECOLLECTION }
     */
    public void setRESPONSECOLLECTION(RESPONSECOLLECTION value) {
        this.responsecollection = value;
    }

}
