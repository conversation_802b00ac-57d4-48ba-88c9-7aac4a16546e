package com.xunge.core.Interceptor;

import com.xunge.core.model.UserLoginInfo;
import com.xunge.core.util.PropertiesLoader;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 创建时间：2018年4月18日 下午4:47:54
 */
public class AreaToDatabaseUtil {

    private final static Logger logger = LoggerFactory.getLogger(AreaToDatabaseUtil.class);
    private static PropertiesLoader prop = null;

    public static String getDatabaseByArea() {
        //非request请求中的sql调用特殊处理一下
        UserLoginInfo loginInfo = null;
        ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (servletRequestAttributes == null) {
            logger.info("request为空，非页面请求");
        } else {
            HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
            loginInfo = (UserLoginInfo) request.getSession().getAttribute("user");
        }
        /**
         * 默认一个数据库
         */
        String prvCode = null;
        String tenna = null;
        // 获取配置文件
        prop = new PropertiesLoader("\\properties\\schema.properties");
        // 获取对应省份和db值
        String prvCodeDB = prop.getProperty("prvCode_DB_enumeration");
        // 获取默认的db
        String defaultDB = prop.getProperty("default_DB");
        if (loginInfo == null) {
            // 获取是否初始化本地化线程省份标识
            prvCode = TenantContextHolder.getTenant();
            logger.info("本地线程获取中获取：" + prvCode);
        } else {
            prvCode = TenantContextHolder.getTenant();
            if (!StringUtils.equals(prvCode, "HQ")) {
                String sessionprvCode = loginInfo.getPrv_code();
                if (StringUtils.isEmpty(prvCode)) {
                    prvCode = sessionprvCode;
                    logger.info("sesion中获取：" + prvCode);
                }
            }
            logger.info("本地线程获取中获取2：" + prvCode);
        }
        // 初始化mycat多分组和省份关系map集合
        Map<String, String> dbMapByPrv = new HashMap<String, String>();
        if (StringUtils.isNotEmpty(prvCodeDB)) {
            // 解析配置文件内容
            String[] codeDb = prvCodeDB.split("\\|");
            if (codeDb.length > 0) {
                for (String strDb : codeDb) {
                    String[] dbBykey = strDb.split(":");
                    String value = dbBykey[1];
                    String[] keys = dbBykey[0].split(",");
                    for (String key : keys) {
                        dbMapByPrv.put(key, value);
                    }
                }
            }
            tenna = dbMapByPrv.get(prvCode);
            if (StringUtils.isEmpty(tenna)) {
                tenna = defaultDB;
            }
        } else {
            tenna = defaultDB;
        }
        return tenna;
    }
}
