package com.xunge.dao.twrrent.settlement;


import com.xunge.core.page.Page;
import com.xunge.model.towerrent.settlement.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @description 铁塔账单表
 * @date 2017年7月6日 下午2:38:10
 */
public interface ITowerBillbalanceDao {
    /**
     * @param towerbillbalanceId 铁塔账单ID
     * @return
     * @description删除铁塔账单数据
     */
    public int deleteByPrimaryKey(String towerbillbalanceId);

    /**
     * @param entity 铁塔账单
     * @return
     * @description插入数据
     */
    public int insertTowerBillbalance(TowerBillbalanceVO entity);

    /**
     * @param entity 铁塔账单
     * @return
     * @description 插入不为空的数据
     */
    public int insertSelective(TowerBillbalanceVO entity);

    /**
     * @param list 租赁账单集合
     * @return
     * @description 批量插入不为空的数据
     */
    public int insertBatchSelective(List<?> list);

    /**
     * @param towerbillbalanceId 铁塔账单ID
     * @return
     * @description 查询铁塔账单
     */
    public TowerBillbalanceVO selectByPrimaryKey(String towerbillbalanceId);

    /**
     * @param entity 铁塔账单
     * @return
     * @description 更新铁塔账单不为空的数据
     */
    public int updateByPrimaryKeySelective(TowerBillbalanceVO entity);

    /**
     * @param vo 租赁账单集合
     * @return
     * @description 批量更新
     */
    public int updateBatchByPrimaryKeySelective(TowerBillbalanceVO vo);

    public int updateConfirmByPrimaryKey(TowerConfirmBillbalanceVO vo);

    /**
     * @param entity
     * @return
     * @description 更新铁塔账单
     */
    public int updateByPrimaryKey(TowerBillbalanceVO entity);

    /**
     * @param paramMap
     * @param paramMap:pageSize
     * @param paramMap:pageNumber
     * @param paramMap:prvId                 运营商地市
     * @param paramMap:pregId                运营商县区
     * @param paramMap:accountMonth          账期月份
     * @param paramMap:businessConfirmNumber 产品业务确认单编号
     * @param paramMap:towerStationCode      站点名称或者编码
     * @return Page<List < TowerBillbalanceVO>>
     * @description 查询铁塔账单分页数据
     */
    public Page<TowerBillbalanceVO> queryPageTowerBillbalance(Map<String, Object> paramMap);

    public Page<TowerBillbalanceVO> queryPageTowerBillbalanceByState(Map<String, Object> paramMap);

    /**
     * @param paramMap
     * @param paramMap:prvId                 运营商地市
     * @param paramMap:pregId                运营商县区
     * @param paramMap:accountMonth          账期月份
     * @param paramMap:businessConfirmNumber 产品业务确认单编号
     * @param paramMap:towerStationCode      站点名称或者编码
     * @return List<TowerBillbalanceVO>
     * @description 查询铁塔账单数据
     */
    public List<TowerBillbalanceVO> queryTowerBillbalance(Map<String, Object> paramMap);

    public List<TowerBillbalanceVO> queryTowerBillbalanceByState(Map<String, Object> paramMap);

    public List<TowerAndMobileBillVO> queryAccountsummaryBillbalance(Map<String, Object> paramMap);

    public List<TowerAndMobileBillVO> queryAccountsummaryBillbalanceConfig(Map<String, Object> paramMap);

    /**
     * 查询铁塔侧账单
     *
     * @param prvId
     * @return
     */
    public List<TowerBillbalanceVO> queryTowerBillbalanceByPrvId(String prvId);

    /**
     * @param paramMap
     * @param paramMap:pageSize
     * @param paramMap:pageNumber
     * @param paramMap:prvId                 运营商地市
     * @param paramMap:pregId                运营商县区
     * @param paramMap:accountMonth          账期月份
     * @param paramMap:businessConfirmNumber 产品业务确认单编号
     * @param paramMap:towerStationCode      站点名称或者编码
     * @return Page<TowerBillbalanceVO>
     * @description 查询移动账单分页数据
     * <AUTHOR>
     */
    public Page<TowerBillbalanceVO> queryPageMobileBillbalance(Map<String, Object> paramMap);

    /**
     * @param paramMap
     * @return
     * @description 查询移动账单分页数据-新综资
     */
    public Page<TowerBillbalanceVO> queryPageMobileBillbalanceConfig(Map<String, Object> paramMap);

    /**
     * @param paramMap
     * @param paramMap:prvId                 运营商地市
     * @param paramMap:pregId                运营商县区
     * @param paramMap:accountMonth          账期月份
     * @param paramMap:businessConfirmNumber 产品业务确认单编号
     * @param paramMap:towerStationCode      站点名称或者编码
     * @return List<TowerBillbalanceVO>
     * @description 查询移动账单或者铁塔数据
     * <AUTHOR>
     */
    public List<TowerBillbalanceVO> queryTowerOrMobileBillbalance(Map<String, Object> paramMap);

    public List<TowerBillbalanceVO> queryTowerOrMobileBillbalanceConfig(Map<String, Object> paramMap);

    /**
     * @return
     * @description 生成账单批量保存数据入库
     * <AUTHOR>
     */
    public int insertBatchMobileBill(List<TowerBillbalanceVO> listVo);

    public int insertBatchMobileBillConfig(List<TowerBillbalanceVO> listVo);

    /**
     * @return
     * @description 重新生成账单批量修改
     * <AUTHOR>
     */
    public int updateBatchMobileBill(TowerBillbalanceVO vo);

    /**
     * @return
     * @description 重新生成账单批量修改-新综资
     * <AUTHOR>
     */
    public int updateBatchMobileBillConfig(TowerBillbalanceVO vo);

    /**
     * 修改移动侧账单
     *
     * @param vo
     * @return
     */
    public int updateMobileBill(TowerBillbalanceVO vo);

    public int updateMobileBillConfig(TowerBillbalanceVO vo);

    /**
     * @param paramMap
     * @return
     * @description 根据参数查询账单信息，用来判断账单是否存在
     */
    public List<TowerBillbalanceVO> queryMobileBill(Map<String, Object> paramMap);

    public List<TowerBillbalanceVO> queryMobileBillConfig(Map<String, Object> paramMap);

    public List<String> selectAccountPeroid(Map<String, Object> paramMap);

    /**
     * @param paraMap
     * @return
     * @description 根据铁塔站址编码和年月查询账单
     */
    public String queryBalance(Map<String, Object> paraMap);

    /**
     * @param paraMap
     * @return
     * @description 根据铁塔站址编码查询账单的月基准参数数据
     */
    public List<TowerBillbalanceVO> queryParameter(Map<String, Object> paraMap);

    /**
     * @param paraMap
     * @return
     * @description 批量修改账单数据导出——起租表
     */
    List<TowerBillbalanceVO> exportModify(Map<String, Object> paraMap);

    /**
     * @param paraMap
     * @return
     * @description 批量修改账单数据导出_新综资
     */
    List<TowerBillbalanceVO> exportModifyConfig(Map<String, Object> paraMap);

    /**
     * 生成汇总报账数据，将汇总过的账单数据添加汇总编码
     *
     * @param map
     * @return
     * <AUTHOR>
     */
    public int updateBillSetSumcode(Map<String, Object> map);

    /**
     * 删除汇总报账数据，将汇总过的账单数据汇总编码字段清空
     *
     * @param map
     * @return
     * <AUTHOR>
     */
    public int updateBillSumcodeToNull(Map<String, Object> map);

    public List<TowerBillbalanceVO> selectById(Map<String, Object> map);

    public List<TowerBillbalanceVO> selectByIdConfig(Map<String, Object> map);

    public int insertBillbalanceChanges(List<MobileBillbalanceChangeVO> tbList);

    List<MobileBillbalanceChangeVO> queryChangeVo(Map<String, Object> map);

    int deleteChange(Map<String, Object> map);

    Page<TowerBillBalanceChargeBackVO> queryPageChargeBackList(Map<String, Object> paramMap);

    List<TowerBillBalanceChargeBackVO> queryChargeBackList(Map<String, Object> paramMap);

    int addChargeBack(TowerBillBalanceChargeBackVO chargeBackVO);

    int deleteChargeBack(Map<String, Object> paramMap);

    int updateChargeBackState(Map<String, Object> paramMap);

    List<TowerChargeBackVo> exportBillBalanceToChargeBack(Map<String, Object> paramMap);

    List<TowerChargeBackToExcelVo> exportTowerChargeBackToExcel(Map<String, Object> paramMap);

    List<String> queryHistoryDeleteIds(Map<String, Object> map);

    List<String> queryDeleteIds(Map<String, Object> map);

    int deleteByMobileId(List<String> tmp);

    List<String> queryHistoryDeleteIdsConfig(Map<String, Object> map);

    List<String> queryDeleteIdsConfig(Map<String, Object> map);

    int deleteByMobileIdConfig(List<String> tmp);

    List<TowerBillbalanceModifyVO> exportTowerBillbalanceModify(Map<String, Object> paraMap);

    List<TowerBillbalanceModifyVO> exportTowerBillbalanceModifyConfig(Map<String, Object> paraMap);

    void updateTowerRentAllFeeTaxToNull(Map<String, Object> params);

    List<TowerBillSummaryVO> queryBillBalanceSummaryList(Map<String, Object> params);

    List<TwrOilConfig> getOilConfigList();
}