package com.xunge.comm.utils;

import com.xunge.comm.ActivityParamComm;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/7/30 10:43
 */
@Slf4j
public class MapUtils {
    /**
     * 缴费map是否包含值
     *
     * @param map
     * @return
     */
    public static boolean checkAbnormalParameter(Map<String, Object> map) {
        // 示例逻辑：校验参数是否为空或包含异常值
        if (map == null || map.isEmpty()) {
            if (map == null) {
                map = new HashMap<>();
            }
            map.put(ActivityParamComm.useElecCost, 0);
            return false;
        }

        if (map.get(ActivityParamComm.useElecCost) != null
                && "1".equals(map.get(ActivityParamComm.useElecCost).toString())) {
            return true;
        } else {
            map.put(ActivityParamComm.useElecCost, 0);
            return false;
        }
    }
}
