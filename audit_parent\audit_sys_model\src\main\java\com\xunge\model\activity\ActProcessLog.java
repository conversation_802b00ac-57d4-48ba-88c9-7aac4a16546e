package com.xunge.model.activity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 流程日志entity
 *
 */
@Data
public class ActProcessLog implements Serializable {

    private static final long serialVersionUID = -8263886550328167849L;
    private Integer id; // 任务编号
    private String procDefKey; // 流程定义KEY
    private String businessTable; // 业务标题
    private String businessId; // 业务表ID
    private String procVars; // 流程变量
    private String assignee; // 当前处理人
    private String nextUserId; // 下一步处理人ID
    private Date createTime; // 创建时间
    private String procInsId; //流程实例ID
    private String comment;//审核意见
    private String procTitle;//流程标题
    private String procType;//流程类型
}
//