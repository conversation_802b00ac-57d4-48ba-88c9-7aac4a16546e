package com.xunge.dao.selfelec;


import com.xunge.model.basedata.DatBaseresource;
import com.xunge.model.selfelec.*;
import com.xunge.model.selfelec.eleverificate.EleBillaccountVerificatedetail;
import com.xunge.model.selfelec.loan.EleLoan;
import com.xunge.model.selfrent.billAccount.VPaymentVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface DatResourceUpdateLogMapper {

    Map<String, Object> queryBaseresourceState(Map<String, Object> map);

    int deleteByPrimaryKey(DatResourceUpdateLogKey key);

    int insert(DatResourceUpdateLog record);

    int insertSelective(DatResourceUpdateLog record);

    List<DatResourceUpdateLog> selectByExample(DatResourceUpdateLogExample example);

    DatResourceUpdateLog selectByPrimaryKey(DatResourceUpdateLogKey key);

    int updateByPrimaryKeySelective(DatResourceUpdateLog record);

    int updateByPrimaryKey(DatResourceUpdateLog record);

    List<DatBaseresource> selectPaymentBaseresources(String billaccountpaymentdetailId);

    List<DatBaseresource> selectVerificateBaseresources(String billaccountpaymentdetailId);

    List<DatBaseresource> selectICVerificateBaseresources(String billaccountpaymentdetailId);

    List<DatBaseresource> selectLoanBaseresources(String billaccountpaymentdetailId);

    EleBillaccountPaymentdetail selectByBillamoutdetailId(String businessId);

    EleLoan selectLoanByBillamoutdetailId(String businessId);

    EleBillaccountVerificatedetail selectVerificateByBillamoutdetailId(String businessId);

    List<DatBaseresource> selectRentPymentBaseresources(String billaccountpaymentdetailId);

    VPaymentVO queryRentPayment(String billaccountpaymentdetailId);

    List<EleBillaccountPaymentdetail> selectByBillamountId(String businessId);

    List<EleBillaccountVerificatedetail> selectByVBillamountId(String businessId);

    List<VPaymentVO> selectByRBillamountId(String businessId);

    List<NetworkInfo> querylistbyResourceIds(@Param("resourceIds") List resourceIds);

    String queryBaseresourceIdByCid(@Param("baseresourceCuid") String baseresourceCuid);

    List<DatBaseresource> queryResourceByIds(@Param("list") List<String> resourceIds);
}