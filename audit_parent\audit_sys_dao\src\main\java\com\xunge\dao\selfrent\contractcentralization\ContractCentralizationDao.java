package com.xunge.dao.selfrent.contractcentralization;

import com.xunge.core.page.Page;
import com.xunge.model.selfrent.contract.DatContractVO;
import com.xunge.model.selfrent.contract.DatSupplierVO;
import com.xunge.model.selfrent.contract.RentContractVO;
import com.xunge.model.selfrent.contractcentralization.RentContract;
import com.xunge.model.selfrent.contractcentralization.RentContractCentralizationVO;
import com.xunge.model.system.user.SysUserVO;

import java.util.List;
import java.util.Map;

/**
 * @创建人 LiXs
 * @创建时间 2018/9/20
 * @描述：
 */
public interface ContractCentralizationDao {

    /**
     * 条件查询租费大集中合同信息
     */
    public List<RentContractVO> queryRentContractList(Map<String, Object> paraMap);

    /**
     * 查询所有主合同信息
     *
     * <AUTHOR>
     */
    public Page<RentContractVO> queryRentContractVO(Map<String, Object> paraMap, int pageNumber, int pageSize);


    public List<RentContractVO> queryRentContractVoList(Map<String, Object> paraMap);

    /**
     * 查询补充协议列表
     *
     * @param paraMap
     * @return
     */
    List<RentContractVO> queryOldDatContractVOList(Map<String, Object> paraMap);

    /**
     * @description 修改租费合同审核状态  租费专用
     * <AUTHOR>
     * @date 创建时间：2017年11月7日
     */
    public int updateRentContractAuditState(Map<String, Object> maps);

    /**
     * @description 修改主合同审核状态  租费专用
     * <AUTHOR>
     * @date 创建时间：2017年11月7日
     */
    public int updateDatContractAuditState(Map<String, Object> maps);

    /**
     * 更新推送状态
     *
     * @param paraMap
     * @return
     */
    int updatePushState(Map<String, Object> paraMap);

    /**
     * description 查询大集中合同导出数据
     *
     * @param paraMap
     * @return
     */
    public List<RentContractCentralizationVO> queryRentContractCentralizationVO(Map<String, Object> paraMap);

    /**
     * 查询合同代码
     *
     * @param map
     * @return
     */
    public List<DatContractVO> checkContractCode(Map<String, Object> map);

    /**
     * 分页查询供应商信息
     *
     * @param paraMap
     * @param pageNumber
     * @param pageSize
     * @return
     */
    public Page<List<DatSupplierVO>> queryDatSupplierByPrvID(Map<String, Object> paraMap,
                                                             int pageNumber, int pageSize);

    /**
     * 根据房屋租赁合同id查询房租对象
     *
     * <AUTHOR>
     */
    public RentContractVO queryRentContractById(Map<String, Object> paraMap);

    /**
     * 根据原合同ID 查询最新的合同信息
     *
     * @param paraMap
     * @return
     */
    RentContractVO queryOldDatContractVO(Map<String, Object> paraMap);

    /**
     * 根据房屋租赁合同表中的主合同id查询主合同对象
     *
     * <AUTHOR>
     */
    public DatContractVO queryDatContractById(Map<String, Object> paraMap);

    /**
     * 根据房屋租赁合同表中的供应商id查询供应商对象
     *
     * <AUTHOR>
     */
    public DatSupplierVO queryDatSupplierById(Map<String, Object> paraMap);

    /**
     * 新增主合同信息
     *
     * <AUTHOR>
     */
    public int insertDatContractVO(DatContractVO datContractVO);

    /**
     * 新增房屋租租赁合同
     *
     * @param rentContractVO
     * @return
     * <AUTHOR>
     */
    public int insertRentContractVO(RentContractVO rentContractVO);

    /**
     * 修改主合同信息
     *
     * @param datContractVO
     * @return
     * <AUTHOR>
     */
    public int updateDatContractVO(DatContractVO datContractVO);

    /**
     * 修改主合同信息
     *
     * @param datContractVO
     * @return
     * <AUTHOR>
     */
    public int updateRelieveDatContractVO(DatContractVO datContractVO);

    /**
     * 修改房租合同信息
     *
     * @param rentContractVO
     * @return
     * <AUTHOR>
     */
    public int updateRentContractVO(RentContractVO rentContractVO);

    /**
     * 修改合同审核状态为未审核通过 推送状态为未推送 同状态为null
     *
     * @param parmMap
     * @return
     */
    public int updateDatContractState(Map<String, Object> parmMap);

    /**
     * 修改合同审核状态为未审核通过
     *
     * @param parmMap
     * @return
     */
    public int updateRentContractState(Map<String, Object> parmMap);

    /**
     * @param oldContractId
     * @return
     */
    RentContractVO selectByOldContractId(String oldContractId);

    public RentContractVO queryRentContractByDatId(Map<String, Object> paraMap);

    public Page<List<SysUserVO>> queryAllUserByRoleName(Map<String, Object> paraMap, int pageNumber, int pageSize);

    public String queryUserIdByUserSampId(String sampId);

    /**
     * 根据承办人Id，获取承办人编号和姓名
     *
     * @param userId
     * @return
     */
    public SysUserVO querySmapInfoBySmapId(String userId);

    /**
     * @参数 批量删除租费合同信息
     */
    int deleteRentContractByRentContractIds(Map<String, Object> paramMap);

    /**
     * @参数 批量删除合同信息
     */
    int deleteDatContractByContractIds(Map<String, Object> paramMap);

    public int updateByContractId(RentContractVO rentContractVO);

    public int insertRentContractInfo(RentContractVO rentContractVO);

    public RentContractVO queryByRentContractId(String rentContractId);

    public RentContractVO queryByDatContractId(String datContractId);

    public int updateByPrimaryKeySelective(RentContractVO rentContractVO);


    /**
     * @param @param paraMap    设定文件
     * @return void    返回类型
     * @throws
     * @Title: updateSupplierInfo
     * @Description: TODO(这里用一句话描述这个方法的作用)
     */

    public int updateSupplierInfo(Map<String, Object> paraMap);


    /**
     * @param @return 设定文件
     * @return List<RentContractVO>    返回类型
     * @throws
     * @Title: queryRentContractList
     * @Description: TODO(这里用一句话描述这个方法的作用)
     */

    public List<RentContractVO> queryContractList();


    /**
     * @param @param  paraMap
     * @param @return 设定文件
     * @return List<RentContractVO>    返回类型
     * @throws
     * @Title: queryRentContracts
     * @Description: TODO(这里用一句话描述这个方法的作用)
     */

    public List<RentContractVO> queryRentContracts(Map<String, Object> paraMap);


    /**
     * @param @param contractId
     * @param @param contractIds    设定文件
     * @return void    返回类型
     * @throws
     * @Title: updateComtractInfo
     * @Description: TODO(这里用一句话描述这个方法的作用)
     */

    public int updateComtractInfo(Map<String, Object> paraMap);


    /**
     * @param @param rentContractVO    设定文件
     * @return void    返回类型
     * @throws
     * @Title: updateById
     * @Description: TODO(这里用一句话描述这个方法的作用)
     */

    public int updateById(RentContractVO rentContractVO);


    /**
     * @param @param paraMap    设定文件
     * @return void    返回类型
     * @throws
     * @Title: deleteRentContractByContractIds
     * @Description: TODO(这里用一句话描述这个方法的作用)
     */

    public int deleteRentContractByContractIds(Map<String, Object> paraMap);


    /**
     * @param @param rentContractVO    设定文件
     * @return void    返回类型
     * @throws
     * @Title: insertRentContract
     * @Description: TODO(这里用一句话描述这个方法的作用)
     */

    public int insertRentContract(RentContract rentContractVO);


    /**
     * @param @param rentContractVO    设定文件
     * @return void    返回类型
     * @throws
     * @Title: updateRentContractById
     * @Description: TODO(这里用一句话描述这个方法的作用)
     */

    public int updateRentContractById(RentContract rentContractVO);

    /**
     * 检查数据是否存在
     *
     * @param paraMap
     * @return
     */
    int countById(Map<String, Object> paraMap);

    /**
     * 通过合同ID查询信息
     *
     * @param paraMap
     * @return
     */
    Map<String, Object> queryBeanById(Map<String, Object> paraMap);
}
