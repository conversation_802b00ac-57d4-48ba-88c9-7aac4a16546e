package com.xunge.dao.selfrent.billaccount;

import com.xunge.model.selfrent.billAccount.RentImageCheckMeterVO;
import com.xunge.model.selfrent.billAccount.RentPaymentVO;

import java.util.List;
import java.util.Map;

public interface IRentPaymentDao {
    /**
     * 根据报账点id查询报账点缴费记录
     *
     * @param billAccountId
     * @return
     * <AUTHOR>
     */
    public List<RentPaymentVO> queryRentPaymentByBillAccountId(String billAccountId);

    /**
     * 根据缴费id查询缴费信息
     *
     * @param paymentId
     * @return
     */
    public List<RentPaymentVO> queryRentPaymentByPaymentId(String paymentId);

    /**
     * 新增报账点缴费记录
     *
     * @param rentPaymentVO
     * @return
     * <AUTHOR>
     */
    public int insertRentPayment(RentPaymentVO rentPaymentVO);

    /**
     * 新增报账点缴费记录时间
     *
     * @param rentPaymentVO
     * @return
     * <AUTHOR>
     */
    public int insertOperateTime(RentPaymentVO rentPaymentVO);

    /**
     * 修改报账点缴费信息
     */
    public int updateRentPayment(RentPaymentVO rentPaymentVO);


    public int updatePaymentBillaccountType(Map<String,Object> map);

    /**
     * 修改缴费记录状态为已删除
     *
     * @return
     */
    public int updateStateToDelete(RentPaymentVO rentPaymentVO);

    /**
     * 根据报账点Id查询该报账点关联的机房、资源点、以及热点的缴费信息
     *
     * @param billaccountId
     * @return
     * <AUTHOR>
     */
    List<Map<String, Object>> queryRentResourcePaymentByBillaccountId(String billaccountId);

    /**
     * 根据报账点Id查询该报账点关联的铁塔的缴费信息
     *
     * @param billaccountId
     * @return
     */
    List<Map<String, Object>> queryRentTowerPaymentByBillaccountId(String billaccountId);

    /**
     * 根据报账点Id查询该报账点关联的机房、资源点、以及热点的缴费信息
     *
     * @param billaccountId
     * @return
     * <AUTHOR>
     */
    List<Map<String, Object>> queryRentResourcePaymentByBillaccountIds(String billaccountId);

    /**
     * 根据报账点Id查询该报账点关联的合同的缴费信息
     *
     * @param billaccountId
     * @return
     * <AUTHOR>
     */
    List<Map<String, Object>> queryRentContractPaymentByBillaccountIds(String billaccountId);

    /**
     * 根据报账点Id查询该报账点关联的机房、资源点、以及热点的缴费信息
     *
     * @param billaccountId
     * @return
     * <AUTHOR>
     */
    List<Map<String, Object>> queryRentResourcePaymentByBillaccountIds2(Map<String, Object> paramMap);

    /**
     * 根据paymentId查询重复图片
     * @param imageCheckMeterVO
     * @return
     */
    List<RentImageCheckMeterVO> queryImageCheckList(RentImageCheckMeterVO imageCheckMeterVO);

}