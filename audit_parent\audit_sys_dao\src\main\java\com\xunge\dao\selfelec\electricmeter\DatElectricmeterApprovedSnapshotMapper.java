package com.xunge.dao.selfelec.electricmeter;


import com.xunge.model.selfelec.electricmeter.DatElectricmeterApprovedSnapshot;
import org.apache.ibatis.annotations.Param;

/**
* <AUTHOR>
* @description 针对表【dat_electricmeter_approved_snapshot(电表信息)】的数据库操作Mapper
* @createDate 2024-08-02 15:13:58
* @Entity generator.domain.DatElectricmeterApprovedSnapshot
*/
public interface DatElectricmeterApprovedSnapshotMapper {

    int deleteByPrimaryKey(Long id);
    int deleteByMeterId(String meterId);

    int insert(DatElectricmeterApprovedSnapshot record);

    int insertSelective(DatElectricmeterApprovedSnapshot record);

    DatElectricmeterApprovedSnapshot selectByPrimaryKey(Long id);
    DatElectricmeterApprovedSnapshot selectByMeterId(String meterId);

    int updateByPrimaryKeySelective(DatElectricmeterApprovedSnapshot record);

    int updateByPrimaryKey(DatElectricmeterApprovedSnapshot record);

    /**
     * 更新快照表换表字段
     *
     * @param meterId 电表id
     */
    void updateFieldById(@Param("meterId") String meterId);
}
