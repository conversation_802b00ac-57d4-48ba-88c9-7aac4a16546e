package com.xunge.model.app.basenum;

import java.io.Serializable;
import java.util.Iterator;
import java.util.Map;
import java.util.TreeMap;

/**
 * TODO: 描述信息
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2019/5/13 10:46
 */
public class BaseEnum<E> implements Serializable {

    private final TreeMap<String, E> enumMap = new TreeMap();

    public void putEnum(E value, String name) {
        this.enumMap.put(name, value);
    }


    public Map<String, E> getAllEnum() {
        return this.enumMap;
    }


    public String getName(E value) {
        return (String) this.enumMap.get(value);
    }


    public String[] getAllNames() {
        String[] names = new String[this.enumMap.size()];
        this.enumMap.values().toArray(names);
        return names;
    }

    public E getValue(String name) {
        Object value = null;
        if (this.enumMap.containsValue(name)) {
            Iterator i = this.enumMap.keySet().iterator();
            while (i.hasNext()) {
                Object key = i.next();
                String enumName = (String) this.enumMap.get(key);
                if (enumName.equals(name)) {
                    value = key;
                    break;
                }
            }
        }
        return (E) value;
    }

}
