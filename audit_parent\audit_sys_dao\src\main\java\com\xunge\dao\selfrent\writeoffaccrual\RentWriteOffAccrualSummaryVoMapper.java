package com.xunge.dao.selfrent.writeoffaccrual;

import com.xunge.model.selfrent.writeoffaccrual.*;
import com.xunge.model.selfrent.writeoffaccrual.query.RentWriteOffAccrualSummaryDto;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.cursor.Cursor;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @ClassName: RentWriteOffAccrualSummaryVoMapper
 * @Description:
 * <AUTHOR>
 * @Date: 2023/8/23 11:03
 * @Version V1.0.0
 * @Since 1.8
 */
public interface RentWriteOffAccrualSummaryVoMapper {
    /**
     * 查询计提冲销汇总信息
     * @param writeOffAccrualSummaryDto 计提冲销汇总DTO对象
     * @return List<RentWriteOffAccrualSummaryVO>
     */
    List<RentWriteOffAccrualSummaryVO> queryRentWriteOffAccrualSummaryListByPage(@Param("dto") RentWriteOffAccrualSummaryDto writeOffAccrualSummaryDto);

    int insertRentAccrualPushDetail(List<RentAccrualPushDetail> list);

    int deletePushDetailByBillamountCode(@Param("accountsummaryCode") String accountsummaryCode);

    int updatePushDetailState(@Param("accountsummaryCode") String accountsummaryCode,@Param("pushedState") Integer pushedState,@Param("glDate") Date glDate);

    /**
     * 查询财务ID无值的计提推送明细数量
     * @param accountsummaryCode
     * @return
     */
    int queryCountExpenseIdIsNull(@Param("accountsummaryCode") String accountsummaryCode);

    /**
     * 更新计提推送明细表财务字段
     * @param accountsummaryCode
     * @param parentExpenseLine
     * @param parentExpenseLineId
     * @param oprCompanyCode
     * @param sourceDocLineNum
     * @return
     */
    int updatePushDetailExpenseInfo(@Param("accountsummaryCode") String accountsummaryCode,@Param("parentExpenseLine") String parentExpenseLine,@Param("parentExpenseLineId") String parentExpenseLineId,
    @Param("oprCompanyCode") String oprCompanyCode,@Param("sourceDocLineNum") String sourceDocLineNum);
    /**
     * 根据汇总ID查询计提冲销汇总信息
     * @param summaryId 计提冲销汇总ID
     * @return RentWriteOffAccrualSummaryVO 计提冲销汇总VO对象
     */
    RentWriteOffAccrualSummaryVO queryRentWriteOffAccrualSummaryById(@Param("summaryId") String summaryId);

    /**
     * 批量删除计提冲销汇总信息
     * @param summaryIdList 计提冲销汇总ID
     */
    void delRentWriteOffAccrualSummary(@Param("summaryIdList") List<String> summaryIdList);

    /**
     * 批量删除计提冲销明细信息
     * @param summaryIdList 计提冲销汇总ID
     */
    void delRentWriteOffAccrualDetail(@Param("summaryIdList") List<String> summaryIdList);

    /**
     * 查询满足冲销计提生成的汇总单基础数据
     * @param vo
     * @return
     */
    List<WriteOffGenerateVo> queryRentWriteOffGenerateDataSummary(WriteOffGenerateVo vo);


    /**
     * 查询满足冲销计提生成的汇总推送明细基础数据
     * @param vo
     * @return
     */
    List<WriteOffGenerateVo> queryRentWriteOffGenerateDataDetail(WriteOffGenerateVo vo);

    /**
     * 生成冲销计提时查询基础数据（累提累冲）
     * @param summaryIdList
     * @return
     */
    List<String> queryAccrualWriteOffBaseDataSummary(@Param("summaryIdList") List<String> summaryIdList);

    /**
     * 生成冲销计提时查询基础数据（差额冲销）
     * @param detailIdList
     * @return
     */
    List<WriteOffDetail> queryAccrualWriteOffBaseDataDetail(@Param("detailIdList") List<String> detailIdList);


    /**
     * 新增冲销计提汇总
     * @param vo
     * @return
     */
    int insertWriteOffSummary(RentWriteOffAccrualSummaryVO vo);

    /**
     * 新增冲销计提汇总明细
     * @param list
     * @return
     */
    int insertWriteOffDetail(List<WriteOffDetail> list);

    /**
     * 删除冲销计提汇总明细
     * @param idList
     * @return
     */
    int deleteWriteOffDetail(@Param("idList") List<String> idList);

    /**
     * 根据计提冲销汇总ID，查询冲销明细信息
     * @param writeOffAccrualSummaryDto 计提冲销汇总DTO对象
     * @return List<RentAccrualPushDetail> 计提财务推送明细集合
     */
    List<RentAccrualPushDetail> queryAccrualWriteOffSummaryDetail(@Param("dto") RentWriteOffAccrualSummaryDto writeOffAccrualSummaryDto);

    List<RentAccrualPushDetail> queryWriteOffDetailAmount(@Param("dto") RentWriteOffAccrualSummaryDto writeOffAccrualSummaryDto);
    /**
     * 根据计提冲销汇总ID，查询累计冲销金额
     * @param writeOffAccrualSummaryDto 计提冲销汇总DTO对象
     * @return List<RentAccrualPushDetail> 计提财务推送明细集合
     */
    List<RentAccrualPushDetail> getWriteOffBookAmountList(@Param("dto") RentWriteOffAccrualSummaryDto writeOffAccrualSummaryDto);

    /**
     * 根据计提冲销汇总ID，查询历史冲销金额
     * @param writeOffAccrualSummaryDto writeOffAccrualSummaryDto 计提冲销汇总DTO对象
     * @return List<RentAccrualPushDetail> 计提财务推送明细集合
     */
    List<RentAccrualPushDetail> getWriteOffOldBookAmountList(@Param("dto") RentWriteOffAccrualSummaryDto writeOffAccrualSummaryDto);

    /**
     * 查询报账人列表信息
     */
    List<String> queryReimbursementUserInfo(@Param("summaryId") String summaryId);

    /**
     * 根据ID，更新计提冲销汇总信息
     * @param writeOffAccrualSummaryDto 计提冲销汇总DTO对象
     */
    void updateRentWriteOffAccrualSummaryById(@Param("dto") RentWriteOffAccrualSummaryDto writeOffAccrualSummaryDto);

    /**
     * 根据ID，更新租费计提财务推送明细信息
     * @param rentAccrualPushDetail 租费计提财务推送明细对象
     */
    void updateRentWriteOffAccrualSummaryDetailById(@Param("detail") RentAccrualPushDetail rentAccrualPushDetail);

    /**
     * 根据ID，更新计提冲销汇总推送状态
     * @param summaryId 计提冲销汇总ID
     * @param pushState 推送状态
     * @param glDate 财务入账日期
     * @param pushErpDate 推送财务时间
     * @return int
     */
    int updateRentAccrualWriteOffSummaryById(@Param("summaryId") String summaryId, @Param("pushState") String pushState, @Param("glDate") Date glDate, @Param("pushErpDate") Date pushErpDate);

    /**
     * 根据汇总ID，更新计提冲销明细推送状态
     * @param summaryId 计提冲销汇总ID
     * @param pushState 推送状态
     * @return int
     */
    int updateRentAccrualWriteOffDetailBySummaryId(@Param("summaryId") String summaryId, @Param("pushState") int pushState);

    /**
     * 更新计提冲销汇总推送状态
     * @param rentAccrualWriteOffSummary 计提冲销汇总VO对象
     */
    void updatePushState(@Param("sum") RentWriteOffAccrualSummaryVO rentAccrualWriteOffSummary);

    /**
     * 批量刷新详情推送状态
     * @param list
     */
    void updateDetailStateByList(List<RentWriteOffAccrualSummaryRefreshDto> list);

    /**
     * 批量刷新推送状态
     * @param list
     */
    void updateRentAccrualWriteOffPushStateBatch(List<RentWriteOffAccrualSummaryRefreshDto> list);

    /**
     * 根据summaryId查询push_detail_id
     * @param summaryId
     * @return
     */
    List<String> queryWriteOffPushDetailIds(@Param("summaryId") String summaryId);

    int queryCountByWritrOffCode(@Param("summaryCode") String summaryCode);

    /**
     * 查询每条推送明细的可冲销金额，排除当前冲销明细
     * @param writeOffDetailId
     * @param pushDetailId
     * @return
     */
    BigDecimal queryCanWriteOffAmountSingle(@Param("writeOffDetailId") String writeOffDetailId,@Param("pushDetailId") String pushDetailId);


    /**
     * 批量查询
     * @param list
     * @return
     */
    List<RentWriteOffAccrualSummaryRefreshDto> queryRentWriteOffAccrualSummaryByIds(List<RentWriteOffAccrualSummaryRequest>  list);

    /**
     * 查询计提冲销汇总明细导出信息
     * @param dto
     */
    Cursor<WriteOffDetailExport> queryWriteOffAccrualDetailForExport(RentWriteOffAccrualSummaryDto dto);

    RentAccrualPushDetail queryAccrualSummaryCodesRent(@Param("summaryCode") String summaryCode);

    int updateFinanceAmountRent(@Param("financeAmount") BigDecimal financeAmount,
                                 @Param("parentExpenseLineId") String parentExpenseLineId,
                                 @Param("accountsummaryCode") String accountsummaryCode);
}
