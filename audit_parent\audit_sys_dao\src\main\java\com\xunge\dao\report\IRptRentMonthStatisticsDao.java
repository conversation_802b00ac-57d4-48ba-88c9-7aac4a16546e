package com.xunge.dao.report;

import com.xunge.model.report.RptRentMonthStatisticsVO;

import java.util.List;
import java.util.Map;

/**
 * @Auther: LinFei Li
 * @Date: 2018/12/26 11:26
 * @Description:
 */
public interface IRptRentMonthStatisticsDao {


    /**
     * 查询租费月报数据
     *
     * @param map
     * @return
     */
    List<RptRentMonthStatisticsVO> queryRentMonthStatistics(Map<String, Object> map);

    /**
     * 查询地市租费月报数据
     *
     * @param map
     * @return
     */
    List<RptRentMonthStatisticsVO> queryRentMonthStatisticsByPregId(Map<String, Object> map);


    /**
     * 查询区县租费月报数据
     *
     * @param map
     * @return
     */
    List<RptRentMonthStatisticsVO> queryRentMonthStatisticsByRegId(Map<String, Object> map);

    /**
     * 应付月报 集团
     */
    List<RptRentMonthStatisticsVO> queryRentPayableMonthStatistics(Map<String, Object> map);

    /**
     * 应付月报 省
     *
     * @param map
     * @return
     */
    List<RptRentMonthStatisticsVO> queryRentPayableMonthStatisticsByPregId(Map<String, Object> map);


    /**
     * 应付月报 地市
     *
     * @param map
     * @return
     */
    List<RptRentMonthStatisticsVO> queryRentPayableMonthStatisticsByRegId(Map<String, Object> map);


    List<RptRentMonthStatisticsVO> queryRentMonthStatisticsByPregIdCount(Map<String, Object> map);


    List<RptRentMonthStatisticsVO> queryRentMonthStatisticsByRegIdCount(Map<String, Object> map);


    List<RptRentMonthStatisticsVO> queryRentMonthStatisticsByPrvIdCount(Map<String, Object> map);


    List<RptRentMonthStatisticsVO> queryRentPayableMonthStatisticsByPrvIdCount(Map<String, Object> map);


    List<RptRentMonthStatisticsVO> queryRentPayableMonthStatisticsByPregIdCount(Map<String, Object> map);


    List<RptRentMonthStatisticsVO> queryRentPayableMonthStatisticsByRegIdCount(Map<String, Object> map);
}
