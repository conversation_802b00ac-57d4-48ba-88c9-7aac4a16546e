package com.xunge.dao.towerrent.trans.impI;

import com.xunge.core.page.Page;
import com.xunge.dao.AbstractBaseDao;
import com.xunge.dao.towerrent.trans.ITwrTransDao;
import com.xunge.filter.PageInterceptor;
import com.xunge.model.towerrent.trans.MobileRentTransChangeVO;
import com.xunge.model.towerrent.trans.MobileRentTransVO;
import com.xunge.model.towerrent.trans.TowerRentTransChange;
import com.xunge.model.towerrent.trans.TowerRentTransVO;

import java.util.List;
import java.util.Map;

/**
 * Created by liuxiao on 2019/7/25.
 */
public class TwrTransDaoImpl extends AbstractBaseDao implements ITwrTransDao {
    final String towerTransSpace = "com.xunge.dao.towerrent.TowerRentTransMapper.";

    final String towerTransChangeSpace = "com.xunge.dao.towerrent.TowerRentTransChangeMapper.";

    final String mobileTransSpace = "com.xunge.dao.towerrent.MobileRentTransMapper.";

    final String mobileTransChangeSpace = "com.xunge.dao.towerrent.MobileRentTransChangeMapper.";

    @Override
    public TowerRentTransVO getRwrRentTransInfoById(String rentInformationTowerTransId) {
        return this.getSqlSession().selectOne(towerTransSpace + "getRwrRentTransInfoById", rentInformationTowerTransId);
    }

    @Override
    public TowerRentTransVO queryTransBeanById(Map<String, Object> map) {
        return this.getSqlSession().selectOne(towerTransSpace + "queryTransBeanById", map);
    }

    @Override
    public MobileRentTransVO getMobileRentTransById(Map<String, String> paramMap) {
        return this.getSqlSession().selectOne(mobileTransSpace + "getMobileRentTransById", paramMap);
    }

    @Override
    public int updateRentTransChangeCheckState(Map<String, String> paramMap) {
        return this.getSqlSession().update(towerTransChangeSpace + "updateRentTransChangeCheckState", paramMap);
    }

    @Override
    public int updateRentTransCheckState(Map<String, String> paramMap) {
        return this.getSqlSession().update(towerTransSpace + "updateRentTransCheckState", paramMap);
    }

    @Override
    public int deleteChangeByBussId(Map<String, String> paramMap) {
        return this.getSqlSession().delete(mobileTransChangeSpace + "deleteChangeByBussId", paramMap);
    }

    @Override
    public int deleteByBussId(Map<String, String> paramMap) {
        return this.getSqlSession().delete(mobileTransSpace + "deleteByBussId", paramMap);
    }

    @Override
    public int insertTransAsTower(Map<String, String> paramMap) {
        return this.getSqlSession().insert(mobileTransSpace + "insertTransAsTower", paramMap);
    }

    @Override
    public int insertTransChangeAsTower(Map<String, String> paramMap) {
        return this.getSqlSession().insert(mobileTransChangeSpace + "insertTransChangeAsTower", paramMap);
    }

    @Override
    public List<TowerRentTransChange> getTwrTransUpdateHistory(Map<String, Object> paraMap) {
        return this.getSqlSession().selectList(towerTransChangeSpace + "getTwrTransUpdateHistory", paraMap);
    }

    @Override
    public int updateMobileRentTransByParam(Map<String, Object> changeMap) {
        return this.getSqlSession().update(mobileTransSpace + "updateMobileRentTransByParam", changeMap);
    }

    @Override
    public int insertBatchByTwrRentChange(Map<String, Object> changeMap) {
        return this.getSqlSession().insert(mobileTransChangeSpace + "insertBatchByTwrRentChange", changeMap);
    }

    @Override
    public MobileRentTransVO getMobileRentTransInfoById(Map<String, Object> param) {
        return this.getSqlSession().selectOne(mobileTransSpace + "getMobileRentTransInfoById", param);
    }

    @Override
    public MobileRentTransVO queryMobileTransBeanById(Map<String, Object> param) {
        return this.getSqlSession().selectOne(mobileTransSpace + "queryMobileTransBeanById", param);
    }

    @Override
    public List<MobileRentTransChangeVO> getMobileChangeHistory(Map<String, String> paramMap) {
        return this.getSqlSession().selectList(mobileTransChangeSpace + "getMobileChangeHistory", paramMap);
    }

    @Override
    public int mobileChangeCheckStateUpdate(Map<String, String> map) {
        return this.getSqlSession().update(mobileTransChangeSpace + "mobileChangeCheckStateUpdate", map);
    }

    @Override
    public int mobileTransCheckStateUpdate(Map<String, String> map) {
        return this.getSqlSession().update(mobileTransSpace + "mobileTransCheckStateUpdate", map);
    }

    @Override
    public Page<TowerRentTransChange> getTwrTransUpdateHistory(Map<String, Object> paraMap, int pageNum, int pageSize) {
        PageInterceptor.startPage(pageNum, pageSize);
        this.getSqlSession().selectList(towerTransChangeSpace + "getTwrTransUpdateHistory", paraMap);
        return PageInterceptor.endPage();
    }

    @Override
    public Page<MobileRentTransChangeVO> getMobileChangeHistory(Map<String, Object> map, int pageNum, int pageSize) {
        PageInterceptor.startPage(pageNum, pageSize);
        this.getSqlSession().selectList(mobileTransChangeSpace + "getMobileChangeHistory", map);
        return PageInterceptor.endPage();
    }
}
