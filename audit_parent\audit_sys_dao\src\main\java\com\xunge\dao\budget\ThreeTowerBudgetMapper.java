package com.xunge.dao.budget;

import com.xunge.model.budget.ThreeTowerBudgetReport;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ThreeTowerBudgetMapper {

    int insertBatchThreeTower(@Param("list") List<ThreeTowerBudgetReport> list);

    List<ThreeTowerBudgetReport> queryThreeTowerBudgetData(@Param("prvId") String prvId, @Param("flowType") Integer flowType,
                                                           @Param("workOrderId") String workOrderId,@Param("auditInfoId") String auditInfoId);

    int deleteThreeTowerBudgetData(@Param("workOrderId") String workOrderId,@Param("flowType") Integer flowType);

    int updateThreeTowerBudgetData(ThreeTowerBudgetReport threeTowerBudgetReport);

    List<ThreeTowerBudgetReport> queryBudgetGenerateData(@Param("prvId") String prvId);
}
