package com.xunge.dao.system.region;

import com.xunge.model.system.province.SysProvinceGroupVO;
import com.xunge.model.system.province.SysProvinceVO;
import com.xunge.model.system.region.RegionVO;
import com.xunge.model.system.region.SysCityVo;
import com.xunge.model.system.region.SysRegionVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface ISysRegionDao {
    /**
     * 条件查询
     *
     * <AUTHOR>
     */
    public List<SysRegionVO> selectByConditions(Map<String, Object> map);

    /**
     * 查询省份分组
     *
     * @return
     * @authorzhujj
     */
    public List<SysProvinceGroupVO> selectByPrvGroup(Map<String, Object> map);

    /**
     * 查询省份列表
     *
     * @param map
     * @return
     */
    public List<SysProvinceVO> selectProvinceByIds(Map<String, Object> map);

    /**
     * 删除
     *
     * <AUTHOR>
     */
    public int delRegion(Map<String, Object> paramMap);

    /**
     * 新增
     *
     * <AUTHOR>
     */
    public int addRegion(SysRegionVO sysRegionVO);

    /**
     * 修改
     *
     * <AUTHOR>
     */
    public int updateRegion(SysRegionVO sysRegionVO, String regId);

    /**
     * 根据id查询
     *
     * <AUTHOR>
     */
    public SysRegionVO getRegionById(String regId);

    /**
     * 根据reg_id查询reg_name
     *
     * @param paramMap
     * @return
     */
    List<String> queryRegNameById(Map<String, Object> paramMap);

    /**
     * 根据name查询
     *
     * <AUTHOR>
     */
    public SysRegionVO getRegionIdByName(String regName);

    /**
     * 根据省份id查询
     *
     * <AUTHOR>
     */
    public List<String> getRegIdByprvId(String prvId);

    /**
     * 根据条件进行查询省份节点下的所有市和区
     *
     * @param paramMap
     * @return
     */
    List<SysRegionVO> queryRegionByConditions(Map<String, Object> paramMap);

    /**
     * 根据区县id查询省分名称
     *
     * <AUTHOR>
     */
    public String getPrvNameById(String regId);


    /**
     * 根据省份id查询省分名称
     *
     * <AUTHOR>
     */
    public String getPrvNameByPrvId(String prvId);


    /**
     * 根据省份code查询省份id
     *
     * <AUTHOR>
     */
    public String getPrvIdByCode(String prvcode);

    public String getRegIdByCode(String code);

    /**
     * 获取所有区域信息
     */
    public List<SysRegionVO> getAddress(Map<String, Object> map);

    /**
     * 获取用户区域信息
     */
    public List<SysRegionVO> getUserAddress(Map<String, Object> map);

    public List<SysRegionVO> getUserAddressReg(Map<String, Object> map);

    /**
     * 获取用户所有管理权限区
     */
    public List<RegionVO> queryManaRegions(Map<String, Object> map);

    /**
     * 获取用户所有管理权限省
     */
    public List<RegionVO> queryManaProvs(Map<String, Object> map);

    /**
     * 根据省份id查询所有市区id
     *
     * @param map
     * @return
     */
    public List<String> queryPRegionIdsByPrvId(Map<String, Object> map);

    /**
     * 根据直辖市省份id查询所有区级id
     *
     * @param map
     * @return
     */
    public List<String> queryRegionIdsByPrvId(Map<String, Object> map);

    /**
     * 根据集团省份id查询所有省级id
     *
     * @param map
     * @return
     */
    public List<String> queryPrvRegionIdsByPrvId(Map<String, Object> map);

    /**
     * 查询所有省份id
     *
     * @param
     * @return
     */
    public List<Map<String, String>> queryPrvIds();

    /**
     * 通过省份flag获取省份id
     *
     * @param PrvFlag
     * @return
     */
    public Map<String, Object> queryPrvIdByFlag(String PrvFlag);

    /**
     * 通过区县id获取省份flag
     *
     * @param regId
     * @return
     */
    public Map<String, Object> queryPrvFlagByRegId(String regId);

    /**
     * 根据区县id查询区县、地市、省名称
     *
     * @param regId
     * @return
     */
    public Map<String, Object> queryAllNameByRegId(String regId);

    /**
     * 根据区县id查询省份id
     *
     * @param regId
     * @return
     */
    public String getPrvIdByRegId(String regId);

    /**
     * 查询某省下所有地市，和该地市下所有的区县
     *
     * @param prv_id
     * @return
     */
    List<SysCityVo> queryAllCityAndReginByPrv(String prv_id);

    /**
     * 获取所有区域信息
     */
    public List<SysRegionVO> getRegionByName(Map<String, Object> map);

    /**
     * 根据省份id查询PrvFlag
     */
    String queryPrvFlagByPrvId(@Param("prvId") String prvId);
}