package com.xunge.dao.energManage.impl;

import com.xunge.dao.AbstractBaseDao;
import com.xunge.dao.energManage.IEnergManageDao;
import com.xunge.model.energManage.EnergTableVO;
import com.xunge.model.energManage.PrvCityVO;
import com.xunge.model.system.region.RegionVO;

import java.util.List;
import java.util.Map;

public class EnergManageDaoImpl extends AbstractBaseDao implements IEnergManageDao {

    final String Namespace = "com.xunge.dao.energManage.EnergManageVOMapper.";

    @Override
    public List<PrvCityVO> queryProvs(Map<String, Object> map) {
        return this.getSqlSession().selectList(Namespace + "queryProvs", map);
    }

    @Override
    public List<PrvCityVO> queryRegions(Map<String, Object> map) {
        return this.getSqlSession().selectList(Namespace + "queryRegions", map);
    }

    @Override
    public List<EnergTableVO> queryAllEnerg(Map<String, Object> map) {
        return this.getSqlSession().selectList(Namespace + "queryAllEnerg", map);
    }

    @Override
    public List<RegionVO> queryManaProvs(Map<String, Object> map) {
        return this.getSqlSession().selectList(Namespace + "queryManaProvs", map);
    }

    @Override
    public List<RegionVO> queryManaRegions(Map<String, Object> map) {
        return this.getSqlSession().selectList(Namespace + "queryManaRegions", map);
    }

}
