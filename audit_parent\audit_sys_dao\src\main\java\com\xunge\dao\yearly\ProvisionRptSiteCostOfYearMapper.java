package com.xunge.dao.yearly;

import com.xunge.model.report.RptPrvEleBasesiteVo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

@Mapper
public interface ProvisionRptSiteCostOfYearMapper {

    List<RptPrvEleBasesiteVo> querySiteCostAll(Map<String, Object> map);

    List<RptPrvEleBasesiteVo> querySiteCostByPrvId(Map<String, Object> map);

    List<RptPrvEleBasesiteVo> querySiteCostByPregId(Map<String, Object> map);

    List<RptPrvEleBasesiteVo> querySiteCostAllTotal(Map<String, Object> map);

    List<RptPrvEleBasesiteVo> querySiteCostByPrvIdTotal(Map<String, Object> map);

    List<RptPrvEleBasesiteVo> querySiteCostByPregIdTotal(Map<String, Object> map);
}
