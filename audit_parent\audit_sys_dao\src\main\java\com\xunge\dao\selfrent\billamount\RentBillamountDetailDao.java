package com.xunge.dao.selfrent.billamount;

import com.xunge.core.page.Page;
import com.xunge.model.selfrent.billamount.RentBillamountDetailVO;
import com.xunge.model.selfrent.billamount.RentFinanceBillamountDetailVO;
import com.xunge.model.selfrent.billamount.RentFinanceBillamountDetailVONew;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 2017-06-26
 * @description 租费报账汇总明细DAO接口
 */
public interface RentBillamountDetailDao {
    /**
     * @param rentBillamountDetail 租费报账汇总明细
     * @return
     * @description 保存租费报账汇总明细
     */
    public int insertRentBillamountDetail(RentBillamountDetailVO rentBillamountDetail);

    /**
     * @param rentBillamountDetail
     * @return
     * @description 批量保存租费报账汇总明细
     */
    public int insertRentBillamountDetailList(List<RentBillamountDetailVO> rentBillamountDetails);

    /**
     * @param billamountId 租费汇总Id
     * @return
     * @description 根据租费汇总表ID查询租费汇总明细
     */
    public List<RentBillamountDetailVO> selectByBillamountId(String billamountId);

    public Page<List<RentBillamountDetailVO>> selectAllByBillamountId(String billamountId, Map<String, Object> map);

    public List<RentBillamountDetailVO> selectAllByBillamountIdNoPage(String billamountId);

    public List<RentFinanceBillamountDetailVO> selectAllFiancedByBillamountIdNopage(String billamountId);

    public List<RentFinanceBillamountDetailVONew> selectAllFiancedByBillamountIdNopageNew(String billamountId);

    /**
     * 删除汇总单明细
     *
     * @param billamountId 汇总单ID
     * @return
     */

    public int deleteBybillamountId(String billamountId);

    /**
     * 删除汇总单明细
     *
     * @param billamountId 汇总单ID
     * @return
     */

    public int deleteBybillamountDetailId(String billamountDetailId);


    /**
     * @param @param paraMap    设定文件
     * @return void    返回类型
     * @throws
     * @Title: updateComtractInfo
     * @Description: TODO(这里用一句话描述这个方法的作用)
     */

    public int updateComtractInfo(Map<String, Object> paraMap);

    int updateBillamountdetailAdjustById(RentBillamountDetailVO param);

    List<RentBillamountDetailVO> selectBillamountdetail(String billamountdetailId);

    List<RentBillamountDetailVO> querySupplierInfos(String billamountId);

    List<RentBillamountDetailVO> queryRentBillamountDetailLeftJoinOther(Map<String, Object> maps);

    List<RentBillamountDetailVO> queryRentBillamountDetailLeftJoinOtherSingle(String billamountDetailId);
}