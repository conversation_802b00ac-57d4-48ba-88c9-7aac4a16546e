package com.xunge.comm;

/**
 * 省份枚举类，用于省份ID和省份代码之间的转换
 */
public enum ProvinceEnum {
    // 总部
    HQ("000000", "HQ"),
    // 北京
    BJ("110000", "BJ"),
    // 天津
    TJ("120000", "TJ"),
    // 河北
    HE("130000", "HE"),
    // 山西
    SX("140000", "SX"),
    // 内蒙古
    NM("150000", "NM"),
    // 辽宁
    LN("210000", "LN"),
    // 吉林
    JL("220000", "JL"),
    // 黑龙江
    HL("230000", "HL"),
    // 上海
    SH("310000", "SH"),
    // 江苏
    JS("320000", "JS"),
    // 浙江
    ZJ("330000", "ZJ"),
    // 安徽
    AH("340000", "AH"),
    // 福建
    FJ("350000", "FJ"),
    // 江西
    JX("360000", "JX"),
    // 山东
    SD("370000", "SD"),
    // 河南
    HA("410000", "HA"),
    // 湖北
    HB("420000", "HB"),
    // 湖南
    HN("430000", "HN"),
    // 广东
    GD("440000", "GD"),
    // 广西
    GX("450000", "GX"),
    // 海南
    HI("460000", "HI"),
    // 重庆
    CQ("500000", "CQ"),
    // 四川
    SC("510000", "SC"),
    // 贵州
    GZ("520000", "GZ"),
    // 云南
    YN("530000", "YN"),
    // 西藏
    XZ("540000", "XZ"),
    // 陕西
    SN("610000", "SN"),
    // 甘肃
    GS("620000", "GS"),
    // 青海
    QH("630000", "QH"),
    // 宁夏
    NX("640000", "NX"),
    // 新疆
    XJ("650000", "XJ"),
    // 台湾
    TW("710000", "TW");

    private final String prvId;
    private final String prvCode;

    ProvinceEnum(String prvId, String prvCode) {
        this.prvId = prvId;
        this.prvCode = prvCode;
    }

    /**
     * 获取省份ID
     */
    public String getPrvId() {
        return prvId;
    }

    /**
     * 获取省份代码
     */
    public String getPrvCode() {
        return prvCode;
    }

    /**
     * 根据省份ID获取省份代码
     * @param prvId 省份ID
     * @return 省份代码
     * @throws IllegalArgumentException 如果找不到对应的省份代码
     */
    public static String getCodeById(String prvId) {
        if (prvId == null || prvId.isEmpty()) {
            throw new IllegalArgumentException("省份ID不能为空");
        }

        for (ProvinceEnum province : ProvinceEnum.values()) {
            if (province.getPrvId().equals(prvId)) {
                return province.getPrvCode();
            }
        }
        throw new IllegalArgumentException("未找到对应的省份代码: " + prvId);
    }

    /**
     * 根据省份代码获取省份ID
     * @param prvCode 省份代码
     * @return 省份ID
     * @throws IllegalArgumentException 如果找不到对应的省份ID
     */
    public static String getIdByCode(String prvCode) {
        if (prvCode == null || prvCode.isEmpty()) {
            throw new IllegalArgumentException("省份代码不能为空");
        }

        for (ProvinceEnum province : ProvinceEnum.values()) {
            if (province.getPrvCode().equals(prvCode)) {
                return province.getPrvId();
            }
        }
        throw new IllegalArgumentException("未找到对应的省份ID: " + prvCode);
    }

    /**
     * 检查省份代码是否有效
     * @param prvCode 省份代码
     * @return 是否有效
     */
    public static boolean isValidCode(String prvCode) {
        if (prvCode == null || prvCode.isEmpty()) {
            return false;
        }

        for (ProvinceEnum province : ProvinceEnum.values()) {
            if (province.getPrvCode().equals(prvCode)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 检查省份ID是否有效
     * @param prvId 省份ID
     * @return 是否有效
     */
    public static boolean isValidId(String prvId) {
        if (prvId == null || prvId.isEmpty()) {
            return false;
        }

        for (ProvinceEnum province : ProvinceEnum.values()) {
            if (province.getPrvId().equals(prvId)) {
                return true;
            }
        }
        return false;
    }
} 