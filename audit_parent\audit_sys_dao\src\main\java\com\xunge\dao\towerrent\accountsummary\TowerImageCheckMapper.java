package com.xunge.dao.towerrent.accountsummary;

import com.xunge.model.towerrent.settlement.TwrImageCheckMeterVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Description:
 * @Author: dxd
 * @Date: 2023/5/17 9:32
 */
public interface TowerImageCheckMapper {

    List<String> getOtherIdAndPunishId(@Param("businessId") String businessId);

    List<TwrImageCheckMeterVO> queryTowerMeterImage(@Param("businessType")String businessType,
                                                    @Param("businessId")String businessId,
                                                    @Param("idList")List<String> idList);

    String queryImageUrlByAttachmentId(@Param("attachmentId") String attachmentId);

    int saveTowerImageMeter(List<TwrImageCheckMeterVO> imageList);

    void updateTowerImageCheckData(@Param("businessId") String businessId);

    List<String> getDatAttachmentCompressIds(String attachmentId);

    void updateTwrImageCheckState(@Param("businessType")String businessType,
                                  @Param("businessId")String businessId);

    void updateTwrImageStateByIds(@Param("attachmentIds") List<String> attachmentIds,
                                  @Param("businessId") String businessId,
                                  @Param("businessType")String businessType);

    List<String> queryTowerMeterImageByAttachmentId(@Param("attachmentId")  String attachmentId, @Param("businessType")String businessType);
}
