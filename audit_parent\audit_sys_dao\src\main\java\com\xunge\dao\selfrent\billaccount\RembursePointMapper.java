package com.xunge.dao.selfrent.billaccount;

import com.xunge.dto.selfelec.AuthorityUser;
import com.xunge.model.basedata.DatBasetower;
import com.xunge.model.selfrent.billAccount.DatBaseResourceDto;
import com.xunge.model.selfrent.billAccount.DatBaseresourcesVO;
import com.xunge.model.selfrent.billAccount.RentBillAccountResourceVO;
import com.xunge.model.selfrent.billAccount.TowerBillAccountExportVo;
import com.xunge.model.selfrent.contract.BillContractVO;
import com.xunge.model.selfrent.contract.DatContractVO;
import com.xunge.model.selfrent.rebursepoint.*;
import com.xunge.model.selfrent.resource.DatBaseResourceVO;
import com.xunge.model.system.dictionary.DictionaryVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface RembursePointMapper {
    List<RentBillaccountVO> queryRembursePointInfo(@Param("dto") RentBillaccountQueryDto queryDto, @Param("user") AuthorityUser authorityUser);

    int queryRembursePointNotRelationTotal(@Param("dto") RentBillaccountQueryDto queryDto, @Param("user") AuthorityUser authorityUser);

    List<RentBillaccountVO> queryRembursePointVO(@Param("dto") RentBillaccountQueryDto queryDto, @Param("user") AuthorityUser authorityUser);

    int queryRembursePointVONotRelationTotal(@Param("dto") RentBillaccountQueryDto queryDto, @Param("user") AuthorityUser authorityUser);

    List<RentBillaccountEptVO> queryRembursePointVOForExport(@Param("dto") RentBillaccountQueryDto queryDto, @Param("user") AuthorityUser authorityUser);

    List<RentBillaccountEptVO> queryRembursePointResourceCountForExport(@Param("dto") RentBillaccountQueryDto queryDto, @Param("user") AuthorityUser authorityUser);

    int queryApprovePaymentById(@Param("billaccountId") String billaccountId, @Param("prvId") String  prvId);

    List<RentBillaccountVO> queryRentBillAccountList(@Param("contractCode") String contractCode, @Param("prvId")  String prvId, @Param("billaccountCode")  String billaccountCode);

    List<DatContractVO> queryDatContractList(@Param("dataFrom") String dataFrom, @Param("contractCode")  String contractCode, @Param("contractsysId")  String contractsysId, @Param("prvId")  String prvId);

    List<RentBillaccountVO> queryRentBillAccountListByResource(@Param("baseresourceType") String baseresourceType, @Param("baseresourceCuid")  String baseresourceCuid, @Param("prvId")  String prvId, @Param("billaccountCode")  String billaccountCode);

    List<RentBillaccountVO> queryRentMultipleBillAccountListByResource(@Param("baseresourceType") String baseresourceType, @Param("baseresourceCuid")  String baseresourceCuid, @Param("prvId")  String prvId);


    List<RentBillaccountVO> queryRentBillAccountListByTower(@Param("towerCid") String towerCid, @Param("prvId")  String prvId, @Param("billaccountCode")  String billaccountCode);

    String queryBaseresourceIdList(@Param("baseresourceType") String baseresourceType, @Param("baseresourceCuid") String baseresourceCuid, @Param("prvId") String prvId);

    String queryTowerIdList(@Param("towerCid") String towerCid, @Param("prvId")  String prvId);

    String queryRegIdByRegName(@Param("regName") String regName, @Param("prvId")  String prvId);

    String queryDeptIdByDeptName(@Param("deptName") String deptName, @Param("prvId")  String prvId);

    int insertBillAcount(RentBillaccountVO billaccount);

    String queryRentContractId(@Param("dataFrom") String dataFrom, @Param("contractCode")  String contractCode, @Param("contractsysId")  String contractsysId, @Param("prvId")  String prvId);

    int insertBillAccountContract(RentBillAccountContractVO rentBillAccountContract);

    String queryDatContractId(@Param("dataFrom") String dataFrom, @Param("contractCode")  String contractCode, @Param("contractsysId")  String contractsysId, @Param("prvId")  String prvId);

    int insertBillContractAgreement(List<BillContractVO> otherContractList);

    int insertBillAccountResource(List<RentBillAccountResourceVO> resourceList);

    int insertBillAccountTower(List<RentBillaccountTowerVO> towerList);

    int insertBillAcountOperateTime(RentBillaccountVO billaccount);

    RentBillaccountVO queryBillAccountByBillAccountCode(@Param("billAccountCode") String billAccountCode, @Param("prvId")  String prvId);

    int updateBillAccount(RentBillaccountVO billaccount);


    List<BillContractVO> queryContractAgreementByBillAccountId(String billAccountId);

    List<DatBaseresourcesVO> queryResourceByBillAccountId(String billAccountId);

    List<DatBasetower> queryTowerByBillAccountId(String billAccountId);

    List<TowerBillAccountExportVo> queryThreeTowerBillAccountByBillAccountId(@Param("billAccountIdList") List<String> billAccountIdList, @Param("prvId") String prvId);

    List<String> queryThreeTowerBillAccountIdByCondition(@Param("dto") RentBillaccountQueryDto queryDto, @Param("user") AuthorityUser authorityUser);

    List<RentBillaccountVO> queryThreeTowerBillAccountResourceCountByCondition(@Param("dto") RentBillaccountQueryDto queryDto, @Param("user") AuthorityUser authorityUser);

    List<RentBillaccountVO> queryThreeTowerBillAccountTowerCountByCondition(@Param("dto") RentBillaccountQueryDto queryDto, @Param("user") AuthorityUser authorityUser);

    List<DatBaseResourceVO> queryResourceNotValidTime(@Param("billaccountId") String billaccountId, @Param("relationState") Integer relationState);

    String queryResourceUpdateState(@Param("baseresourceId") String baseresourceId);

    List<DatContractVO> queryDatContractByContractCode(@Param("contractCode") String contractCode, @Param("prvId") String prvId);

    List<DatBaseResourceVO> queryResourceByBaseresourceCuid(@Param("baseresourceType") String baseresourceType, @Param("baseresourceCuid") String baseresourceCuid, @Param("prvId") String prvId);

    List<DatBasetower> queryTowerByTowerCid(@Param("towerCid") String towerCid, @Param("prvId") String prvId);

    List<DatBaseResourceVO> queryResourceInfoForBillAccount(DatBaseResourceDto datBaseResourceDto);

    List<DatBaseResourceVO> queryResourceInfoForMultipleBillAccount(DatBaseResourceDto datBaseResourceDto);
}
