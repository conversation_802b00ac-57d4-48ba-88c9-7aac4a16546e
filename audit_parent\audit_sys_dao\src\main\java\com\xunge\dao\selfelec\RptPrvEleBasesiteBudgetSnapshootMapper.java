package com.xunge.dao.selfelec;

import com.xunge.model.report.RptPrvEleBasesiteBudgetVo;

import java.util.List;
import java.util.Map;

public interface RptPrvEleBasesiteBudgetSnapshootMapper {

    public List<RptPrvEleBasesiteBudgetVo> querySiteElecInfoByPrv(Map<String, Object> map);

    public List<RptPrvEleBasesiteBudgetVo> querySiteElecInfoByPreg(Map<String, Object> map);

    public List<RptPrvEleBasesiteBudgetVo> querySiteElecInfoByReg(Map<String, Object> map);

}