package com.xunge.comm.utils;


public class TypeConvertUtil {
    /**
     * 报账点费用类型
     *
     * @param type {"自维_1", "塔维_2", "代持_3"}, orderNum = "5", needMerge = true)
     * @return
     */
    public static String dealAmountType(Integer type) {
        if (type == null) {
            return "-";
        }
        String result;
        switch (type) {
            case 1:
                result = "自维";
                break;
            case 2:
                result = "塔维";
                break;
            case 3:
                result = "代持";
                break;
            default:
                result = "-";
        }
        return result;
    }

    /**
     * 新能源报账点类型
     *
     * @param type {"光伏报账点_1", "风能报账点_2", "水电报账点_3","一站多路电报账点_4", "其他新能源报账点_5"}, orderNum = "6", needMerge = true)
     * @return
     */
    public static String dealNewEnergyBillaccountType(Integer type) {
        if (type == null) {
            return "-";
        }
        String result;
        switch (type) {
            case 1:
                result = "光伏报账点";
                break;
            case 2:
                result = "风能报账点";
                break;
            case 3:
                result = "水电报账点";
                break;
            case 4:
                result = "一站多路电报账点";
                break;
            case 5:
                result = "其他新能源报账点";
                break;
            default:
                result = "-";
        }
        return result;
    }
}
