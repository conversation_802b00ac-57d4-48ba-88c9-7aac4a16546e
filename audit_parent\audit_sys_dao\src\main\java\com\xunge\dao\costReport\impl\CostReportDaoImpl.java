package com.xunge.dao.costReport.impl;

import com.xunge.dao.AbstractBaseDao;
import com.xunge.dao.costReport.CostReportDao;
import com.xunge.model.Code;

import java.util.List;
import java.util.Map;

public class CostReportDaoImpl extends AbstractBaseDao implements CostReportDao {
    final String Namespace = "com.xunge.dao.costReport.CostReportDao.";

    @Override
    public void insert(List<Map<String, Object>> datas) {
        this.getSqlSession().insert(Namespace + "insert", datas);
    }

    @Override
    public void insertCode(List<Code> datas) {
        this.getSqlSession().insert(Namespace + "insertCode", datas);
    }

    @Override
    public List<Map<String, Object>> getTotalCompany(Map<String, Object> params) {
        return this.getSqlSession().selectList(Namespace + "getTotalCompany", params);
    }

    @Override
    public List<Map<String, Object>> queryCostReportMonth(Map<String, Object> params) {
        return this.getSqlSession().selectList(Namespace + "queryCostReportMonth", params);
    }

    @Override
    public List<Map<String, Object>> queryCostReoprtLastYear(Map<String, Object> params) {
        return this.getSqlSession().selectList(Namespace + "queryCostReoprtLastYear", params);
    }

    @Override
    public List<Map<String, Object>> getCompany(String prvCode) {
        return this.getSqlSession().selectList(Namespace + "getCompany", prvCode);
    }

    @Override
    public List<Map<String, Object>> queryByCompanyCodeAndDate(Map<String, Object> params) {
        return this.getSqlSession().selectList(Namespace + "queryByCompanyCodeAndDate", params);
    }

    @Override
    public void deleteByDate(List<String> list) {
        this.getSqlSession().delete(Namespace + "deleteByDate", list);
    }

    @Override
    public void insertStatu(Map<String, Object> map) {
        this.getSqlSession().insert(Namespace + "insertStatu", map);
    }

    @Override
    public void updataStatu(Map<String, Object> map) {
        this.getSqlSession().update(Namespace + "updataStatu", map);
    }

    @Override
    public List<Map<String, Object>> findSStatus(Map<String, Object> map) {
        return this.getSqlSession().selectList(Namespace + "findSStatus", map);
    }

    @Override
    public List<Map<String, Object>> selectLogs(Map<String, Object> map) {
        return this.getSqlSession().selectList(Namespace + "selectLogs", map);
    }
}


