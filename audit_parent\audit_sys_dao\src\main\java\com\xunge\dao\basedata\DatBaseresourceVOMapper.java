package com.xunge.dao.basedata;

import com.xunge.model.basedata.DatBaseresource;
import com.xunge.model.basedata.DatBaseresourceVO;
import com.xunge.model.basedata.DatBaseresourceVOExample;
import com.xunge.model.basedata.ResourcePowerChangeVo;
import com.xunge.model.basedata.vo.DatBaseresourceEnum;
import com.xunge.model.basedata.vo.ResourceQueryVO;
import com.xunge.model.report.RptRentReimburseVo;
import com.xunge.model.selfrent.billAccount.RentPaymentVO;
import com.xunge.model.selfrent.contract.RentContractVO;
import com.xunge.model.selfrent.resource.DatBaseResourceVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface DatBaseresourceVOMapper {

    List<DatBaseresourceVO> queryDatBaseresourceVOEle(Map<String, Object> map);


    List<DatBaseresourceVO> queryDatBaseresourceVORent(Map<String, Object> map);

    int countByExample(DatBaseresourceVOExample example);

    int deleteByExample(DatBaseresourceVOExample example);

    int deleteByPrimaryKey(Map<String, Object> map);

    int insert(DatBaseresourceVO record);

    int insertSelective(DatBaseresourceVO record);

    List<DatBaseresourceVO> selectByExample(DatBaseresourceVOExample example);

    /**
     * @description 根据站点Id 查找 资源点信息
     * <AUTHOR>
     * @date 创建时间：2017年8月7日
     */
    List<DatBaseresourceVO> getDatBaseresourceById(Map<String, Object> map);

    DatBaseresourceVO selectByPrimaryKey(Map<String, Object> map);

    int updateByExampleSelective(@Param("record") DatBaseresourceVO record, @Param("example") DatBaseresourceVOExample example);

    int updateByExample(@Param("record") DatBaseresourceVO record, @Param("example") DatBaseresourceVOExample example);

    int updateByPrimaryKeySelective(DatBaseresourceVO record);

    int updateByPrimaryKey(DatBaseresourceVO record);

    boolean batchInsert(List<DatBaseresourceVO> datas);

    boolean delByCuidsAndPrvid(Map<String, Object> map);

    boolean batchUpdate(List<DatBaseresourceVO> datas);

    boolean batchUpdateForAudit(List<DatBaseresourceVO> datas);

    /**
     * 查询资源点信息审核页面
     *
     * @param hashMaps
     * @return
     * <AUTHOR>
     */
    public List<DatBaseresourceVO> queryDatBaseresource(Map<String, Object> hashMaps);

    /**
     * 查询站台台账信息
     *
     * @param hashMaps
     * @return
     * <AUTHOR>
     */
    public List<RentPaymentVO> queryBillAcount(ResourceQueryVO vo);

    /**
     * queryBillAcountLedger
     *
     * @param vo
     * @return List<RentPaymentVO> 返回类型
     * @description << 查询站台台账信息 >>
     * <AUTHOR>
     * @version V1.0
     * @date 2018年8月1日
     * @email <EMAIL>
     */
    public List<RptRentReimburseVo> queryBillAcountLedger(ResourceQueryVO vo);

    public RentContractVO queryDataContract(String rentcontractId);

    public List<DatBaseresourceVO> queryBaseSource(ResourceQueryVO resourceQueryVO);

    /**
     * @description 判断资源代码唯一性
     * <AUTHOR>
     * @date 创建时间：2017年8月25日
     */
    public List<DatBaseresourceVO> checkBaseresourceCode(Map<String, Object> map);

    public List<Map<String, Object>> queryAllResourceNumByPrvid(Map<String, Object> map);

    public int selectNolinkResource(Map<String, Object> map);

    public int selectAllResource(Map<String, Object> map);

    public int setRecordDeptId(Map<String, String> map);

    /**
     * 根据billaccountId查询机房信息
     *
     * @param map
     * @return
     * <AUTHOR>
     */
    public List<DatBaseresourceVO> queryBaseresourceByBillaccountId(Map<String, Object> map);

    /**
     * 查询总数
     *
     * @param map
     * @return
     */
    public int queryDatBaseresourceByCount(Map<String, Object> map);

    /**
     * 查询截取的数
     *
     * @param map
     * @return
     */
    String queryBaseresourceSubStr(Map<String, Object> map);

    String queryBaseresourceIdbyTowerCode(String towerId);

    DatBaseresourceVO queryBeanById(Map<String, Object> map);

    void setFreeFlag(Map queryMap);

    boolean updateExceptionResourceBatch(List<DatBaseresource> datas);

    List<DatBaseResourceVO> queryBaseResource(Map<String, Object> paraMap);

    DatBaseresourceVO getRentPaymentDetailByResource(String paymentdetailId);

    int insertPowerChangeRecord(ResourcePowerChangeVo powerChangeVo);

    List<ResourcePowerChangeVo> queryPowerChangeRecordDetail(Map<String, Object> paraMap);

    int queryPowerChangeRecordCount(Map<String, Object> paraMap);

    int updateResourceChangeRange(Map<String, Object> paraMap);

    List<DatBaseresourceVO> queryDatBaseresourceForReal(String billaccountId);

    /**
     * 动态获取综资枚举值
     * @param paraMap
     * @return
     */
	List<DatBaseresourceEnum> queryResEnum(Map<String, Object> paraMap);


     /**
     * 根据字段所属类型获取综资枚举值
     * @param paraMap
     * @return
     */
    List<DatBaseresourceEnum> queryResEnumsByFieldOfType(Map<String, Object> paraMap);

    /**
     * 获取所有综资枚举值
     * @return
     */
    List<DatBaseresourceEnum> queryAllResEnums();

    List<DatBaseresourceVO> queryNormalServiceResource(List<String> resourceIds);

    /**
     * 根据资源字段类型，动态获取综资枚举值
     * @param paraMap 参数
     * @return List<DatBaseresourceEnum> 资源枚举集合
     */
    List<DatBaseresourceEnum> queryResEnumByResourceFields(Map<String, Object> paraMap);

    Integer queryResourceByIds(List<String> resourceIds);
}