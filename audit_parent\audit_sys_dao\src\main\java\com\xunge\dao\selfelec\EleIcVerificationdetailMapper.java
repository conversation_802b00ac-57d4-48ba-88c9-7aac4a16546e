package com.xunge.dao.selfelec;

import com.xunge.model.app.AppEleMeterDegree;
import com.xunge.model.selfelec.EleIcVerificationdetail;
import com.xunge.model.selfelec.eleverificate.EleBillaccountVerificatedetail;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface EleIcVerificationdetailMapper {
    int countByExample(EleIcVerificationdetailExample example);

    int deleteByExample(EleIcVerificationdetailExample example);

    int deleteByPrimaryKey(String icVerificationdetailId);

    int insert(EleIcVerificationdetail record);

    int insertSelective(EleIcVerificationdetail record);

    List<EleIcVerificationdetail> selectByExampleWithBLOBs(EleIcVerificationdetailExample example);

    List<EleIcVerificationdetail> selectByExample(EleIcVerificationdetailExample example);

    EleIcVerificationdetail selectByPrimaryKey(String icVerificationdetailId);

    int updateByExampleSelective(@Param("record") EleIcVerificationdetail record, @Param("example") EleIcVerificationdetailExample example);

    int updateByExampleWithBLOBs(@Param("record") EleIcVerificationdetail record, @Param("example") EleIcVerificationdetailExample example);

    int updateByExample(@Param("record") EleIcVerificationdetail record, @Param("example") EleIcVerificationdetailExample example);

    int updateByPrimaryKeySelective(EleIcVerificationdetail record);

    int updateByPrimaryKeyWithBLOBs(EleIcVerificationdetail record);

    int updateByPrimaryKey(EleIcVerificationdetail record);

    public List<EleIcVerificationdetail> getIcElectricmeterByConditionsShow(Map<String, Object> map);

    List<EleIcVerificationdetail> getIcElectricmeterByConditionsShowMore(Map<String, Object> map);

    public List<EleIcVerificationdetail> getIcElectricmeterByVerificationId(String verificationId);

    /**
     * @Title: getTotalDegreeActual @Description: TODO(这里用一句话描述这个方法的作用) @param @param
     * verificationId @param @return 设定文件 @return List<EleIcVerificationdetail> 返回类型 @throws
     */

    public List<EleIcVerificationdetail> getTotalDegreeActual(@Param("verificationId") String verificationId);

    List<Map<String, Object>> queryICDegreeNum(Map<String, Object> map);

    List<AppEleMeterDegree> selectICMeterInfo(Map<String, Object> map);

    List<EleIcVerificationdetail> queryLastIcMeterNumByBillaccount(@Param("meterIds") List<String> meterIds, @Param("verificationId") String verificationId);

    List<EleIcVerificationdetail> queryLastIcMeterInfoByBillamountDate(@Param("billaccountId") String billaccountId,
                                                                       @Param("startDate")String startDate,
                                                                       @Param("id")String id);
}