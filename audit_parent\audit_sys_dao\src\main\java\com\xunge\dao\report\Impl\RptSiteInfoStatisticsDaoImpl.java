package com.xunge.dao.report.Impl;

import com.xunge.dao.AbstractBaseDao;
import com.xunge.dao.report.IRptSiteInfoStatisticsDao;
import com.xunge.model.report.RptSiteInfoStatisticsVO;

import java.util.List;
import java.util.Map;

/**
 * @Auther: LinFei Li
 * @Date: 2018/12/13 16:58
 * @Description:
 */
public class RptSiteInfoStatisticsDaoImpl extends AbstractBaseDao implements IRptSiteInfoStatisticsDao {

    final String Namespace = "com.xunge.mapping.report.RptSiteInfoStatisticsVOMapper.";

    @Override
    public List<RptSiteInfoStatisticsVO> querySiteData(Map<String, Object> map) {
        return this.getSqlSession().selectList(Namespace + "querySiteData", map);
    }

    @Override
    public List<RptSiteInfoStatisticsVO> querySiteDataByPregId(Map<String, Object> map) {
        return this.getSqlSession().selectList(Namespace + "querySiteDataByPregId", map);
    }

    @Override
    public List<RptSiteInfoStatisticsVO> querySiteDataByRegId(Map<String, Object> map) {
        return this.getSqlSession().selectList(Namespace + "querySiteDataByRegId", map);
    }
}
