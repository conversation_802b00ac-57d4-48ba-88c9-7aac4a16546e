package com.xunge.model.contract;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @desc :合同实体
 */
public class BigDatContractVo implements Serializable {
    private static final long serialVersionUID = 133810434303553876L;
    private String supplierId;
    private String supplierCode;
    private String supplierName;
    private String supplierSite;
    private Integer supplierType;
    private Integer accountType;
    private Integer supplierState;
    private Integer dataFrom;
    private String createUser;
    private String createIp;
    private Date createTime;
    private String prvSname;
    private String pregId;
    private String pregName;
    private String regId;
    private String regName;
    private Integer isDownshare;
    private String sysDepId;
    private String userId;
    private String managerDepartment;
    private String managerUser;
    private String contractCode;
    private String contractName;
    private Integer contractType;
    private Date contractStartdate;
    private Date contractEnddate;
    private Date contractSigndate;
    private Date contractChangeenddate;
    private Date contractChangedate;
    private BigDecimal contractYearquantity;
    private String contractCheckname1;
    private String contractCheckname2;
    private String oldContractId;
    private String oldContractCode;
    private String contractFlow;
    private String contractSpaceresource;
    private Integer contractState;
    private String contractNote;
    private Integer auditingState;
    private String auditingUserId;
    private Date auditingDate;
    private String auditingRoleCode;
    private String sysContractCode;
    private String tag;
    private String contractIntroduction;
    private String elecontractId;
    private Integer priceType;
    private String elecontractPrice;
    private Integer includePriceTax;
    private BigDecimal flatPrice;
    private BigDecimal peakPrice;
    private BigDecimal valleyPrice;
    private BigDecimal topPrice;
    private Integer supplyMethod;
    private Integer buyMethod;
    private Integer paymentMethod;
    private BigDecimal taxRate;
    private Integer isIncludeAll;
    private Integer paySign;
    private BigDecimal paySignAccount;
    private BigDecimal contractMoney;
    private BigDecimal contractTax;
    private BigDecimal contractTotalAmount;
    private BigDecimal contractYearAmount;
    private Integer independentMeter;
    private BigDecimal cmccRatio;
    private BigDecimal unicomRatio;
    private BigDecimal telcomRatio;
    private Integer includeLoss;
    private Integer lossType;
    private String paymentUser;
    private String paymentTelphone;
    private String elecontractNote;
    private String rentcontractId;
    private String contractId;
    private String paymentperiodId;
    private String prvId;
    private String contractsysId;
    private BigDecimal totalAmountnotax;
    private Integer includeTax;
    private BigDecimal billamountTaxratio;
    private BigDecimal taxAmount;
    private BigDecimal totalAmount;
    private BigDecimal yearAmount;
    private Integer addressType;
    private Integer houseType;
    private Integer chargeType;
    private Integer propertyType;
    private BigDecimal propertyArea;
    private String propertyAddress;
    private String propertyName;
    private String rentcontractNote;
    private Integer auditState;
    private Integer rentPeriod;
    private Integer electricPeriod;
    private String contractStatus;
    private String smapUserId;//承办人id
    private Integer mainBody;//我方主体信息
    private String isSigned;//是否以总部名义签署
    private Integer draftType;//补充协议类型
    private String oldContractName;//原合同名称
    private String contractUrl;//合同url
    private String bankUser;//供应商开户行
    private String depositBank;//供应商开户行名称
    private String bankAccount;//供应商开户行账户
    private Date lastUpdateTime;//最后更新时间
    private Integer dataType;//合同类型
    private String incExpType;//收支类型
    private String retroactive;//是否补充协议

    /**
     * 合同已报账金额
     */
    private BigDecimal totalClaimAmount;
    /**
     * 合同的最后报账时间
     */
    private Date lastClaimDate;
    /**
     * 合同关联的报账单在资金系统已完成付款金额
     */
    private BigDecimal totalPayAmount;
    /**
     * 本合同最后付款时间
     */
    private Date lastPaymentDate;

    /**
     * 合同总金额
     */
    private BigDecimal totalContractAmount;

    private List<String> contractIds;

    public List<String> getContractIds() {
        return contractIds;
    }

    public void setContractIds(List<String> contractIds) {
        this.contractIds = contractIds;
    }

    public String getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(String supplierId) {
        this.supplierId = supplierId;
    }

    public String getSupplierCode() {
        return supplierCode;
    }

    public void setSupplierCode(String supplierCode) {
        this.supplierCode = supplierCode;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public String getSupplierSite() {
        return supplierSite;
    }

    public void setSupplierSite(String supplierSite) {
        this.supplierSite = supplierSite;
    }

    public Integer getSupplierType() {
        return supplierType;
    }

    public void setSupplierType(Integer supplierType) {
        this.supplierType = supplierType;
    }

    public Integer getAccountType() {
        return accountType;
    }

    public void setAccountType(Integer accountType) {
        this.accountType = accountType;
    }

    public String getBankUser() {
        return bankUser;
    }

    public void setBankUser(String bankUser) {
        this.bankUser = bankUser;
    }

    public String getDepositBank() {
        return depositBank;
    }

    public void setDepositBank(String depositBank) {
        this.depositBank = depositBank;
    }

    public String getBankAccount() {
        return bankAccount;
    }

    public void setBankAccount(String bankAccount) {
        this.bankAccount = bankAccount;
    }

    public Integer getSupplierState() {
        return supplierState;
    }

    public void setSupplierState(Integer supplierState) {
        this.supplierState = supplierState;
    }

    public Integer getDataFrom() {
        return dataFrom;
    }

    public void setDataFrom(Integer dataFrom) {
        this.dataFrom = dataFrom;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public String getCreateIp() {
        return createIp;
    }

    public void setCreateIp(String createIp) {
        this.createIp = createIp;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getPrvSname() {
        return prvSname;
    }

    public void setPrvSname(String prvSname) {
        this.prvSname = prvSname;
    }

    public String getPregId() {
        return pregId;
    }

    public void setPregId(String pregId) {
        this.pregId = pregId;
    }

    public String getPregName() {
        return pregName;
    }

    public void setPregName(String pregName) {
        this.pregName = pregName;
    }

    public String getRegId() {
        return regId;
    }

    public void setRegId(String regId) {
        this.regId = regId;
    }

    public String getRegName() {
        return regName;
    }

    public void setRegName(String regName) {
        this.regName = regName;
    }

    public Integer getIsDownshare() {
        return isDownshare;
    }

    public void setIsDownshare(Integer isDownshare) {
        this.isDownshare = isDownshare;
    }

    public String getSysDepId() {
        return sysDepId;
    }

    public void setSysDepId(String sysDepId) {
        this.sysDepId = sysDepId;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getManagerDepartment() {
        return managerDepartment;
    }

    public void setManagerDepartment(String managerDepartment) {
        this.managerDepartment = managerDepartment;
    }

    public String getManagerUser() {
        return managerUser;
    }

    public void setManagerUser(String managerUser) {
        this.managerUser = managerUser;
    }

    public String getContractCode() {
        return contractCode;
    }

    public void setContractCode(String contractCode) {
        this.contractCode = contractCode;
    }

    public String getContractName() {
        return contractName;
    }

    public void setContractName(String contractName) {
        this.contractName = contractName;
    }

    public Integer getContractType() {
        return contractType;
    }

    public void setContractType(Integer contractType) {
        this.contractType = contractType;
    }

    public Date getContractStartdate() {
        return contractStartdate;
    }

    public void setContractStartdate(Date contractStartdate) {
        this.contractStartdate = contractStartdate;
    }

    public Date getContractEnddate() {
        return contractEnddate;
    }

    public void setContractEnddate(Date contractEnddate) {
        this.contractEnddate = contractEnddate;
    }

    public Date getContractSigndate() {
        return contractSigndate;
    }

    public void setContractSigndate(Date contractSigndate) {
        this.contractSigndate = contractSigndate;
    }

    public Date getContractChangeenddate() {
        return contractChangeenddate;
    }

    public void setContractChangeenddate(Date contractChangeenddate) {
        this.contractChangeenddate = contractChangeenddate;
    }

    public Date getContractChangedate() {
        return contractChangedate;
    }

    public void setContractChangedate(Date contractChangedate) {
        this.contractChangedate = contractChangedate;
    }

    public BigDecimal getContractYearquantity() {
        return contractYearquantity;
    }

    public void setContractYearquantity(BigDecimal contractYearquantity) {
        this.contractYearquantity = contractYearquantity;
    }

    public String getContractCheckname1() {
        return contractCheckname1;
    }

    public void setContractCheckname1(String contractCheckname1) {
        this.contractCheckname1 = contractCheckname1;
    }

    public String getContractCheckname2() {
        return contractCheckname2;
    }

    public void setContractCheckname2(String contractCheckname2) {
        this.contractCheckname2 = contractCheckname2;
    }

    public String getOldContractId() {
        return oldContractId;
    }

    public void setOldContractId(String oldContractId) {
        this.oldContractId = oldContractId;
    }

    public String getOldContractCode() {
        return oldContractCode;
    }

    public void setOldContractCode(String oldContractCode) {
        this.oldContractCode = oldContractCode;
    }

    public String getContractFlow() {
        return contractFlow;
    }

    public void setContractFlow(String contractFlow) {
        this.contractFlow = contractFlow;
    }

    public String getContractSpaceresource() {
        return contractSpaceresource;
    }

    public void setContractSpaceresource(String contractSpaceresource) {
        this.contractSpaceresource = contractSpaceresource;
    }

    public Integer getContractState() {
        return contractState;
    }

    public void setContractState(Integer contractState) {
        this.contractState = contractState;
    }

    public String getContractNote() {
        return contractNote;
    }

    public void setContractNote(String contractNote) {
        this.contractNote = contractNote;
    }

    public Integer getAuditingState() {
        return auditingState;
    }

    public void setAuditingState(Integer auditingState) {
        this.auditingState = auditingState;
    }

    public String getAuditingUserId() {
        return auditingUserId;
    }

    public void setAuditingUserId(String auditingUserId) {
        this.auditingUserId = auditingUserId;
    }

    public Date getAuditingDate() {
        return auditingDate;
    }

    public void setAuditingDate(Date auditingDate) {
        this.auditingDate = auditingDate;
    }

    public String getAuditingRoleCode() {
        return auditingRoleCode;
    }

    public void setAuditingRoleCode(String auditingRoleCode) {
        this.auditingRoleCode = auditingRoleCode;
    }

    public String getSysContractCode() {
        return sysContractCode;
    }

    public void setSysContractCode(String sysContractCode) {
        this.sysContractCode = sysContractCode;
    }

    public String getTag() {
        return tag;
    }

    public void setTag(String tag) {
        this.tag = tag;
    }

    public String getContractIntroduction() {
        return contractIntroduction;
    }

    public void setContractIntroduction(String contractIntroduction) {
        this.contractIntroduction = contractIntroduction;
    }

    public String getElecontractId() {
        return elecontractId;
    }

    public void setElecontractId(String elecontractId) {
        this.elecontractId = elecontractId;
    }

    public Integer getPriceType() {
        return priceType;
    }

    public void setPriceType(Integer priceType) {
        this.priceType = priceType;
    }

    public String getElecontractPrice() {
        return elecontractPrice;
    }

    public void setElecontractPrice(String elecontractPrice) {
        this.elecontractPrice = elecontractPrice;
    }

    public Integer getIncludePriceTax() {
        return includePriceTax;
    }

    public void setIncludePriceTax(Integer includePriceTax) {
        this.includePriceTax = includePriceTax;
    }

    public BigDecimal getFlatPrice() {
        return flatPrice;
    }

    public void setFlatPrice(BigDecimal flatPrice) {
        this.flatPrice = flatPrice;
    }

    public BigDecimal getPeakPrice() {
        return peakPrice;
    }

    public void setPeakPrice(BigDecimal peakPrice) {
        this.peakPrice = peakPrice;
    }

    public BigDecimal getValleyPrice() {
        return valleyPrice;
    }

    public void setValleyPrice(BigDecimal valleyPrice) {
        this.valleyPrice = valleyPrice;
    }

    public BigDecimal getTopPrice() {
        return topPrice;
    }

    public void setTopPrice(BigDecimal topPrice) {
        this.topPrice = topPrice;
    }

    public Integer getSupplyMethod() {
        return supplyMethod;
    }

    public void setSupplyMethod(Integer supplyMethod) {
        this.supplyMethod = supplyMethod;
    }

    public Integer getBuyMethod() {
        return buyMethod;
    }

    public void setBuyMethod(Integer buyMethod) {
        this.buyMethod = buyMethod;
    }

    public Integer getPaymentMethod() {
        return paymentMethod;
    }

    public void setPaymentMethod(Integer paymentMethod) {
        this.paymentMethod = paymentMethod;
    }

    public BigDecimal getTaxRate() {
        return taxRate;
    }

    public void setTaxRate(BigDecimal taxRate) {
        this.taxRate = taxRate;
    }

    public Integer getIsIncludeAll() {
        return isIncludeAll;
    }

    public void setIsIncludeAll(Integer isIncludeAll) {
        this.isIncludeAll = isIncludeAll;
    }

    public Integer getPaySign() {
        return paySign;
    }

    public void setPaySign(Integer paySign) {
        this.paySign = paySign;
    }

    public BigDecimal getPaySignAccount() {
        return paySignAccount;
    }

    public void setPaySignAccount(BigDecimal paySignAccount) {
        this.paySignAccount = paySignAccount;
    }

    public BigDecimal getContractMoney() {
        return contractMoney;
    }

    public void setContractMoney(BigDecimal contractMoney) {
        this.contractMoney = contractMoney;
    }

    public BigDecimal getContractTax() {
        return contractTax;
    }

    public void setContractTax(BigDecimal contractTax) {
        this.contractTax = contractTax;
    }

    public BigDecimal getContractTotalAmount() {
        return contractTotalAmount;
    }

    public void setContractTotalAmount(BigDecimal contractTotalAmount) {
        this.contractTotalAmount = contractTotalAmount;
    }

    public BigDecimal getContractYearAmount() {
        return contractYearAmount;
    }

    public void setContractYearAmount(BigDecimal contractYearAmount) {
        this.contractYearAmount = contractYearAmount;
    }

    public Integer getIndependentMeter() {
        return independentMeter;
    }

    public void setIndependentMeter(Integer independentMeter) {
        this.independentMeter = independentMeter;
    }

    public BigDecimal getCmccRatio() {
        return cmccRatio;
    }

    public void setCmccRatio(BigDecimal cmccRatio) {
        this.cmccRatio = cmccRatio;
    }

    public BigDecimal getUnicomRatio() {
        return unicomRatio;
    }

    public void setUnicomRatio(BigDecimal unicomRatio) {
        this.unicomRatio = unicomRatio;
    }

    public BigDecimal getTelcomRatio() {
        return telcomRatio;
    }

    public void setTelcomRatio(BigDecimal telcomRatio) {
        this.telcomRatio = telcomRatio;
    }

    public Integer getIncludeLoss() {
        return includeLoss;
    }

    public void setIncludeLoss(Integer includeLoss) {
        this.includeLoss = includeLoss;
    }

    public Integer getLossType() {
        return lossType;
    }

    public void setLossType(Integer lossType) {
        this.lossType = lossType;
    }

    public String getPaymentUser() {
        return paymentUser;
    }

    public void setPaymentUser(String paymentUser) {
        this.paymentUser = paymentUser;
    }

    public String getPaymentTelphone() {
        return paymentTelphone;
    }

    public void setPaymentTelphone(String paymentTelphone) {
        this.paymentTelphone = paymentTelphone;
    }

    public String getElecontractNote() {
        return elecontractNote;
    }

    public void setElecontractNote(String elecontractNote) {
        this.elecontractNote = elecontractNote;
    }

    public String getRentcontractId() {
        return rentcontractId;
    }

    public void setRentcontractId(String rentcontractId) {
        this.rentcontractId = rentcontractId;
    }

    public String getContractId() {
        return contractId;
    }

    public void setContractId(String contractId) {
        this.contractId = contractId;
    }

    public String getPaymentperiodId() {
        return paymentperiodId;
    }

    public void setPaymentperiodId(String paymentperiodId) {
        this.paymentperiodId = paymentperiodId;
    }

    public String getPrvId() {
        return prvId;
    }

    public void setPrvId(String prvId) {
        this.prvId = prvId;
    }

    public String getContractsysId() {
        return contractsysId;
    }

    public void setContractsysId(String contractsysId) {
        this.contractsysId = contractsysId;
    }

    public BigDecimal getTotalAmountnotax() {
        return totalAmountnotax;
    }

    public void setTotalAmountnotax(BigDecimal totalAmountnotax) {
        this.totalAmountnotax = totalAmountnotax;
    }

    public Integer getIncludeTax() {
        return includeTax;
    }

    public void setIncludeTax(Integer includeTax) {
        this.includeTax = includeTax;
    }

    public BigDecimal getBillamountTaxratio() {
        return billamountTaxratio;
    }

    public void setBillamountTaxratio(BigDecimal billamountTaxratio) {
        this.billamountTaxratio = billamountTaxratio;
    }

    public BigDecimal getTaxAmount() {
        return taxAmount;
    }

    public void setTaxAmount(BigDecimal taxAmount) {
        this.taxAmount = taxAmount;
    }

    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    public BigDecimal getYearAmount() {
        return yearAmount;
    }

    public void setYearAmount(BigDecimal yearAmount) {
        this.yearAmount = yearAmount;
    }

    public Integer getAddressType() {
        return addressType;
    }

    public void setAddressType(Integer addressType) {
        this.addressType = addressType;
    }

    public Integer getHouseType() {
        return houseType;
    }

    public void setHouseType(Integer houseType) {
        this.houseType = houseType;
    }

    public Integer getChargeType() {
        return chargeType;
    }

    public void setChargeType(Integer chargeType) {
        this.chargeType = chargeType;
    }

    public Integer getPropertyType() {
        return propertyType;
    }

    public void setPropertyType(Integer propertyType) {
        this.propertyType = propertyType;
    }

    public BigDecimal getPropertyArea() {
        return propertyArea;
    }

    public void setPropertyArea(BigDecimal propertyArea) {
        this.propertyArea = propertyArea;
    }

    public String getPropertyAddress() {
        return propertyAddress;
    }

    public void setPropertyAddress(String propertyAddress) {
        this.propertyAddress = propertyAddress;
    }

    public String getPropertyName() {
        return propertyName;
    }

    public void setPropertyName(String propertyName) {
        this.propertyName = propertyName;
    }

    public String getRentcontractNote() {
        return rentcontractNote;
    }

    public void setRentcontractNote(String rentcontractNote) {
        this.rentcontractNote = rentcontractNote;
    }

    public Integer getAuditState() {
        return auditState;
    }

    public void setAuditState(Integer auditState) {
        this.auditState = auditState;
    }

    public Integer getRentPeriod() {
        return rentPeriod;
    }

    public void setRentPeriod(Integer rentPeriod) {
        this.rentPeriod = rentPeriod;
    }

    public Integer getElectricPeriod() {
        return electricPeriod;
    }

    public void setElectricPeriod(Integer electricPeriod) {
        this.electricPeriod = electricPeriod;
    }

    public String getContractStatus() {
        return contractStatus;
    }

    public void setContractStatus(String contractStatus) {
        this.contractStatus = contractStatus;
    }

    public String getSmapUserId() {
        return smapUserId;
    }

    public void setSmapUserId(String smapUserId) {
        this.smapUserId = smapUserId;
    }

    public String getIsSigned() {
        return isSigned;
    }

    public void setIsSigned(String isSigned) {
        this.isSigned = isSigned;
    }

    public Integer getMainBody() {
        return mainBody;
    }

    public void setMainBody(Integer mainBody) {
        this.mainBody = mainBody;
    }

    public Integer getDraftType() {
        return draftType;
    }

    public void setDraftType(Integer draftType) {
        this.draftType = draftType;
    }

    public String getOldContractName() {
        return oldContractName;
    }

    public void setOldContractName(String oldContractName) {
        this.oldContractName = oldContractName;
    }

    public String getContractUrl() {
        return contractUrl;
    }

    public void setContractUrl(String contractUrl) {
        this.contractUrl = contractUrl;
    }

    public Date getLastUpdateTime() {
        return lastUpdateTime;
    }

    public void setLastUpdateTime(Date lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    public Integer getDataType() {
        return dataType;
    }

    public void setDataType(Integer dataType) {
        this.dataType = dataType;
    }

    public String getIncExpType() {
        return incExpType;
    }

    public void setIncExpType(String incExpType) {
        this.incExpType = incExpType;
    }

    public String getRetroactive() {
        return retroactive;
    }

    public void setRetroactive(String retroactive) {
        this.retroactive = retroactive;
    }

    public BigDecimal getTotalClaimAmount() {
        return totalClaimAmount;
    }

    public void setTotalClaimAmount(BigDecimal totalClaimAmount) {
        this.totalClaimAmount = totalClaimAmount;
    }

    public Date getLastClaimDate() {
        return lastClaimDate;
    }

    public void setLastClaimDate(Date lastClaimDate) {
        this.lastClaimDate = lastClaimDate;
    }

    public BigDecimal getTotalPayAmount() {
        return totalPayAmount;
    }

    public void setTotalPayAmount(BigDecimal totalPayAmount) {
        this.totalPayAmount = totalPayAmount;
    }

    public Date getLastPaymentDate() {
        return lastPaymentDate;
    }

    public void setLastPaymentDate(Date lastPaymentDate) {
        this.lastPaymentDate = lastPaymentDate;
    }

    public BigDecimal getTotalContractAmount() {
        return totalContractAmount;
    }

    public void setTotalContractAmount(BigDecimal totalContractAmount) {
        this.totalContractAmount = totalContractAmount;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((contractId == null) ? 0 : contractId.hashCode());
        return result;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        BigDatContractVo other = (BigDatContractVo) obj;
        if (contractId == null) {
            if (other.contractId != null)
                return false;
        } else if (!contractId.equals(other.contractId))
            return false;
        return true;
    }

}