package com.xunge.dao.selfelec;

import com.xunge.model.selfelec.VDatElectricmeter;
import com.xunge.model.selfelec.VDatElectricmeterExample;
import com.xunge.model.selfelec.VDatElectricmeterWithBLOBs;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface VDatElectricmeterMapper {
    
    int countByExample(VDatElectricmeterExample example);

    
    int deleteByExample(VDatElectricmeterExample example);

    
    int insert(VDatElectricmeterWithBLOBs record);

    
    int insertSelective(VDatElectricmeterWithBLOBs record);

    
    List<VDatElectricmeterWithBLOBs> selectByExampleWithBLOBs(VDatElectricmeterExample example);

    
    List<VDatElectricmeter> selectByExample(VDatElectricmeterExample example);

    
    int updateByExampleSelective(@Param("record") VDatElectricmeterWithBLOBs record, @Param("example") VDatElectricmeterExample example);

    
    int updateByExampleWithBLOBs(@Param("record") VDatElectricmeterWithBLOBs record, @Param("example") VDatElectricmeterExample example);

    
    int updateByExample(@Param("record") VDatElectricmeter record, @Param("example") VDatElectricmeterExample example);
}