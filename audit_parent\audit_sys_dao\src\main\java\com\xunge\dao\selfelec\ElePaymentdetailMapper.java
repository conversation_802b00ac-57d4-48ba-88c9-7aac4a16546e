package com.xunge.dao.selfelec;

import com.xunge.model.selfelec.EleBillaccountPaymentdetail;
import com.xunge.model.selfelec.ElePaymentdetail;
import com.xunge.model.selfelec.ElePaymentdetailExample;
import lombok.Data;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

public interface ElePaymentdetailMapper {

    //查询报账汇总-缴费明细
    List<ElePaymentdetail> queryElePaymentDetails(@Param("billamountId") String billamountId, @Param("isFinance") String isFinance);

    int countByExample(ElePaymentdetailExample example);

    
    int deleteByExample(ElePaymentdetailExample example);

    
    int deleteByPrimaryKey(String paymentdetailId);

    
    int insert(ElePaymentdetail record);

    
    int insertSelective(ElePaymentdetail record);

    
    List<ElePaymentdetail> selectByExample(ElePaymentdetailExample example);

    
    ElePaymentdetail selectByPrimaryKey(String paymentdetailId);

    
    int updateByExampleSelective(@Param("record") ElePaymentdetail record, @Param("example") ElePaymentdetailExample example);

    
    int updateByExample(@Param("record") ElePaymentdetail record, @Param("example") ElePaymentdetailExample example);

    
    int updateByPrimaryKeySelective(ElePaymentdetail record);

    int updateByPrimaryKeySelectiveBydraft(ElePaymentdetail record);

    
    int updateByPrimaryKey(ElePaymentdetail record);

    /**
     * 根据报账点缴费编码查询报账点信息
     *
     * @param billaccountpaymentdetailId
     * @return
     *//*
	   public ElePaymentdetail queryPaymentDetailInfoById(String billaccountpaymentdetailId);*/

    Map<String, Object> queryEleBillaccountPaymentIspecial(@Param("billaccountpaymentdetailId") String billaccountpaymentdetailId);

    /**
     * 查询环比缴费额定功率标杆
     *
     * @param
     * @return
     * @date 2019年04月24日
     * <AUTHOR>
     */
    Map<String, Object> queryNowPaymentPowerratingDegree(Map<String, Object> map);

    /**
     * 查询同比缴费周期和额定功率标杆
     *
     * @param
     * @return
     * @date 2019年04月24日
     * <AUTHOR>
     */
    List<Map<String, String>> queryLastPaymentPowerratingDegree(Map<String, String> map);

    EleBillaccountPaymentdetail queryLastPayment(Map<String, String> map);
    @Deprecated
    List<ElePaymentdetail> queryLastPaymentdetailOld(Map<String, String> map);

    List<EleBillaccountPaymentdetail> queryLastPaymentdetailByMeterIds(Map<String, Object> map);
    @Deprecated
    List<ElePaymentdetail> queryNextPaymentdetailOld(Map<String, String> map);
    List<String> queryLastPaymentIds(Map<String, Object> map);
    List<ElePaymentdetail> queryLastPaymentdetail(Map<String, Object> map);
    List<String> queryNextPaymentIds(Map<String, Object> map);
    List<ElePaymentdetail> queryNextPaymentdetail(Map<String, Object> map);

    /**
     * 获取缴费详情中的一条总本期读数
     *
     * @param billAccountPaymentDetailId id
     * @return 结果
     */
    @Select("select total_now_readnum from ele_paymentdetail where billaccountpaymentdetail_id=#{billAccountPaymentDetailId} limit 1")
    BigDecimal selectTotalNowReadNum(String billAccountPaymentDetailId);

    int updateCmccByPrimaryKey(ElePaymentdetail payment);

    List<ElePaymentdetail> getPaymentInfo(@Param("meterId") String meterId, @Param("payDay") String payDay, @Param("flag") String flag, @Param("type") String type);
    List<ElePaymentdetail> queryEleVerificationDetails(@Param("billamountId") String billamountId);
    List<ElePaymentdetail> queryEleICVerificationDetails(@Param("billamountId") String billamountId);

    /**
     * 获取电表最近两次缴费
     * @param map meterId：电表id
     * @return
     */
    List<ElePaymentdetail> recordLastMeter(Map<String, String> map);
}
