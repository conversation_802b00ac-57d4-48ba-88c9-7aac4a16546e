package com.xunge.dao.system.user;

import com.xunge.core.page.Page;
import com.xunge.model.system.portaluser.ExportUser;
import com.xunge.model.system.portaluser.ImportUser;
import com.xunge.model.system.region.SysRegionVO;
import com.xunge.model.system.roleuser.RoleUserVO;
import com.xunge.model.system.user.ReimburserVO;
import com.xunge.model.system.user.SysUserPasswdRecord;
import com.xunge.model.system.user.SysUserVO;
import com.xunge.model.towerrent.accountsummary.TwrReimburserVO;
import com.xunge.model.towerrent.accountsummary.query.TwrReimburserQueryDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface ISysUserDao {

    SysUserVO queryUserInfoBySmapId(String smapId);

    /**
     * 根据roleid查询用户
     */
    public Page<List<SysUserVO>> queryAllUserByRoleId(Map<String, Object> paraMap, int pageNumber, int pageSize);

    /**
     * 根据userid查询用户
     */
    public SysUserVO queryAllUser(String userId);

    /**
     * 根据登陆用户名查找到用户信息
     */
    SysUserVO getByUserLoginName(String userLoginname);

    SysUserVO queryUserByUserLoginName(String userLoginname);
    /**
     * 根据用户名查询用户存在不存在
     *
     * @param userLoginname
     * @return
     */
    List<SysUserVO> getUserListByUserLoginName(String userLoginname);

    /**
     * 根据登陆用户名和省份标记查询用户信息
     *
     * @param userLoginname
     * @param prvFlag
     * @return
     */
    SysUserVO getByUserLoginNamePrvFlag(Map<String, Object> map);

    /**
     * 根据登陆用户ID找到用户信息
     */
    SysUserVO getByUserId(String userId);

    /**
     * 查询全部用户信息
     */
    Page<List<SysUserVO>> querySysUser(Map<String, Object> paramMap, int pageNumber, int pageSize);

    /**
     * 添加用户
     */
    int insertSysUser(SysUserVO sysUser);

    /**
     * 根据用户ID修改用户信息
     */
    int updateSysUserByUserId(SysUserVO sysUser);

    /**
     * 根据用户账号 用户名 模糊查找用户
     */
    Page<List<SysUserVO>> querySysUserByname(Map<String, Object> paramMap, int cur_page_num, int page_count);

    /**
     * 修改用户状态
     */
    int updateUserStateBatch(Map<String, Object> paramMap);

    /**
     * 根据登陆用户查找省份编码信息
     */
    SysRegionVO queryPrvIdByUser(SysUserVO user);

    /**
     * 根据登陆用户查找角色关联信息
     */
    List<String> queryRoleIdsByUser(SysUserVO user);

    /**
     * 根据登陆用户查找角色编码关联信息
     */
    List<String> queryRoleCodesByUser(SysUserVO user);

    /**
     * 添加用户角色信息
     */
    public int insertRoleUser(Map map);

    /**
     * 根据用户ID查找所有角色信息
     */
    public List<String> queryUserRole(Map map);

    /**
     * 根据用户ID查找所有角色用户关系信息对象
     */
    public List<RoleUserVO> queryUserRoleVOByUserId(Map<String, Object> map);

    /**
     * 根据用户ID删除所有角色信息
     */
    public void deleteRoleByUsreID(String userId);

    /**
     * 增加用户部门关系
     */
    int insertDepartmentUser(Map map);

    /**
     * 通过用户ID查找用户所有的部门
     */
    public List<String> queryUserDepartment(Map map);

    /**
     * 通过用户ID删除用户所有的部门
     */
    public void deleteDepartmentByUsreID(String userId);

    /**
     * 新增用户区县关系
     */
    public int insertUserRegion(Map<String, Object> userRegionMap);

    /**
     * 修改用户角色关系状态
     */
    public int updateUserRoleState(Map<String, Object> map);

    /**
     * 修改用户部门关系状态
     */
    public int updateUserDeptState(Map<String, Object> map);

    /**
     * 根据用户id查询所有区县id
     */
    public List<String> queryRegionId(Map<String, Object> paraMap);

    /**
     * 修改用户区县状态
     */
    public int updateUserRegion(Map<String, Object> userRegionMap);

    /**
     * 查询所有用户以及相关部门、角色
     */
    public Page<List<SysUserVO>> queryUserAll(Map<String, Object> paraMap, int pageNumber, int pageSize);

    /**
     * 获取用户信息
     *
     * <AUTHOR>
     */
    public SysUserVO queryUserInfo(Map<String, Object> paraMap);

    SysUserVO queryUserInfoForRobot(String uid);

    /**
     * 修改用户信息
     *
     * <AUTHOR>
     */
    public int updateUserInfo(Map<String, Object> paraMap);

    /**
     * 根据角色id查询用户信息
     *
     * @param paraMap
     * @return
     */
    public List<SysUserVO> queryUserByRole(Map<String, Object> paraMap);

    /**
     * 根据用户id查询用户名称和电话
     *
     * @param paraMap
     * @return
     */
    public SysUserVO queryUserByUserId(Map<String, Object> paraMap);

    /**
     * 根据用户id查询用户名称和电话
     *
     * @param userId
     * @return
     */
    public SysUserVO queryUserById(String userId);

    /**
     * 根据用户id查询用户名称和用户id
     *
     * @param paraMap
     * @return
     */
    public SysUserVO queryUserIdByUserId(Map<String, Object> paraMap);

    /**
     * 根据登陆用户名及省份ID查找用户判断相同省份有无相同用户
     */
    public List<SysUserVO> queryUserByLoginNameAndPrvId(Map<String, Object> paraMap);

    /**
     * 根据SMAPID及省份ID查找用户判断相同省份有无相同SMAPID
     */
    public List<SysUserVO> queryUserBySmapIdAndPrvId(Map<String, Object> paraMap);

    /**
     * 根据省份查询该省是否开通
     */
    public List<SysUserVO> queryUserByProjectName(Map<String, Object> paraMap);

    public SysUserVO provinceWhetherOpen(Map<String, Object> map);

    /**
     * query所有承办人信息
     *
     * @param map
     * @return List<Map < String, String>> 返回类型
     * @description <描述>
     * <AUTHOR>
     * @version V1.0
     * @date 2018年9月5日
     * @email <EMAIL>
     */
    public Page<Map<String, String>> queryAllSmapUser(Map<String, Object> map, int pageNumber, int pageSize);

    public Page<List<SysUserVO>> queryAllUserByRoleName(Map<String, Object> paraMap, int pageNumber, int pageSize);

    public String queryUserIdByUserSampId(String sampId);

    /**
     * 根据承办人Id，获取承办人编号和姓名
     *
     * @param userId
     * @return
     */
    public SysUserVO querySmapInfoByUserId(String userId);


    /**
     * @param @param  uid
     * @param @return 设定文件
     * @return SysUserVO    返回类型
     * @throws
     * @Title: getByUid
     * @Description: TODO(这里用一句话描述这个方法的作用)
     */

    public SysUserVO getByUid(String uid);


    /**
     * @param @param  user
     * @param @return 设定文件
     * @return SysUserVO    返回类型
     * @throws
     * @Title: getUserInfo
     * @Description: TODO(这里用一句话描述这个方法的作用)
     */

    public SysUserVO getUserInfo(SysUserVO user);


    public int resetPassword(Map<String, Object> map);

    public SysUserVO queryInfoByUserName(Map<String, Object> map);

    public SysUserVO queryUserInfoByparam(Map<String, Object> map);

    public SysUserVO queryUserInfoByparamLoginname(Map<String, Object> map);

    int updateCompanyType(SysUserVO userCompany);

    int insertCompanyType(SysUserVO userCompany);

    public List<SysUserVO> selectUserCompanyType(SysUserVO userCompany);

    /**
     * 根据统一门户pid查询用户信息
     *
     * @param pid
     * @return
     */
    SysUserVO getByPortalUserId(String pid);

    /**
     * 根据手机号查询用户信息
     *
     * @param pid
     * @return
     */
    List<SysUserVO> queryInfoByUserPhone(String userPhone);

    /**
     * 查询所有可用用户
     *
     * @param paraMap
     * @return
     */
    List<ExportUser> queryUserAllAvailable(Map<String, Object> paraMap);

    /**
     * 根据smapId查询用户
     *
     * @param smapId
     * @return
     */
    List<SysUserVO> queryAllUserBySmapId(String smapId);

    /**
     * 解除用户与门户系统绑定
     *
     * @param pid
     */
    void rmoveUserPortalId(String pid);

    void updateUserPid(ImportUser u);

    List<SysUserVO> selectAccUserCompanyType(SysUserVO userCompany);

    int ifKnowRobot(String userId);

    int addNoticeUser(String userId);

    String selectStaffCodeByUserId(String userId);

    SysUserVO getBySmapIdPrvFlag(Map<String, Object> map);

    int updateCompanyByUserId(SysUserVO sysUserVO);

    List<ReimburserVO> queryReimburserList(ReimburserVO reimburserVO);

    List<TwrReimburserVO> queryUserListByUserId(@Param("userId") String createUserId);

    List<TwrReimburserVO> queryTwrReimburserList(TwrReimburserQueryDto reimburserVO);

    ReimburserVO queryPushUserCompanyName(ReimburserVO reimburserVO);

    List<SysUserPasswdRecord> queryLastThreeModifyRecords(String userName);

   int saveModifyRecords(SysUserPasswdRecord record);

}
