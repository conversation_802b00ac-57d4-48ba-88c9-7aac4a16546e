package com.xunge.dao.report.Impl;

import com.xunge.dao.AbstractBaseDao;
import com.xunge.dao.report.IRptEleSaveCostDao;
import com.xunge.model.report.RptEleSaveCostVO;

import java.util.List;
import java.util.Map;

public class RptEleSaveCostDaoImpl extends AbstractBaseDao implements IRptEleSaveCostDao {

    final String RptEleSaveCostNamespace = "com.xunge.mapping.report.RptEleSaveCostVOMapper.";

    @Override
    public List<RptEleSaveCostVO> queryAllEleSaveCost(Map<String, Object> paraMap) {
        return this.getSqlSession().selectList(RptEleSaveCostNamespace + "queryAllEleSaveCost", paraMap);
    }

    @Override
    public List<RptEleSaveCostVO> queryEleSaveCostByPrvId(Map<String, Object> paraMap) {
        return this.getSqlSession().selectList(RptEleSaveCostNamespace + "queryEleSaveCostByPrvId", paraMap);
    }

    @Override
    public List<RptEleSaveCostVO> queryEleSaveCostByPregId(Map<String, Object> paraMap) {
        return this.getSqlSession().selectList(RptEleSaveCostNamespace + "queryEleSaveCostByPregId", paraMap);
    }

}
