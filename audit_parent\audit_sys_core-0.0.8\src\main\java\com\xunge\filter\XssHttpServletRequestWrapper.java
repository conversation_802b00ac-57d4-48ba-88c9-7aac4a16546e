package com.xunge.filter;

import com.alibaba.fastjson.JSON;

import javax.servlet.ServletInputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletRequestWrapper;
import java.io.IOException;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class XssHttpServletRequestWrapper extends HttpServletRequestWrapper {

    boolean isUpData = false;//判断是否是上传 上传忽略

    public XssHttpServletRequestWrapper(HttpServletRequest request) {
        super(request);
        String contentType = request.getContentType();
        if (null != contentType) {
            isUpData = contentType.startsWith("multipart");
        }
    }

    /**
     * 过滤一下字符串，连同前后< xxx >yyy< / xxx >全部消除。
     * 不区分大小写、空格可识别
     * <br>"function", "window\\.", "javascript:", "script",
     * <br>"js:", "about:", "file:", "document\\.", "vbs:", "frame",
     * <br>"cookie", "onclick", "onfinish", "onmouse", "onexit=",
     * <br>"onerror", "onclick", "onkey", "onload", "onfocus", "onblur"
     *
     * @param htmlStr
     * @return
     */
    public static String filterSafe(String htmlStr) {
        Pattern p = null; // 正则表达式
        Matcher m = null; // 操作的字符串
        StringBuffer tmp = null;
        String str = "";
        boolean isHave = false;
        String[] Rstr = {"meta", "script", "object", "embed"};
        if (htmlStr == null || !(htmlStr.length() > 0)) {
            return "";
        }
        str = htmlStr.toLowerCase();
        for (int i = 0; i < Rstr.length; i++) {
            p = Pattern.compile("<" + Rstr[i] + "(.[^>])*>");
            m = p.matcher(str);
            tmp = new StringBuffer();
            if (m.find()) {
                m.appendReplacement(tmp, "<" + Rstr[i] + ">");
                while (m.find()) {

                    m.appendReplacement(tmp, "<" + Rstr[i] + ">");
                }
                isHave = true;
            }

            m.appendTail(tmp);
            str = tmp.toString();

            p = Pattern.compile("</" + Rstr[i] + "(.[^>])*>");
            m = p.matcher(str);
            tmp = new StringBuffer();
            if (m.find()) {
                m.appendReplacement(tmp, "</" + Rstr[i] + ">");
                while (m.find()) {
                    m.appendReplacement(tmp, "</" + Rstr[i] + ">");
                }
                isHave = true;
            }
            m.appendTail(tmp);
            str = tmp.toString();

        }

        // System.out.println(str);

        String[] Rstr1 = {"function", "window\\.", "javascript:", "script",
                "js:", "about:", "file:", "document\\.", "vbs:", "frame",
                "cookie", "onclick", "onfinish", "onmouse", "onexit=",
                "onerror", "onclick", "onkey", "onload", "onfocus", "onblur"};

        for (int i = 0; i < Rstr1.length; i++) {
            p = Pattern.compile("<([^<>])*" + Rstr1[i] + "([^<>])*>([^<>])*</([^<>])*>");

            m = p.matcher(str);
            tmp = new StringBuffer();
            if (m.find()) {
                m.appendReplacement(tmp, "");
                while (m.find()) {
                    m.appendReplacement(tmp, "");
                }
                isHave = true;
            }
            m.appendTail(tmp);
            str = tmp.toString();
        }

        if (isHave) {
            htmlStr = str;
        }

        htmlStr = htmlStr.replaceAll("%3C", "<");
        htmlStr = htmlStr.replaceAll("%3E", ">");
        htmlStr = htmlStr.replaceAll("%2F", "");
        htmlStr = htmlStr.replaceAll("&#", "<b>&#</b>");
        return htmlStr;
    }

    @Override
    public String getHeader(String name) {
//		return StringEscapeUtils.escapeHtml4(super.getHeader(name));
        return replaceSpecial(super.getHeader(name));
    }

    @Override
    public String getQueryString() {
//		return StringEscapeUtils.escapeHtml4(super.getQueryString());
        return replaceSpecial(super.getQueryString());
    }

    @Override
    public String getParameter(String name) {
//		return StringEscapeUtils.escapeHtml4(super.getParameter(name));
        return replaceSpecial(super.getParameter(name));
    }

    @Override
    public String[] getParameterValues(String name) {
        String[] values = super.getParameterValues(name);
        if (values != null) {
            int length = values.length;
            String[] escapseValues = new String[length];
            for (int i = 0; i < length; i++) {
//				escapseValues[i] = StringEscapeUtils.escapeHtml4(values[i]);
                escapseValues[i] = replaceSpecial(values[i]);
            }
            return escapseValues;
        }
        return super.getParameterValues(name);
    }


////	@Override
//	public BufferedReader getReader() throws IOException {
//		  String enc = getCharacterEncoding();
//	        if(enc == null) enc = "UTF-8";
//	        return new BufferedReader(new InputStreamReader(getInputStream()));
//	}

    @Override
    public ServletInputStream getInputStream() throws IOException {
        if (isUpData) {
            return super.getInputStream();
        } else {
            String body = HttpGetBody.getBodyString(this.getRequest());
            ServletInputStream inputStream = null;
            if (body != null && !"".contentEquals(body)) {
                if (body.contains("[") && body.contains("]")) {
                    body = replaceSpecial(body);
                    inputStream = new PostServletInputStream(body);
                    return inputStream;
                } else {
                    if (body.contains("{") && body.contains("}")) {
                        Map<String, Object> paramMap = JSON.parseObject(body);
                        for (Map.Entry<String, Object> entry : paramMap.entrySet()) {
                            if (entry==null){
                                continue;
                            }
                            String value="";
                            if(entry.getValue()!=null) {
                                value=entry.getValue().toString();
                            }
                            paramMap.put(replaceSpecial(entry.getKey()),
                                    replaceSpecial(value));
                        }
                        body = JSON.toJSONString(paramMap);
                    } else {
                        body = replaceSpecial(body);
                    }
                    inputStream = new PostServletInputStream(body);
                    return inputStream;
                }
            } else {
                return inputStream;
            }
        }

//		ServletRequest req = this.getRequest();
//		String body = HttpGetBody.getBodyString(req);
//		ServletInputStream inputStream = null;
//		if(body!=null&&!"".contentEquals( body )&&(body.contains( "<" )||body.contains( ">" ))){
//    		body = replaceSpecial(body);
//    		inputStream =  new PostServletInputStream(body);
//    		return inputStream;
//		}else {
//			if(body!=null&&!"".contentEquals( body )){
//				return inputStream;
//			}else {
//				inputStream =  new PostServletInputStream(body);
//	    		return inputStream;
//			}
//		}
    }

    /*
     * 替换<>字符
     */
    public String replaceSpecial(String str) {
//		String pic = "img";
//		if(str!=null) {
//			if(str.contains( pic )) {
////				str = str.replace( "&lt;", "<" );
////				str = str.replace( "&gt;", ">" );
//				str = str.replace("javascript:","");
//				str = str.replace("script","");
//				str = str.replace("alert","");
//				str = str.replace("function","");
//				str = str.replace("console","");
//				str = str.replace("about","");
//				str = str.replace("file","");
//				str = str.replace("document","");
//				str = str.replace("vbs","");
//				str = str.replace("frame","");
//				str = str.replace("cookie","");
//				str = str.replace("onclick","");
//				str = str.replace("onfinish","");
//				str = str.replace("onmouse","");
//				str = str.replace("onexit","");
//				str = str.replace("onerror","");
//				str = str.replace("onkey","");
//				str = str.replace("onfocus","");
//				str = str.replace("onload","");
//				str = str.replace("onblur","");
//			}else {
////				str = str.replace( "<", "&lt;" );
////				str = str.replace( ">", "&gt;" );
//				str = str.replace("javascript:","");
//				str = str.replace("script","");
//			}
//		}

        if (str != null) {
            str = str.replace("<", "&lt;");
            str = str.replace(">", "&gt;");
            str = str.replace("javascript:", "");
            str = str.replace("alert", "");
        }
        return str;
    }
}
