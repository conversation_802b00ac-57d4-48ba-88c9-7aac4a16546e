package com.xunge.dao.budget;

import com.xunge.model.budget.RentBudgetBaseData;
import com.xunge.model.budget.RentBudgetReport;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface RentBudgetMapper {

    int insertBatchRent(@Param("list") List<RentBudgetReport> list);

    List<RentBudgetReport> queryRentBudgetData(@Param("prvId") String prvId, @Param("flowType") Integer flowType,
                                               @Param("workOrderId") String workOrderId,@Param("auditInfoId") String auditInfoId);

    int deleteRentBudgetData(@Param("workOrderId") String workOrderId,@Param("flowType") Integer flowType);

    int updateRentBudgetData(RentBudgetReport rentBudgetReport);

    List<RentBudgetBaseData> queryBudgetGenerateData(@Param("prvId") String prvId);

}
