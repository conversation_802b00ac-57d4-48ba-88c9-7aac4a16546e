package com.xunge.dao.finance.ext.mapper;

import com.xunge.model.finance.ext.accClaim.Billamount;
import com.xunge.model.finance.ext.accClaim.Billamountdetail;
import com.xunge.model.selfelec.accrualoffs.AccrualOffsDetail;
import com.xunge.model.selfelec.accrualoffs.ElePaymentOffsDetail;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ElecVerificationBillamountExtMapper {

    Billamount selectByPrimaryKey(String billamountId);

    List<Billamount> getEleLoanBillamount(@Param("loanCodes") String[] loanCodes);

    int updateBillAmountStateById(Billamount ele);

    int updateSecondBillamountId(Billamountdetail amountDetail);

    int updateSecondBillamountIdByPayment(Billamountdetail amountDetail);

    List<ElePaymentOffsDetail> queryVerificationOffsDetail(@Param("billamountId") String billamountId);

    void updateVerificationOffsDetail(List<ElePaymentOffsDetail> offsDetail);

    List<AccrualOffsDetail> selectVerificationOffsDetail(String billamountCode);

    void deteleVerificationOffsDetail(ElePaymentOffsDetail off);

}
