package com.xunge.dao.selfrent.normalreport;

import com.xunge.model.selfrent.normalreport.TxRentNormalReportVo;
import com.xunge.model.selfrent.normalreport.YfRentNormalReportVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface NormalReportDao {


    List<TxRentNormalReportVo> queryTxRentNormalReportListSum(@Param("prvId") String prvId,@Param("pregId") String pregId,
                                                              @Param("reportYear") String reportYear,@Param("reportMonth") String reportMonth);

    List<YfRentNormalReportVo> queryYfRentNormalReportListSum(@Param("prvId") String prvId,@Param("pregId") String pregId,
                                                            @Param("reportYear") String reportYear,@Param("reportMonth") String reportMonth);

    List<TxRentNormalReportVo> queryTxRentNormalReportList(@Param("prvId") String prvId, @Param("pregId") String pregId,
                                                           @Param("selectPrvId") String selectPrvId, @Param("selectPregId") String selectPregId,
                                                           @Param("reportYear") String reportYear, @Param("reportMonth") String reportMonth,
                                                           @Param("isPrvCityLevel") Integer isPrvCityLevel);

    List<YfRentNormalReportVo> queryYfRentNormalReportList(@Param("prvId") String prvId,@Param("pregId") String pregId,
                                                           @Param("selectPrvId") String selectPrvId, @Param("selectPregId") String selectPregId,
                                                           @Param("reportYear") String reportYear,@Param("reportMonth") String reportMonth,
                                                         @Param("isPrvCityLevel") Integer isPrvCityLevel);

}
