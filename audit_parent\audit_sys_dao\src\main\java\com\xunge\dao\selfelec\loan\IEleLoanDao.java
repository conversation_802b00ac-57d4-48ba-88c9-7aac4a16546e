package com.xunge.dao.selfelec.loan;

import com.xunge.dao.core.CrudDao;
import com.xunge.model.finance.ext.accClaim.Billamount;
import com.xunge.model.selfelec.EleLoanBillamountExport;
import com.xunge.model.selfelec.HistInsVO;
import com.xunge.model.selfelec.VEleBillaccount;
import com.xunge.model.selfelec.VEleBillaccountPaymentInfo;
import com.xunge.model.selfelec.eleverificate.VEleBillaccountVerificateInfo;
import com.xunge.model.selfelec.loan.EleLoan;
import com.xunge.model.selfelec.loan.EleLoanBenchmark;
import com.xunge.model.selfelec.loan.EleLoanExportMeterInfo;
import com.xunge.model.selfelec.loan.EleLoanExportVo;
import com.xunge.model.selfelec.loan.EleLoanSyncExportVO;
import com.xunge.model.selfelec.loan.EleLoandetail;
import com.xunge.model.selfelec.verification.EleVerificationBillamountdetail;
import com.xunge.model.selfrent.contract.CuringRentContractExportVO;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.cursor.Cursor;

import java.util.List;
import java.util.Map;

/**
 * 借款明细DAO接口
 *
 * <AUTHOR>
 * @version 2018-03-01
 */
public interface IEleLoanDao extends CrudDao<EleLoan> {

    /**
     * @description 根据loan_code借款编号获取借款信息
     * <AUTHOR>
     * @date 创建时间：2018年03月05日
     */
    public EleLoan getByCode(EleLoan eleLoan);

    /**
     * 根据id获取数据集合
     *
     * @param
     * @return
     */
    public List<EleLoan> getList(Map<String, Object> paraMap);

    @MapKey("loanId")
    Map<String,EleLoan> getVerificationAmountByLoanIds(@Param("loanIdList") List<String> loanIdList);

    /***
     * 根据先款后票的缴费id，获取借款信息
     * @param paraMap
     * @return
     */
    public EleLoan getOne(Map<String, Object> paraMap);


    /**
     * 根据id获取数据集合
     *
     * @param
     * @return
     */
    public List<EleLoan> getListImport(Map<String, Object> paraMap);


    /**
     * @description 分页查询报账点预付费明细维护数据
     * <AUTHOR>
     * @date 创建时间：2018年03月05日
     */
    public List<VEleBillaccount> queryEleLoanList(Map<String, Object> paraMap);
    List<VEleBillaccount> queryEleLoanListV1(Map<String, Object> paraMap);

    public List<VEleBillaccount> queryEleLoanListFcontract(Map<String, Object> paraMap);

    /**
     * @description 全部导出报账点预付费明细维护数据
     * <AUTHOR>
     * @date 创建时间：2018年08月21日
     */
    public List<VEleBillaccount> exportEleLoanList(Map<String, Object> paraMap);

    /**
     * @description 勾选导出报账点预付费明细维护数据
     * <AUTHOR>
     * @date 创建时间：2018年08月21日
     */
    public List<VEleBillaccount> checkEleLoanList(Map<String, Object> paraMap);

    /**
     * @description 新增报账点关联资源 沉余字段
     * <AUTHOR>
     * @date 创建时间：2018年03月05日
     */
    public int insertEleLoanBenchmarkMsg(EleLoanBenchmark eleLoanBenchmark);

    /**
     * @description 批量查询预付款信息
     * <AUTHOR>
     * @date 创建时间：2018年03月05日
     */
    public List<EleLoan> queryEleLoanPageInfo(Map<String, Object> map);

    public List<EleLoan> queryFinanceEleLoanPageInfo(Map<String, Object> map);

    /**
     * @description 批量查询解款明细
     * <AUTHOR>
     * @date 创建时间：2018年03月05日
     */
    public EleLoan queryEleLoanDetail(Map<String, Object> map);

    /**
     * @description 查询解款明细
     * <AUTHOR>
     * @date 创建时间：2018年03月05日
     */
    public EleLoan getEleLoanDetail(Map<String, Object> map);

    EleLoan selectDataStateByloanId(Map<String, Object> map);

    public EleLoan getFinanceEleLoanDetail(Map<String, Object> map);

    /**
     * @description 修改借款明细
     * <AUTHOR>
     * @date 创建时间：2018年03月05日
     */
    public void updateEleLoan(EleLoan eleLoan);
    /**
     * @description 修改借款明细(刷新报账点)
     * <AUTHOR>
     * @date 创建时间：2018年03月05日
     */
    public void updateEleLoanSelective(EleLoan eleLoan);

    /**
     * @description 更新状态
     * <AUTHOR>
     * @date 创建时间：2018年03月05日
     */
    public int updateDataState(EleLoan loan);

    /**
     * @description 更新上次提交审核时间
     * <AUTHOR>
     */
    public int updateLastAuditingDate(Map<String, Object> hashMaps);

    /**
     * @description 批量查询解款明细
     * <AUTHOR>
     * @date 创建时间：2018年03月05日
     */
    public List<EleLoan> getPushEleLoanDetail(Map<String, Object> map);

    /**
     * @description 批量查询借款信息
     * <AUTHOR>
     * @date 创建时间：2018年03月05日
     */
    public EleLoan getEleLoanDetailById(Map<String, Object> map);

    /**
     * @description 删除借款
     * <AUTHOR>
     * @date 创建时间：2018年03月05日
     */
    public void deleteList(@Param("list") List<EleLoan> list);

    /**
     * @description 根据预付款明细计算删除后的借款总余额
     * <AUTHOR>
     * @date 创建时间：2018年03月05日
     */
    public List<EleLoan> getNewEleLoan(Map<String, Object> map);

    /**
     * @description 更新删除状态
     * <AUTHOR>
     * @date 创建时间：2018年03月05日
     */
    public void deleteEleLoan(EleLoan eleLoan);

    public List<EleLoan> getAmountListList(Map<String, Object> map);

    public List<EleLoan> queryPushEleLoanPageInfo(Map<String, Object> map);

    /**
     * @description 删除未推送的汇总
     * <AUTHOR>
     * @date 创建时间：2018年03月05日
     */
    public void deletePushEleLoan(@Param("ids") List<String> ids);

    public EleLoan getCheckResult(Map<String, Object> map);

    public void updateBillamountIdByLoanId(Map<String, Object> map);

    /**
     * @description 根据汇总单主键更新
     * <AUTHOR>
     */
    public void updateBillamountidIsNull(@Param("ids") List<String> ids);

    /**
     * @description 根据汇总单主键删除
     * <AUTHOR>
     */
    public List<EleLoan> queryEleLoanListByBillamount(@Param("billamountId") String billamountId);

    /**
     * @Title: updateBillamountidIsNull @Description: TODO(这里用一句话描述这个方法的作用) @param @param
     * billamountIds @param @param billamountdetailIds 设定文件 @return void 返回类型 @throws
     */

    public void updateBillamountIdNull(@Param("billamountIds") List<String> billamountIds,
                                       @Param("billamountdetailIds") List<String> billamountdetailIds);

    public List<EleLoan> queryEleLoanListForNoAll(Map<String, Object> paraMap);

    public List<EleLoan> queryEleLoanListForNoAllFcontract(Map<String, Object> paraMap);


    /**
     * @param @param supplierId
     * @param @param supplierIds    设定文件
     * @return void    返回类型
     * @throws
     * @Title: updateSupplierInfo
     * @Description: TODO(这里用一句话描述这个方法的作用)
     */

    public void updateSupplierInfo(@Param("supplierId") String supplierId, @Param("supplierIds") List<String> supplierIds);


    /**
     * @param @param elecontractId
     * @param @param elecontractIds    设定文件
     * @return void    返回类型
     * @throws
     * @Title: updateComtractInfo
     * @Description: TODO(这里用一句话描述这个方法的作用)
     */

    public void updateComtractInfo(@Param("elecontractId") String elecontractId, @Param("elecontractIds") List<String> elecontractIds);

    public String selectAttachementBusinessId(String attachementBusinessId);

    /**
     * 查找对应提单人id
     *
     * @param loanId
     * @return
     */
    public String selectOperateUserId(String loanId);

    /**
     * 根据ID和关键字key模糊查询
     *
     * @param paraMap
     * @return
     */
    EleLoan queryBeanByIdAndKey(Map<String, Object> paraMap);

    /**
     * @return List<EleLoan>    返回类型
     * @description 查询重复的缴费单数据
     * <AUTHOR>
     * @version V1.0
     * @date 2019年4月24日
     */
    public List<EleLoan> queryIntersection();

    public EleLoan queryMaxBillAccountEndedForLoan(String billaccountId);

    public EleLoan selectAttchementByLoadEntity(String bussinessId);

    // 根据id更新预付费得批量上传业务建
    public void updateEleLoandById(Map<String, Object> map);

    public void updateNewFlagByBillamountId(Billamount amount);

    List<EleLoanSyncExportVO> selectEleLoanSyncExport(Map<String, Object> map);

    List<HistInsVO> histoicFlowList(String businessKeyTable);


    EleLoan querySubmitUserById(@Param("loanId") String loanId);

    public void updateNewFlagByBillamountId(@Param("billamountId") String billamountId, @Param("newFlag") int newFlag);

    public List<EleLoanBillamountExport> exportVerificationDetails(Map paramMap);

    public List<EleLoan> queryEleLoanNewFlag(EleVerificationBillamountdetail eleVerificationBillamountdetail);

    void updateRedundancyEleLoan(EleLoan eleLoan);

    EleLoan getAllEleLoanDetail(Map<String, Object> map);
    EleLoan getEleLoanVerfDetail(Map<String, Object> map);

    EleLoan getEleLoanVerfInfoDetail(Map<String, Object> map);

    List<EleLoan> queryEleLoanPage(Map<String, Object> map);

    List<EleLoan> queryEleLoanMin(List<String> billaccountIds);

    List<EleLoan> queryEleLoanPageBetter(Map<String, Object> map);

    List<EleLoanSyncExportVO> selectEleLoanSyncExportNew(Map<String, Object> map);

    EleLoan getEleLoanCountVerification(Map<String, Object> map);

    List<EleLoan> queryLoanByBillaccountId(Map<String, Object> map);

    int updateLoanAdditionalRemark(Map<String, Object> paraMap);

    EleLoan selectEleLoanDetailForRefresh(Map<String, Object> paraMap);

    /**
     * 查询历史报账点的关联关系
     * @param map
     * @return
     */
    List<EleLoandetail> queryValidateStatus(Map<String, Object> map);

    List<EleLoan> queryThreeMonthNoVerificationDate(Map<String, Object> map);

    List<EleLoan> queryEleLoanAuditing(Map<String, Object> map);
    @MapKey("billaccountId")
    Map<String,EleLoan> queryMinLoanCodeByBillAccountIds(@Param("billaccountIds") List<String> billaccountIds);
    List<EleLoan> queryEleLoanAudited(Map<String, Object> map);

    EleLoan queryById(@Param("loanId") String loanId);
    List<EleLoan> queryVerificationId(@Param("loanIds") List<String> loanIds);
    EleLoan queryVerifByLoanId(@Param("loanId") String loanIds);

    int updateEleVerification(Map<String, Object> map);

    int updatePayFirstFlag(String verificationId);

    Cursor<EleLoanExportVo> downLoadEleLoanList(EleLoan eleLoan);

    List<EleLoanExportMeterInfo> findPayFirstMeterList(EleLoan eleLoan);

    List<VEleBillaccountVerificateInfo> queryPayFirstOverFlow(@Param("loanIds") List<String> loanIds);

   VEleBillaccountVerificateInfo queryPayFirstOverFlowOne(@Param("loanId") String loanId);
    List<EleLoan> queryLoanState(@Param("elecontractId") String elecontractId);

    Integer queryContractBuymethod(@Param("billaccountId") String billaccountId);

    String queryVerificateEndDate(@Param("verificationId") String verificationId);

    String queryMeterTypeEnum(@Param("loanId") String loanId);
}