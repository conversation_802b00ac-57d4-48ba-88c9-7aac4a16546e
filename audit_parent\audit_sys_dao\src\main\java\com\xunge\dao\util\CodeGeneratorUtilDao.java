package com.xunge.dao.util;

import org.apache.ibatis.annotations.Mapper;

import java.util.Map;
@Mapper
public interface CodeGeneratorUtilDao {
    /**
     * 查询最小不连续code
     *
     * @param param
     * @return
     * <AUTHOR>
     */
    public String selectMinCodeFromTable(Map<?, ?> param);

    /**
     * 查询自增最大code
     *
     * @param param
     * @return
     * <AUTHOR>
     */
    public String selectMaxCodeFromTable(Map<?, ?> param);

    /**
     * 获取最大可用编码完整信息
     *
     * @param param
     * @return
     * <AUTHOR>
     */
    public String selectMaxCodeAllInfoFromTable(Map<?, ?> param);

}
