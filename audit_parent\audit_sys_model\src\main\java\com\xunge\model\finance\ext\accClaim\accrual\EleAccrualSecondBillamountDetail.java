package com.xunge.model.finance.ext.accClaim.accrual;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 计提二次汇总明细信息
 *
 * <AUTHOR>
 * @date 2020-07-31
 */
@Data
public class EleAccrualSecondBillamountDetail {

    private String prvId;

    private String pregId;

    private String regId;

    /**
     * 二次汇总编码
     */
    private String secondBillamountCode;

    /**
     * 列账金额(二次汇总计提金额)
     */
    private BigDecimal bookAmount;

    /**
     * 合同编码
     */
    private String contractCode;

    /**
     * 合同名称
     */
    private String contractName;

    /**
     * 供应商编码
     */
    private String supplierCode;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 业务大类编码
     */
    private String classCode;

    /**
     * 业务大类名称
     */
    private String className;

    /**
     * 业务小类编码
     */
    private String classSmCode;

    /**
     * 业务小类名称
     */
    private String classSmName;

    /**
     * 业务活动编码
     */
    private String activityCode;

    /**
     * 业务活动名称
     */
    private String activityName;

    /**
     * 所属成本中心
     */
    private String costCenter;

    /**
     * 所属成本中心描述
     */
    private String costCenterDesc;

    /**
     * 市场段编码
     */
    private String marketCode;

    /**
     * 市场段名称
     */
    private String marketName;

    /**
     * 产品段编码
     */
    private String productCode;

    /**
     * 产品段名称
     */
    private String productName;

    /**
     * 计提单id 记录明细用
     */
    private String accrualId;

    /**
     * 合同/固化id
     */
    private String contractId;

    /**
     * 汇总单id
     */
    private String billamountId;
    
    /**
     * 是否包干
     */
    private Integer isIncludeAll;

    /**
     * 报账点数量
     */
    private Integer includeBillaccountNum;

}
