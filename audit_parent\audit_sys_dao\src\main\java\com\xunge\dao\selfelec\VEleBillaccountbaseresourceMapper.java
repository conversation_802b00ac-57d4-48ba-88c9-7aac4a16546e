package com.xunge.dao.selfelec;

import com.xunge.model.selfelec.VEleBillaccountbaseresource;
import com.xunge.model.selfelec.VEleBillaccountbaseresourceExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface VEleBillaccountbaseresourceMapper {
    
    int countByExample(VEleBillaccountbaseresourceExample example);

    
    int deleteByExample(VEleBillaccountbaseresourceExample example);

    
    int insert(VEleBillaccountbaseresource record);

    
    int insertSelective(VEleBillaccountbaseresource record);

    
    List<VEleBillaccountbaseresource> selectByExample(VEleBillaccountbaseresourceExample example);

    List<VEleBillaccountbaseresource> selectByExample2(Map<String, Object> map);

    
    int updateByExampleSelective(@Param("record") VEleBillaccountbaseresource record,
                                 @Param("example") VEleBillaccountbaseresourceExample example);

    
    int updateByExample(@Param("record") VEleBillaccountbaseresource record, @Param("example") VEleBillaccountbaseresourceExample example);
}