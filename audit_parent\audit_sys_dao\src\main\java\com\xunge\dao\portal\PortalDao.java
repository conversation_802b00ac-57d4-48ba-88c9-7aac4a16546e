package com.xunge.dao.portal;

import com.xunge.model.portal.EleInfo;
import com.xunge.model.portal.RentInfo;
import com.xunge.model.portal.SiteInfo;
import com.xunge.model.portal.TowerInfo;

import java.util.List;
import java.util.Map;

/**
 * @ClassName PortalDao
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/9/21 11:20
 */
public interface PortalDao {
    List<SiteInfo> querySite(Map<String, Object> param);

    List<SiteInfo> querySiteHq(Map<String, Object> param);

    List<EleInfo> queryEle(Map<String, Object> param);

    List<EleInfo> queryEleHq(Map<String, Object> param);

    List<RentInfo> queryRent(Map<String, Object> param);

    List<RentInfo> queryRentHq(Map<String, Object> param);

    List<TowerInfo> queryTower(Map<String, Object> param);

    List<TowerInfo> queryTowerHq(Map<String, Object> param);

    List<Map<String, Object>> getEleReportHq(Map<String, Object> param);

    List<Map<String, Object>> getEleReport(Map<String, Object> param);

    List<Map<String, Object>> getRentReportHq(Map<String, Object> param);

    List<Map<String, Object>> getRentReport(Map<String, Object> param);

    List<Map<String, Object>> getTowerReportHq(Map<String, Object> param);

    List<Map<String, Object>> getTowerReport(Map<String, Object> param);

    String getPrvId(Map<String, Object> param);
}
