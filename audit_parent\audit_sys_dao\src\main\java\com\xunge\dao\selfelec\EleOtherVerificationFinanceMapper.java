package com.xunge.dao.selfelec;

import com.xunge.model.selfelec.EleOtherVerificationFinance;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Version: 1.0.0
 * @Author: <PERSON>
 * @Email: <EMAIL>
 * @Time: 2019-08-05 09:15
 * @Description:
 */
public interface EleOtherVerificationFinanceMapper {
    /**
     * 查询对应缴费单主键的所有其它费用集合
     *
     * @param billaccountpaymentdetailId 核销缴费单主键
     * @return 集合
     */
    List<EleOtherVerificationFinance> queryOtherAmountFinanceList(@Param("billaccountpaymentdetailId") String billaccountpaymentdetailId);

    int insert(EleOtherVerificationFinance eleOtherVerificationFinance);

    /**
     * 根据汇总单ID删除明细信息
     *
     * @param billamountId
     */
    int deleteByBillamountId(String billamountId);

    /**
     * 根据汇总单详情ID删除明细信息
     *
     * @param billamountDetailId
     */
    int deleteByBillamountDetailId(String billamountDetailId);

}
