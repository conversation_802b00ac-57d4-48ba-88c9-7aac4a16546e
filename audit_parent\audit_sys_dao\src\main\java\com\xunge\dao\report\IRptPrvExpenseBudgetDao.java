package com.xunge.dao.report;

import com.xunge.core.page.Page;
import com.xunge.model.report.RptPrvExpenseBudgetApplyLogVO;
import com.xunge.model.report.RptPrvExpenseBudgetApplyVO;
import com.xunge.model.report.RptPrvExpenseBudgetStateVO;
import com.xunge.model.report.RptPrvExpenseBudgetVO;

import java.util.List;
import java.util.Map;

/**
 * @创建人 LiangCheng
 * @创建时间 2018/12/12 0012
 * @描述：
 */
public interface IRptPrvExpenseBudgetDao {

    //查询预算管理
    public List<RptPrvExpenseBudgetVO> queryExpenseBudget(RptPrvExpenseBudgetVO rptPrvExpenseBudgetVO);

    //查询预算管理  合计
    public List<RptPrvExpenseBudgetVO> queryExpenseBudgetTotal(RptPrvExpenseBudgetVO rptPrvExpenseBudgetVO);

    //新增预算管理
    public void saveExpenseBudget(RptPrvExpenseBudgetVO rptPrvExpenseBudgetVO);

    //修改预算管理
    public void editExpenseBudget(RptPrvExpenseBudgetVO rptPrvExpenseBudgetVO);

    //查询地市费用预算列表
    public List<RptPrvExpenseBudgetVO> queryPregExpenseBudget(RptPrvExpenseBudgetVO rptPrvExpenseBudgetVO);

    //查询地市费用预算列表
    public List<RptPrvExpenseBudgetVO> queryPregExpenseBudgetTotal(RptPrvExpenseBudgetVO rptPrvExpenseBudgetVO);

    //查询区县费用预算列表
    public List<RptPrvExpenseBudgetVO> queryRegExpenseBudget(RptPrvExpenseBudgetVO rptPrvExpenseBudgetVO);

    //查询区县费用预算列表
    public List<RptPrvExpenseBudgetVO> queryRegExpenseBudgetTotal(RptPrvExpenseBudgetVO rptPrvExpenseBudgetVO);

    //查询集团侧各省份预算执行情况
    public List<RptPrvExpenseBudgetStateVO> queryExpenseBudgetProgressGroup(Map<String, Object> map);

    //查询集团侧各省份预算执行情况
    public List<RptPrvExpenseBudgetStateVO> queryExpenseBudgetProgressGroupTotal(Map<String, Object> map);

    //查询省侧各地市预算执行情况
    public List<RptPrvExpenseBudgetStateVO> queryExpenseBudgetProgressProvince(Map<String, Object> map);

    //查询省侧各地市预算执行情况
    public List<RptPrvExpenseBudgetStateVO> queryExpenseBudgetProgressProvinceTotal(Map<String, Object> map);

    //查询省侧各区县预算执行情况
    public List<RptPrvExpenseBudgetStateVO> queryExpenseBudgetProgressCity(Map<String, Object> map);

    //查询省侧各区县预算执行情况
    public List<RptPrvExpenseBudgetStateVO> queryExpenseBudgetProgressCityTotal(Map<String, Object> map);

    //费用预算申请
    public void saveExpenseBudgetApply(Map<String, Object> map);

    public void editExpenseState(Map<String, Object> map);

    public void editExpenseApplyState(Map<String, Object> map);

    public List<RptPrvExpenseBudgetApplyVO> queryExpenseBudgetApply(Map<String, Object> map);

    public void saveExpenseBudgetApplyLog(Map<String, Object> map);

    public List<RptPrvExpenseBudgetApplyLogVO> queryExpenseBudgetApplyHistoryList(Map<String, Object> map);

    public Page<RptPrvExpenseBudgetApplyVO> queryExpenseBudgetApplyList(Map<String, Object> map, int pageNumber, int pageSize);

    public String queryApplyCount(Map<String, Object> map);


}
