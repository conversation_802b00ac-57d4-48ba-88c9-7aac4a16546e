package com.xunge.core.util.date;

import com.thoughtworks.xstream.converters.ConversionException;
import com.thoughtworks.xstream.converters.Converter;
import com.thoughtworks.xstream.converters.MarshallingContext;
import com.thoughtworks.xstream.converters.UnmarshallingContext;
import com.thoughtworks.xstream.io.HierarchicalStreamReader;
import com.thoughtworks.xstream.io.HierarchicalStreamWriter;
import com.xunge.core.util.DateUtils;
import lombok.extern.slf4j.Slf4j;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;

@Slf4j
public class DateConverter implements Converter {
    public static Date converteToDateTime(String value) {
        Date d = null;
        SimpleDateFormat dateFm = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            d = dateFm.parse(value);
        } catch (ParseException e) {
            log.error("DateConverter 出错", e);
        }
        return d;
    }

    public static Date converteToDate(String value) {
        Date d = null;
        try {
            d = DateUtils.parseDate(value, "yyyy-MM-dd", "yyyy/MM/dd");
        } catch (ParseException e) {
            log.error("DateConverter 出错", e);
        }
        return d;
    }

    public static Date getDate(int dayNmu) {
        Date dNow = new Date();
        Date dBefore = new Date();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(dNow);
        calendar.add(5, dayNmu);
        dBefore = calendar.getTime();
        return dBefore;
    }

    public static String getCurrectTime() {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date d = new Date();
        return formatter.format(d);
    }

    public static boolean isValidDate(String dataString) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            sdf.parse(dataString);
        } catch (ParseException e) {
            return false;
        }
        return true;
    }

    public static String getTimeByDate(Date d) {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return formatter.format(d);
    }

    @Override
    public boolean canConvert(Class arg0) {
        return Date.class == arg0;
    }

    @Override
    public void marshal(Object arg0, HierarchicalStreamWriter arg1, MarshallingContext arg2) {
        SimpleDateFormat dateFm = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        arg1.setValue(dateFm.format(arg0));
    }

    @Override
    public Object unmarshal(HierarchicalStreamReader reader, UnmarshallingContext arg1) {
        GregorianCalendar calendar = new GregorianCalendar();
        SimpleDateFormat dateFm = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            calendar.setTime(dateFm.parse(reader.getValue()));
        } catch (ParseException e) {
            throw new ConversionException(e.getMessage(), e);
        }
        return calendar.getTime();
    }
}
