package com.xunge.model.basedata;

import com.xunge.model.BaseActVO;

import java.util.Date;

public class DatBaseresourceNote extends BaseActVO {
    private String updateBaseresourceId;

    private String updateNote;

    private String updatePrvId;

    private Date startTime;

    private String operateUserId;

    public String getUpdateBaseresourceId() {
        return updateBaseresourceId;
    }

    public void setUpdateBaseresourceId(String updateBaseresourceId) {
        this.updateBaseresourceId = updateBaseresourceId;
    }

    public String getUpdateNote() {
        return updateNote;
    }

    public void setUpdateNote(String updateNote) {
        this.updateNote = updateNote;
    }

    public String getUpdatePrvId() {
        return updatePrvId;
    }

    public void setUpdatePrvId(String updatePrvId) {
        this.updatePrvId = updatePrvId;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public String getOperateUserId() {
        return operateUserId;
    }

    public void setOperateUserId(String operateUserId) {
        this.operateUserId = operateUserId;
    }

}