package com.xunge.dao.towerrent.room;

import com.xunge.model.towerrent.room.MobileRoom;

import java.util.List;
import java.util.Map;

/**
 * 描述：
 * Created on 2019/8/2.
 * <p>Title:</p>
 * <p>Copyright:Copyright (c) 2017</p>
 * <p>Company:安徽科大国创</p>
 * <p>Department:西南二区BU</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @update
 */
public interface IMobileRoomDao {

    /**
     * 根据条件获取移动侧起租数据
     *
     * @param paraMap
     * @return
     */
    List<MobileRoom> getMobileRentRoomsByParam(Map<String, Object> paraMap);

    /**
     * 复制铁塔起租单到移动侧
     *
     * @param auditMap
     * @return
     */
    int insertAsTower(Map<String, Object> auditMap);

    /**
     * 条件修改
     *
     * @param changeMap
     * @return
     */
    int updateMobileRentByParam(Map<String, Object> changeMap);

    /**
     * 设置部门
     *
     * @param map
     * @return
     */
    int setRecordDeptId(Map<String, Object> map);

    /**
     * 微站起租单批量插入
     *
     * @param map
     * @return
     */
    int mobileRoomBatchSave(Map<String, Object> map);
}
