package com.xunge.dao.twrrent.punish;

import com.xunge.core.page.Page;
import com.xunge.model.towerrent.punish.TwrRegPunishVO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 2017-07-20
 * @description 地市扣罚表
 */
public interface ITwrRegPunishDao {
    /**
     * @param list
     * @return
     * @description 根据ID编码物理删除
     * <AUTHOR>
     */
    public int deleteByPrimaryKey(List<String> list);

    /**
     * @param record
     * @return
     * @description 保存地市扣罚表数据
     * <AUTHOR>
     */
    public int insertTwrRegPunishVO(TwrRegPunishVO record);

    /**
     * @param record
     * @return
     * @description 批量保存城市扣罚数据
     * <AUTHOR>
     */
    public int insertBatchTwrRegPunishVO(List<TwrRegPunishVO> record);

    /**
     * @param record
     * @return
     * @description 保存地市扣罚表不为空的数据
     * <AUTHOR>
     */
    public int insertSelective(TwrRegPunishVO record);

    /**
     * @param twrRegPunishId
     * @return
     * @description 根据ID编码查询地方扣罚信息
     * <AUTHOR>
     */
    public TwrRegPunishVO selectByPrimaryKey(String twrRegPunishId);

    /**
     * @param twrRegPunishId
     * @return
     * @description 根据ID编码查询直辖地方扣罚信息
     * <AUTHOR>
     */
    public TwrRegPunishVO selectByPrimaryKeyMuni(String twrRegPunishId);


    /**
     * @param twrRegPunish
     * @return
     * @description 查询数据
     * <AUTHOR>
     */
    public List<TwrRegPunishVO> selectByTwrRegPunish(TwrRegPunishVO twrRegPunish);

    /**
     * @param record
     * @return
     * @description 修改地市扣罚表不为空的数据
     * <AUTHOR>
     */
    public int updateByPrimaryKeySelective(TwrRegPunishVO record);

    /**
     * @param record
     * @return
     * @description 修改地市扣罚表
     * <AUTHOR>
     */
    public int updateByPrimaryKey(TwrRegPunishVO record);

    /**
     * @param record
     * @return
     * @description 根据编码修改地市扣罚表状态
     * <AUTHOR>
     */
    public int updateStateByPrimaryKey(TwrRegPunishVO record);

    /**
     * @param record
     * @return
     * @description 根据编码修改地市扣罚表审核状态
     * <AUTHOR>
     */
    public int updateAuditStateByPrimaryKey(TwrRegPunishVO record);

    /**
     * @param record
     * @return
     * @description 根据编码修改汇总ID
     * <AUTHOR>
     */
    public int updateAccountsummaryIDByPrimaryKey(TwrRegPunishVO record);

    /**
     * @param record
     * @return
     * @description 根据编码集合批量修改
     * <AUTHOR>
     */
    public int updateAccountsummaryIDByBatchID(List<TwrRegPunishVO> record);

    /**
     * @param map
     * @return
     * @description 分页查询地方扣罚表
     * <AUTHOR>
     */
    public Page<TwrRegPunishVO> selectTwrRegPunishPage(Map<String, Object> map);

    /**
     * @param map
     * @return
     * @description 分页查询直辖市地市扣罚表
     * <AUTHOR>
     */
    public Page<TwrRegPunishVO> selectTwrRegPunishMuniPage(Map<String, Object> map);

    /**
     * @param map
     * @return
     * @description 查询地方扣罚集合
     * <AUTHOR>
     */
    public List<TwrRegPunishVO> selectTwrRegPunishList(Map<String, Object> map);

    List<TwrRegPunishVO> selectTwrRegPunishExpect(Map<String, Object> map);

    /**
     * @param params
     * @return
     * @description 根据参数查询地市指标扣罚
     * <AUTHOR>
     */
    List<Map<String, Object>> queryTwrRegPunishMapListByCondition(Map<String, Object> params);

    public int updatePunishSumcodeToNull(Map<String, Object> map);

    /**
     * 修改地市扣罚状态
     *
     * @param record
     * @return
     */
    int checkTwrRegPunish(TwrRegPunishVO record);
}