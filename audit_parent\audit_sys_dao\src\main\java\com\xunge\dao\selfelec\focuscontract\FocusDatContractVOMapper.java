package com.xunge.dao.selfelec.focuscontract;

import com.xunge.model.selfelec.focuscontract.FocusDatContract;
import com.xunge.model.selfelec.focuscontract.FocusDatContractVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface FocusDatContractVOMapper {

    //int deleteByPrimaryKey(String contractId);
    int deleteByPrimaryKey(Map<String, Object> paraMap);

    int insert(FocusDatContractVO record);

    FocusDatContractVO selectByPrimaryKey(Map<String, Object> paraMap);

    int updateByPrimaryKey(FocusDatContractVO contract);

    int updateByContractFlow(FocusDatContractVO focusDatContractVO);

    List<FocusDatContractVO> queryByContractFlow(FocusDatContractVO focusDatContractVO);

    List<FocusDatContractVO> queryByIds(@Param("ids") List<String> ids);

    int updateContractState(FocusDatContractVO focusDatContract);

    FocusDatContractVO queryByContractId(@Param("contractId") String contractId);

    int updateStateById(FocusDatContractVO focusDatContract);


    FocusDatContractVO getUrl(@Param("contractId") String contractId);


    FocusDatContractVO getEleUrl(String contractId);


    void deleteByContractIds(Map<String, Object> paraMap);


    /**
     * @param @param dv    设定文件
     * @return void    返回类型
     * @throws
     * @Title: updateByPrimaryKeySelective
     * @Description: TODO(这里用一句话描述这个方法的作用)
     */

    void updateByPrimaryKeySelective(FocusDatContractVO dv);

    public List<FocusDatContractVO> checkCode(Map<String, Object> map);

    public List<FocusDatContractVO> checkCode2(Map<String, Object> map);

    public void deleteExecution(Map<String, Object> map);

    public void deleteIdentitylink(Map<String, Object> map);

    public void deleteTask(Map<String, Object> map);

    public void deleteSubscr(Map<String, Object> map);

    public void deleteVariable(Map<String, Object> map);

    /**
     * @param @param dv    设定文件
     * @return void    返回类型
     * @throws
     * @Title:
     * @Description: 查询原合同及原合同的补充协议
     */
    List<String> getDatContractList(FocusDatContract fv);

    List<String> getProcInstId(Map<String, Object> map);

    void updateStatusByIds(@Param("contractIds") List<String> contractIds);

    FocusDatContractVO queryDatContractByFlow(FocusDatContractVO focusDatContractVO);


    /**
     * @param 设定文件
     * @return void    返回类型
     * @throws
     * @Title: queryContrractInfos
     * @Description: TODO(这里用一句话描述这个方法的作用)
     */

    List<FocusDatContractVO> queryContractInfos();


    /**
     * @param @param  focusDatContractVO
     * @param @return 设定文件
     * @return List<FocusDatContractVO>    返回类型
     * @throws
     * @Title: queryContracts
     * @Description: TODO(这里用一句话描述这个方法的作用)
     */

    List<FocusDatContractVO> queryContracts(FocusDatContractVO focusDatContractVO);


    /**
     * @param @param  focusDatContractVO
     * @param @return 设定文件
     * @return List<FocusDatContractVO>    返回类型
     * @throws
     * @Title: queryContractsByCode
     * @Description: TODO(这里用一句话描述这个方法的作用)
     */

    List<FocusDatContractVO> queryContractsByCode(FocusDatContractVO focusDatContractVO);


    /**
     * @param @param fv    设定文件
     * @return void    返回类型
     * @throws
     * @Title: insertFocusDatContract
     * @Description: TODO(这里用一句话描述这个方法的作用)
     */

    void insertFocusDatContract(FocusDatContract fv);


    /**
     * @param @param dv    设定文件
     * @return void    返回类型
     * @throws
     * @Title: updateFocusDatContract
     * @Description: TODO(这里用一句话描述这个方法的作用)
     */

    void updateFocusDatContract(FocusDatContract dv);

    List<FocusDatContractVO> queryFocusContractList(@Param("contractItem") String contractItem);
}