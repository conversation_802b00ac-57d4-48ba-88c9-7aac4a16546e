package com.xunge.dao.selfelec.electricmeter;


import com.xunge.model.selfelec.electricmeter.DatElectricmeterChangeRecord;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【dat_electricmeter_change_record】的数据库操作Mapper
* @createDate 2024-08-02 15:12:57
* @Entity generator.domain.DatElectricmeterChangeRecord
*/
public interface DatElectricmeterChangeRecordMapper {

    int deleteByPrimaryKey(Long id);

    int insert(DatElectricmeterChangeRecord record);

    int insertSelective(DatElectricmeterChangeRecord record);

    DatElectricmeterChangeRecord selectByPrimaryKey(Long id);
    List<DatElectricmeterChangeRecord> selectByMeterId(String meterId);

    int updateByPrimaryKeySelective(DatElectricmeterChangeRecord record);

    int updateByPrimaryKey(DatElectricmeterChangeRecord record);

    int insertForBatch(List<DatElectricmeterChangeRecord> record);

	List<DatElectricmeterChangeRecord> queryResetRecord(String meterId);

}
