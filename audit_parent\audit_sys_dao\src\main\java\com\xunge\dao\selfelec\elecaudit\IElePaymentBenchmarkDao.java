package com.xunge.dao.selfelec.elecaudit;

import com.xunge.model.selfelec.ElePaymentBenchmark;
import com.xunge.model.selfelec.ElecPaymentBenchmarkInfo;
import com.xunge.model.selfelec.benchmark.EleBenchmarkActualBean;

import java.util.List;
import java.util.Map;

public interface IElePaymentBenchmarkDao {
    /**
     * 条件查询缴费标杆信息
     *
     * @param paramMap
     * @return
     * <AUTHOR>
     */
    public List<ElePaymentBenchmark> queryAllByForginKey(Map<String, Object> paramMap);

    /**
     * 缴费标杆信息新增
     *
     * @param list
     * @return
     * <AUTHOR>
     */
    public int insertBenchmarkInfo(List<ElePaymentBenchmark> paramMap);

    /**
     * 删除缴费标杆数据
     *
     * @param param
     * @return
     * <AUTHOR>
     */
    public int delPaymentBenchmark(String billaccountpaymentdetailId);

    /**
     *
     */
    public List<ElecPaymentBenchmarkInfo> getRecorBenchmarkData();

    /**
     * 批量插入标杆值
     * @param list
     * @return
     */
    int insertBenchmarkActual(List<EleBenchmarkActualBean> list);

    /**
     * 根据缴费单编码查标杆实际值
     * @param billaccountpaymentdetailId
     * @return
     */
    List<EleBenchmarkActualBean> selectBenchmarkActualByPid(String billaccountpaymentdetailId);

    /**
     * 根据主键删除标杆实际值
     * @param ids
     */
    int deleteBenchmarkActualByIds(List<Integer> ids);
}
