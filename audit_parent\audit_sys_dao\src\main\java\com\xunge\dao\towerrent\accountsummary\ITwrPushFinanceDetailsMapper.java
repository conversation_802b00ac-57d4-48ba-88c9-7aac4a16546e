package com.xunge.dao.towerrent.accountsummary;

import com.xunge.model.towerrent.accountsummary.TwrPushFinanceDetails;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Description:
 * @Author: dxd
 * @Date: 2023/3/21 16:05
 */
public interface ITwrPushFinanceDetailsMapper {

    int insertDetails(List<TwrPushFinanceDetails> pushFinanceDetails);

    int deletePushDetails(@Param("twrAccountSummaryCode") String twrAccountSummaryCode);

    int updatePunshDetail(@Param("costCenter") String costCenter,@Param("twrAccountSummaryId") String twrAccountSummaryId);
}
