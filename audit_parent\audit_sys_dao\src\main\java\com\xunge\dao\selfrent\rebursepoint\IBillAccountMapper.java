package com.xunge.dao.selfrent.rebursepoint;

import com.xunge.model.selfrent.rebursepoint.ResourceCanChoose;
import com.xunge.model.selfrent.rebursepoint.ResourceCanChooseRequest;
import com.xunge.model.selfrent.rebursepoint.TowerCanChoose;
import com.xunge.model.selfrent.rebursepoint.TowerCanChooseRequest;

import java.util.List;

public interface IBillAccountMapper {

    /**
     * 查询可选择的资源（普通报账点、特殊报账点）
     * @param query
     * @return
     */
    List<ResourceCanChoose> queryResourceCanChooseForBillaccount(ResourceCanChooseRequest query);

    /**
     * 查询可选择的资源（一站多合同报账点）
     * @param query
     * @return
     */
    List<ResourceCanChoose> queryResourceCanChooseForMultiBillaccount(ResourceCanChooseRequest query);

    /**
     * 查询报账点可选择的铁塔
     * @param query
     * @return
     */
    List<TowerCanChoose> queryTowerCanChooseForBillaccount(TowerCanChooseRequest query);
}
