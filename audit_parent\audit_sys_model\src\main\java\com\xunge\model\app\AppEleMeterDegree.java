package com.xunge.model.app;

import java.math.BigDecimal;
import java.util.Date;

public class AppEleMeterDegree {

    private String uuid;

    /**
     * 电表id
     */
    private String meterId;

    /**
     * 电表编码
     */
    private String meterCode;

    /**
     * 用户id
     */
    private String createUserId;

    /**
     * 数据类型
     */
    private String sourceType;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 电表读数
     */
    private BigDecimal degreeNum;

    /**
     * 创建用户
     */
    private String createUserName;

    /**
     * 是否手动录入 1自动录入 2手动录入
     */
    private Integer isAuto;

    /**
     * 文件上传路径
     */
    private String filePath;

    /**
     * 经度
     */
    private BigDecimal longitude;

    /**
     * 纬度
     */
    private BigDecimal latitude;

    /**
     * 抄表地址
     */
    private String address;

    /**
     * 备注
     */
    private String remark;

    /**
     * 手工录入电表读数
     */
    private BigDecimal degreeNumManMade;

    private String fileByte; //app附件的二进制再base64编码

    /**
     * '充值前读数'
     */
    private BigDecimal degreeNumBefore;
    /**
     * '充值后读数'
     */
    private BigDecimal degreeNumAfter;
    /**
     * '充值前照片'
     */
    private String filePathBefore;
    /**
     * '充值后照片'
     */
    private String filePathAfter;

    public String getFileByte() {
        return fileByte;
    }

    public void setFileByte(String fileByte) {
        this.fileByte = fileByte;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid == null ? null : uuid.trim();
    }

    public String getMeterId() {
        return meterId;
    }

    public void setMeterId(String meterId) {
        this.meterId = meterId == null ? null : meterId.trim();
    }

    public String getMeterCode() {
        return meterCode;
    }

    public void setMeterCode(String meterCode) {
        this.meterCode = meterCode == null ? null : meterCode.trim();
    }

    public String getCreateUserId() {
        return createUserId;
    }

    public void setCreateUserId(String createUserId) {
        this.createUserId = createUserId == null ? null : createUserId.trim();
    }

    public String getSourceType() {
        return sourceType;
    }

    public void setSourceType(String sourceType) {
        this.sourceType = sourceType == null ? null : sourceType.trim();
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public BigDecimal getDegreeNum() {
        return degreeNum;
    }

    public void setDegreeNum(BigDecimal degreeNum) {
        this.degreeNum = degreeNum;
    }

    public String getCreateUserName() {
        return createUserName;
    }

    public void setCreateUserName(String createUserName) {
        this.createUserName = createUserName == null ? null : createUserName.trim();
    }

    public Integer getIsAuto() {
        return isAuto;
    }

    public void setIsAuto(Integer isAuto) {
        this.isAuto = isAuto;
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath == null ? null : filePath.trim();
    }

    public BigDecimal getLongitude() {
        return longitude;
    }

    public void setLongitude(BigDecimal longitude) {
        this.longitude = longitude;
    }

    public BigDecimal getLatitude() {
        return latitude;
    }

    public void setLatitude(BigDecimal latitude) {
        this.latitude = latitude;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address == null ? null : address.trim();
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

    public BigDecimal getDegreeNumManMade() {
        return degreeNumManMade;
    }

    public void setDegreeNumManMade(BigDecimal degreeNumManMade) {
        this.degreeNumManMade = degreeNumManMade;
    }

    public BigDecimal getDegreeNumBefore() {
        return degreeNumBefore;
    }

    public void setDegreeNumBefore(BigDecimal degreeNumBefore) {
        this.degreeNumBefore = degreeNumBefore;
    }

    public BigDecimal getDegreeNumAfter() {
        return degreeNumAfter;
    }

    public void setDegreeNumAfter(BigDecimal degreeNumAfter) {
        this.degreeNumAfter = degreeNumAfter;
    }

    public String getFilePathBefore() {
        return filePathBefore;
    }

    public void setFilePathBefore(String filePathBefore) {
        this.filePathBefore = filePathBefore;
    }

    public String getFilePathAfter() {
        return filePathAfter;
    }

    public void setFilePathAfter(String filePathAfter) {
        this.filePathAfter = filePathAfter;
    }
}