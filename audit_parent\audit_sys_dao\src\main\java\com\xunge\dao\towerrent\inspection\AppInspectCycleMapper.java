package com.xunge.dao.towerrent.inspection;

import com.xunge.model.towerrent.inspection.AppInspectCycle;

import java.util.List;

/**
 * 巡检周期相关操作
 *
 * <AUTHOR>
 * @date 2019/5/12 22:05
 */
public interface AppInspectCycleMapper {

    /**
     * 增加记录
     *
     * @param record
     * @return
     */
    int insertSelective(AppInspectCycle record);

    /**
     * 逻辑删除
     *
     * @param cycleId
     * @return
     */
    int deleteByPrimaryKey(String cycleId);

    /**
     * 修改
     *
     * @param record
     * @return
     */
    int updateByPrimaryKeySelective(AppInspectCycle record);

    /**
     * 通过主键查询
     *
     * @param cycleId
     * @return
     */
    AppInspectCycle selectByPrimaryKey(String cycleId);

    /**
     * 通过地市ID查询
     *
     * @param pregId
     * @return
     */
    AppInspectCycle selectByPregId(String pregId);

    /**
     * 通过实体查询
     *
     * @param appInspectCycle
     * @return
     */
    List<AppInspectCycle> query(AppInspectCycle appInspectCycle);

}
