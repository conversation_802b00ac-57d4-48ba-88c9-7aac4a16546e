package com.xunge.dao.twrrent.monthlyReport;

import com.xunge.model.towerrent.monthlyReport.RptMonthlyKeyIndexAnalysisVo;

import java.util.List;
import java.util.Map;

public interface IMonthlyReportKeyIndexAnalysisDao {

    /**
     * 查询上月关键指标分析月报基础数据
     *
     * @return
     */
    Map<Object, Object> createMonthlyReportKeyIndexAnalysisThis(Map<String, Object> map);

    /**
     * 查询上上月关键指标分析月报基础数据
     *
     * @return
     */
    Map<Object, Object> createMonthlyReportKeyIndexAnalysisBefore(Map<String, Object> map);

    /**
     * 新增关键指标分析月报数据
     *
     * @param list 月报数据对象
     */
    int insertMonthlyReportKeyIndexAnalysis(List<RptMonthlyKeyIndexAnalysisVo> list);

    /**
     * 删除关键指标分析月报数据
     *
     * @param map 月报数据对象
     * @return
     */
    int deleteMonthlyReportKeyIndexAnalysis(Map<String, Object> map);

    /**
     * 根据省份ID查询关键指标分析月报数据
     *
     * @param map
     * @return
     */
    List<RptMonthlyKeyIndexAnalysisVo> selectMonthlyReportKeyIndexAnalysis(Map<String, Object> map);

    /**
     * 查询关键指标分析月报数据
     *
     * @param map 省份、地市、区县
     * @return
     */
    List<RptMonthlyKeyIndexAnalysisVo> queryMonthlyReportKeyIndexAnalysis(Map<String, Object> map);
}
