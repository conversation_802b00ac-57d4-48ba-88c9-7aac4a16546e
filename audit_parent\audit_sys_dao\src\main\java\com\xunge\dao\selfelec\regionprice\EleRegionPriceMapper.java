package com.xunge.dao.selfelec.regionprice;

import com.xunge.model.selfelec.regionprice.EleRegionPriceVO;

import java.util.List;
import java.util.Map;

/**
 * 区域单价表
 * <AUTHOR>
 */
public interface EleRegionPriceMapper {

    /**
     * 保存区域单价
     * @param map
     * @return
     */
    Integer saveEleRegionPrice(Map<String, Object> map);

    /**
     * 编辑区域单价
     * @param map
     * @return
     */
    Integer editEleRegionPrice(Map<String, Object> map);

    /**
     * 查询单个区域单价信息
     * @param map
     * @return
     */
    EleRegionPriceVO queryEleRegionPrice(Map<String, Object> map);

    /**
     * 查询全部区域单价信息（不包含已删除）
     * @param map
     * @return
     */
    List<EleRegionPriceVO> queryAllEleRegionPrice(Map<String, Object> map);

    /**
     * 删除区域单价
     * @param map
     * @return
     */
    Integer delEleRegionPrice(Map<String, Object> map);

    /**
     * 批量导入
     * @param map
     */
    void insertImportEleRegionPrice(Map<String, Object> map);
    /**
     * 带条件根据配置颗粒粒度查询获取单价-查单条
     * @param param
     * @return
     */
    EleRegionPriceVO getOneEleRegionPriceWithConfigByCondition(Map<String, Object> param);
    /**
     * 带条件根据配置颗粒粒度查询获取单价-分页
     * @param param
     * @return
     */
    List<EleRegionPriceVO> getListEleRegionPriceWithConfigByCondition(Map<String, Object> param);

    /**
     * 提交审核 保存
     * @param map
     * @return
     */
    Integer auditEleRegionPrice(Map<String, Object> map);

    /**
     * 区域单价校验
     * @param map
     * @return
     */
    List<EleRegionPriceVO> queryEleRegionPriceCheck(Map<String, Object> map);
}
