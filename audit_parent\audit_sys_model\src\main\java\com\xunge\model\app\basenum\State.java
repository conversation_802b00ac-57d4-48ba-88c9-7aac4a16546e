package com.xunge.model.app.basenum;

/**
 * @Auther: hxh
 * @Date: 2018/9/2 19:48
 * @Description:
 */
public enum State {

    /**
     * 可用
     */
    usable("0", 0),
    /**
     * 已删除、未审核
     */
    deleted("-1", 1),

    /**
     * 停用
     */
    stop("9", 9);

    private String strValue;

    private int intValue;

    State(String strValue, int intValue) {
        this.strValue = strValue;
        this.intValue = intValue;
    }

    public String getStrValue() {
        return strValue;
    }

    public void setStrValue(String strValue) {
        this.strValue = strValue;
    }

    public int getIntValue() {
        return intValue;
    }

    public void setIntValue(int intValue) {
        this.intValue = intValue;
    }
}
