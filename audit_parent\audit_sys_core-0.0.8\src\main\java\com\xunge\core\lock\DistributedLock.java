package com.xunge.core.lock;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.data.redis.core.script.DefaultRedisScript;

import java.util.Collections;
import java.util.UUID;

/**
 * 基于 StringRedisTemplate 实现的分布式锁
 */
@Slf4j
@Component
public class DistributedLock {

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    // Lua脚本保证原子性：判断值是否匹配并删除key
    private static final String RELEASE_LOCK_SCRIPT =
        "if redis.call('get', KEYS[1]) == ARGV[1] then " +
        "return redis.call('del', KEYS[1]) " +
        "else return 0 end";

    private final DefaultRedisScript<Long> releaseLockScript =
        new DefaultRedisScript<>(RELEASE_LOCK_SCRIPT, Long.class);

    /**
     * 加锁操作（带超时）
     *
     * @param lockName       锁的 key
     * @param acquireTimeout 获取锁的最大等待时间（毫秒）
     * @param timeout        锁的过期时间（毫秒）
     * @return 锁标识（UUID），用于后续解锁
     */
    public String lockWithTimeout(String lockName, long acquireTimeout, long timeout) {
        String identifier = UUID.randomUUID().toString();
        String lockKey = "lock:" + lockName;
        long endTime = System.currentTimeMillis() + acquireTimeout;
        long expireSeconds = timeout / 1000;

        while (System.currentTimeMillis() < endTime) {
            Boolean success = stringRedisTemplate.opsForValue()
                .setIfAbsent(lockKey, identifier, java.time.Duration.ofSeconds(expireSeconds));
            if (Boolean.TRUE.equals(success)) {
                return identifier;
            }
            try {
                Thread.sleep(10);
            } catch (InterruptedException e) {
                log.error("DistributedLock 加锁中断异常", e);
//                Thread.currentThread().interrupt();
            }
        }

        return null; // 获取锁失败
    }

    /**
     * 释放锁（使用Lua脚本确保原子性）
     *
     * @param lockName   锁的 key
     * @param identifier 锁的标识符（加锁时返回的UUID）
     * @return 是否成功释放
     */
    public boolean releaseLock(String lockName, String identifier) {
        String lockKey = "lock:" + lockName;
        try {
            Long result = stringRedisTemplate.execute(releaseLockScript,
                Collections.singletonList(lockKey), identifier);
            return result != null && result > 0;
        } catch (Exception e) {
            log.error("redis释放锁异常", e);
            return false;
        }
    }
}
