package com.xunge.dao.selfelec;

import com.xunge.model.payment.PaymentExportOtherInfo;
import com.xunge.model.selfelec.*;
import com.xunge.model.selfelec.dto.QueryRepeatDto;
import com.xunge.model.selfelec.inspect.ElectricPredictionPaymentCycle;
import com.xunge.model.selfelec.vo.CommonMeterDataVo;
import com.xunge.model.selfelec.vo.EleOtherAmountFinanceLastBillamountDateVO;
import com.xunge.model.selfelec.vo.PaymentResourceDetailVo;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;
import java.util.Map;

/**
 * @创建人 LiXs
 * @创建时间 2019/11/11
 * @描述：
 */
public interface ElecPaymentMapper {

    /**
     * 根据缴费单编码查询缴费记录
     *
     * @param paymentCode
     * @return
     */
    List<ElecPaymentVO> queryElecPaymentVOByPaymentCode(String paymentCode);

    /**
     * 查询数据库中最大数值的code
     *
     * @param param
     * @return
     */
    Map<String, Object> queryMaxCode(Map<String, Object> param);

    List<VEleBillaccountPaymentInfo> queryUserListByIds(Map<String, Object> cond);

    List<PaymentResourceDetailVo> queryPaymentResource(@Param("billaccountId") String billaccountId, @Param("paymentCode") String paymentCode);
    List<PaymentResourceDetailVo> queryPaymentResourceNew(@Param("billaccountId") String billaccountId, @Param("paymentCode") String paymentCode,@Param("startDate") String startDate,@Param("endDate") String endDate);
    List<Map<String,String>> queryPaymentPowerdata(@Param("billaccountId") String billaccountId, @Param("paymentCode") String paymentCode);
    List<Map<String,String>> queryBillaccounPowerdata(@Param("billaccountId") String billaccountId);
    List<PaymentResourceDetailVo> queryDatBaseResourceForAdd(String billaccountId);

    List<PaymentResourceDetailVo> queryDatBaseResourceForAddNet(@Param("billaccountId")String billaccountId,@Param("startDate") String startDate,@Param("endDate") String endDate);

    @Delete("DELETE FROM payment_resource_detail WHERE payment_code=#{paymentCode}")
    void deletePaymentResource(@Param("paymentCode") String paymentCode);

    void insertPaymentResource(@Param("voList") List<PaymentResourceDetailVo> voList);

    void updatePaymentResource(List<PaymentResourceDetailVo> list);

    List<PaymentResourceDetailVo> queryDatBaseResource(@Param("list") List<String> list);

    List<String> getPaymentCodes(Map<String, Object> paraMap);

    List<String> getVerificateCodes(Map<String, Object> paraMap);

    void deletePaymentResources(@Param("list") List<String> list);

    int deletePaymentPowerdataDetail(@Param("list") List<String> list);

    /**
     * 获取最近一笔退网状态的缴费数据
     *
     * @param baseresourceId 资源id
     * @return 结果
     */
    PaymentResourceDetailVo queryLastResourceLogout(@Param("paymentCode") String paymentCode, @Param("baseresourceId") String baseresourceId, @Param("dataFrom") Integer dataFrom);

    List<PaymentResourceDetailVo> queryLastResourceLogoutNew(@Param("list") List<String> list, @Param("paymentCode") String paymentCode, @Param("paymentCheckResType") String paymentCheckResType);

    void updatePaymentResourceDetail(@Param("paymentCode") String paymentCode, @Param("billamountStart") String billamountStart,
                                     @Param("billamountEnd") String billamountEnd);

    /**
     * 更新缴费单 插入当前登陆人信息
     *
     * @param param
     */
    void updatePaymentByCurrentUser(Map param);


    /**
     * 最近一个报账点缴费电表数据
     *
     * @param vo
     * @return 结果
     */
    CommonMeterDataVo getLastOneMeterData(CommonMeterDataVo vo);

    @Select("select meter_id from ele_paymentdetail where billaccountpaymentdetail_id=#{billaccountpaymentdetailId}")
    List<String> getMeterIds(String billaccountpaymentdetailId);

    @Update("update ele_payment set meter_change=#{meterChange} where payment_code=#{paymentCode}")
    void updateMeterChange(@Param("paymentCode") String paymentCode, @Param("meterChange") String meterChange);

    /**
     * 得到合同信息
     *
     * @param elecontractId 合同id
     * @return 结果
     */
    @Select("SELECT elecontract_id elecontractId,elecontract_price elecontractPrice," +
            "flat_price flatPrice,peak_price peakPrice," +
            "valley_price valleyPrice,top_price topPrice " +
            "FROM ele_contract  " +
            "WHERE elecontract_id=#{elecontractId} LIMIT 1")
    EleContract priceForContract(String elecontractId);

    /**
     * 得到合同信息(后付费)
     *
     * @param elecontractId 合同id
     * @return 结果
     */
    @Select("SELECT elecontract_id elecontractId,elecontract_price elecontractPrice," +
            "flat_price flatPrice,peak_price peakPrice," +
            "valley_price valleyPrice,top_price topPrice " +
            "FROM ele_contract_backups  " +
            "WHERE elecontract_id=#{elecontractId} " +
            "AND billaccountpaymentdetail_id=#{billaccountpaymentdetailId }LIMIT 1")
    EleContract priceForContractBackups(@Param("elecontractId") String elecontractId, @Param("billaccountpaymentdetailId") String billaccountpaymentdetailId);

    /**
     * 得到合同信息(核销)
     *
     * @param elecontractId 合同id
     * @return 结果
     */
    @Select("SELECT elecontract_id elecontractId,elecontract_price elecontractPrice," +
            "flat_price flatPrice,peak_price peakPrice," +
            "valley_price valleyPrice,top_price topPrice " +
            "FROM ele_contract_history  " +
            "WHERE elecontract_id=#{elecontractId} " +
            "AND verification_id=#{billaccountpaymentdetailId }LIMIT 1")
    EleContract priceForContractHistory(@Param("elecontractId") String elecontractId, @Param("billaccountpaymentdetailId") String billaccountpaymentdetailId);

    int updatePaymentAdditionalRemark(Map<String, Object> paraMap);

    String selectBillamountEndDate(@Param("billaccountId") String billaccountId);

    List<PaymentExportOtherInfo> queryOtherPaymentInfo(@Param("idList") List<String> idList);
    /**
     * GUYGMMAN-3296
     * 电费IC核销与后付费时段重复校验【**************】
     *
     * <AUTHOR>
     */
    List<QueryRepeatDto> queryRepeatPayment(QueryRepeatDto queryRepeatDto);

    /**
     * 根据缴费单CODE 获取 缴费单信息，自定义表
     */
    VEleBillaccountPaymentInfo queryPaymentByCode(@Param("tableName") String tableName,@Param("code") String code);

    List<BillaccountDateInfo> selectBillaccountDateInfo(@Param("billaccountIdList") List<String> billaccountIdList);

    List<Integer> selectPaymentResourceByCode(String paymentCode);

    void deletePaymentResourceByIds(@Param("list") List<Integer> list);

    String queryMaxPaymentCode(@Param("list") List<String> list, @Param("paymentCode") String paymentCode, @Param("paymentCheckResType") String paymentCheckResType);

    Map<String,String> selectBillamountIdFromElePaymentForUpdate(@Param("billaccountpaymentdetailId") String billaccountpaymentdetailId);

    List< Map<String,Object>> selectAuditingStateFromElePaymentForUpdate(@Param("idList")List<String> idList);

    String hasPaymentOrVerificationOfBillaccount(String billaccountId);

    String hasLoanOfBillaccount(String billaccountId);

    List<Map<String, Object>> queryMinBillamountStartdateForBillaccounts(@Param("billaccountIds") List<String> billaccountIds, @Param("billaccountId") String billaccountId);

    List<EleOtherAmountFinanceLastBillamountDateVO> queryAmountDetailOfLastPayment(Map<String, Object> paraMap);

    List<EleOtherAmountFinanceLastBillamountDateVO> queryOtherAmountDetailsOfLastPayment(@Param("id") String id);

    List<ElectricPredictionPaymentCycle> queryLast4Payment(@Param("billaccountId") String billaccountId,
                                                           @Param("currentStartDate") String currentStartDate,
                                                           @Param("type") int type);

    /**
     * 查询某个时间之前的报账点缴费
     *
     * @param startDate     缴费开始时间
     * @param billaccountId 报账点id
     * @param payId 缴费单id
     * @return 返回缴费
     */
    String selectPaymentByConditon(@Param("startDate") String startDate, @Param("billaccountId") String billaccountId, @Param("payId") String payId);


	BillaccountDateInfo selectBillaccountDateInfoByBillaccountId(String billaccountId);


	BillaccountDateInfo selectLastPaymentByBillaccountId(String billaccountId);
}
