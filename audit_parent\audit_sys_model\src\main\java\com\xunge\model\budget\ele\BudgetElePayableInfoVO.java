package com.xunge.model.budget.ele;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;


/**
 * <AUTHOR> LiangCheng
 * @date : 2022-07-04
 * @desc 应付数据表
 */
@Data
@ToString
public class BudgetElePayableInfoVO implements Serializable {
    /**
     * 省份ID
     */
    private String prvId;

    /**
     * 省份名称
     */
    @Excel(name = "省份",orderNum = "2",width=20)
    private String prvName;

    /**
     * 年份
     */
    private Integer onYear;

    /**
     * 月份
     */
    @Excel(name = "月份",orderNum = "1",width=20)
    private Integer onMonth;

    /**
     * 站点类型
     */
    @Excel(name = "站点类型",orderNum = "3",replace = {"汇总_0", "核心机楼_1", "汇聚传输站点_2", "基站_3", "室分及WLAN_4", "家客集客_5", "IDC机房_6", "基地_7", "其他_8"},width=20)
    private Integer siteType;

    /**
     * 总站点总数
     */
    @Excel(name = "总站点总数",orderNum = "34",groupName = "不区分三种类型",width=20)
    private Integer siteNum;

    /**
     * 转供电站点
     */
    @Excel(name = "转供电站点",orderNum = "35",groupName = "不区分三种类型",width=20)
    private Integer transferSiteNum;

    /**
     * 转供电比例
     */
    @Excel(name = "转供电比例",orderNum = "36",numFormat = "##########.##########",groupName = "不区分三种类型",width=20)
    private BigDecimal transferSiteRadio;

    /**
     * 直供+转供电费
     */
    @Excel(name = "直供+转供电费",orderNum = "37",numFormat = "##########.##########",groupName = "不区分三种类型",width=20)
    private BigDecimal straightTransferAmount;

    /**
     * 直供+转供电量
     */
    @Excel(name = "直供+转供电量",orderNum = "38",numFormat = "##########.##########",groupName = "不区分三种类型",width=20)
    private BigDecimal straightTransferDegree;

    /**
     * 用电成本
     */
    @Excel(name = "用电成本",orderNum = "38",numFormat = "##########.##########",groupName = "不区分三种类型",width=20)
    private BigDecimal electroCosts;

    /**
     * 直供电站点
     */
    @Excel(name = "直供电站点",orderNum = "40",groupName = "不区分三种类型",width=20)
    private Integer straightSiteNum;

    /**
     * 直供转供站点总数
     */
    @Excel(name = "直供转供站点总数",orderNum = "41",groupName = "不区分三种类型",width=20)
    private Integer straightTransferSiteNum;

    /**
     * 单站用电量
     */
    @Excel(name = "单站用电量",orderNum = "42",numFormat = "##########.##########",groupName = "不区分三种类型",width=20)
    private BigDecimal siteDegreeAvg;

    /**
     * 包干电费
     */
    @Excel(name = "包干电费",orderNum = "43",numFormat = "##########.##########",groupName = "不区分三种类型",width=20)
    private BigDecimal includeAmount;

    /**
     * 电费合计
     */
    @Excel(name = "电费合计",orderNum = "44",numFormat = "##########.##########",groupName = "不区分三种类型",width=20)
    private BigDecimal amountTotal;

    private String workOrderId;

    @Excel(name ="直供电站点数量(个)",orderNum = "4",groupName = "自维站点",width=20)
    private Integer selfStraightSiteNum;
    @Excel(name ="直供电电费(元)(不含税不含电损)",orderNum = "5",numFormat = "##########.##########",groupName = "自维站点",width=20)
    private BigDecimal selfStraightAmountOutloss;
    @Excel(name ="直供电电损费(元)(不含税不含电损)",orderNum = "6",numFormat = "##########.##########",groupName = "自维站点",width=20)
    private BigDecimal selfStraightAmountLoss;
    @Excel(name ="直供电电量(度)",orderNum = "7",numFormat = "##########.##########",groupName = "自维站点",width=20)
    private BigDecimal selfStraightDegree;
    @Excel(name ="转供电站点数量(个)",orderNum = "8",groupName = "自维站点",width=20)
    private Integer selfTransferSiteNum;
    @Excel(name ="转供电电费(元)(不含税不含电损)",orderNum = "9",numFormat = "##########.##########",groupName = "自维站点",width=20)
    private BigDecimal selfTransferAmountOutloss;
    @Excel(name ="转供电电损费(元)(不含税不含电损)",orderNum = "10",numFormat = "##########.##########",groupName = "自维站点",width=20)
    private BigDecimal selfTransferAmountLoss;
    @Excel(name ="转供电电量(度)",orderNum = "11",numFormat = "##########.##########",groupName = "自维站点",width=20)
    private BigDecimal selfTransferDegree;
    @Excel(name ="包干站点数量(个)",orderNum = "12",groupName = "自维站点",width=20)
    private Integer selfIncludeSiteNum;
    @Excel(name ="包干站点金额(元)",orderNum = "13",numFormat = "##########.##########",groupName = "自维站点",width=20)
    private BigDecimal selfIncludeAmount;
    @Excel(name ="直供电站点数量(个)",orderNum = "14",groupName = "代持（移动替铁塔缴纳电费）",width=20)
    private Integer heldStraightSiteNum;
    @Excel(name ="直供电电费(元)(不含税不含电损)",orderNum = "15",numFormat = "##########.##########",groupName = "代持（移动替铁塔缴纳电费）",width=20)
    private BigDecimal heldStraightAmountOutloss;
    @Excel(name ="直供电电损费(元)(不含税不含电损)",orderNum = "16",numFormat = "##########.##########",groupName = "代持（移动替铁塔缴纳电费）",width=20)
    private BigDecimal heldStraightAmountLoss;
    @Excel(name ="直供电电量(度)",orderNum = "17",numFormat = "##########.##########",groupName = "代持（移动替铁塔缴纳电费）",width=20)
    private BigDecimal heldStraightDegree;
    @Excel(name ="转供电站点数量(个)",orderNum = "18",groupName = "代持（移动替铁塔缴纳电费）",width=20)
    private Integer heldTransferSiteNum;
    @Excel(name ="转供电电费(元)(不含税不含电损)",orderNum = "19",numFormat = "##########.##########",groupName = "代持（移动替铁塔缴纳电费）",width=20)
    private BigDecimal heldTransferAmountOutloss;
    @Excel(name ="转供电电损费(元)(不含税不含电损)",orderNum = "20",numFormat = "##########.##########",groupName = "代持（移动替铁塔缴纳电费）",width=20)
    private BigDecimal heldTransferAmountLoss;
    @Excel(name ="转供电电量(度)",orderNum = "21",numFormat = "##########.##########",groupName = "代持（移动替铁塔缴纳电费）",width=20)
    private BigDecimal heldTransferDegree;
    @Excel(name ="包干站点数量(个)",orderNum = "22",groupName = "代持（移动替铁塔缴纳电费）",width=20)
    private Integer heldIncludeSiteNum;
    @Excel(name ="包干站点金额(元)",orderNum = "23",numFormat = "##########.##########",groupName = "代持（移动替铁塔缴纳电费）",width=20)
    private BigDecimal heldIncludeAmount;
    @Excel(name ="直供电站点数量(个)",orderNum = "24",groupName = "铁塔电费",width=20)
    private Integer towerStraightSiteNum;
    @Excel(name ="直供电电费(元)(不含税不含电损)",orderNum = "25",numFormat = "##########.##########",groupName = "铁塔电费",width=20)
    private BigDecimal towerStraightAmountOutloss;
    @Excel(name ="直供电电损费(元)(不含税不含电损)",orderNum = "26",numFormat = "##########.##########",groupName = "铁塔电费",width=20)
    private BigDecimal towerStraightAmountLoss;
    @Excel(name ="直供电电量(度)",orderNum = "27",numFormat = "##########.##########",groupName = "铁塔电费",width=20)
    private BigDecimal towerStraightDegree;
    @Excel(name ="转供电站点数量(个)",orderNum = "28",groupName = "铁塔电费",width=20)
    private Integer towerTransferSiteNum;
    @Excel(name ="转供电电费(元)(不含税不含电损)",orderNum = "29",numFormat = "##########.##########",groupName = "铁塔电费",width=20)
    private BigDecimal towerTransferAmountOutloss;
    @Excel(name ="转供电电损费(元)(不含税不含电损)",orderNum = "30",numFormat = "##########.##########",groupName = "铁塔电费",width=20)
    private BigDecimal towerTransferAmountLoss;
    @Excel(name ="转供电电量(度)",orderNum = "31",numFormat = "##########.##########",groupName = "铁塔电费",width=20)
    private BigDecimal towerTransferDegree;
    @Excel(name ="包干站点数量(个)",orderNum = "32",groupName = "铁塔电费",width=20)
    private Integer towerIncludeSiteNum;
    @Excel(name ="包干站点金额(元)",orderNum = "33",numFormat = "##########.##########",groupName = "铁塔电费",width=20)
    private BigDecimal towerIncludeAmount;

}

