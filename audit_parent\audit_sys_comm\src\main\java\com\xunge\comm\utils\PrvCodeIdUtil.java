package com.xunge.comm.utils;

import com.google.common.collect.BiMap;
import com.google.common.collect.HashBiMap;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Set;

/**
 * 省份CODE和编码双向查询
 *
 * <AUTHOR>
 * @date 2022/7/5 9:41
 */
public final class PrvCodeIdUtil {

    private static final BiMap<String, String> idCodeMap = HashBiMap.create(36);

    /**
     * 直辖市:北京 天津 上海 重庆
     */
    public static final List<String> municipalityList = new ArrayList<>(Arrays.asList("110000", "120000", "310000", "500000"));

    static {
        //北京市
        idCodeMap.put("BJ", "110000");
        //天津市
        idCodeMap.put("TJ", "120000");
        //河北省
        idCodeMap.put("HE", "130000");
        //山西省
        idCodeMap.put("SX", "140000");
        //内蒙古自治区
        idCodeMap.put("NM", "150000");
        //辽宁省
        idCodeMap.put("LN", "210000");
        //吉林省
        idCodeMap.put("JL", "220000");
        //黑龙江省
        idCodeMap.put("HL", "230000");
        //上海市
        idCodeMap.put("SH", "310000");
        //江苏省
        idCodeMap.put("JS", "320000");
        //浙江省
        idCodeMap.put("ZJ", "330000");
        //安徽省
        idCodeMap.put("AH", "340000");
        //福建省
        idCodeMap.put("FJ", "350000");
        //江西省
        idCodeMap.put("JX", "360000");
        //山东省
        idCodeMap.put("SD", "370000");
        //河南省
        idCodeMap.put("HA", "410000");
        //湖北省
        idCodeMap.put("HB", "420000");
        //湖南省
        idCodeMap.put("HN", "430000");
        //广东省
        idCodeMap.put("GD", "440000");
        //广西省
        idCodeMap.put("GX", "450000");
        //海南省
        idCodeMap.put("HI", "460000");
        //重庆市
        idCodeMap.put("CQ", "500000");
        //四川省
        idCodeMap.put("SC", "510000");
        //贵州省
        idCodeMap.put("GZ", "520000");
        //云南省
        idCodeMap.put("YN", "530000");
        //西藏自治区
        idCodeMap.put("XZ", "540000");
        //陕西省
        idCodeMap.put("SN", "610000");
        //甘肃省
        idCodeMap.put("GS", "620000");
        //青海省
        idCodeMap.put("QH", "630000");
        //宁夏回族自治区
        idCodeMap.put("NX", "640000");
        //新疆维吾尔自治区
        idCodeMap.put("XJ", "650000");
        //台湾省
        idCodeMap.put("TW", "710000");
        //香港特别行政区
        idCodeMap.put("XG", "720000");
        //集团
        idCodeMap.put("JT", "000000");
    }

    /**
     * 根据prvCode获取prvId
     */
    public static String getPrvIdByCode(String prvCode) {
        Set<String> prvCodes = idCodeMap.keySet();
        if (!prvCodes.contains(prvCode)) {
            throw new IllegalArgumentException("省份：" + prvCode + "不存在");
        }
        return idCodeMap.get(prvCode);
    }

    /**
     * 根据prvId获取prvCode
     */
    public static String getPrvCodeById(String prvId) {
        Set<String> prvIds = idCodeMap.values();
        if (!prvIds.contains(prvId)) {
            throw new IllegalArgumentException("省份：" + prvId + "不存在");
        }
        return idCodeMap.inverse().get(prvId);
    }
}
