package com.xunge.dao.towerrent.tiny;

import com.xunge.core.page.Page;
import com.xunge.model.towerrent.settlement.OtherFeeTinyVO;
import com.xunge.model.towerrent.settlement.TowerAndMobileBillTinyConfirmVO;
import com.xunge.model.towerrent.settlement.TowerBillBalanceTinyVO;

import java.util.List;
import java.util.Map;

/**
 * TODO: 查询微站账单相关信息
 *
 * <AUTHOR>
 */
public interface ITinyFeeDao {

    List<TowerBillBalanceTinyVO> queryTinyBill(Map<String, Object> paramMap);

    int updateTinyFeeSumcodeToNull(Map<String, Object> paramMap);

    int updateTinyBillSetSumcode(TowerBillBalanceTinyVO tinyVO);

    List<TowerAndMobileBillTinyConfirmVO> queryAccountedTinyBill(Map<String, Object> paramMap);

    Page<TowerAndMobileBillTinyConfirmVO> queryAccountedTinyBillByPage(
            Map<String, Object> paraMap, int pageNumber, int pageSize);

    List<OtherFeeTinyVO> queryAccountedOtherFeeTiny(Map<String, Object> paramMap);

    Page<List<OtherFeeTinyVO>> queryAccountedOtherFeeTinyByPage(Map<String, Object> paraMap, int pageNumber, int pageSize);

    TowerAndMobileBillTinyConfirmVO queryTinyRental(Map<String, Object> map);
}
