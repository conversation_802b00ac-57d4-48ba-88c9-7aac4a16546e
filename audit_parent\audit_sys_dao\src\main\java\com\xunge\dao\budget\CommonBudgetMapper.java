package com.xunge.dao.budget;

import com.xunge.model.budget.*;
import com.xunge.model.job.SysProvinceVO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Map;

public interface CommonBudgetMapper {

    int insertBudgetOrder(BudgetWorkOrder budgetWorkOrder);

    /**
     * 更新工单基本信息
     * @param budgetWorkOrder
     * @return
     */
    int updateBudgetOrderInfo(BudgetWorkOrder budgetWorkOrder);

    /**
     * 更新工单状态
     * @param workOrderState
     * @param workOrderId
     * @return
     */
    int updateBudgetOrderState(@Param("workOrderState") Integer workOrderState, @Param("workOrderId")String workOrderId);

    /**
     * 更新工单提交人
     * @param workOrderId
     * @param submitUserLoginName
     */
    void updateSubmitUser(@Param("workOrderId")String workOrderId, @Param("submitUserLoginName") String submitUserLoginName);

    /**
     * 更新工单下发时间
     * @param workOrderId
     * @return
     */
    int updateBudgetOrderIssueTime(@Param("workOrderId")String workOrderId,@Param("date") Date date);

    int insertPrvConfig(PrvConfig prvConfig);

    int updatePrvConfig(PrvConfig prvConfig);

    List<BudgetWorkOrder> queryBudgetOrder(BudgetWorkOrder orderParam);

    List<PrvConfig> queryPrvConfig(@Param("prvId") String prvId, @Param("prvEscalationStatus") Integer prvEscalationStatus,@Param("workOrderId") String workOrderId);

    List<PrvConfig> queryAllPrvIfExistOrder(@Param("budgetType") Integer budgetType,@Param("budgetTime") String budgetTime);

    String queryPrvSname(@Param("prvId") String prvId);

    String queryPrvIdBySname(@Param("prvSname") String prvSname);

    int deleteDraft(@Param("workOrderId") String workOrderId,@Param("workOrderState") Integer workOrderState);

    int deletePrvConfig(@Param("workOrderId") String workOrderId,@Param("prvId") String prvId);

    int insertAuditInfo(BudgetAuditInfo budgetAuditInfo);

    int updateAuditResultInfo(BudgetAuditInfo budgetAuditInfo);

    List<BudgetAuditInfo> queryAuditInfo(@Param("workOrderId") String workOrderId,@Param("operaType") Integer operaType);

    int insertNationwideEnclosureDetail(NationwideEnclosureDetailVo nationwideEnclosureDetailVo);

    int insertNationwideExplain(NationwideExplainVo nationwideExplainVo);

    List<BudgetWorkOrder> queryFinalBudgetOrderAll(BudgetWorkOrder orderParam);

    String getSendMsgUserPhone(@Param("roleCode") String roleCode);

    /**
     * 查询该工单草稿生成月份
     * @param workOrderId
     * @return
     */
    Integer queryDraftCreateMonth(@Param("workOrderId") String workOrderId);

}
