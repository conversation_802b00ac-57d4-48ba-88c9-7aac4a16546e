package com.xunge.dao.towerrent.comm;

import com.xunge.model.towerrent.common.change.MobileChanges;

import java.util.List;
import java.util.Map;

/**
 * 描述：
 * Created on 2019/8/2.
 * <p>Title:</p>
 * <p>Copyright:Copyright (c) 2017</p>
 * <p>Company:安徽科大国创</p>
 * <p>Department:西南二区BU</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @update
 */
public interface IMobileChangeDao {
    /**
     * 删除移动侧历史维护记录和移动侧起租单
     *
     * @param param
     */
    int deleteTwrDataAndCompareHisData(Map<String, Object> param);

    /**
     * 删除移动侧起租单
     *
     * @param param
     * @return
     */
    int deleteTwrData(Map<String, Object> param);

    /**
     * 插入已审核的移动侧维护历史记录
     *
     * @return
     */
    int insertMobileHisChange(Map<String, Object> param);

    /**
     * 插入未审核维护历史记录
     *
     * @return
     */
    int insertMobileChange(Map<String, Object> param);

    /**
     * 查询移动侧维护记录
     *
     * @param paramMap
     * @return
     */
    List<MobileChanges> queryMobileHisChangeById(Map<String, Object> paramMap);

    /**
     * 修改移动侧维护历史记录
     *
     * @param param
     * @return
     */
    int updateMobileHisChange(Map<String, Object> param);

    /**
     * 移动侧审核状态修改、未审核维护记录修改
     *
     * @param param
     * @return
     */
    int mobileStatusAndChangesUpdate(Map<String, Object> param);

    /**
     * @param param
     * @return
     */
    MobileChanges queryMobileChangesById(Map<String, Object> param);

    /**
     * 未审核的维护记录修改
     *
     * @param param
     * @return
     */
    int updateMobileChange(Map<String, Object> param);

    /**
     * 删除已审核过的维护记录
     *
     * @param param
     */
    int deleteMobileChanges(Map<String, Object> param);
}
