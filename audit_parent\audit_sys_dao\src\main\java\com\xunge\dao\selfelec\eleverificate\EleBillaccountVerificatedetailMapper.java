package com.xunge.dao.selfelec.eleverificate;

import com.xunge.model.selfelec.EleBillaccountPaymentdetail;
import com.xunge.model.selfelec.dto.QueryRepeatDto;
import com.xunge.model.selfelec.eleverificate.EleBillaccountVerificatedetail;
import com.xunge.model.selfelec.eleverificate.EleBillaccountVerificatedetailExample;
import com.xunge.model.selfelec.eleverificate.VEleBillaccountVerificateInfo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.util.List;
import java.util.Map;

public interface EleBillaccountVerificatedetailMapper {

    Map<String, Object> queryEleLossv(Map<String, Object> map);

    
    int countByExample(EleBillaccountVerificatedetailExample example);

    
    int deleteByExample(EleBillaccountVerificatedetailExample example);

    
    int deleteByPrimaryKey(String billaccountverificatedetailId);

    
    int insert(EleBillaccountVerificatedetail record);

    
    int insertSelective(EleBillaccountVerificatedetail record);

    
    List<EleBillaccountVerificatedetail> selectByExample(EleBillaccountVerificatedetailExample example);

    
    EleBillaccountVerificatedetail selectByPrimaryKey(String verificationId);

    /**
     * 根据id查询信息
     *
     * @param billaccountverificatedetailId
     * @return
     * <AUTHOR>
     */
    public EleBillaccountVerificatedetail queryById(String billaccountverificatedetailId);

    
    int updateByExampleSelective(@Param("record") EleBillaccountVerificatedetail record,
                                 @Param("example") EleBillaccountVerificatedetailExample example);

    
    int updateByExample(@Param("record") EleBillaccountVerificatedetail record, @Param("example") EleBillaccountVerificatedetailExample example);

    
    int updateByPrimaryKeySelective(EleBillaccountVerificatedetail record);

    @Update("UPDATE ele_verification SET over_flow=#{overFlow} WHERE verification_id=#{verificationId}")
    void updateOverFlow(@Param("verificationId") String verificationId, @Param("overFlow") int overFlow);

    @Update("UPDATE ele_verification SET tax_payment_type=#{taxPaymentType} WHERE verification_id=#{verificationId}")
    void updateTaxPaymentType(@Param("verificationId") String verificationId, @Param("taxPaymentType") Integer taxPaymentType);

    
    int updateByPrimaryKey(EleBillaccountVerificatedetail record);

    /**
     * 修改流程状态
     *
     * @param hashMaps
     * @return
     * <AUTHOR>
     */
    int updateActivityCommit(Map<String, Object> hashMaps);

    /**
     * 更新上次提交审核时间
     *
     * <AUTHOR>
     */
    int updateLastAuditingDate(Map<String, Object> hashMaps);

    /**
     * 审核状态
     *
     * @param hashMaps
     * @return
     */
    int updateAuditingState(Map<String, Object> hashMaps);

    int updateVerificateState(Map<String, Object> hashMap);

    /**
     * 根据报账点id查询缴费记录信息
     *
     * <AUTHOR>
     */
    List<EleBillaccountVerificatedetail> queryVerificateByBillAccountId(String id);

    /**
     * GUYGMMAN-958
     * 根据报账点id查询缴费记录信息(允许导入历史缴费)
     *
     * <AUTHOR>
     */
    List<EleBillaccountVerificatedetail> queryVerificateByBillAccountIdNew(@Param("billaccountId") String billaccountId);
    /**
     * GUYGMMAN-3296
     * 电费IC核销与后付费时段重复校验【**************】
     *
     * <AUTHOR>
     */
    List<QueryRepeatDto> queryRepeatVerificate(QueryRepeatDto queryRepeatDto);

    /**
     * 根据报账点编码查询此最大缴费期终的信息
     *
     * @return
     */
    public VEleBillaccountVerificateInfo queryMaxBillAccountEnded(String billaccountId);

    /**
     * 根据报账点id查询缴费记录信息
     *
     * @param billaccountId
     * @return
     * <AUTHOR>
     */
    public List<Map<String, Object>> queryResourceVerificateByBillaccountId(String billaccountId);

    public List<EleBillaccountVerificatedetail> queryIcVerificateByBillAccountId(String billaccountId);

    public List<Map<String, Object>> queryResourceIcVerificateByBillaccountId(String billaccountId);

    EleBillaccountVerificatedetail findVerificationCodeById(Map<String, Object> map);

    public EleBillaccountVerificatedetail queryVerificateById(String billaccountverificatedetailId);

    public List<Map<String, Object>> queryMeterVerificateByBillaccountId(String billaccountId);


    List<EleBillaccountVerificatedetail> queryNowMeterByPayment(Map<String, Object> param);

    /**
     * 更新数据
     *
     * @param record 需要更新的数据
     * @return 条数
     */
    int updateById(EleBillaccountVerificatedetail record);

    int updateThreeFlag(Map<String, Object> param);

    EleBillaccountVerificatedetail queryByVerificationId(String verificationId);

    List<EleBillaccountVerificatedetail> queryByBillamountId(String billamountId);

    List<EleBillaccountVerificatedetail> queryLastMeterNumByBillaccount(@Param("billaccountId") String billaccountId, @Param("startDate")String startDate);

    int updateBillAudit(@Param("verificationCode") String verificationCode, @Param("newUserLoginname") String newUserLoginname);
}