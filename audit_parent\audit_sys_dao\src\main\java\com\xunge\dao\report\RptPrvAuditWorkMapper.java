package com.xunge.dao.report;

import com.xunge.model.report.RptPrvAuditWork;
import com.xunge.model.report.RptPrvAuditWorkExample;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface RptPrvAuditWorkMapper {
    int countByExample(RptPrvAuditWorkExample example);

    int deleteByExample(RptPrvAuditWorkExample example);

    int insert(RptPrvAuditWork record);

    int insertSelective(RptPrvAuditWork record);

    List<RptPrvAuditWork> selectByExample(RptPrvAuditWorkExample example);

    int updateByExampleSelective(@Param("record") RptPrvAuditWork record, @Param("example") RptPrvAuditWorkExample example);

    int updateByExample(@Param("record") RptPrvAuditWork record, @Param("example") RptPrvAuditWorkExample example);

    List<RptPrvAuditWork> queryStatementInfo(RptPrvAuditWork rptPrvAuditWork);

    RptPrvAuditWork queryStatementInfoTotal(RptPrvAuditWork rptPrvAuditWork);

    List<RptPrvAuditWork> queryPrvStatementInfo(RptPrvAuditWork rptPrvAuditWork);

    List<RptPrvAuditWork> queryManageDetail(RptPrvAuditWork rptPrvAuditWork);

    List<RptPrvAuditWork> queryByRelativeId(String relativeId);

    List<RptPrvAuditWork> exportAuditWorkData(Map<String, Object> map);

    List<String> selectBillNameFromEleBillaccount(Map<String, Object> map);

    List<String> selectBillNameFromTeleBillaccount(Map<String, Object> map);

    List<String> selectBillNameFromRentBillaccount(Map<String, Object> map);

    List<String> getProcKeys(Map<String, Object> map);

    @MapKey("submitUserId")
    Map<String,RptPrvAuditWork> getSubmitUserName(@Param("submitUserIds")List<String> submitUserIds);

    @MapKey("relativeCode")
    Map<String,RptPrvAuditWork> getDatContractEnterUserName(@Param("relativeCodes")List<String> relativeCodes);
    @MapKey("relativeCode")
    Map<String,RptPrvAuditWork> getEleBillaccountEnterUserName(@Param("relativeCodes")List<String> relativeCodes);
    @MapKey("relativeCode")
    Map<String,RptPrvAuditWork> getElePaymentEnterUserName(@Param("relativeCodes")List<String> relativeCodes);
    @MapKey("relativeCode")
    Map<String,RptPrvAuditWork> getEleLoanEnterUserName(@Param("relativeCodes")List<String> relativeCodes);
    @MapKey("relativeCode")
    Map<String,RptPrvAuditWork> getEleVerificationEnterUserName(@Param("relativeCodes")List<String> relativeCodes);
    @MapKey("relativeCode")
    Map<String,RptPrvAuditWork> getTelePaymentEnterUserName(@Param("relativeCodes")List<String> relativeCodes);
    @MapKey("relativeCode")
    Map<String,RptPrvAuditWork> getRentPaymentEnterUserName(@Param("relativeCodes")List<String> relativeCodes);
    @MapKey("relativeCode")
    Map<String,RptPrvAuditWork> getRentContractEnterUserName(@Param("relativeCodes")List<String> relativeCodes);
    @MapKey("relativeCode")
    Map<String,RptPrvAuditWork> getRentBillaccountEnterUserName(@Param("relativeCodes")List<String> relativeCodes);
}