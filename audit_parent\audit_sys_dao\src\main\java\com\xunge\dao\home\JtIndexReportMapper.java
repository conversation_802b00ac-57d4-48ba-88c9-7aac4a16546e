package com.xunge.dao.home;

import com.xunge.model.maintenance.MainFee;
import com.xunge.model.report.RptIdPrvcode;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface JtIndexReportMapper {

    Map<String, Object> totalSum(Map<String, Object> map);

    //    首页第一面头部
    Map<String, Object> getYearCount(Map<String, Object> map);

    Map<String, Object> getYearCountTx(Map<String, Object> map);

    //    首页第一面头部
    Map<String, Object> getMonCount(Map<String, Object> map);

    Map<String, Object> getMonCountTx(Map<String, Object> map);

    List<Map<String, Object>> getMapDateYear(Map<String, Object> map);

    List<Map<String, Object>> getMapDateYearTx(Map<String, Object> map);

    List<Map<String, Object>> getMapDateMon(Map<String, Object> map);

    List<Map<String, Object>> getMapDateMonTx(Map<String, Object> map);

    List<Map<String, Object>> getEleMonth(@Param("year") int year, @Param("month") int month);

    List<Map<String, Object>> getEleMonthTx(@Param("year") int year, @Param("month") int month);

    List<Map<String, Object>> getRentMonth(@Param("year") int year, @Param("month") int month);

    List<Map<String, Object>> getRentMonthTx(@Param("year") int year, @Param("month") int month);

    List<Map<String, Object>> getTowerMonth(@Param("year") int year, @Param("month") int month);

    List<Map<String, Object>> getTowerMonthTx(@Param("year") int year, @Param("month") int month);

    List<Map<String, Object>> getEleReportMon(@Param("year") int year, @Param("month") int month);

    List<Map<String, Object>> getEleReportMonTx(@Param("year") int year, @Param("month") int month);

    Map<String, Object> getEleReportMonSum(@Param("year") int year, @Param("month") int month);

    Map<String, Object> getEleReportMonSumTx(@Param("year") int year, @Param("month") int month);

    List<Map<String, Object>> getEleReportMonByPrvId(@Param("year") int year, @Param("month") int month, @Param("prvId") String prvId);

    List<Map<String, Object>> getEleReportMonByPrvIdTx(@Param("year") int year, @Param("month") int month, @Param("prvId") String prvId);

    Map<String, Object> getEleReportMonByPrvIdSum(@Param("year") int year, @Param("month") int month, @Param("prvId") String prvId);

    Map<String, Object> getEleReportMonByPrvIdSumTx(@Param("year") int year, @Param("month") int month, @Param("prvId") String prvId);

    List<Map<String, Object>> getEleReportMonByPregId(@Param("year") int year, @Param("month") int month, @Param("pregId") String pregId);

    List<Map<String, Object>> getEleReportMonByPregIdTx(@Param("year") int year, @Param("month") int month, @Param("pregId") String pregId);

    Map<String, Object> getEleReportMonByPregIdSum(@Param("year") int year, @Param("month") int month, @Param("pregId") String pregId);

    Map<String, Object> getEleReportMonByPregIdSumTx(@Param("year") int year, @Param("month") int month, @Param("pregId") String pregId);

    List<Map<String, Object>> getRentReportMon(@Param("year") int year, @Param("month") int month);

    List<Map<String, Object>> getRentReportMonTx(@Param("year") int year, @Param("month") int month);

    Map<String, Object> getRentReportMonSum(@Param("year") int year, @Param("month") int month);

    Map<String, Object> getRentReportMonSumTx(@Param("year") int year, @Param("month") int month);

    List<Map<String, Object>> getRentReportMonByPrvId(@Param("year") int year, @Param("month") int month, @Param("prvId") String prvId);

    List<Map<String, Object>> getRentReportMonByPrvIdTx(@Param("year") int year, @Param("month") int month, @Param("prvId") String prvId);

    Map<String, Object> getRentReportMonByPrvIdSum(@Param("year") int year, @Param("month") int month, @Param("prvId") String prvId);

    Map<String, Object> getRentReportMonByPrvIdSumTx(@Param("year") int year, @Param("month") int month, @Param("prvId") String prvId);

    List<Map<String, Object>> getRentReportMonByPregId(@Param("year") int year, @Param("month") int month, @Param("pregId") String pregId);

    List<Map<String, Object>> getRentReportMonByPregIdTx(@Param("year") int year, @Param("month") int month, @Param("pregId") String pregId);

    Map<String, Object> getRentReportMonByPregIdSum(@Param("year") int year, @Param("month") int month, @Param("pregId") String pregId);

    Map<String, Object> getRentReportMonByPregIdSumTx(@Param("year") int year, @Param("month") int month, @Param("pregId") String pregId);

    List<Map<String, Object>> getTowerReportMon(@Param("year") int year, @Param("month") int month);

    List<Map<String, Object>> getTowerReportMonTx(@Param("year") int year, @Param("month") int month);

    Map<String, Object> getTowerReportMonSum(@Param("year") int year, @Param("month") int month);

    Map<String, Object> getTowerReportMonSumTx(@Param("year") int year, @Param("month") int month);

    List<Map<String, Object>> getTowerReportMonByPrvId(@Param("year") int year, @Param("month") int month, @Param("prvId") String prvId);

    List<Map<String, Object>> getTowerReportMonByPrvIdTx(@Param("year") int year, @Param("month") int month, @Param("prvId") String prvId);

    Map<String, Object> getTowerReportMonByPrvIdSum(@Param("year") int year, @Param("month") int month, @Param("prvId") String prvId);

    Map<String, Object> getTowerReportMonByPrvIdSumTx(@Param("year") int year, @Param("month") int month, @Param("prvId") String prvId);

    List<Map<String, Object>> getTowerReportMonByPregId(@Param("year") int year, @Param("month") int month, @Param("pregId") String pregId);

    List<Map<String, Object>> getTowerReportMonByPregIdTx(@Param("year") int year, @Param("month") int month, @Param("pregId") String pregId);

    Map<String, Object> getTowerReportMonByPregIdSum(@Param("year") int year, @Param("month") int month, @Param("pregId") String pregId);

    Map<String, Object> getTowerReportMonByPregIdSumTx(@Param("year") int year, @Param("month") int month, @Param("pregId") String pregId);

    List<Map<String, Object>> getRentAccountInfo(@Param("year") int year, @Param("month") int month, @Param("regId") String regId);

    /**
     * 报账点审核通过的缴费单数量
     *
     * @param year
     * @param month
     * @param regId
     * @return
     */
    List<Map<String, Object>> getElePayOrderNum(@Param("year") int year, @Param("month") int month, @Param("regId") String regId);

    /**
     * 报账点审核通过的缴费点年电费，年电量
     *
     * @param year
     * @param month
     * @param regId
     * @return
     */
    List<Map<String, Object>> getAmountAndDegreeList(@Param("year") int year, @Param("month") int month, @Param("regId") String regId);

    /**
     * 报账点审核通过的缴费单超标年电费，年电量；
     *
     * @param year
     * @param month
     * @param regId
     * @return
     */
    List<Map<String, Object>> getOverAmountAndDegreeList(@Param("year") int year, @Param("month") int month, @Param("regId") String regId);

    List<Map<String, Object>> getMonthlyAmountAndDegreeList(@Param("year") int year, @Param("regId") String regId, @Param("jitiAmountList") List<String> l1, @Param("tanxiaoAmountList") List<String> l2, @Param("jitiDegreeList") List<String> l3, @Param("tanxiaoDegreeList") List<String> l4);

    /**
     * 集体与摊销月报中某月的摊销和计提的；
     *
     * @param year
     * @param month
     * @param txd
     * @param jtd
     * @param txa
     * @param jta
     * @param table
     * @return
     */
    List<Map<String, Object>> getEleAvg(@Param("year") int year, @Param("month") int month, @Param("txd") String txd, @Param("jtd") String jtd, @Param("txa") String txa, @Param("jta") String jta, @Param("table") String table);

    /**
     * 电费柱状图每月的总度数和金额
     *
     * @param txd
     * @param jtd
     * @param txa
     * @param jta
     * @param table
     * @return
     */
    List<Map<String, Object>> getMonTotal(@Param("txd") String txd, @Param("jtd") String jtd, @Param("txa") String txa, @Param("jta") String jta, @Param("table") String table);

    /**
     * 获取id和省份编码，作为切库使用
     *
     * @param txd
     * @param jtd
     * @param txa
     * @param jta
     * @param table
     * @return
     */
    List<RptIdPrvcode> getIdPrvcode(@Param("txd") String txd, @Param("jtd") String jtd, @Param("txa") String txa, @Param("jta") String jta, @Param("table") String table);

    Map<String, Object> getResourceSiteNum(Map<String, Object> map);

    List<RptIdPrvcode> getTopDegree(@Param("txd") String txd, @Param("jtd") String jtd, @Param("txa") String txa, @Param("jta") String jta, @Param("table") String table);

    List<RptIdPrvcode> getLowDegree(@Param("txd") String txd, @Param("jtd") String jtd, @Param("txa") String txa, @Param("jta") String jta, @Param("table") String table);

    List<RptIdPrvcode> getTopAmount(@Param("txd") String txd, @Param("jtd") String jtd, @Param("txa") String txa, @Param("jta") String jta, @Param("table") String table);

    List<RptIdPrvcode> getLowAmount(@Param("txd") String txd, @Param("jtd") String jtd, @Param("txa") String txa, @Param("jta") String jta, @Param("table") String table);

    List<Map<String, Object>> getTopDegreeTotal(@Param("txd") String txd, @Param("jtd") String jtd, @Param("txa") String txa, @Param("jta") String jta, @Param("table") String table);

    List<Map<String, Object>> getLowDegreeTotal(@Param("txd") String txd, @Param("jtd") String jtd, @Param("txa") String txa, @Param("jta") String jta, @Param("table") String table);

    List<Map<String, Object>> getTopAmountTotal(@Param("txd") String txd, @Param("jtd") String jtd, @Param("txa") String txa, @Param("jta") String jta, @Param("table") String table);

    List<Map<String, Object>> getLowAmountTotal(@Param("txd") String txd, @Param("jtd") String jtd, @Param("txa") String txa, @Param("jta") String jta, @Param("table") String table);

    List<MainFee> getMainFeeList(@Param("date") String date, @Param("provCode") String provCode);
}
