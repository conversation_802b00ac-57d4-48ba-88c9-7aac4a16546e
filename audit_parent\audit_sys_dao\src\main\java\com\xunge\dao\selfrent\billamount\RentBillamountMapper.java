package com.xunge.dao.selfrent.billamount;

import com.xunge.dto.selfelec.AuthorityUser;
import com.xunge.model.selfrent.billamount.RentBillAmountChangeLog;
import com.xunge.model.selfrent.billamount.RentBillamountPaymentVO;
import com.xunge.model.selfrent.billamount.RentBillamountVO;
import com.xunge.model.selfrent.billamount.query.BillamountExportQueryDto;
import com.xunge.model.selfrent.billamount.query.BillamountQueryDto;
import com.xunge.model.selfrent.billamount.writeoff.WriteOffCondition;
import com.xunge.model.selfrent.billamount.writeoff.WriteOffQueryVo;
import com.xunge.model.selfrent.writeoffaccrual.RentAccrualPushDetail;
import com.xunge.model.selfrent.writeoffaccrual.WriteOffDetail;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.cursor.Cursor;

import java.util.List;

/**
 * @ClassName: RentBillamountMapper
 * @Description: 租费报账汇总查询Mapper
 * <AUTHOR>
 * @Date: 2023/4/13 19:04
 * @Version V1.0.0
 * @Since 1.8
 */
public interface RentBillamountMapper {
    /**
     * 根据报账汇总查询对象和权限用户对象，查询报账汇总ID、报账点ID、返回单编码数据
     * @param billAmountQueryDto 报账汇总查询对象
     * @param authorityUser 权限用户对象
     * @return List<RentBillamountVO> 集合
     */
    List<RentBillamountVO> queryBillAmountIdAndBillAccountIdAndClaimNum(@Param("dto") BillamountQueryDto billAmountQueryDto, @Param("user") AuthorityUser authorityUser);

    /**
     * 根据报账汇总ID，查询报账汇总其他字段数据
     * @param billAmountIdList 报账汇总ID集合
     * @return List<RentBillamountVO> 集合
     */
    List<RentBillamountVO> queryBillAmountOtherField(@Param("list") List<String> billAmountIdList);

    /**
     * 根据游标查询报账汇总明细数据
     * @param billAmountQueryDto 报账汇总查询对象
     * @param authorityUser 权限用户对象
     * @return Cursor<RentBillamountPaymentVO> 游标集合
     */
    Cursor<RentBillamountPaymentVO> queryRentBillAmountByCursor(@Param("dto") BillamountExportQueryDto billAmountQueryDto, @Param("user") AuthorityUser authorityUser);

    /**
     * 根据游标查询报账汇总明细数据
     * @param billAmountQueryDto 报账汇总查询对象
     * @param authorityUser 权限用户对象
     * @return Cursor<RentBillamountPaymentVO> 游标集合
     */
    Cursor<RentBillamountPaymentVO> queryRentBillAmountDetailByCursor(@Param("dto") BillamountExportQueryDto billAmountQueryDto, @Param("user") AuthorityUser authorityUser);

    /**
     * 根据汇总单id查询冲销数据的前提条件
     * @param billamountId
     * @return
     */
    List<WriteOffCondition> queryWriteOffConditionByBillamountId(@Param("billamountId") String billamountId);

    /**
     * 查询报账可冲销的基础数据
     * @param writeOffQueryVo
     * @return
     */
    List<RentAccrualPushDetail> queryBaseWriteOffData(WriteOffQueryVo writeOffQueryVo);

    /**
     * 查询汇总单对应的冲销明细
     * @param billamountId
     * @return
     */
    List<WriteOffDetail> getWriteOffDetailByBillamountId(@Param("billamountId") String billamountId);

    /**
     * 更新租费报账汇总信息
     * @param rentBillamountVO 租费报账汇总VO对象
     * @return int
     */
    int updateRentBillAmount(RentBillamountVO rentBillamountVO);

    /**
     * 更新租费报账汇总明细信息
     * @param rentBillamountVO 租费报账汇总VO对象
     * @return int
     */
    int updateBillAmountDetail(RentBillamountVO rentBillamountVO);

    /**
     * 更新租费报账汇总缴费信息
     * @param rentBillamountVO 租费报账汇总VO对象
     * @return int
     */
    int updateBillAmountPayment(RentBillamountVO rentBillamountVO);

    /**
     * 插入租费报账汇总变更日志
     * @param logList 变更日志集合
     * @return int
     */
    int insertRentBillAmountChangeBatch(List<RentBillAmountChangeLog> logList);

    /**
     * 租费、三方塔汇总查询维护列表页面查询
     *
     * @param billAmountQueryDto
     * @param authorityUser
     * @return
     */
    List<RentBillamountVO> queryBillAmountList(@Param("dto") BillamountQueryDto billAmountQueryDto, @Param("user") AuthorityUser authorityUser);

    List<RentBillamountVO> queryRentAmountInfoByIdList(@Param("idList") List<String> idList);

}
