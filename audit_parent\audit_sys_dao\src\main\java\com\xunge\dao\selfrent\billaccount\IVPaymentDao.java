package com.xunge.dao.selfrent.billaccount;

import com.github.pagehelper.PageInfo;
import com.xunge.core.page.Page;
import com.xunge.model.basedata.DatBaseresource;
import com.xunge.model.selfrent.billAccount.RentPaymentMutiContractVO;
import com.xunge.model.selfrent.billAccount.VPaymentVO;
import com.xunge.model.selfrent.billAccount.VPaymentVOExport;
import com.xunge.model.selfrent.billAccount.VSpecialPaymentVOExport;
import com.xunge.model.selfrent.billamount.BillamountLogVO;
import com.xunge.model.selfrent.billamount.PaymentOtherFeeDetailVo;
import com.xunge.model.selfrent.billamount.PaymentResourceMergeVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface IVPaymentDao {
    /**
     * 查询报账点缴费记录
     *
     * @param hashMaps
     * @param pageNumber
     * @param pageSize
     * @return
     * <AUTHOR>
     */
    public Page<VPaymentVO> queryPayment(Map<String, Object> hashMaps, int pageNumber, int pageSize);

    /**
     * 查询所有报账点缴费记录
     *
     * <AUTHOR>
     */
    public PageInfo<VPaymentVO> queryAllPayment(Map<String, Object> hashMaps, int pageNumber, int pageSize);

    /**
     * 查询所有报账点缴费记录（导出）
     *
     * <AUTHOR>
     */
    public List<VPaymentVO> queryAllPayment(Map<String, Object> hashMaps);

    /**
     * 查询合同缴费记录
     *
     * <AUTHOR>
     */
    public Page<VPaymentVO> queryPaymentContract(Map<String, Object> hashMaps, int pageNumber, int pageSize);

    public Page<VPaymentVO> queryFinancePaymentContract(Map<String, Object> hashMaps, int pageNumber, int pageSize);


    /**
     * 根据id查询合同缴费记录
     *
     * <AUTHOR>
     */
    public VPaymentVO queryPaymentContractById(Map<String, Object> map);

    /**
     * 审核页面回显
     *
     * <AUTHOR>
     */
    public List<VPaymentVO> queryContractPayment(Map<String, Object> hashMaps);

    /**
     * 审核页面回显(合同大集中)
     *
     * <AUTHOR>
     */
    public List<VPaymentVO> queryFinanceContractPayment(Map<String, Object> hashMaps);

    /**
     * 提交审核
     *
     * <AUTHOR>
     */
    public int updateActivityCommit(Map<String, Object> hashMaps);

    /**
     * 更改payment缴费记录状态
     *
     * <AUTHOR>
     */
    public int updateState(Map<String, Object> hashMaps);

    /**
     * 更新上次提交审核时间
     *
     * <AUTHOR>
     */
    public int updateLastAuditingDate(Map<String, Object> hashMaps);

    /**
     * 修改租费缴费记录汇总id
     *
     * @param map
     * @param map: paymentId 缴费记录id
     * @param map: billamountId 汇总id
     * @return
     * <AUTHOR>
     */
    public int updateBillamountIdByPaymentId(Map<String, Object> hashMaps);

    /**
     * 修改租费缴费记录汇总id为空
     *
     * @param BillamountId 报账汇总单ID
     * @return
     * <AUTHOR>
     */
    public int updateBillamountIdIsNullByBillamountId(String BillamountId);

    /**
     * 修改租费缴费记录汇总id为空
     *
     * @param BillamountId 报账汇总单明细ID
     * @return
     * <AUTHOR>
     */
    public int updateBillamountIdIsNullByBillamountDetailId(String BillamountDetailId);

    /**
     * 查询待汇总缴费信息-分页
     *
     * @param hashMaps
     * @param hashMaps:pageNumber（必填）当前页
     * @param hashMaps:pageSize（必填）每页显示多少条
     * @param hashMaps:billType            报账类型
     * @param hashMaps:paymentMethod       支付方式
     * @param hashMaps:contractCode        合同编码
     * @param hashMaps:paymentId           缴费信息主键
     * @return
     * <AUTHOR>
     */
    public Page<List<VPaymentVO>> queryContractPaymentByNoAmount(Map<String, Object> hashMaps);

    public Page<List<VPaymentVO>> queryContractPaymentByNoAmountFcontract(Map<String, Object> hashMaps);

    /**
     * 查询待汇总缴费信息-不分页
     *
     * @param hashMaps
     * @param hashMaps:pageNumber（必填）当前页
     * @param hashMaps:pageSize（必填）每页显示多少条
     * @param hashMaps:billType            报账类型
     * @param hashMaps:paymentMethod       支付方式
     * @param hashMaps:contractCode        合同编码
     * @param hashMaps:paymentId           缴费信息主键
     * @return
     * <AUTHOR>
     */
    public List<VPaymentVO> queryContractPaymentByNoAmountList(Map<String, Object> hashMaps);

    /**
     * 审核完成数据退回
     *
     * @param map
     * @return
     */
    public int updateStateToSendBack(Map<String, Object> map);

    /**
     * 查询待汇总缴费信息-不分页
     *
     * @param hashMaps
     * @param hashMaps:pageNumber（必填）当前页
     * @param hashMaps:pageSize（必填）每页显示多少条
     * @param hashMaps:billType            报账类型
     * @param hashMaps:paymentMethod       支付方式
     * @param hashMaps:contractCode        合同编码
     * @param hashMaps:paymentId           缴费信息主键
     * @return
     * <AUTHOR>
     */
    public List<VPaymentVOExport> queryContractPaymentExportByNoAmountList(Map<String, Object> hashMaps);

    /**
     * 首页全查缴费记录信息
     *
     * @param map
     * @return
     */
    public List<Map<String, String>> queryPaymentDetailByCondition(Map<String, Object> map);

    /**
     * 查询符合条件的供应商数据
     *
     * @param paraMap
     * @return
     */
    public List<String> queryRentPayMentBySupplierMap(Map<String, Object> maps);

    /**
     * 导出特殊报账点缴费信息
     *
     * @param hashMaps
     * @return
     */
    public List<VSpecialPaymentVOExport> queryAllSpecialPayment(Map<String, Object> hashMaps);


    /**
     * 根据租费汇总缴费明细查询缴费单信息
     *
     * @param BillamountdetailId
     * @return
     */
    public VPaymentVO queryPaymentByBillamountdetailId(String BillamountdetailId);

    /**
     * 更新收款方银行账户、收款方银行行号、收款方名称
     *
     * @param record
     * @return
     */
    int updateReceiptInfo(VPaymentVO record);


    /**
     * @param @param paraMap    设定文件
     * @return void    返回类型
     * @throws
     * @Title: updateSupplierInfo
     * @Description: TODO(这里用一句话描述这个方法的作用)
     */

    public int updateSupplierInfo(Map<String, Object> paraMap);


    public int updateComtractInfo(Map<String, Object> paraMap);

    public List<VPaymentVO> queryIntersection();

    /**
     * 检查数据是否存在
     *
     * @param paraMap
     * @return
     */
    int countById(Map<String, Object> paraMap);

    /**
     * 通过合同ID查询信息
     *
     * @param paraMap
     * @return
     */
    Map<String, Object> queryBeanById(Map<String, Object> paraMap);

    public VPaymentVO queryById(String paymentId);

    List<PaymentOtherFeeDetailVo> queryOtherAmountByPaymentId(@Param("paymentId") String paymentId);

    PaymentResourceMergeVo queryBaseResourceByPaymentId(@Param("paymentId") String paymentId);

    List<VPaymentVO> queryPaymentByBillamountdId(Map<String, Object> map);

    int updateIsBack(Map<String, Object> mapo);

    int countBillAccountAndPaymentByStatus(Map<String, Object> map);

    /**
     * 一站多合同 查询报账时关联的资源信息及报账点
     *
     * @param paramMap
     * @return
     */
    PageInfo<RentPaymentMutiContractVO> getRentMultiContractInfo(Map<String, Object> map, int pageNumber, int pageSize);

    /**
     * 查询该报账单是否需要省公司网络部租费稽核人员审核
     * 判断依据：1：报账点关联资源存在“工程”状态；2：缴费期终-关联合同期始≥12个月 or 缴费期终-缴费期始≥12个月
     * @param paymentId
     * @return
     */
    int ifNeedPrvNetCheck(@Param("paymentId") String paymentId);

    List<DatBaseresource> getBillaccountResources(@Param("billaccountId")String billaccountId);

    String getCancelReason(@Param("paymentId") String paymentId);

    int updateCancelReason(Map<String, Object> paramMap);

    BillamountLogVO queryBillAmountLogByRemark(String billAmountCode);

    /**
     * 根据缴费单ID查询缴费单关联的资源名称、资源编码、资源总数
     * @param paymentId 缴费单ID
     * @return VPaymentVO 缴费单vo对象
     */
    VPaymentVO queryRentPaymentDatBaseResourceByPaymentId(String paymentId);
}

