package com.xunge.dao.selfrent.warning;

import com.xunge.core.page.Page;
import com.xunge.model.basedata.DatAttachment;
import com.xunge.model.basedata.EleAnomalousWarningVo;
import com.xunge.model.contract.DatContractVO;
import com.xunge.model.selfrent.billAccount.RentPaymentVO;
import com.xunge.model.selfrent.billAccount.VPaymentVO;
import com.xunge.model.selfrent.warning.RentAnomalousResourceVO;
import com.xunge.model.selfrent.warning.RentAnomalousWarningDetailVo;
import com.xunge.model.selfrent.warning.RentAnomalousWarningRecoveryExport;
import com.xunge.model.selfrent.warning.RentAnomalousWarningVO;
import com.xunge.model.system.region.RegionVO;

import java.util.List;
import java.util.Map;

/**
 * @创建人 LiangCheng
 * @创建时间 2019/10/8 0008
 * @描述：
 */
public interface RentAnomalousWarningDao {

    public Page<RentAnomalousWarningVO> queryRentAnomalousWarningList(Map<String, Object> map, int pageNumber, int pageSize);

    public Page<RentAnomalousWarningVO> queryRentAnomalousWarningList2(Map<String, Object> map, int pageNumber, int pageSize);

    public VPaymentVO queryPayment(Map<String, Object> map);

    public void saveRevocery(RentAnomalousWarningVO rentAnomalousWarningVO);

    public RentAnomalousWarningVO queryRentAnomalousWarning(RentAnomalousWarningVO rentAnomalousWarningVO);

    public RentAnomalousWarningVO queryRentAnomalousWarning1(Map<String, Object> map);

    public void submitAudit(RentAnomalousWarningVO rentAnomalousWarningVO);

    public void editRevocery(RentAnomalousWarningVO rentAnomalousWarningVO);

    public Page<RentAnomalousWarningVO> queryRentAnomalousWarningCheckList(Map<String, Object> map, int pageNumber, int pageSize);

    public int rentAnomalousWarningSubmitAudit(Map<String, Object> map);

    public Page<RentAnomalousWarningVO> queryRentAnomalousWarningListGroup(Map<String, Object> map, int pageNumber, int pageSize);

    List<EleAnomalousWarningVo> selectRentAnomalousWarning(Map<String, Object> map);

    List<EleAnomalousWarningVo> selectRentAnomalousWarningToDo(Map<String, Object> map);

    public Page<RentAnomalousWarningVO> queryRentRecoveryRecordListGroup(Map<String, Object> map, int pageNumber, int pageSize);

    public List<RegionVO> queryPrv(Map<String, Object> map);

    public List<RegionVO> queryPreg(Map<String, Object> map);

    public List<RegionVO> queryReg(Map<String, Object> map);

    public int delWarningRecord(RentAnomalousWarningVO rentAnomalousWarningVO);

    int delWarningTime(Map<String, String> delMap);

    List<DatAttachment> queryDatAttachmentByParam(DatAttachment record);

    int deleteFile(DatAttachment datAttachment);

    List<DatContractVO> queryDatContract(Map<String, Object> map);

    void updateRentAnomalousWarningById(RentAnomalousWarningVO warningVO);

    List<RentAnomalousWarningVO> queryRentAnomalousHangUpRecordList(Map<String, Object> map);

    List<RentAnomalousWarningVO> queryRentAnomalousWarningAll();

    void delRentAnomalousWarningAll(String prvId);

    void insertRentAnomalousWarning(List prvdatas);

    List<RentAnomalousWarningDetailVo> queryRentAnomalousWarningDetailList(Map<String, Object> map);

    RentAnomalousWarningVO queryRentAnomalousWarningByStatus(RentAnomalousWarningVO nomalous);

    RentPaymentVO queryRentPaymentByPaymentId(String paymentId);

    RentPaymentVO queryRentPaymentByBillAccountCode(String billaccountCode);
}
