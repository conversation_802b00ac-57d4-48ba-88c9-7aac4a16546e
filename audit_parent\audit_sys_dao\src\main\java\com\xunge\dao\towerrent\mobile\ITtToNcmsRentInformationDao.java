package com.xunge.dao.towerrent.mobile;


import com.xunge.core.page.Page;
import com.xunge.model.towerrent.mobile.TtToNcmsRentInformationVo;

import java.util.List;
import java.util.Map;

public interface ITtToNcmsRentInformationDao {

    /**
     * 分页查询起租单
     *
     * @param map
     * @param pageNum
     * @param pageSize
     * @return
     */
    Page<TtToNcmsRentInformationVo> queryByCondition(Map<String, Object> map, int pageNum, int pageSize);

    List<TtToNcmsRentInformationVo> exportTtToNcmsRentInfos(Map<String, Object> paraMap);

    TtToNcmsRentInformationVo queryOneTtToNcmsRentInfo(String rentinformationId);
}
