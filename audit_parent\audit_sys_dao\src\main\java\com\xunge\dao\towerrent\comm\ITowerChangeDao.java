package com.xunge.dao.towerrent.comm;

import com.xunge.model.towerrent.common.change.TowerChanges;

import java.util.List;
import java.util.Map;

/**
 * 描述：
 * Created on 2019/8/1.
 * <p>Title:</p>
 * <p>Copyright:Copyright (c) 2017</p>
 * <p>Company:安徽科大国创</p>
 * <p>Department:西南二区BU</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @update
 */
public interface ITowerChangeDao {

    /**
     * 查询铁塔侧对比历史记录
     *
     * @param paraMap
     * @return
     */
    List<TowerChanges> getTwrCompareHistory(Map<String, Object> paraMap);

    /**
     * 修改未确认的变动记录
     *
     * @param paramMap
     * @return
     */
    int updateTowerChangeById(Map<String, Object> paramMap);

    /**
     * 查询单个未审核的变动记录
     *
     * @param paramMap
     * @return
     */
    TowerChanges queryTowerChangesById(Map paramMap);

    /**
     * 条件查询未审核的变动记录
     *
     * @param paramMap
     * @return
     */
    List<TowerChanges> queryTowerChangesByParam(Map<String, Object> paramMap);

    /**
     * 根据ID在未审核表中删除即将审核通过的对比变动
     *
     * @return
     */
    int deleteTowerChangesById(Map<String, Object> param);

    /**
     * 向审核过的对比表中插入数据
     *
     * @param param
     * @return
     */
    int insertTowerHisChange(Map<String, Object> param);
}
