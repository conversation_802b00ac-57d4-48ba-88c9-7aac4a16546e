package com.xunge.model.activity;

import cn.afterturn.easypoi.excel.annotation.Excel;

import java.util.Date;

/**
 * 描述：
 * Created on 2021-01-19.
 * <p>Title:</p>
 * <p>Copyright:Copyright (c) 2017</p>
 * <p>Company:安徽科大国创</p>
 * <p>Department:西南二区</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @update
 */

public class TaskAlreadyExcelExport {

    @Excel(name = "待办类别", orderNum = "1")
    private String title;

    @Excel(name = "审核环节", orderNum = "2")
    private String taskName;

    @Excel(name = "工单名称", orderNum = "3")
    private String name;

    @Excel(name = "上级审核人", orderNum = "4")
    private String superiorAssigneeName;

    @Excel(name = "上级审核意见", orderNum = "5")
    private String comment;

    @Excel(name = "审核时间", orderNum = "6")
    private String superiorAssigneeTime;

    @Excel(name = "产生时间", orderNum = "7", format = "yyyy-MM-dd HH:mm:ss")
    private Date beginDate;

    @Excel(name = "处理时间", orderNum = "8", format = "yyyy-MM-dd HH:mm:ss")
    private Date endDate;

    @Excel(name = "处理状态", orderNum = "9", replace = {"已处理_1", "待处理_0", "已驳回_-1", "已签收_9", "已完结_11", "_null"})
    private String status;

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getTaskName() {
        return taskName;
    }

    public void setTaskName(String taskName) {
        this.taskName = taskName;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getSuperiorAssigneeName() {
        return superiorAssigneeName;
    }

    public void setSuperiorAssigneeName(String superiorAssigneeName) {
        this.superiorAssigneeName = superiorAssigneeName;
    }

    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }

    public String getSuperiorAssigneeTime() {
        return superiorAssigneeTime;
    }

    public void setSuperiorAssigneeTime(String superiorAssigneeTime) {
        this.superiorAssigneeTime = superiorAssigneeTime;
    }

    public Date getBeginDate() {
        return beginDate;
    }

    public void setBeginDate(Date beginDate) {
        this.beginDate = beginDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
}
