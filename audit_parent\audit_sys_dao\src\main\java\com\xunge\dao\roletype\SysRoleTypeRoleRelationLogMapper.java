package com.xunge.dao.roletype;

import com.xunge.model.roletype.SysRoleTypeRoleRelationLog;

import java.util.List;

public interface SysRoleTypeRoleRelationLogMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table sys_roletype_role_relation_log
     *
     * @mbggenerated Thu May 05 08:39:24 CST 2022
     */
    int deleteByPrimaryKey(Long logId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table sys_roletype_role_relation_log
     *
     * @mbggenerated Thu May 05 08:39:24 CST 2022
     */
    int insert(SysRoleTypeRoleRelationLog record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table sys_roletype_role_relation_log
     *
     * @mbggenerated Thu May 05 08:39:24 CST 2022
     */
    int insertSelective(SysRoleTypeRoleRelationLog record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table sys_roletype_role_relation_log
     *
     * @mbggenerated Thu May 05 08:39:24 CST 2022
     */
    SysRoleTypeRoleRelationLog selectByPrimaryKey(Long logId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table sys_roletype_role_relation_log
     *
     * @mbggenerated Thu May 05 08:39:24 CST 2022
     */
    int updateByPrimaryKeySelective(SysRoleTypeRoleRelationLog record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table sys_roletype_role_relation_log
     *
     * @mbggenerated Thu May 05 08:39:24 CST 2022
     */
    int updateByPrimaryKeyWithBLOBs(SysRoleTypeRoleRelationLog record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table sys_roletype_role_relation_log
     *
     * @mbggenerated Thu May 05 08:39:24 CST 2022
     */
    int updateByPrimaryKey(SysRoleTypeRoleRelationLog record);


    int batchInsertRelationLog(List<SysRoleTypeRoleRelationLog> list);
}