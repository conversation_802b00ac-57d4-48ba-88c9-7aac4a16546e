package com.xunge.comm.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import sun.misc.BASE64Decoder;
import sun.misc.BASE64Encoder;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.util.Objects;


/**
 * 图片工具
 */
@Slf4j
public class ImageUtil {

    /**
     * 图片展示
     *
     * @param path     路径
     * @param response response
     */
    public static void imageShow(String path, HttpServletResponse response) {
        OutputStream toClient = null;
        InputStream fis = null;
        try {
            response.setCharacterEncoding("UTF-8");
            response.setContentType("image/jpeg");
            // path是指欲下载的文件的路径。
            File file = new File(path);
            response.addHeader("Content-Length", "" + file.length());
            fis = new FileInputStream(file);
            toClient = response.getOutputStream();
            IOUtils.copy(fis, toClient);
            toClient.flush();
        } catch (IOException e) {
            log.error("ImageUtil 出错", e);
        } finally {
            try {
                if (toClient != null) {
                    toClient.close();
                }
                if (fis != null) {
                    fis.close();
                }
            } catch (IOException e) {
                log.error("ImageUtil 出错", e);
            }
        }
    }

    /**
     * base64编码字符串转换为图片
     *
     * @param imgStr base64编码字符串
     * @param path   图片路径
     */
    public static void base64StrToImage(String imgStr, String path) {
        if (imgStr == null)
            return;
        BASE64Decoder decoder = new BASE64Decoder();
        OutputStream out =null;
        try {
            // 解密
            byte[] b = decoder.decodeBuffer(imgStr);
            // 处理数据
            for (int i = 0; i < b.length; ++i) {
                if (b[i] < 0) {
                    b[i] += 256;
                }
            }
            //文件夹不存在则自动创建
            File tempFile = new File(path);
            if (!tempFile.getParentFile().exists()) {
                tempFile.getParentFile().mkdirs();
            }
            out = new FileOutputStream(tempFile);
            out.write(b);
            out.flush();
        } catch (Exception e) {
            log.error("ImageUtil 出错", e);
        }finally {
            try {
                if (out != null) {
                    out.close();
                }
            }catch (Exception e){
                log.error("ImageUtil 出错", e);
            }
        }
    }

    /**
     * 图片转base64字符串
     *
     * @param path 图片路径
     * @return base64字符
     */
    public static String imageToBase64Str(String path) throws IOException {
        File file = new File(path);
        if (!file.exists()) {
            throw new FileNotFoundException("path:"+path);
        }
        try (FileInputStream inputStream=new FileInputStream(file)){
            byte[] data = new byte[(int) file.length()];
            int read = inputStream.read(data);
            if (read == 0) {
                log.warn("empty file,path:{}",path);
                return "";
            }
            BASE64Encoder encoder = new BASE64Encoder();
            return encoder.encode(data);
        }
    }

    public static void main(String[] args) throws IOException {
        String base64Str = imageToBase64Str("E:\\usr\\webdata\\files\\ZJ\\20200922\\广厦城市之巅_1600736140947.png");
        System.out.println(base64Str);

        base64StrToImage(base64Str, "E:\\usr\\webdata\\files\\ZJ\\20200926\\1.png");
    }
}
