package com.xunge.model.budget.twr;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2022/7/28
 * @description 站址退网
 */
@Data
public class BudgetTwrLogoutVO {

    /**
     * 省份ID
     */
    private String prvId;

    /**
     * 省份名称
     */
    private String prvName;
    /**
     * 业务类型({塔类,1}, {室分,2}, {微站,3}, {传输,4}, {非标,5}, {合计,6})
     */
    private Integer productType;

    /**
     * 单站服务费（年化均值）
     */
    private BigDecimal hireLogoutSiteFee;
    /**
     * 单订单服务费（年化均值）
     */
    private BigDecimal hireLogoutOrderFee;
    /**
     * 退租站址数
     */
    private Integer hireLogoutBackSiteNumber;
    /**
     * 退租订单数
     */
    private Integer hireLogoutBackOrderNumber;
    /**
     * 月份数（退租站址）
     */
    private BigDecimal hireLogoutSiteMonthNumber;
    /**
     * 月份数（退租订单）
     */
    private BigDecimal hireLogoutOrderMonthNumber;
    /**
     * 站址退租退网-预算金额
     */
    private BigDecimal hireLogoutBudgetFee;
    /**
     * 站址退租退网-核减金额
     */
    private BigDecimal hireLogoutSubtractFee;

    /**
     * 调整金额
     */
    private BigDecimal hireLogoutAdjustFee;
    private BigDecimal hireLogoutAdjustFeeAfter;

    private String hireLogoutRemark;

}
