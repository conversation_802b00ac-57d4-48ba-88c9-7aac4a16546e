package com.xunge.dao.selfelec.verification;

import com.xunge.model.selfelec.EleVerificationBillamountExport;
import com.xunge.model.selfelec.eleverificate.EleVerificatedetail;
import com.xunge.model.selfelec.eleverificate.VEleBillaccountVerificateInfo;
import com.xunge.model.selfelec.verification.EleVerification;
import com.xunge.model.selfelec.verification.EleVerificationExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface EleVerificationMapper {
    int countByExample(EleVerificationExample example);

    int deleteByExample(EleVerificationExample example);

    int insert(EleVerification record);

    int insertSelective(EleVerification record);

    List<EleVerification> selectByExampleWithBLOBs(EleVerificationExample example);

    List<EleVerification> selectByExample(EleVerificationExample example);

    int updateByExampleSelective(@Param("record") EleVerification record, @Param("example") EleVerificationExample example);

    int updateByExampleWithBLOBs(@Param("record") EleVerification record, @Param("example") EleVerificationExample example);

    int updateByExample(@Param("record") EleVerification record, @Param("example") EleVerificationExample example);

    int updateBillamountIdIsNull(List<String> BillamountIds);

    int updateBillamountIdIsNullByDetailId(String BillamountdetailId);

    List<EleVerification> selectListByBillamountDetialIds(List<String> BillamountdetailIds);

    int updateReceiptInfo(EleVerification record);

    List<String> queryInquiryClaimPmtBillamount(Map map);

    /**
     * 根据缴费单编码查询缴费记录
     *
     * @param verificationCode
     * @return
     */
    List<EleVerification> queryElecVerificationVOByPaymentCode(String verificationCode);

    /**
     * 查询数据库中最大数值的code
     *
     * @param param
     * @return
     */
    Map<String, Object> queryMaxCode(Map<String, Object> param);

    List<EleVerificationBillamountExport> exportVerificationDetails(Map paramMap);

    int updateIsBack(Map<String, Object> map);

    EleVerification queryAuditingStateByVerificationId(@Param("verificationId") String verificateId);

    int updatecVerificationAdditionalRemark(Map<String, Object> paraMap);

    /**
     * 根据核销id查核销
     * @param verificationId
     * @return
     */
    VEleBillaccountVerificateInfo queryById(@Param("verificationId") String verificationId);

    Map<String,String> selectBillamountIdFromEleVerificationForUpdate(@Param("verificationId") String verificationId);

    List< Map<String,Object>> selectAuditingStateFromEleVerificationForUpdate(@Param("idList")List<String> idList);

    List<EleVerification> queryVerifState(String elecontractId);

    List<EleVerificatedetail> queryEleVerificationMeterIds(@Param("verificationId") String verificateId);
}