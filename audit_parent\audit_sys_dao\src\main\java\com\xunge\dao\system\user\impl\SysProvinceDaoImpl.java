package com.xunge.dao.system.user.impl;

import com.xunge.dao.AbstractBaseDao;
import com.xunge.dao.system.user.ISysProvinceDao;
import com.xunge.model.SysProvinceTreeVO;
import com.xunge.model.system.province.SysProvinceVO;

import java.util.List;
import java.util.Map;

/**
 * 用户管理dao实现类
 */
public class SysProvinceDaoImpl extends AbstractBaseDao implements ISysProvinceDao {

    final String Namespace = "com.xunge.dao.SysProvinceDao.";

    @Override
    public List<SysProvinceTreeVO> queryOnePro(Map<String, Object> paraMap) {
        // TODO Auto-generated method stub
        return this.getSqlSession().selectList(Namespace + "queryOnePro", paraMap);
    }

    @Override
    public List<SysProvinceTreeVO> queryAllSimpleProvince() {
        return this.getSqlSession().selectList(Namespace + "queryAllSimpleProvince");
    }

    @Override
    public List<SysProvinceVO> queryProvinceByCode(Map<String, Object> paraMap) {
        return this.getSqlSession().selectList(Namespace + "queryProvinceByCode", paraMap);
    }

    @Override
    public List<SysProvinceVO> queryProvinceById(Map<String, Object> paraMap) {
        return this.getSqlSession().selectList(Namespace + "queryProvinceById", paraMap);
    }

}
