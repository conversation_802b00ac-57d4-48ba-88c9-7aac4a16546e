package com.xunge.model.budget;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 预算审核节点表
 * @TableName budget_audit_info
 */
@Data
public class BudgetAuditInfo implements Serializable {
    /**
     * 流程记录ID
     */
    private String id;

    /**
     * 预算工单id
     */
    private String workOrderId;

    /**
     * 操作类型（0：省侧 1：集团）
     */
    private Integer operaType;

    /**
     * 审批节点
     */
    private String checkNode;

    /**
     * 审批人
     */
    private String checkUser;

    /**
     * 审批时间
     */
    private Date checkTime;

    /**
     * 审批动作
     */
    private String checkAction;

    /**
     * 审批意见
     */
    private String checkRemark;

    private static final long serialVersionUID = 1L;
}