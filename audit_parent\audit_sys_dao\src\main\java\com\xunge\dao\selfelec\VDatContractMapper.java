package com.xunge.dao.selfelec;

import com.xunge.model.selfelec.VDatContract;
import com.xunge.model.selfelec.VDatContractExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface VDatContractMapper {
    
    int countByExample(VDatContractExample example);

    
    int deleteByExample(VDatContractExample example);

    
    int insert(VDatContract record);

    
    int insertSelective(VDatContract record);

    
    List<VDatContract> selectByExample(VDatContractExample example);

    
    int updateByExampleSelective(@Param("record") VDatContract record, @Param("example") VDatContractExample example);

    
    int updateByExample(@Param("record") VDatContract record, @Param("example") VDatContractExample example);
}