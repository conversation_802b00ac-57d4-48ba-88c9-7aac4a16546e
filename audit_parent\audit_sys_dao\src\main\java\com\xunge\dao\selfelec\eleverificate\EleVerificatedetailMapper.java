package com.xunge.dao.selfelec.eleverificate;

import com.xunge.model.selfelec.EleOtherAmountFinance;
import com.xunge.model.selfelec.eleverificate.EleVerificatedetail;
import com.xunge.model.selfelec.eleverificate.EleVerificatedetailExample;
import com.xunge.model.selfelec.verification.EleVerificationSyncExportVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface EleVerificatedetailMapper {
    
    int countByExample(EleVerificatedetailExample example);

    
    int deleteByExample(EleVerificatedetailExample example);

    
    int deleteByPrimaryKey(String paymentdetailId);

    
    int insert(EleVerificatedetail record);

    
    int insertSelective(EleVerificatedetail record);

    
    List<EleVerificatedetail> selectByExample(EleVerificatedetailExample example);

    
    EleVerificatedetail selectByPrimaryKey(String paymentdetailId);

    
    int updateByExampleSelective(@Param("record") EleVerificatedetail record, @Param("example") EleVerificatedetailExample example);

    
    int updateByExample(@Param("record") EleVerificatedetail record, @Param("example") EleVerificatedetailExample example);

    
    int updateByPrimaryKeySelective(EleVerificatedetail record);

    
    int updateByPrimaryKey(EleVerificatedetail record);

    List<EleVerificationSyncExportVO> queryEleVerificationExports(Map<String, Object> map);

    public List<EleOtherAmountFinance> selectInOtherCost(List<String> verificationIds);

    int updateCmccRatioByPrimaryKey(EleVerificatedetail payment);
}
