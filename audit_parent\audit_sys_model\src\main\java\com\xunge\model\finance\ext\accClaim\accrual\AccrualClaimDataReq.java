package com.xunge.model.finance.ext.accClaim.accrual;

import com.xunge.core.model.UserLoginInfo;
import com.xunge.model.system.company.UserCompany;
import com.xunge.model.system.user.SysUserVO;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 计提汇总单推送请求数据
 */
@Data
@NoArgsConstructor
public class AccrualClaimDataReq {

    private String token;

    private UserLoginInfo loginInfo;

    private SysUserVO sysUserVO;

    private EleAccrualBillamount eleAccrualBillamount;

    private List<EleAccrualSecondBillamountDetail> eleAccrualSecondBillamountDetails;

    private String sourceDocumentUrl;

    //业务类型：0电费，1租费，2三方塔
    private Integer businessType;

    //报账人公司部门信息
    private UserCompany userCompany;

    /**
     * 是否无纸化
     */
    private Boolean isPaperless;

}
