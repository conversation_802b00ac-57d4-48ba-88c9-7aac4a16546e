package com.xunge.dao.twrrent.resourceinfo;

import com.xunge.model.towerrent.rentmanager.TowerRegionVO;

import java.util.List;
import java.util.Map;

/**
 * 起租管理 站址编码关联关系表dao接口
 *
 * <AUTHOR>
 */
public interface ITowerLinkDao {
    /**
     * 查询关联关系信息
     */
    public List<String> queryAllTowerLink(Map<String, Object> paraMap);

    /**
     * 资源信息地市区县
     *
     * @return
     */
    public List<TowerRegionVO> querySiteRegion(String prvId);

    /**
     * 站点信息地市区县
     *
     * @return
     */
    public List<TowerRegionVO> querySourceRegion(String prvId);

    /**
     * 资源信息地市区县,资源类别=0（机房）
     *
     * @return
     */
    public List<TowerRegionVO> querySiteRegionWithResType0(String prvId);

    /**
     * 站点信息地市区县，资源类别=1（资源点）
     *
     * @return
     */
    public List<TowerRegionVO> querySourceRegionWithResType1(String prvId);

    /**
     * 站点信息地市区县，资源类别=3（位置点）
     *
     * @return
     */
    public List<TowerRegionVO> querySourceRegionWithResType3(String prvId);

    /**
     * 站点信息地市区县，资源类别=2（热点）
     *
     * @param prvId
     * @return
     */
    List<TowerRegionVO> querySourceRegionWithResType2(String prvId);

    List<TowerRegionVO> queryOldSiteRegionWithResType0(String prvId);
}
