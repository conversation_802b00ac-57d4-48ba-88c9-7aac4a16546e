package com.xunge.comm.basedata.collection;

/**
 * 合同状态
 *
 * <AUTHOR>
 */
public class ContractComm {

    /**
     * 1：电费合同
     */
    public final static String contract_type_1 = "1";
    /**
     * 2：电费合同
     */
    public final static String contract_type_2 = "2";
    /**
     * 3：综合合同
     */
    public final static String contract_type_3 = "3";
    /**
     * 是否向下共享 1-是 0-否
     */
    public final static Integer IS_DOWNSHAR_YES = 1;
    /**
     * 是否向下共享 1-是 0-否
     */
    public final static Integer IS_DOWNSHAR_NO = 0;

    /**
     * 服务请求输入完整性校验不 通过
     */
    public final static String BIZ_01 = "BIZ-01";
    /**
     * 服务业务规则处理不通过
     */
    public final static String BIZ_02 = "BIZ-02";
    /**
     * 业务系统处理超时
     */
    public final static String BIZ_03 = "BIZ-03";
    /**
     * 业务系统其它处理异常
     */
    public final static String BIZ_04 = "BIZ-04";
    /**
     * PRI_KEY为空
     */
    public final static String ERROR_CODE_01 = "ERROR_CODE_01";
    /**
     * 省公司代码为空
     */
    public final static String ERROR_CODE_02 = "ERROR_CODE_02";
    /**
     * 合同流水号为空
     */
    public final static String ERROR_CODE_03 = "ERROR_CODE_03";
    /**
     * 退回原因为空
     */
    public final static String ERROR_CODE_04 = "ERROR_CODE_04";
    /**
     * 合同流水号存在多条数据
     */
    public final static String ERROR_CODE_05 = "ERROR_CODE_05";
    /**
     * 不存在该流水号
     */
    public final static String ERROR_CODE_06 = "ERROR_CODE_06";
    /**
     * 合同更新失败！
     */
    public final static String ERROR_CODE_07 = "ERROR_CODE_07";
    /**
     * 未推送的合同不能退回
     */
    public final static String ERROR_CODE_08 = "ERROR_CODE_08";
    /**
     * 履行中的合同不能退回
     */
    public final static String ERROR_CODE_09 = "ERROR_CODE_09";
    /**
     * 已推送的合同不能再次退回
     */
    public final static String ERROR_CODE_10 = "ERROR_CODE_10";
    public final static String ERROR_CODE_11 = "ERROR_CODE_11";
    public final static String ERROR_CODE_12 = "ERROR_CODE_12";
    public static final String ERROR_CODE_13 = "ERROR_CODE_13";
    public final static String MSGHEADER = "msgheader";
    public final static String INPUTCOLLECTION = "inputcollection";
    /**
     * 补充协议类型-变更
     */
    public final static String CHANGE_CRT = "CHANGE_CRT";
    /**
     * 补充协议类型-转让
     */
    public final static String TRANSFER_CTR = "TRANSFER_CTR";
    /**
     * 补充协议类型-接触协议
     */
    public final static String PROTOCOL_RELIEVE = "PROTOCOL_RELIEVE";
    /**
     * 起草类型-补充协议
     */
    public final static String PROTOCOL = "PROTOCOL";
    /**
     * 起草类型-新合同
     */
    public static final String NEW = "NEW";
    /**
     * 起草方式-默认新合同
     */
    public static final String NEW_CRT = "NEW_CRT";
    /**
     * 我方主体-TD
     */
    public static final String TD = "TD";
    /**
     * 我方主体-上市
     */
    public static final String ERP = "ERP";
    /**
     * 我方主体-通服
     */
    public static final String CS = "CS";
    /**
     * 我方主体-集团
     */
    public static final String CMCC = "CMCC";
    /**
     * 我方主体-铁通
     */
    public static final String TT = "TT";
    /**
     * 收支类型-无收无支类
     */
    public static final String PAYMENT = "PAYMENT";

}
