package com.xunge.comm.utils;

import com.google.common.collect.Maps;
import com.xunge.core.util.PropertiesLoader;
import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.FileUploadException;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.apache.commons.fileupload.servlet.ServletFileUpload;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.io.IOException;
import java.security.SecureRandom;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2018年05月16日
 */
public class UploadUtil {
    private static final Logger logger = LoggerFactory.getLogger(UploadUtil.class);
    public static final String FORM_FIELDS = "form_fields";
    public static final String FILE_FIELDS = "file_fields";
    private static final String TEMP_PATH = "/temp";
    private static PropertiesLoader loader = new PropertiesLoader(new String[]{"properties/sysConfig.properties"});
    Map<String, Object> fields = Maps.newHashMap();
    //private long maxSize = 32000000L;
    private long maxSize = 209715200L;
    private Map<String, String> extMap = new HashMap();
    private String basePath;
    private String dirName;
    private String tempPath;
    private String fileName;
    private String savePath;
    private String saveUrl;
    private String fileUrl;
    private String dataUrl;
    private static Random rand;
    static {
        try {
            rand = SecureRandom.getInstanceStrong();
        }catch (Exception e){
            logger.error("随机数初始化失败：",e);
        }
    }
    public UploadUtil() {
        this.basePath = loader.getProperty("UploadUrls");
        this.dirName = "images";
        this.tempPath = this.basePath + "/temp";
        this.extMap.put("images", "gif,jpg,jpeg,png,bmp");
        this.extMap.put("flashs", "swf,flv");
        this.extMap.put("medias", "swf,flv,mp3,wav,wma,wmv,mid,avi,mpg,asf,rm,rmvb");
        this.extMap.put("files", "doc,docx,xls,xlsx,ppt,pdf,htm,html,txt,zip,rar,gz,bz2,gif,jpg,jpeg,png,bmp");
        this.extMap.put("uploadPictures", "jpg,png,gif,webp,jpeg,jfif,bmp");
    }

    public static String getFileExtension(String fileName) {
        return fileName != null && fileName.lastIndexOf(".") != -1 && fileName.lastIndexOf(".") != fileName.length() - 1
                ? StringUtils.lowerCase(fileName.substring(fileName.lastIndexOf(".") + 1))
                : null;
    }

    public static String getFileNameWithoutExtension(String fileName) {
        return fileName != null && fileName.lastIndexOf(".") != -1 ? fileName.substring(0, fileName.lastIndexOf(".")) : null;
    }

    public Map<String, Object> uploadFile(HttpServletRequest request, String newName, String path) {
        Map<String, Object> infos = Maps.newHashMap();
        String err = this.validateFields(request, path);
        infos.put("err", err);
        Map<String, Object> fieldsMap = new HashMap();
        if (err.equals("true")) {
            fieldsMap = this.initFields(request);
        }

        List<FileItem> fiList = (List) ((Map) fieldsMap).get("file_fields");
        if (fiList != null) {
            Iterator i$ = fiList.iterator();

            while (i$.hasNext()) {
                FileItem item = (FileItem) i$.next();
                String name = item.getName();
                String fileExtension;
                if (StringUtils.isBlank(newName)) {
                    fileExtension = getFileNameWithoutExtension(name);
                    newName = fileExtension + "_" + System.currentTimeMillis();
                }
                fileExtension = getFileExtension(name);
                if (!com.xunge.core.util.FileUtils.validataFileSuffix(fileExtension)) {
                    infos.put("saveErr", "非合法的文件后缀【" + fileExtension + "】");
                    continue;
                }
                this.fileName = StringUtils.isNotBlank(newName) ? newName.replace(" ", "") + "." + fileExtension : name.replace(" ", "");
                this.fileName = this.fileName.replace(" ", "");
                newName = newName.replace(" ", "");
                infos.put("fileName", this.fileName);
                infos.put("name", name);
                infos.put("saveErr", this.saveFile(item, newName));
            }

            infos.put("savePath", this.savePath);
            infos.put("saveUrl", this.saveUrl);
            infos.put("fileUrl", this.fileUrl);
            infos.put("path", this.dataUrl + this.fileName);
        }

        return infos;
    }

    public Map<String, Object> uploadPic(HttpServletRequest request, String newName, String path, String SuffixProperties) {
        Map<String, Object> infos = Maps.newHashMap();
        String err = this.validateFields(request, path);
        infos.put("err", err);
        Map<String, Object> fieldsMap = new HashMap();
        if (err.equals("true")) {
            fieldsMap = this.initFields(request);
        }

        List<FileItem> fiList = (List) ((Map) fieldsMap).get("file_fields");
        if (fiList != null) {
            Iterator i$ = fiList.iterator();

            while (i$.hasNext()) {
                FileItem item = (FileItem) i$.next();
                String name = item.getName();
                String fileExtension;
                if (StringUtils.isBlank(newName)) {
                    fileExtension = getFileNameWithoutExtension(name);
                    newName = fileExtension + "_" + System.currentTimeMillis();
                }
                fileExtension = getFileExtension(name);
                if (!com.xunge.core.util.FileUtils.validataFileSuffixByCustom(fileExtension, SuffixProperties)) {
                    infos.put("saveErr", "非合法的文件后缀【" + fileExtension + "】");
                    continue;
                }
                this.fileName = StringUtils.isNotBlank(newName) ? newName.replace(" ", "") + "." + fileExtension : name.replace(" ", "");
                this.fileName = this.fileName.replace(" ", "");
                this.dirName = "uploadPictures";
                newName = newName.replace(" ", "");
                infos.put("fileName", this.fileName);
                infos.put("name", name);
                infos.put("saveErr", this.saveFile(item, newName));
            }

            infos.put("savePath", this.savePath);
            infos.put("saveUrl", this.saveUrl);
            infos.put("fileUrl", this.fileUrl);
            infos.put("path", this.dataUrl + this.fileName);
        }

        return infos;
    }

    public Map<String, Object> batchUploadFile(HttpServletRequest request, String path) throws InterruptedException {
        Map<String, Object> infos = Maps.newHashMap();
        String err = this.validateFields(request, path);
        infos.put("err", err);
        Map<String, Object> fieldsMap = new HashMap();
        if (err.equals("true")) {
            fieldsMap = this.initFields(request);
        }

        List<FileItem> fiList = (List) ((Map) fieldsMap).get("file_fields");
        if (fiList != null) {
            Iterator i$ = fiList.iterator();
            StringBuilder buf = new StringBuilder();
            while (i$.hasNext()) {
                FileItem item = (FileItem) i$.next();
                String name = item.getName();
                String tmpFileName = "";
                String tmpName = getFileNameWithoutExtension(name);//获取去除后缀的文件名
                String fileExt = getFileExtension(name);//获取文件后缀
                String newName = tmpName + "_" + System.currentTimeMillis();
                this.fileName = newName.replace(" ", "") + "." + fileExt;
                this.fileName = this.fileName.replace(" ", "");
                newName = newName.replace(" ", "");
                tmpFileName = newName;
                infos.put("saveErr", this.saveFile(item, newName));
                infos.put("name", name);
                buf.append(this.dataUrl + this.fileName + ",");
                newName = "";
            }
            infos.put("path", buf.substring(0, buf.length() - 1));
        }

        return infos;
    }

    private String validateFields(HttpServletRequest request, String path) {
        String errorInfo = "true";
        String contentType = request.getContentType();
        int contentLength = request.getContentLength();
        this.savePath = this.basePath + "/";
        this.saveUrl = this.basePath + "/";
        File uploadDir = new File(this.savePath);
        if (contentType != null && contentType.startsWith("multipart")) {
            if (this.maxSize < (long) contentLength) {
                System.out.println("上传文件大小超出文件最大大小");
                errorInfo = "上传文件大小超出文件最大大小[" + this.maxSize / 1024 / 1024 + "M]";
            } else if (!ServletFileUpload.isMultipartContent(request)) {
                errorInfo = "请选择文件";
            } else if (!uploadDir.isDirectory()) {
                errorInfo = "上传目录[" + this.savePath + "]不存在";
            } else if (!uploadDir.canWrite()) {
                errorInfo = "上传目录[" + this.savePath + "]没有写权限";
            } else if (!this.extMap.containsKey(this.dirName)) {
                errorInfo = "目录名不正确";
            } else {
                this.savePath = this.savePath + this.dirName + "/";
                this.saveUrl = this.saveUrl + this.dirName + "/";
                this.dataUrl = "/" + this.dirName + "/";
                if (StringUtils.isNotBlank(path)) {
                    this.savePath = this.savePath + path + "/";
                    this.saveUrl = this.saveUrl + path + "/";
                    this.dataUrl = this.dataUrl + path + "/";
                }

                File saveDirFile = new File(this.savePath);
                if (!saveDirFile.exists()) {
                    saveDirFile.mkdirs();
                }

                SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
                String ymd = sdf.format(new Date());
                this.savePath = this.savePath + ymd + "/";
                this.saveUrl = this.saveUrl + ymd + "/";
                this.dataUrl = this.dataUrl + ymd + "/";
                File dirFile = new File(this.savePath);
                if (!dirFile.exists()) {
                    dirFile.mkdirs();
                }

                this.tempPath = this.tempPath + "/";
                File file = new File(this.tempPath);
                if (!file.exists()) {
                    file.mkdirs();
                }
            }
        } else {
            System.out.println("请求不包含multipart/form-data流");
            errorInfo = "请求不包含multipart/form-data流";
        }

        return errorInfo;
    }

    private Map<String, Object> initFields(HttpServletRequest request) {
        boolean isMultipart = ServletFileUpload.isMultipartContent(request);
        if (isMultipart) {
            DiskFileItemFactory factory = new DiskFileItemFactory();
            factory.setSizeThreshold(10485760);
            factory.setRepository(new File(this.tempPath));
            ServletFileUpload upload = new ServletFileUpload(factory);
            upload.setHeaderEncoding("UTF-8");
            upload.setSizeMax(this.maxSize);
            List items = null;

            try {
                items = upload.parseRequest(request);
            } catch (FileUploadException var11) {
                var11.printStackTrace();
            }

            if (items != null && items.size() > 0) {
                Iterator<FileItem> iter = items.iterator();
                ArrayList list = new ArrayList();

                while (iter.hasNext()) {
                    FileItem item = (FileItem) iter.next();
                    if (item.isFormField()) {
                        String name = item.getFieldName();
                        String value = item.getString();
                        this.fields.put(name, value);
                    } else {
                        list.add(item);
                    }
                }

                this.fields.put("form_fields", this.fields);
                this.fields.put("file_fields", list);
            }
        }

        return this.fields;
    }

    private String saveFile(FileItem item, String newName) {
        String error = "true";
        String fileName = item.getName().replace(" ", "");
        String fileExt = fileName.substring(fileName.lastIndexOf(".") + 1).toLowerCase();
        if (item.getSize() > this.maxSize) {
            error = "上传文件大小超过限制";
        } else if (!Arrays.asList(((String) this.extMap.get(this.dirName)).split(",")).contains(fileExt)) {
            error = "上传文件扩展名是不允许的扩展名。\n只允许" + (String) this.extMap.get(this.dirName) + "格式。";
        } else {
            String newFileName;
            if (StringUtils.isNotBlank(newName)) {
                newFileName = newName + "." + fileExt;
            } else if ("".equals(fileName.trim())) {
                SimpleDateFormat df = new SimpleDateFormat("yyyyMMddHHmmss");
                newFileName = df.format(new Date()) + "_" + rand.nextInt(1000) + "." + fileExt;
            } else {
                newFileName = fileName;
            }

            this.fileUrl = this.saveUrl + newFileName;

            try {
                File uploadedFile = new File(this.savePath, newFileName);
                item.write(uploadedFile);
            } catch (IOException var8) {
                var8.printStackTrace();
                System.out.println("上传失败了！！！");
            } catch (Exception var9) {
                var9.printStackTrace();
            }
        }

        return error;
    }

    public String getSavePath() {
        return this.savePath;
    }

    public void setSavePath(String savePath) {
        this.savePath = savePath;
    }

    public String getSaveUrl() {
        return this.saveUrl;
    }

    public void setSaveUrl(String saveUrl) {
        this.saveUrl = saveUrl;
    }

    public long getMaxSize() {
        return this.maxSize;
    }

    public void setMaxSize(long maxSize) {
        this.maxSize = maxSize;
    }

    public Map<String, String> getExtMap() {
        return this.extMap;
    }

    public void setExtMap(Map<String, String> extMap) {
        this.extMap = extMap;
    }

    public String getBasePath() {
        return this.basePath;
    }

    public void setBasePath(String basePath) {
        this.basePath = basePath;
        this.tempPath = basePath + "/temp";
    }

    public String getDirName() {
        return this.dirName;
    }

    public void setDirName(String dirName) {
        this.dirName = dirName;
    }

    public String getTempPath() {
        return this.tempPath;
    }

    public void setTempPath(String tempPath) {
        this.tempPath = tempPath;
    }

    public String getFileUrl() {
        return this.fileUrl;
    }

    public void setFileUrl(String fileUrl) {
        this.fileUrl = fileUrl;
    }

    public String getFileName() {
        return this.fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getDataUrl() {
        return this.dataUrl;
    }

    public void setDataUrl(String dataUrl) {
        this.dataUrl = dataUrl;
    }

    public Map<String, Object> getFields() {
        return this.fields;
    }

    public void setFields(Map<String, Object> fields) {
        this.fields = fields;
    }
}
