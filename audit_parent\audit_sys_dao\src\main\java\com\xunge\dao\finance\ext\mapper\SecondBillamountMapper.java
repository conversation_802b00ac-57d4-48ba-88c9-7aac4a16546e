package com.xunge.dao.finance.ext.mapper;

import com.xunge.model.finance.ext.accClaim.SecondBillamount;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface SecondBillamountMapper {

    Map<String, Object> queryMaxCode(Map<String, Object> param);

    void insertListForEle(List<SecondBillamount> secondBillamounts);

    void insertListForRent(List<SecondBillamount> secondBillamounts);

    void insertListForTele(List<SecondBillamount> secondBillamounts);

    void insertListForLoan(List<SecondBillamount> secondBillamounts);

    void insertListForDsEle(List<SecondBillamount> secondBillamounts);

    void inserVerBillamounttList(@Param("eleSecondBillamounts") List<SecondBillamount> eleSecondBillamounts);

    void delSecondBillamount(Map<String, Object> param);

    List<Map<String, String>> querySecondBillamountCodeByBillamountdetailId(Map<String, String> param);

    List<Map<String, String>> querySecondBillamountCodeBeforePush(Map<String, String> param);

    List<Map<String, String>> querySecondBillamountCodePushed(Map<String, String> param);

    int updateMergedConditionForSecondBillamountCodePushed(Map<String, String> param);

    int insertSecondBillamountCodeBeforePush(@Param("bizType") String bizType, @Param("list") List<SecondBillamount> secondBillamounts);

    int deleteSecondBillamountByBeforePush(@Param("bizType") String bizType, @Param("billamountId") String billamountId);

}
