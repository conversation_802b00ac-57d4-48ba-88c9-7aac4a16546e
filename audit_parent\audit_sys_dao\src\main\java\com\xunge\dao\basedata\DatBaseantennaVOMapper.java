package com.xunge.dao.basedata;

import com.xunge.model.basedata.DatBaseantennaVO;

import java.util.List;
import java.util.Map;

public interface DatBaseantennaVOMapper {
    /**
     * 根据省份id查询所有天线数据
     *
     * @param prvId
     * @return
     */
    List<DatBaseantennaVO> queryAllAntenna(String prvId);

    /**
     * 将需要删除的天线信息状态置为-1
     *
     * @param map
     * @return
     */
    int delByCuidsAndPrvid(Map<String, Object> map);

    /**
     * 新增天线数据入库
     *
     * @param list
     * @return
     */
    int insertAntenna(List<DatBaseantennaVO> list);

    /**
     * 查询天线列表信息
     *
     * @param hashMaps
     * @return
     */
    List<DatBaseantennaVO> queryDatbaseantennaPage(Map<String, Object> hashMaps);

    /**
     * 根据id查询天线信息
     *
     * @param hashMaps
     * @return
     */
    DatBaseantennaVO queryDatbaseantennaById(Map<String, Object> map);

    /**
     * 插入天线数据
     *
     * @param datBaseantennaVO
     * @return
     * @date 2018年03月09日
     * <AUTHOR>
     */
    int insert(DatBaseantennaVO datBaseantennaVO);

    /**
     * 批量删除天线信息
     *
     * @param map
     * @return
     * @date 2018年03月12日
     * <AUTHOR>
     */
    int deleteByPrimaryKey(Map<String, Object> map);

    /**
     * 修改天线天线信息
     *
     * @param baseantenna
     * @return
     * @date 2018年03月12日
     * <AUTHOR>
     */
    int updateByPrimaryKey(DatBaseantennaVO baseantenna);

    /**
     * 查询后天线编码后6位
     *
     * @param map
     * @return
     */
    String queryMaxTannaCuid(Map<String, Object> map);

    DatBaseantennaVO queryBeanById(Map<String, Object> map);
}
