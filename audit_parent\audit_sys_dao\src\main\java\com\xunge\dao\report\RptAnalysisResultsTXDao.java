package com.xunge.dao.report;


import com.xunge.model.report.RptPrvEleBasesiteVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;


public interface RptAnalysisResultsTXDao {
    List<RptPrvEleBasesiteVo> querySiteCostTx(@Param("prvId") String prvId, @Param("pregId") String pregId,
                                              @Param("year") int year, @Param("month") int month, @Param("level") int level,
                                              @Param("userPregIds") List<String> userPregIds, @Param("userRegIds") List<String> userRegIds, @Param("annual") int annual);

}
