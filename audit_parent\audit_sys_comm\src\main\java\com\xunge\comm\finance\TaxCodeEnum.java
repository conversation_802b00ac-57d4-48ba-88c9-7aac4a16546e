package com.xunge.comm.finance;

public enum TaxCodeEnum {
    VAT17("VAT17", 0.17),
    VAT1_5("VAT1.5", 0.015),
    VAT10("VAT10", 0.1),
    VAT13("VAT13", 0.13),
    VAT5("VAT5", 0.05),
    VAT11("VAT11", 0.11),
    VAT16("VAT16", 0.16),
    VAT6("VAT6", 0.06),
    VAT0("VAT0", 0),
    VAT3("VAT3", 0.03),
    VAT9("VAT9", 0.09),
    VAT1("VAT1", 0.01),
    UNDEFINED("UNDEFINED", -1);

    private String desc;
    private double value;

    TaxCodeEnum(String desc, double value) {
        this.desc = desc;
        this.value = value;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public double getValue() {
        return value;
    }

    public static TaxCodeEnum setValue(double value) {
        if (value == 0.17) {
            return VAT17;
        } else if (value == 0.015) {
            return VAT1_5;
        } else if (value == 0.1) {
            return VAT10;
        } else if (value == 0.13) {
            return VAT13;
        } else if (value == 0.05) {
            return VAT5;
        } else if (value == 0.11) {
            return VAT11;
        } else if (value == 0.16) {
            return VAT16;
        } else if (value == 0.06) {
            return VAT6;
        } else if (value == 0) {
            return VAT0;
        } else if (value == 0.03) {
            return VAT3;
        } else if (value == 0.09) {
            return VAT9;
        } else if (value == 0.01) {
            return VAT1;
        } else {
            return UNDEFINED;
        }
    }
}
