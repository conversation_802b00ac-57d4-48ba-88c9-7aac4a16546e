package com.xunge.dao.selfrent.billamount.impl;

import com.xunge.core.page.Page;
import com.xunge.dao.AbstractBaseDao;
import com.xunge.dao.selfrent.billamount.RentBillamountDetailDao;
import com.xunge.filter.PageInterceptor;
import com.xunge.model.selfrent.billamount.RentBillamountDetailVO;
import com.xunge.model.selfrent.billamount.RentFinanceBillamountDetailVO;
import com.xunge.model.selfrent.billamount.RentFinanceBillamountDetailVONew;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2017年6月27日 上午10:14:34
 */
public class RentBillAmountDetailDaoImpl extends AbstractBaseDao implements RentBillamountDetailDao {

    final String Namespace = "com.xunge.dao.RentBillamountDetailVOMapper.";

    public int insertRentBillamountDetail(RentBillamountDetailVO rentBillamountDetail) {
        // TODO Auto-generated method stub
        return this.getSqlSession().insert(Namespace + "insertRentBillamountDetail", rentBillamountDetail);
    }

    public int insertRentBillamountDetailList(List<RentBillamountDetailVO> rentBillamountDetails) {
        // TODO Auto-generated method stub
        return this.getSqlSession().insert(Namespace + "insertRentBillamountDetailList", rentBillamountDetails);
    }

    public List<RentBillamountDetailVO> selectByBillamountId(String billamountId) {

        return this.getSqlSession().selectList(Namespace + "selectByBillamountId", billamountId);
    }

    public Page<List<RentBillamountDetailVO>> selectAllByBillamountId(String billamountId, Map<String, Object> map) {
        PageInterceptor.startPage(Integer.parseInt(map.get("pageNumber").toString()), Integer.parseInt(map.get("pageSize").toString()));
        this.getSqlSession().selectList(Namespace + "selectAllByBillamountId", billamountId);
        return PageInterceptor.endPage();
    }

    public List<RentBillamountDetailVO> selectAllByBillamountIdNoPage(String billamountId) {
        return this.getSqlSession().selectList(Namespace + "selectAllByBillamountId", billamountId);
    }

    public List<RentFinanceBillamountDetailVO> selectAllFiancedByBillamountIdNopage(String billamountId) {
        return this.getSqlSession().selectList(Namespace + "selectAllFinanceByBillamountId", billamountId);
    }

    public List<RentFinanceBillamountDetailVONew> selectAllFiancedByBillamountIdNopageNew(String billamountId) {
        return this.getSqlSession().selectList(Namespace + "selectAllFinanceByBillamountIdNew", billamountId);
    }

    @Override
    public int deleteBybillamountId(String billamountId) {
        // TODO Auto-generated method stub
        return this.getSqlSession().delete(Namespace + "deleteBybillamountId", billamountId);
    }

    @Override
    public int deleteBybillamountDetailId(String billamountDetailId) {
        // TODO Auto-generated method stub
        return this.getSqlSession().delete(Namespace + "deleteByPrimaryKey", billamountDetailId);
    }


    /* (non Javadoc)
     * Title: updateComtractInfo
     * Description:
     * @param paraMap
     * @return
     * @see com.xunge.dao.selfrent.billamount.RentBillamountDetailDao#updateComtractInfo(java.util.Map)
     */

    @Override
    public int updateComtractInfo(Map<String, Object> paraMap) {
        // TODO Auto-generated method stub
        return this.getSqlSession().update(Namespace + "updateComtractInfo", paraMap);
    }

    @Override
    public int updateBillamountdetailAdjustById(RentBillamountDetailVO param) {
        return this.getSqlSession().update(Namespace + "updateBillamountdetailAdjustById", param);
    }

    @Override
    public List<RentBillamountDetailVO> selectBillamountdetail(String billamountdetailId) {
        return this.getSqlSession().selectList(Namespace + "selectBillamountdetail", billamountdetailId);
    }

    @Override
    public List<RentBillamountDetailVO> querySupplierInfos(String billamountId) {
        return this.getSqlSession().selectList(Namespace + "querySupplierInfos", billamountId);
    }

    @Override
    public List<RentBillamountDetailVO> queryRentBillamountDetailLeftJoinOther(Map<String, Object> maps) {
        return this.getSqlSession().selectList(Namespace + "queryRentBillamountDetailLeftJoinOther", maps);
    }

    @Override
    public List<RentBillamountDetailVO> queryRentBillamountDetailLeftJoinOtherSingle(String billamountDetailId) {
        return this.getSqlSession().selectList(Namespace + "queryRentBillamountDetailLeftJoinOtherSingle", billamountDetailId);
    }
}
