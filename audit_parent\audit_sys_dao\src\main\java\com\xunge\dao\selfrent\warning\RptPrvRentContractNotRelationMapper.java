package com.xunge.dao.selfrent.warning;


import com.xunge.model.selfrent.warning.ContractNotRelationParam;
import com.xunge.model.selfrent.warning.RentContractHisRelation;
import com.xunge.model.selfrent.warning.RptPrvRentContractNotRelation;
import com.xunge.model.selfrent.warning.RptPrvRentContractNotRelationCur;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface RptPrvRentContractNotRelationMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(RptPrvRentContractNotRelation record);

    int insertSelective(RptPrvRentContractNotRelation record);

    RptPrvRentContractNotRelation selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(RptPrvRentContractNotRelation record);

    int updateByPrimaryKey(RptPrvRentContractNotRelation record);

    List<RptPrvRentContractNotRelation> queryList(ContractNotRelationParam param);

    List<RptPrvRentContractNotRelationCur> queryListCur(ContractNotRelationParam param);

    List<RentContractHisRelation> queryHisRelation(@Param("rentContractId") String rentContractId);
}