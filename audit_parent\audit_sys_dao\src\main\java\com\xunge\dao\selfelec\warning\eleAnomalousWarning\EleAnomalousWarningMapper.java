package com.xunge.dao.selfelec.warning.eleAnomalousWarning;

import com.xunge.model.selfelec.eleAnomalousWarning.EleAnomalousWarning;
import com.xunge.model.system.region.RegionVO;

import java.util.List;
import java.util.Map;

public interface EleAnomalousWarningMapper {

    List<EleAnomalousWarning> queryEleAnomalousWarningList(Map<String, Object> paraMap);

    List<RegionVO> queryPrv(Map<String, Object> map);

    List<RegionVO> queryPreg(Map<String, Object> map);

    List<RegionVO> queryReg(Map<String, Object> map);


}