package com.xunge.dao.selfelec;

import com.xunge.model.selfelec.ElePaymentPowerdataDetail;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ElePaymentPowerdataDetailMapper {
    int deleteByPrimaryKey(String elePaymentpowerdataId);

    int deleteByPrimaryKeys(@Param("elePaymentpowerdataIds") List<String> elePaymentpowerdataIds);

    int insert(ElePaymentPowerdataDetail record);

    int insertList(@Param("list") List<ElePaymentPowerdataDetail> list);

    int insertSelective(ElePaymentPowerdataDetail record);

    ElePaymentPowerdataDetail selectByPrimaryKey(String elePaymentpowerdataId);

    List<String> selectElePaymentpowerdataIdsByPaymentCode(String paymentCode);

    int updateByPrimaryKeySelective(ElePaymentPowerdataDetail record);

    int updateByPrimaryKey(ElePaymentPowerdataDetail record);

    int updateBillamountStartAndEndByPaymentCode(ElePaymentPowerdataDetail record);
}