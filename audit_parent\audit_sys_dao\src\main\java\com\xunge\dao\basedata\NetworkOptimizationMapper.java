package com.xunge.dao.basedata;

import com.xunge.model.basedata.NetworkOptimization;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface NetworkOptimizationMapper {
    List<NetworkOptimization> queryNetworkOptimization(Map<String,Object> param);
    List<NetworkOptimization> queryNetworkByMonth(Map<String,Object> param);
    List<NetworkOptimization> queryNetworkOptimizationData(@Param("resourceCodeList") List<String> resourceCodeList, @Param("yearmonthList") List<String> yearmonthList);
}
