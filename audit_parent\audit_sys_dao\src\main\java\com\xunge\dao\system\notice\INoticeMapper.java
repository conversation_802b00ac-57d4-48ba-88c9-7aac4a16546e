package com.xunge.dao.system.notice;

import com.xunge.model.system.notice.NoticeInfo;

import java.util.List;
import java.util.Map;

public interface INoticeMapper {
    Integer unreadNoticeInfoNum(Map<String, Object> map);

    List<NoticeInfo> queryNoticeInfo(Map<String, Object> param);

    NoticeInfo queryNoticeInfoDetail(Map<String, Object> param);

    Integer readNoticeInfo(Map<String, Object> param);

    Integer delReadInfo(Map<String, Object> param);

    Integer isReadNoticeInfo(Map<String, Object> param);

    NoticeInfo queryLastNoticeInfoDetail(Map<String, Object> param);
}
