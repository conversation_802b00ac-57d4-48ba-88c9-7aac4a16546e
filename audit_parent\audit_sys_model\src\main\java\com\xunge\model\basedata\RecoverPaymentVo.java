package com.xunge.model.basedata;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xunge.model.BaseActVO;

import java.util.Date;

/**
 * recoverPaymentVo
 *
 * <AUTHOR>
 * @version V1.0
 * @description <文件描述: >
 * @date 2019/10/9
 * @email <EMAIL>
 */
public class RecoverPaymentVo extends BaseActVO {
    private String recoverId;
    private String recoverCode;
    private String recoverMoney;
    private String recoverState;
    private String recoverExplain;
    private String billaccountCode;
    private String billaccountName;
    private String paymentCode;
    private String paymentId;
    private String billamountStartdate;
    private String billamountEnddate;
    private String prvId;
    private String prvName;
    private String pregId;
    private String pregName;
    private String regId;
    private String regName;
    private String createTime;
    private String createBy;
    private String updateTime;
    private String updateBy;
    private String taskId;
    private String billaccountId;
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date passTime;

    public String getRecoverId() {
        return recoverId;
    }

    public void setRecoverId(String recoverId) {
        this.recoverId = recoverId;
    }

    public String getRecoverCode() {
        return recoverCode;
    }

    public void setRecoverCode(String recoverCode) {
        this.recoverCode = recoverCode;
    }

    public String getRecoverMoney() {
        return recoverMoney;
    }

    public void setRecoverMoney(String recoverMoney) {
        this.recoverMoney = recoverMoney;
    }

    public String getRecoverState() {
        return recoverState;
    }

    public void setRecoverState(String recoverState) {
        this.recoverState = recoverState;
    }

    public String getRecoverExplain() {
        return recoverExplain;
    }

    public void setRecoverExplain(String recoverExplain) {
        this.recoverExplain = recoverExplain;
    }

    public String getBillaccountCode() {
        return billaccountCode;
    }

    public void setBillaccountCode(String billaccountCode) {
        this.billaccountCode = billaccountCode;
    }

    public String getBillaccountName() {
        return billaccountName;
    }

    public void setBillaccountName(String billaccountName) {
        this.billaccountName = billaccountName;
    }

    public String getPaymentCode() {
        return paymentCode;
    }

    public void setPaymentCode(String paymentCode) {
        this.paymentCode = paymentCode;
    }

    public String getPaymentId() {
        return paymentId;
    }

    public void setPaymentId(String paymentId) {
        this.paymentId = paymentId;
    }

    public String getBillamountStartdate() {
        return billamountStartdate;
    }

    public void setBillamountStartdate(String billamountStartdate) {
        this.billamountStartdate = billamountStartdate;
    }

    public String getBillamountEnddate() {
        return billamountEnddate;
    }

    public void setBillamountEnddate(String billamountEnddate) {
        this.billamountEnddate = billamountEnddate;
    }

    public String getPrvId() {
        return prvId;
    }

    public void setPrvId(String prvId) {
        this.prvId = prvId;
    }

    public String getPrvName() {
        return prvName;
    }

    public void setPrvName(String prvName) {
        this.prvName = prvName;
    }

    public String getPregId() {
        return pregId;
    }

    public void setPregId(String pregId) {
        this.pregId = pregId;
    }

    public String getPregName() {
        return pregName;
    }

    public void setPregName(String pregName) {
        this.pregName = pregName;
    }

    public String getRegId() {
        return regId;
    }

    public void setRegId(String regId) {
        this.regId = regId;
    }

    public String getRegName() {
        return regName;
    }

    public void setRegName(String regName) {
        this.regName = regName;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public String getBillaccountId() {
        return billaccountId;
    }

    public void setBillaccountId(String billaccountId) {
        this.billaccountId = billaccountId;
    }

    public Date getPassTime() {
        return passTime;
    }

    public void setPassTime(Date passTime) {
        this.passTime = passTime;
    }
}
