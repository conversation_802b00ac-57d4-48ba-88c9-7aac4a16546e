package com.xunge.core.util;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @Description 数字工具类
 */
public class NumberUtils {

    /**
     * num大于等于10，四舍五入取整。num小于10，四舍五入保留2位小数
     * 三位分节法展示数字
     *
     * @param num
     * @return
     */
    public static String numConvert(BigDecimal num) {
        DecimalFormat df = new DecimalFormat("#,###");
        String number = "";
        if (num != null) {
            BigDecimal ten = new BigDecimal("10");
            /*
             * 大于等于10，取整，否则四舍五入保留两位小数
             * */
            if (num.compareTo(ten)< 0) {
                if (num.compareTo(BigDecimal.ZERO) != 0) {
                    num = num.setScale(2, BigDecimal.ROUND_HALF_UP);
                    number = num.toString();
                } else {
                    number = "0";
                }
            } else {
                num = num.setScale(0, BigDecimal.ROUND_HALF_UP);
                number = df.format(num);
            }
        }
        return number;
    }

    /**
     * num大于等于10，四舍五入取整。num小于10，四舍五入保留2位小数
     * 三位分节法展示数字
     *
     * @param num
     * @return
     */
    public static String numConvertInteger(Integer num) {
        DecimalFormat df = new DecimalFormat("#,###");
        String number = "";
        if (num != null) {
            number = df.format(num);
        }
        return number;
    }

    /**
     * a为0 返回/
     * b为0 返回0
     * a除以b乘100，四舍五入保留两位小数，百分比展示
     *
     * @param a
     * @param b
     * @return
     */
    public static String numFix(BigDecimal a, BigDecimal b) {
        String number = "/";
        BigDecimal num;
        if (a.compareTo(BigDecimal.ZERO) != 0) {
            if (b.compareTo(BigDecimal.ZERO) != 0) {
                num = b.divide(a, 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal("100")).setScale(2, BigDecimal.ROUND_HALF_UP);
                number = num.toString();
                if (number.equals("0.00")) {
                    number = "0";
                } else {
                    number = number + "%";
                }
            } else {
                number = "0";
            }
        }
        return number;
    }

    /**
     * Float类型的运算
     *
     * @param
     * @return
     * @date 2019年04月09日
     * <AUTHOR>
     */
    public static BigDecimal floatCalc(Float a, Float b, int calc) {
        BigDecimal result = new BigDecimal(0);
        if (a != null && b != null) {
            BigDecimal aa = new BigDecimal(Float.toString(a));
            BigDecimal bb = new BigDecimal(Float.toString(b));
            switch (calc) {
                case 0:// +
                    result = aa.add(bb);
                    break;
                case 1:// -:
                    result = aa.subtract(bb);
                    break;
                case 2:// *
                    result = aa.multiply(bb);
                    break;
                case 3:// /
                    result = aa.divide(bb);
                    break;
                default:
                    break;
            }
        }
        return result;
    }

    /**
     * 2进制解析异常种类
     * overproof 存值为 2的n次方 2 4 8 16 .。。 以及他们之间的相加组合
     * 拆分解析 eg：overproof=14 拆分为 14=8+4+2
     * 14转2进制 => 1110 对应  =2^3 + 2^2 + 2^1
     *
     * @param source 需要解析的数字 输入：14
     * @return 返回解析出来的结合 返回：集合[8,4,2]
     */
    public static List<Integer> parseNumber2(Integer source) {
        if (source == null) {
            return Collections.emptyList();
        }
        //获取2进制字符串 14 -> ‘1110’
        String s = Integer.toBinaryString(source);

        List<Integer> target = new ArrayList<>();

        char[] chars = s.toCharArray();
        for (int i = 0; i < chars.length; i++) {
            char element = chars[i];
            //只取位上是1的
            if ('1' == (element)) {
                BigInteger t = BigInteger.valueOf(2);
                //"1110"的 第1位‘1’ => 2的3次方 第2位‘1’ => 2的2次方 第3位‘1’ => 2的1次方
                BigInteger ans = t.pow(chars.length - 1 - i);
                target.add(ans.intValue());
            }
        }
        return target;
    }
}
