package com.xunge.dao.selfelec.accrual;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

import com.xunge.model.selfelec.VDatElectricmeter;
import com.xunge.model.selfelec.VEleBillaccount;
import com.xunge.model.selfelec.VEleBillaccountbaseresource;
import com.xunge.model.selfelec.VEleBillaccountcontract;
import com.xunge.model.selfelec.accrual.EleAccrual;
import com.xunge.model.selfelec.accrual.EleAccrualBalanceJoin;
import com.xunge.model.selfelec.accrual.EleAccrualBalanceOffset;
import com.xunge.model.selfelec.accrual.EleAccrualDetail;
import com.xunge.model.selfelec.accrual.EleAccrualRegAmount;
import com.xunge.model.selfelec.accrual.HisAmountAndDegree;
import com.xunge.model.selfelec.paymentDate.PaymentDateVO;

public interface AccrualMangeMapper {

	List<VEleBillaccount> queryBillaccountByConditions(Map<String, Object> map);

	int getTeleBillaccountCount();

	List<PaymentDateVO> queryPaymentAndAccrualHis(Map<String, Object> map);

	VEleBillaccountcontract getAccrualContract(String accrualId);

	List<Map<String, Object>> getElePaymentById(String paymentId);

	List<HisAmountAndDegree> getHisAmountAndDegree(Map<String, Object> map);

	Map<String, Object> billaccountPower(Map<String, Object> map);

	int insertAccrualRegAmount(EleAccrualRegAmount regAmount);

	int updateAccrualRegAmount(EleAccrualRegAmount regAmount);

	void deleteRegAmount(String accrualId);

	void deleteRegAmountByDetailList(List<EleAccrualDetail> del);

	EleAccrual getClassInfo(@Param("className")String className, @Param("classSmName")String classSmName, @Param("activityName")String activityName);

	EleAccrual getCostCenter(@Param("costCenter") String costCenter, @Param("costCenterName") String costCenterName);

	String getContractRelId(@Param("contractId") String contractId);

	EleAccrual getHisContract(Map<String, Object> map);

	HisAmountAndDegree getOtherSum(@Param("id")String id, @Param("fm")String fm);

	HisAmountAndDegree getDetailSum(@Param("id")String id, @Param("fm")String fm);

	List<VEleBillaccount> queryBillaccount(Map<String, Object> map);

	void deleteBalanceOffset(@Param("accrualId") String accrualId);

	void insertBalanceOffset(@Param("balanceList") List<EleAccrualBalanceOffset> balanceList);

	EleAccrualBalanceOffset getAccrualBalanceOffset(@Param("accrualId") String accrualId);

	List<EleAccrualBalanceJoin> showAccrualBalanceJoin(Map<String, Object> map);

	List<EleAccrualBalanceOffset> showBillaccountOffset(Map<String, Object> map);

	List<EleAccrualBalanceJoin> getAccrualBalanceJoinList(Map<String, Object> map);

	List<EleAccrualBalanceOffset> getAccrualBalanceOffsetList(Map<String, Object> map);

	void deleteBalanceJoin(Map<String, Object> map);

	void updateBalanceOffset(EleAccrualBalanceOffset o);

	List<PaymentDateVO> queryPaymentHisFlush(String billaccountId);

	List<EleAccrual> getRegionPrice(Map<String, Object> pMap);

	List<VEleBillaccount> queryBillaccountByList(List<String> billaccountIds);

	void insertAccrualRegAmountList(@Param("list") List<EleAccrualRegAmount> list);

	/**
	* @Description: 查询增量报账点计提
	* <AUTHOR>   
	* @date 2022年7月5日 上午9:41:50 
	* @param map
	 */
	List<VEleBillaccount> queryIncrementalbillaccount(Map<String, Object> map);

	void deleteBalanceOffset(Map<String, Object> map);

	List<String> queryFinaceBillaccount(@Param("billamountId")String billamountId, @Param("type")String type);

	/** 
	* @Description: 查询报账点无需计提区间
	* <AUTHOR>   
	* @date 2022年10月20日 下午2:40:59 
	* @return  
	*/ 
	List<PaymentDateVO> queryBillaccountNoAccrual(Map<String, Object> map);

	List<VEleBillaccountcontract> getContractInfoByBillaccounts(@Param("prvId")String prvId, @Param("billaccountIds")List<String> lists);

	List<VEleBillaccountbaseresource> getResInfoByBillaccounts(@Param("billaccountIds")List<String> lists);

	List<VDatElectricmeter> getMeterInfoByBillaccounts(@Param("billaccountIds")List<String> lists);

	EleAccrual getContractByAccrual(String contractId);
}
