package com.xunge.model.activity;

import java.io.Serializable;
import java.util.Date;

/**
 * 审核上级实体类
 *
 * <AUTHOR>
 */
public class ActHiTaskinst implements Serializable {

    /**
     *
     */
    private static final long serialVersionUID = -6695382583254759577L;

    private String id; //主键id
    private String procDefId;
    private String taskDefKey;
    private String procInstId;
    private String executionId;
    private String name;
    private String parentTaskId;
    private String description;
    private String owner;
    private String assignee;
    private Date startTime;
    private Date claimTime;
    private Date endTime;
    private Long duration;
    private String deleteReason;
    private Integer priority;
    private Date dueDate;
    private String formKey;
    private String category;
    private String tenantId;
    private String userName;
    private String comment;
    private String majorId;

    public ActHiTaskinst() {
        super();
        // TODO Auto-generated constructor stub
    }

    public ActHiTaskinst(String id, String procDefId, String taskDefKey, String procInstId, String executionId, String name, String parentTaskId,
                         String description, String owner, String assignee, Date startTime, Date claimTime, Date endTime, Long duration, String deleteReason,
                         Integer priority, Date dueDate, String formKey, String category, String tenantId) {
        super();
        this.id = id;
        this.procDefId = procDefId;
        this.taskDefKey = taskDefKey;
        this.procInstId = procInstId;
        this.executionId = executionId;
        this.name = name;
        this.parentTaskId = parentTaskId;
        this.description = description;
        this.owner = owner;
        this.assignee = assignee;
        this.startTime = startTime;
        this.claimTime = claimTime;
        this.endTime = endTime;
        this.duration = duration;
        this.deleteReason = deleteReason;
        this.priority = priority;
        this.dueDate = dueDate;
        this.formKey = formKey;
        this.category = category;
        this.tenantId = tenantId;
    }

    public static long getSerialversionuid() {
        return serialVersionUID;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getProcDefId() {
        return procDefId;
    }

    public void setProcDefId(String procDefId) {
        this.procDefId = procDefId;
    }

    public String getTaskDefKey() {
        return taskDefKey;
    }

    public void setTaskDefKey(String taskDefKey) {
        this.taskDefKey = taskDefKey;
    }

    public String getProcInstId() {
        return procInstId;
    }

    public void setProcInstId(String procInstId) {
        this.procInstId = procInstId;
    }

    public String getExecutionId() {
        return executionId;
    }

    public void setExecutionId(String executionId) {
        this.executionId = executionId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getParentTaskId() {
        return parentTaskId;
    }

    public void setParentTaskId(String parentTaskId) {
        this.parentTaskId = parentTaskId;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getOwner() {
        return owner;
    }

    public void setOwner(String owner) {
        this.owner = owner;
    }

    public String getAssignee() {
        return assignee;
    }

    public void setAssignee(String assignee) {
        this.assignee = assignee;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getClaimTime() {
        return claimTime;
    }

    public void setClaimTime(Date claimTime) {
        this.claimTime = claimTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public Long getDuration() {
        return duration;
    }

    public void setDuration(Long duration) {
        this.duration = duration;
    }

    public String getDeleteReason() {
        return deleteReason;
    }

    public void setDeleteReason(String deleteReason) {
        this.deleteReason = deleteReason;
    }

    public Integer getPriority() {
        return priority;
    }

    public void setPriority(Integer priority) {
        this.priority = priority;
    }

    public Date getDueDate() {
        return dueDate;
    }

    public void setDueDate(Date dueDate) {
        this.dueDate = dueDate;
    }

    public String getFormKey() {
        return formKey;
    }

    public void setFormKey(String formKey) {
        this.formKey = formKey;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    @Override
    public String toString() {
        return "ActHiTaskinst [id=" + id + ", procDefId=" + procDefId + ", taskDefKey=" + taskDefKey + ", procInstId=" + procInstId + ", executionId="
                + executionId + ", name=" + name + ", parentTaskId=" + parentTaskId + ", description=" + description + ", owner=" + owner
                + ", assignee=" + assignee + ", startTime=" + startTime + ", claimTime=" + claimTime + ", endTime=" + endTime + ", duration="
                + duration + ", deleteReason=" + deleteReason + ", priority=" + priority + ", dueDate=" + dueDate + ", formKey=" + formKey
                + ", category=" + category + ", tenantId=" + tenantId + "]";
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }

    public String getMajorId() {
        return majorId;
    }

    public void setMajorId(String majorId) {
        this.majorId = majorId;
    }
}
