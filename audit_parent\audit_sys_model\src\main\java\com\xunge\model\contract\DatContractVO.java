package com.xunge.model.contract;

import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.Date;

@Slf4j
public class DatContractVO implements Cloneable {
    private String contractId;

    private String contractsysId;

    private String prvId;

    private String prvSname;

    private String pregId;

    private String pregName;

    private String regId;

    private String regName;

    private Integer isDownshare;

    private String sysDepId;

    private String userId;

    private String managerDepartment;

    private String managerUser;

    private String contractCode;

    private String contractName;

    private Integer contractType;

    private Date contractStartdate;

    private Date contractEnddate;

    private Date contractSigndate;

    private Date contractChangeenddate;

    private Date contractChangedate;

    private BigDecimal contractYearquantity;

    private String contractCheckname1;

    private String contractCheckname2;

    private String oldContractId;

    private String oldContractCode;

    private String contractFlow;

    private String contractIntroduction;

    private String contractSpaceresource;

    private Integer contractState;

    private String contractNote;

    private Integer auditingState;

    private String auditingUserId;

    private Date auditingDate;

    private Integer dataFrom;

    private String tag;

    private String smapUserId;//承办人id
    private Integer mainBody;//我方主体信息
    private String isSigned;//是否以总部名义签署
    private Integer draftType;//补充协议类型
    private String oldContractName;//原合同名称
    private String contractUrl;//合同url
    private String bankUser;//供应商开户行
    private String depositBank;//供应商开户行名称
    private String bankAccount;//供应商开户行账户
    private Date lastUpdateTime;//最后更新时间
    private String contractStatus;//合同状态
    private Integer dataType;//合同类型
    private Integer updType;//根据标识更新，0：contractCode, 1:contractsysId
    private String incExpType;//收支类型
    private String retroactive;//是否补充协议
    private String create_user;
    private String create_ip;
    private Date create_time;
    private String update_user;
    private String update_ip;
    private Date update_time;

    public String getTag() {
        return tag;
    }

    public void setTag(String tag) {
        this.tag = tag;
    }

    public String getContractId() {
        return contractId;
    }

    public void setContractId(String contractId) {
        this.contractId = contractId == null ? null : contractId.trim();
    }

    public String getContractsysId() {
        return contractsysId;
    }

    public void setContractsysId(String contractsysId) {
        this.contractsysId = contractsysId == null ? null : contractsysId.trim();
    }

    public String getPrvId() {
        return prvId;
    }

    public void setPrvId(String prvId) {
        this.prvId = prvId == null ? null : prvId.trim();
    }

    public String getPrvSname() {
        return prvSname;
    }

    public void setPrvSname(String prvSname) {
        this.prvSname = prvSname == null ? null : prvSname.trim();
    }

    public String getPregId() {
        return pregId;
    }

    public void setPregId(String pregId) {
        this.pregId = pregId == null ? null : pregId.trim();
    }

    public String getPregName() {
        return pregName;
    }

    public void setPregName(String pregName) {
        this.pregName = pregName == null ? null : pregName.trim();
    }

    public String getRegId() {
        return regId;
    }

    public void setRegId(String regId) {
        this.regId = regId == null ? null : regId.trim();
    }

    public String getRegName() {
        return regName;
    }

    public void setRegName(String regName) {
        this.regName = regName == null ? null : regName.trim();
    }

    public Integer getIsDownshare() {
        return isDownshare;
    }

    public void setIsDownshare(Integer isDownshare) {
        this.isDownshare = isDownshare;
    }

    public String getSysDepId() {
        return sysDepId;
    }

    public void setSysDepId(String sysDepId) {
        this.sysDepId = sysDepId == null ? null : sysDepId.trim();
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId == null ? null : userId.trim();
    }

    public String getManagerDepartment() {
        return managerDepartment;
    }

    public void setManagerDepartment(String managerDepartment) {
        this.managerDepartment = managerDepartment == null ? null : managerDepartment.trim();
    }

    public String getManagerUser() {
        return managerUser;
    }

    public void setManagerUser(String managerUser) {
        this.managerUser = managerUser == null ? null : managerUser.trim();
    }

    public String getContractCode() {
        return contractCode;
    }

    public void setContractCode(String contractCode) {
        this.contractCode = contractCode == null ? null : contractCode.trim();
    }

    public String getContractName() {
        return contractName;
    }

    public void setContractName(String contractName) {
        this.contractName = contractName == null ? null : contractName.trim();
    }

    public Integer getContractType() {
        return contractType;
    }

    public void setContractType(Integer contractType) {
        this.contractType = contractType;
    }

    public Date getContractStartdate() {
        return contractStartdate;
    }

    public void setContractStartdate(Date contractStartdate) {
        this.contractStartdate = contractStartdate;
    }

    public Date getContractEnddate() {
        return contractEnddate;
    }

    public void setContractEnddate(Date contractEnddate) {
        this.contractEnddate = contractEnddate;
    }

    public Date getContractSigndate() {
        return contractSigndate;
    }

    public void setContractSigndate(Date contractSigndate) {
        this.contractSigndate = contractSigndate;
    }

    public Date getContractChangeenddate() {
        return contractChangeenddate;
    }

    public void setContractChangeenddate(Date contractChangeenddate) {
        this.contractChangeenddate = contractChangeenddate;
    }

    public Date getContractChangedate() {
        return contractChangedate;
    }

    public void setContractChangedate(Date contractChangedate) {
        this.contractChangedate = contractChangedate;
    }

    public BigDecimal getContractYearquantity() {
        return contractYearquantity;
    }

    public void setContractYearquantity(BigDecimal contractYearquantity) {
        this.contractYearquantity = contractYearquantity;
    }

    public String getContractCheckname1() {
        return contractCheckname1;
    }

    public void setContractCheckname1(String contractCheckname1) {
        this.contractCheckname1 = contractCheckname1 == null ? null : contractCheckname1.trim();
    }

    public String getContractCheckname2() {
        return contractCheckname2;
    }

    public void setContractCheckname2(String contractCheckname2) {
        this.contractCheckname2 = contractCheckname2 == null ? null : contractCheckname2.trim();
    }

    public String getOldContractId() {
        return oldContractId;
    }

    public void setOldContractId(String oldContractId) {
        this.oldContractId = oldContractId == null ? null : oldContractId.trim();
    }

    public String getOldContractCode() {
        return oldContractCode;
    }

    public void setOldContractCode(String oldContractCode) {
        this.oldContractCode = oldContractCode == null ? null : oldContractCode.trim();
    }

    public String getContractFlow() {
        return contractFlow;
    }

    public void setContractFlow(String contractFlow) {
        this.contractFlow = contractFlow == null ? null : contractFlow.trim();
    }

    public String getContractIntroduction() {
        return contractIntroduction;
    }

    public void setContractIntroduction(String contractIntroduction) {
        this.contractIntroduction = contractIntroduction == null ? null : contractIntroduction.trim();
    }

    public String getContractSpaceresource() {
        return contractSpaceresource;
    }

    public void setContractSpaceresource(String contractSpaceresource) {
        this.contractSpaceresource = contractSpaceresource == null ? null : contractSpaceresource.trim();
    }

    public Integer getContractState() {
        return contractState;
    }

    public void setContractState(Integer contractState) {
        this.contractState = contractState;
    }

    public String getContractNote() {
        return contractNote;
    }

    public void setContractNote(String contractNote) {
        this.contractNote = contractNote == null ? null : contractNote.trim();
    }

    public Integer getAuditingState() {
        return auditingState;
    }

    public void setAuditingState(Integer auditingState) {
        this.auditingState = auditingState;
    }

    public String getAuditingUserId() {
        return auditingUserId;
    }

    public void setAuditingUserId(String auditingUserId) {
        this.auditingUserId = auditingUserId == null ? null : auditingUserId.trim();
    }

    public Date getAuditingDate() {
        return auditingDate;
    }

    public void setAuditingDate(Date auditingDate) {
        this.auditingDate = auditingDate;
    }

    public Integer getDataFrom() {
        return dataFrom;
    }

    public void setDataFrom(Integer dataFrom) {
        this.dataFrom = dataFrom;
    }

    public String getCreate_user() {
        return create_user;
    }

    public void setCreate_user(String create_user) {
        this.create_user = create_user;
    }

    public String getCreate_ip() {
        return create_ip;
    }

    public void setCreate_ip(String create_ip) {
        this.create_ip = create_ip;
    }

    public Date getCreate_time() {
        return create_time;
    }

    public void setCreate_time(Date create_time) {
        this.create_time = create_time;
    }

    public String getUpdate_user() {
        return update_user;
    }

    public void setUpdate_user(String update_user) {
        this.update_user = update_user;
    }

    public String getUpdate_ip() {
        return update_ip;
    }

    public void setUpdate_ip(String update_ip) {
        this.update_ip = update_ip;
    }

    public Date getUpdate_time() {
        return update_time;
    }

    public void setUpdate_time(Date update_time) {
        this.update_time = update_time;
    }

    public String getSmapUserId() {
        return smapUserId;
    }

    public void setSmapUserId(String smapUserId) {
        this.smapUserId = smapUserId;
    }

    public Integer getMainBody() {
        return mainBody;
    }

    public void setMainBody(Integer mainBody) {
        this.mainBody = mainBody;
    }

    public String getIsSigned() {
        return isSigned;
    }

    public void setIsSigned(String isSigned) {
        this.isSigned = isSigned;
    }

    public Integer getDraftType() {
        return draftType;
    }

    public void setDraftType(Integer draftType) {
        this.draftType = draftType;
    }

    public String getBankUser() {
        return bankUser;
    }

    public void setBankUser(String bankUser) {
        this.bankUser = bankUser;
    }

    public String getDepositBank() {
        return depositBank;
    }

    public void setDepositBank(String depositBank) {
        this.depositBank = depositBank;
    }

    public String getBankAccount() {
        return bankAccount;
    }

    public void setBankAccount(String bankAccount) {
        this.bankAccount = bankAccount;
    }

    public String getOldContractName() {
        return oldContractName;
    }

    public void setOldContractName(String oldContractName) {
        this.oldContractName = oldContractName;
    }

    public String getContractUrl() {
        return contractUrl;
    }

    public void setContractUrl(String contractUrl) {
        this.contractUrl = contractUrl;
    }

    public Date getLastUpdateTime() {
        return lastUpdateTime;
    }

    public void setLastUpdateTime(Date lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    public String getContractStatus() {
        return contractStatus;
    }

    public void setContractStatus(String contractStatus) {
        this.contractStatus = contractStatus;
    }

    public Integer getDataType() {
        return dataType;
    }

    public void setDataType(Integer dataType) {
        this.dataType = dataType;
    }

    public Integer getUpdType() {
        return updType;
    }

    public void setUpdType(Integer updType) {
        this.updType = updType;
    }

    public String getIncExpType() {
        return incExpType;
    }

    public void setIncExpType(String incExpType) {
        this.incExpType = incExpType;
    }

    public String getRetroactive() {
        return retroactive;
    }

    public void setRetroactive(String retroactive) {
        this.retroactive = retroactive;
    }

    @Override
    public Object clone() {
        DatContractVO addr = null;
        try {
            addr = (DatContractVO) super.clone();
        } catch (CloneNotSupportedException e) {
            log.error("DatContractVO 出错", e);
        }
        return addr;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;
        DatContractVO other = (DatContractVO) obj;
        if (contractCheckname2 == null) {
            if (other.contractCheckname2 != null) return false;
        } else if (!contractCheckname2.equals(other.contractCheckname2)) return false;
        if (contractCode == null) {
            if (other.contractCode != null) return false;
        } else if (!contractCode.equals(other.contractCode)) return false;
        if (contractEnddate == null) {
            if (other.contractEnddate != null) return false;
        } else if (!contractEnddate.equals(other.contractEnddate)) return false;
        if (contractFlow == null) {
            if (other.contractFlow != null) return false;
        } else if (!contractFlow.equals(other.contractFlow)) return false;
        if (contractIntroduction == null) {
            if (other.contractIntroduction != null) return false;
        } else if (!contractIntroduction.equals(other.contractIntroduction)) return false;
        if (contractName == null) {
            if (other.contractName != null) return false;
        } else if (!contractName.equals(other.contractName)) return false;
        if (contractSigndate == null) {
            if (other.contractSigndate != null) return false;
        } else if (!contractSigndate.equals(other.contractSigndate)) return false;
        if (contractSpaceresource == null) {
            if (other.contractSpaceresource != null) return false;
        } else if (!contractSpaceresource.equals(other.contractSpaceresource)) return false;
        if (contractStartdate == null) {
            if (other.contractStartdate != null) return false;
        } else if (!contractStartdate.equals(other.contractStartdate)) return false;
        if (contractState == null) {
            if (other.contractState != null) return false;
        } else if (!contractState.equals(other.contractState)) return false;
        if (contractType == null) {
            if (other.contractType != null) return false;
        } else if (!contractType.equals(other.contractType)) return false;
        if (contractsysId == null) {
            if (other.contractsysId != null) return false;
        } else if (!contractsysId.equals(other.contractsysId)) return false;
        if (dataFrom == null) {
            if (other.dataFrom != null) return false;
        } else if (!dataFrom.equals(other.dataFrom)) return false;
        if (isDownshare == null) {
            if (other.isDownshare != null) return false;
        } else if (!isDownshare.equals(other.isDownshare)) return false;
        if (managerDepartment == null) {
            if (other.managerDepartment != null) return false;
        } else if (!managerDepartment.equals(other.managerDepartment)) return false;
        if (oldContractId == null) {
            if (other.oldContractId != null) return false;
        } else if (!oldContractId.equals(other.oldContractId)) return false;
        if (pregId == null) {
            if (other.pregId != null) return false;
        } else if (!pregId.equals(other.pregId)) return false;
        if (regId == null) {
            if (other.regId != null) return false;
        } else if (!regId.equals(other.regId)) return false;
        return true;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((contractCheckname2 == null) ? 0 : contractCheckname2.hashCode());
        result = prime * result + ((contractCode == null) ? 0 : contractCode.hashCode());
        result = prime * result + ((contractEnddate == null) ? 0 : contractEnddate.hashCode());
        result = prime * result + ((contractFlow == null) ? 0 : contractFlow.hashCode());
        result = prime * result + ((contractIntroduction == null) ? 0 : contractIntroduction.hashCode());
        result = prime * result + ((contractName == null) ? 0 : contractName.hashCode());
        result = prime * result + ((contractSigndate == null) ? 0 : contractSigndate.hashCode());
        result = prime * result + ((contractSpaceresource == null) ? 0 : contractSpaceresource.hashCode());
        result = prime * result + ((contractStartdate == null) ? 0 : contractStartdate.hashCode());
        result = prime * result + ((contractState == null) ? 0 : contractState.hashCode());
        result = prime * result + ((contractType == null) ? 0 : contractType.hashCode());
        result = prime * result + ((contractsysId == null) ? 0 : contractsysId.hashCode());
        result = prime * result + ((dataFrom == null) ? 0 : dataFrom.hashCode());
        result = prime * result + ((isDownshare == null) ? 0 : isDownshare.hashCode());
        result = prime * result + ((managerDepartment == null) ? 0 : managerDepartment.hashCode());
        result = prime * result + ((oldContractId == null) ? 0 : oldContractId.hashCode());
        result = prime * result + ((pregId == null) ? 0 : pregId.hashCode());
        result = prime * result + ((regId == null) ? 0 : regId.hashCode());
        return result;
    }

}