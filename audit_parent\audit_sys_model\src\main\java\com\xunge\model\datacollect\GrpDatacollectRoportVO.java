package com.xunge.model.datacollect;

import cn.afterturn.easypoi.excel.annotation.Excel;

import java.io.Serializable;

public class GrpDatacollectRoportVO implements Serializable {

    /**
     *
     */
    private static final long serialVersionUID = 3438563381866997949L;
    //省份
    @Excel(name = "省份", orderNum = "1")
    private String prvName;
    //退单数
    @Excel(name = "退单数", orderNum = "3")
    private Integer backNum;
    //回单超时单数
    @Excel(name = "回单超时单数", orderNum = "5")
    private Integer outTimeNum;

    public String getPrvName() {
        return prvName;
    }

    public void setPrvName(String prvName) {
        this.prvName = prvName;
    }

    public Integer getBackNum() {
        return backNum;
    }

    public void setBackNum(Integer backNum) {
        this.backNum = backNum;
    }

    public Integer getOutTimeNum() {
        return outTimeNum;
    }

    public void setOutTimeNum(Integer outTimeNum) {
        this.outTimeNum = outTimeNum;
    }
}
