package com.xunge.model.budget.ele;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR> LiangCheng
 * @date : 2022-07-04
 * @desc 规模校正表
 */

@Data
public class BudgetEleScaleRegulateVO implements Serializable {
    /**
     * 省份
     */
    private String prvId;

    /**
     * 省份名称
     */
    private String prvName;

    /**
     * 年份
     */
    private Integer onYear;

    /**
     * 站点类型
     */
    private Double siteType;

    /**
     * 存量系统或集团建议值
     */
    private Integer stockGroup;

    /**
     * 存量实际值
     */
    private Integer stockActual;

    /**
     * 存量备注
     */
    private String stockRemark;

    /**
     * 存量规模总额
     */
    private BigDecimal stockScaleTotal;

    /**
     * 补齐计划部规划新增规模
     */
    private BigDecimal supplementaryGroup;

    /**
     * 补齐实际新增规模
     */
    private BigDecimal supplementaryActual;

    /**
     * 规模单位
     */
    private String scaleUnit;

    /**
     * 补齐规模备注
     */
    private String supplementaryRemark;

    /**
     * 补齐均在网周期
     */
    private BigDecimal supplementaryNetworkPeroid;

    /**
     * 补齐总电量
     */
    private BigDecimal supplementaryTotoalDegree;

    /**
     * 补齐总电费
     */
    private BigDecimal supplementaryTotalAmount;

    /**
     * 新增计划部规划新增规模
     */
    private BigDecimal addGroup;

    /**
     * 新增实际新增规模
     */
    private BigDecimal addActual;

    /**
     * 新增规模备注
     */
    private String addRemark;

    /**
     * 新增平均在网周期
     */
    private BigDecimal addNetworkPeroid;

    /**
     * 新增总电量
     */
    private BigDecimal addTotoalDegree;

    /**
     * 新增总电费
     */
    private BigDecimal addTotalAmount;

    /**
     * 退网实际退网规模
     */
    private BigDecimal backnetScale;

    /**
     * 退网平均在网周
     */
    private BigDecimal backnetNetworkPeroid;

    /**
     * 退网减少总电量
     */
    private BigDecimal backnetLowerTotalDegree;

    /**
     * 退网减少总电费
     */
    private BigDecimal backnetLowerTotalAmount;

    /**
     * 电费总额
     */
    private BigDecimal backnetTotalAmount;

    /**
     * 说明
     */
    private String backnetRemark;

    /**
     * 节点
     */
    private Integer nodeType;

    private String workOrderId;
}

