package com.xunge.dao.twrrent.monthlyReport;


import com.xunge.model.towerrent.monthlyReport.*;
import com.xunge.model.towerrent.settlement.OtherFeeVO;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

public interface IMonthlyReportCreateDao {
    /**
     * 获取分类查询移动侧某月的起租订单，根据区县、是否是2018年开始起租、和产品ID分组，以及移动侧报账前不包含扣罚的金额和已报账的订单数
     *
     * @param param
     * @return
     */
    List<RptMonthlyTwrSite> getPrvMonthTwrSiteBaseReport(Map<String, Object> param);

    /**
     * 获取上月的已支付完成的站址，未去重
     */
    List<PayCompleteTwrSite> getPrvPayCompleteTowerSite();

    /**
     * 获取上月移动侧的铁塔共享数集合，未去重
     *
     * @return
     */
    List<MobileTwrSiteShareNum> getMobileTwrSiteShareNum();

    int deletePrvTwrSiteData(String prvId);

    int batchInsertTwrSiteData(List<RptMonthlyTwrSite> reportBaseList);

    /**
     * 获取省份塔类服务费数据
     *
     * @return
     */
    List<RptMonthlyTwrServiceCost> getTowerTypeServiceChargeMonthPrv();

    /**
     * 获取地市塔类服务费数据
     *
     * @return
     */
    List<RptMonthlyTwrServiceCost> getTowerTypeServiceChargeMonthPreg();

    /**
     * 获取区县塔类服务费数据
     *
     * @return
     */
    List<RptMonthlyTwrServiceCost> getTowerTypeServiceChargeMonthReg();

    /**
     * 获取省份计算的扣罚总金额
     *
     * @return
     */
    BigDecimal getPrvBackAmount();

    /**
     * 获取地市计算的扣罚总金额
     *
     * @return
     */
    List<TowerMonthlyPunish> getPregBackAmount();

    /**
     * 获取区县计算的扣罚总金额
     *
     * @return
     */
    List<TowerMonthlyPunish> getRegBackAmount();

    /**
     * 删除前一天生成的数据
     */
    int deleteByMonAndPrvId(Map<String, String> param);

    /**
     * 插入塔类数据
     */
    int addTowerTypeServiceChargeMonth(List<RptMonthlyTwrServiceCost> list);

    //获取省份预算及进度数据-大集中省份
    List<RptMonthlyBudgetAndProgress> getBudgetAndProgressMonthPrv(Map<String, Object> param);

    //地市-大集中省份
    List<RptMonthlyBudgetAndProgress> getBudgetAndProgressMonthPreg(Map<String, Object> param);

    //区县-大集中省份
    List<RptMonthlyBudgetAndProgress> getBudgetAndProgressMonthReg(Map<String, Object> param);

    //获取省份预算及进度数据-非大集中省份
    List<RptMonthlyBudgetAndProgress> getBudgetAndProgressMonthPrv1(Map<String, Object> param);

    //地市-非大集中省份
    List<RptMonthlyBudgetAndProgress> getBudgetAndProgressMonthPreg1(Map<String, Object> param);

    //区县-非大集中省份
    List<RptMonthlyBudgetAndProgress> getBudgetAndProgressMonthReg1(Map<String, Object> param);

    //查询其他费用表- 非直辖市省份
    List<OtherFeeVO> getRegSumOther();

    //查询其他费用表- 直辖市省份
    List<OtherFeeVO> getRegSumOtherforMunicipality();

    //删除前一天生成的数据
    int deleteBudgetAndProgress(Map<String, Object> param);

    //插入数据
    int addBudgetAndProgressesMonth(List<RptMonthlyBudgetAndProgress> list);

    //铁塔站址月报省份数据
    List<RptMonthlyIconTowerCost> getIronTowerMonthPrv();

    //铁塔站址月报入库
    int addIronTowerTMonth(List<RptMonthlyIconTowerCost> list);

    int deleteIronByMonAndPrvId(Map<String, String> param);


    //查询地市站址
    List<RptMonthlyIconTowerCost> getSitePregList();

    //查询地市服务费
    List<RptMonthlyIconTowerCost> getChargePregList();

    //查询区县站址
    List<RptMonthlyIconTowerCost> getSiteRegList();

    //查询区县服务费
    List<RptMonthlyIconTowerCost> getChargeRegList();


    /**
     * 获取省份铁塔服务费数据
     *
     * @return
     */
    List<RptMonthlyTwrServiceCost> getIronTowerTypeServiceChargeMonthPrv(Map<String, String> map);

    /**
     * 获取地市铁塔服务费数据
     *
     * @return
     */
    List<RptMonthlyTwrServiceCost> getIronTowerTypeServiceChargeMonthPreg(Map<String, String> map);

    /**
     * 获取区县铁塔服务费数据
     *
     * @return
     */
    List<RptMonthlyTwrServiceCost> getIronTowerTypeServiceChargeMonthReg(Map<String, String> map);

    /**
     * 获取区县计算的扣罚总金额
     *
     * @return
     */
    List<TowerMonthlyPunish> getIronRegBackAmount();

    int deleteIronTowerByMonAndPrvId(Map<String, String> param);

    /**
     * 插入铁塔服务费数据
     */
    int addIronTowerTypeServiceChargeMonth(List<RptMonthlyTwrServiceCost> list);
}
