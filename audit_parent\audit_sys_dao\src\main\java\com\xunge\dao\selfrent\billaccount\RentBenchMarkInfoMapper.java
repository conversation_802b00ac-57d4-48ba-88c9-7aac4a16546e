package com.xunge.dao.selfrent.billaccount;

import com.xunge.model.selfrent.billAccount.DatBaseresourcesVO;
import com.xunge.model.selfrent.billAccount.RentBenchmarkingInformation;
import com.xunge.model.selfrent.contract.RentContractVO;
import com.xunge.model.selfrent.vo.RentBenchmarkParaInfoVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface RentBenchMarkInfoMapper {

    RentBenchmarkingInformation selectBenchmarkInfoByBillaccountId(@Param("billaccountId") String billAccountId);

    RentContractVO selectByPrimaryKey(@Param("rentcontractId")String rentcontractId);

    List<DatBaseresourcesVO> queryBaseresourceByBaseresourceIds(@Param("baseresourceIdsList") List<String> baseresourceIdsList);

    int deleteBenchmarInfokByPrimaryKey(@Param("billaccountId") String billAccountId);

    int deleteBenchmarkByPrimaryKey(@Param("billaccountId") String billAccountId);

    int insertBenchmarkInfo(List<RentBenchmarkParaInfoVO>  list);

    int insertBenchmarking(RentBenchmarkingInformation  rentBenchmarkingInformation);


}
