package com.xunge.core.util;

import lombok.extern.slf4j.Slf4j;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * <AUTHOR>
 * @Description 参数计算类
 */
@Slf4j
public class DownZipUtil {
    private static PropertiesLoader loader = new PropertiesLoader("properties/sysConfig.properties");
    public static final String basePath = loader.getProperty("UploadUrls");

    /**
     * 压缩并导出文件
     *
     * @param zipPath         压缩文件临时路径 路径最后不要有 /
     * @param zipName         压缩为文件名 **.zip
     * @param createFilesPath 需要压缩的文件列表
     * @param request
     * @param response
     * @return
     * @throws IOException
     */
    public static boolean downloadZip(String zipPath, String zipName, List<String> createFilesPath, HttpServletRequest request,
                                      HttpServletResponse response) {
        byte[] buffer = new byte[1024];

        String strZipPath = zipPath + File.separator + zipName;
        ZipOutputStream out = null;
        try {
            File tmpZip = new File(zipPath);
            if (!tmpZip.exists()) {
                tmpZip.mkdirs();
            }
            File tmpZipFile = new File(strZipPath);
            if (!tmpZipFile.exists()) {
                boolean createResult=tmpZipFile.createNewFile();
                if (!createResult){
                    log.error("创建文件失败！");
                }
            }
            out = new ZipOutputStream(new FileOutputStream(strZipPath));
            if (createFilesPath.size() > 0) {
                for (int i = 0; i < createFilesPath.size(); i++) {
                    String filePath = createFilesPath.get(i);
                    File file = new File(filePath);
                    String finmenu = filePath.substring(filePath.lastIndexOf(File.separator) + 1);
                    if (file.exists()) {//判断文件是不是存在
                        FileInputStream fis = null;
                        try {

                            fis = new FileInputStream(file);// NOSONAR
                            //创建目录层级
                            ZipEntry entry = new ZipEntry(finmenu);//在ZIP文件中的目录结构，最前面不要有斜杠。
                            out.putNextEntry(entry);
                            // 设置压缩文件内的字符编码，不然会变成乱码
                            //out.setEncoding("UTF-8");
                            int len;
                            // 读入需要下载的文件的内容，打包到zip文件
                            while ((len = fis.read(buffer)) > 0) {
                                out.write(buffer, 0, len);
                            }
                        } catch (Exception e) {
                            log.error("DownZipUtil 出错", e);
                        } finally {
                            if (out != null) {
                                out.closeEntry();
                            }
                            if (fis != null) {
                                fis.close();
                            }
                        }

                    }
                }
            }
            if (out != null) {
                out.close();
            }
            downloadFile(strZipPath, zipPath, zipName, response);
        } catch (Exception e) {
            log.error("DownZipUtil 出错", e);
        } finally {
            try {
                if (out != null) {
                    out.close();
                }
            } catch (IOException e) {
                // TODO Auto-generated catch block
                log.error("DownZipUtil 出错", e);
            }
        }
        return true;
    }

    /**
     * 以压缩文件导出
     *
     * @param fileName
     * @param filePath
     * @param response
     */
    public static void downloadFile(String menu, String filePath, String fileName, HttpServletResponse response) {
        response.setCharacterEncoding("utf-8");
        // response.setContentType("application/octet-stream");
        BufferedInputStream fis = null;
        OutputStream toClient = null;
        try {
            File file = new File(filePath, fileName);
            // 以流的形式下载文件。
            fis = new BufferedInputStream(new FileInputStream(file.getPath()));
            byte[] buffer = new byte[fis.available()];
            while (true) {
                int read = fis.read(buffer);
                //判断是不是读到了数据流的末尾 ，防止出现死循环。
                if (read == -1) {
                    break;
                }
            }
            // 清空response
            response.reset();
            toClient = new BufferedOutputStream(response.getOutputStream());
            response.setContentType("application/octet-stream");
            response.setHeader("Content-Disposition", "attachment;filename=" + new String(fileName.getBytes("utf-8"), "ISO8859-1"));
            toClient.write(buffer);
            toClient.flush();
            File tmpZipFile = new File(menu);
            tmpZipFile.delete();
        } catch (IOException e) {
            log.error("DownZipUtil 出错", e);

        } finally {
            try {
                if (fis != null) {
                    fis.close();
                }
            } catch (IOException e) {
                // TODO Auto-generated catch block
                log.error("DownZipUtil 出错", e);
            }
            try {
                if (toClient != null) {
                    toClient.close();
                }
            } catch (IOException e) {
                // TODO Auto-generated catch block
                log.error("DownZipUtil 出错", e);
            }
        }
    }
}