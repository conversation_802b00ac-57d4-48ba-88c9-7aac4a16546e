package com.xunge.model.app.basenum;

/**
 * TODO: 描述信息
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2019/5/13 11:43
 */
public class TowerType extends BaseEnum<String> {

    public static final TowerType self = new TowerType();

    private TowerType() {
        super.putEnum("落地角钢塔", "1");
        super.putEnum("楼顶角钢塔", "2");
        super.putEnum("落地四管塔", "3");
        super.putEnum("落地三管塔", "4");
        super.putEnum("楼顶三管塔", "5");
        super.putEnum("落地内爬单管塔", "6");
        super.putEnum("落地外爬单管塔", "7");
        super.putEnum("楼顶单管塔", "8");
        super.putEnum("落地拉线塔", "9");
        super.putEnum("楼顶拉线塔", "10");
        super.putEnum("楼顶井字架", "11");
        super.putEnum("落地景观塔", "12");
        super.putEnum("楼顶景观塔", "13");
        super.putEnum("抱杆", "14");
        super.putEnum("桅杆", "15");
        super.putEnum("楼顶美化天线", "16");
        super.putEnum("集束天线", "17");
        super.putEnum("其他", "18");
        super.putEnum("普通地面塔", "1110");
        super.putEnum("简易塔", "1130");
        super.putEnum("楼面抱杆", "1220");
        super.putEnum("景观塔", "1120");
        super.putEnum("普通楼面塔", "1210");
        super.putEnum("无铁塔", "-1");
    }
}
