package com.xunge.model.budget.ele;

import lombok.Data;

import java.util.List;

/**
 * @author: LiangCheng
 * Date: 2022/7/21 9:13
 * Description: 电费预算 参数vo
 */
@Data
public class BudgetEleParamVO {

    /**
     * 省份字符串
     */
    private String prvIds;

    /**
     * 年份
     */
    private Integer onYear;

    /**
     * 来源，0集团草稿，1省侧填报,2省侧查看、审核，3集团查看，4集团调整
     */
    private Integer formType;

    /**
     * 节点
     */
    private Integer nodeType;

    /**
     * 工单id
     */
    private String workOrderId;

    private Integer groupType;

    /**
     * 成本影响表使用，0页面展示，1导出，页面展示不需要+行，1导出需要
     */
    private Integer affectType;

    private String workName;

    private String redisKey;

    private List<String> prvIdList;

    private List<String> notPrvIdList;

    /**
     * 审核节点id
     */
    private String auditInfoId;

    /**
     * 保存或者修改
     * 仅适用对数据库的持久化数据，从redis中获取的数据不用此参数
     * 如上报集团给集团是修改，省侧快照是插入
     * 保存0，修改1
     */
    private Integer saveOrEdit;

    BudgetEleElectorCostVO budgetEleElectorCostVO;

    BudgetEleBasestationFactorVO budgetEleBasestationFactorVO;

    BudgetEleScaleRegulateVO budgetEleScaleRegulateVO;

    BudgetEleCostAffectVO budgetEleCostAffectVO;

    BudgetEleCountVO budgetEleCountVO;

    List<BudgetEleElectorCostVO> electorCostList;

    List<BudgetEleBasestationFactorVO> basestationFactorList;

    List<BudgetEleScaleRegulateVO> scaleRegulateList;

    List<BudgetEleCostAffectVO> costAffectList;

    List<BudgetEleCountVO> eleCountList;

}
