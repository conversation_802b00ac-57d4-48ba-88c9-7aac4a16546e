package com.xunge.model.finance.ext.accClaim.accwsdl;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import java.util.ArrayList;
import java.util.List;


/**
 * &lt;p&gt;RESPONSECOLLECTION complex type的 Java 类。
 * <p>
 * &lt;p&gt;以下模式片段指定包含在此类中的预期内容。
 * <p>
 * &lt;pre&gt;
 * &amp;lt;complexType name="RESPONSECOLLECTION"&amp;gt;
 * &amp;lt;complexContent&amp;gt;
 * &amp;lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&amp;gt;
 * &amp;lt;sequence&amp;gt;
 * &amp;lt;element name="RESPONSECOLLECTION_ITEM" type="{http://soa.cmcc.com/OSB_RBS_CMF_HQ_ImportBatchAccruedClaimDocSrv}RESPONSECOLLECTION_ITEM" maxOccurs="unbounded" minOccurs="0"/&amp;gt;
 * &amp;lt;/sequence&amp;gt;
 * &amp;lt;/restriction&amp;gt;
 * &amp;lt;/complexContent&amp;gt;
 * &amp;lt;/complexType&amp;gt;
 * &lt;/pre&gt;
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "RESPONSECOLLECTION", propOrder = {
        "responsecollectionitem"
})
public class RESPONSECOLLECTION {

    @XmlElement(name = "RESPONSECOLLECTION_ITEM")
    protected List<RESPONSECOLLECTIONITEM> responsecollectionitem;

    /**
     * Gets the value of the responsecollectionitem property.
     * <p>
     * &lt;p&gt;
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a &lt;CODE&gt;set&lt;/CODE&gt; method for the responsecollectionitem property.
     * <p>
     * &lt;p&gt;
     * For example, to add a new item, do as follows:
     * &lt;pre&gt;
     * getRESPONSECOLLECTIONITEM().add(newItem);
     * &lt;/pre&gt;
     * <p>
     * <p>
     * &lt;p&gt;
     * Objects of the following type(s) are allowed in the list
     * {@link RESPONSECOLLECTIONITEM }
     */
    public List<RESPONSECOLLECTIONITEM> getRESPONSECOLLECTIONITEM() {
        if (responsecollectionitem == null) {
            responsecollectionitem = new ArrayList<RESPONSECOLLECTIONITEM>();
        }
        return this.responsecollectionitem;
    }

}
