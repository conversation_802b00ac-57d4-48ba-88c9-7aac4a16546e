package com.xunge.dao.twrrent.resourceinfo;

import com.xunge.model.towerrent.rentmanager.TwrNewresConfig;

import java.util.Map;

/**
 * TODO：
 *
 * <AUTHOR>
 * @version 1.0 2018-06-27/15:31
 */
public interface INewresConfigDao {
    /**
     * 停用或启用
     *
     * @return
     */
    int startServer(Map<String, Object> param);

    /**
     * 获取某省的综资使用情况
     *
     * @param prvId
     * @return
     */
    TwrNewresConfig getTwrNewresConfig(String prvId);
}
