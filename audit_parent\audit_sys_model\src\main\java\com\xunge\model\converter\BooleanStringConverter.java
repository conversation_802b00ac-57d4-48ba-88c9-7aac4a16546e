package com.xunge.model.converter;


import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.CellData;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;

/**
 * 布尔型转化器
 * <AUTHOR>
 * @date 2020-07-22 12:00
 */
public class BooleanStringConverter implements Converter<String> {

    private static final String YES = "是";
    private static final String NO = "否";
    private static final String YES_STR = "1";
    private static final String NO_STR= "0";

    @Override
    public Class supportJavaTypeKey() {
        return String.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }

    @Override
    public String convertToJavaData(ReadCellData cellData, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        String value = cellData.getStringValue();
        if (YES.equals(value)){
            return YES_STR;
        }else if (NO.equals(value)){
            return NO_STR;
        }
        return null;
    }

    @Override
    public WriteCellData<?> convertToExcelData(String value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        if (value == null){
            return new WriteCellData<>("");
        }else if (YES_STR.equals(value)){
            return new WriteCellData<>(YES);
        }else if (NO_STR.equals(value)){
            return new WriteCellData<>(NO);
        }
        return new WriteCellData<>(value);
    }
}
