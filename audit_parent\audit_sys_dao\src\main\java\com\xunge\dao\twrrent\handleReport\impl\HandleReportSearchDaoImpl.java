package com.xunge.dao.twrrent.handleReport.impl;

import com.xunge.core.page.Page;
import com.xunge.dao.AbstractBaseDao;
import com.xunge.dao.twrrent.handleReport.IHandleReportSearchDao;
import com.xunge.filter.PageInterceptor;
import com.xunge.model.towerrent.monthlyReport.*;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 月报查询
 */
@Repository("handleReportSearchDao")
public class HandleReportSearchDaoImpl extends AbstractBaseDao implements IHandleReportSearchDao {

    private final String namespace = "com.xunge.dao.twrrent.handleReport.TwrHandleReportMapper.";

    @Override
    public Page<RptMonthlyTwrSite> qyeryPageTwrSiteReportPrv(Map<String, Object> param, int pageSize, int pageNum) {
        PageInterceptor.startPage(pageNum, pageSize);
        this.getSqlSession().selectList(namespace + "qyeryPageTwrSiteReportPrv", param);
        return PageInterceptor.endPage();
    }

    @Override
    public List<RptMonthlyTwrSite> queryListTwrSiteReportPrv(Map<String, Object> param) {
        return this.getSqlSession().selectList(namespace + "qyeryPageTwrSiteReportPrv", param);
    }

    @Override
    public Page<RptMonthlyTwrSite> qyeryPageTwrSiteReportCity(Map<String, Object> param, int pageSize, int pageNum) {
        PageInterceptor.startPage(pageNum, pageSize);
        this.getSqlSession().selectList(namespace + "qyeryPageTwrSiteReportCity", param);
        return PageInterceptor.endPage();
    }

    @Override
    public List<RptMonthlyTwrSite> queryListTwrSiteReportCity(Map<String, Object> param) {
        return this.getSqlSession().selectList(namespace + "qyeryPageTwrSiteReportCity", param);
    }

    @Override
    public Page<RptMonthlyTwrSite> qyeryPageTwrSiteReportRegion(Map<String, Object> param, int pageSize, int pageNum) {
        PageInterceptor.startPage(pageNum, pageSize);
        this.getSqlSession().selectList(namespace + "qyeryPageTwrSiteReportRegion", param);
        return PageInterceptor.endPage();
    }

    @Override
    public List<RptMonthlyTwrSite> queryListTwrSiteReportRegion(Map<String, Object> param) {
        return this.getSqlSession().selectList(namespace + "qyeryPageTwrSiteReportRegion", param);
    }

    @Override
    public List<TwrSiteMoneyCompareWithService> getCompareInfoPrv(Map<String, Object> param) {
        return this.getSqlSession().selectList(namespace + "getCompareInfoPrv", param);
    }

    @Override
    public List<TwrSiteMoneyCompareWithService> getCompareInfoCity(Map<String, Object> param) {
        return this.getSqlSession().selectList(namespace + "getCompareInfoCity", param);
    }

    @Override
    public List<TwrSiteMoneyCompareWithService> getCompareInfoRegion(Map<String, Object> param) {
        return this.getSqlSession().selectList(namespace + "getCompareInfoRegion", param);
    }

    @Override
    public List<RptMonthlyBudgetAndProgress> querybudgetAndProgressPrv(Map<String, Object> map) {
        return this.getSqlSession().selectList(namespace + "querybudgetAndProgressPrv", map);
    }

    @Override
    public List<RptMonthlyBudgetAndProgress> querybudgetAndProgressCity(Map<String, Object> map) {
        return this.getSqlSession().selectList(namespace + "querybudgetAndProgressCity", map);
    }

    @Override
    public List<RptMonthlyBudgetAndProgress> querybudgetAndProgressReg(Map<String, Object> map) {
        return this.getSqlSession().selectList(namespace + "querybudgetAndProgressReg", map);
    }

    @Override
    public BigDecimal queryTotalAmount(Map<String, Object> map) {
        return this.getSqlSession().selectOne(namespace + "queryTotalAmount", map);
    }

    @Override
    public Integer queryNumAmount(Map<String, Object> map) {
        return this.getSqlSession().selectOne(namespace + "queryNumAmount", map);
    }


    @Override
    public List<RptMonthlyIconTowerCost> queryIronTowerReportSearchPrv(Map<String, Object> map) {
        return this.getSqlSession().selectList(namespace + "queryIronTowerReportSearchPrv", map);
    }

    @Override
    public List<RptMonthlyIconTowerCost> queryIronTowerReportSearchPreg(Map<String, Object> map) {
        return this.getSqlSession().selectList(namespace + "queryIronTowerReportSearchPreg", map);
    }

    @Override
    public List<RptMonthlyIconTowerCost> queryIronTowerReportSearchReg(Map<String, Object> map) {
        return this.getSqlSession().selectList(namespace + "queryIronTowerReportSearchReg", map);
    }


    @Override
    public List<RptMonthlyBudgetAndProgressNew> getBudgetAndProgressCopeDataWhole(Map<String, Object> paraMap) {
        return this.getSqlSession().selectList(namespace + "getBudgetAndProgressCopeDataWhole", paraMap);
    }

    @Override
    public List<RptMonthlyBudgetAndProgressNew> getBudgetAndProgressCopeDataPrv(Map<String, Object> paraMap) {
        return this.getSqlSession().selectList(namespace + "getBudgetAndProgressCopeDataPrv", paraMap);
    }

    @Override
    public List<RptMonthlyBudgetAndProgressNew> getBudgetAndProgressCopeDataPreg(Map<String, Object> paraMap) {
        return this.getSqlSession().selectList(namespace + "getBudgetAndProgressCopeDataPreg", paraMap);
    }

    @Override
    public List<RptMonthlyIconTowerCost> queryTowerServiceFeeSearchPrv(Map<String, Object> map) {
        return this.getSqlSession().selectList(namespace + "queryTowerServiceFeeSearchPrv", map);
    }

    @Override
    public List<RptMonthlyIconTowerCost> queryTowerServiceFeeSearchPreg(Map<String, Object> map) {
        return this.getSqlSession().selectList(namespace + "queryTowerServiceFeeSearchPreg", map);
    }

    @Override
    public List<RoomCostReportVo> queryRoomCostReport(Map<String, Object> map) {
        return this.getSqlSession().selectList(namespace + "queryRoomCostReport", map);
    }

    @Override
    public List<RoomCostReportVo> queryRoomCostReportDetail(Map<String, Object> map) {
        return this.getSqlSession().selectList(namespace + "queryRoomCostReportDetail", map);
    }

    @Override
    public List<ProductStructureFor5g> query5gAnalysisReport(Map<String, Object> map) {
        return this.getSqlSession().selectList(namespace + "query5gAnalysisReport", map);
    }

    @Override
    public List<TwrOilPowerFee> queryTwrOilPowerFeeListPrv(Map<String, Object> paraMap) {
        return this.getSqlSession().selectList(namespace + "queryTwrOilPowerFeeListPrv", paraMap);
    }

    @Override
    public List<TwrOilPowerFee> queryTwrOilPowerFeeListPreg(Map<String, Object> paraMap) {
        return this.getSqlSession().selectList(namespace + "queryTwrOilPowerFeeListPreg", paraMap);
    }

    @Override
    public List<TwrOilPowerFee> queryTwrOilPowerFeeListReg(Map<String, Object> paraMap) {
        return this.getSqlSession().selectList(namespace + "queryTwrOilPowerFeeListReg", paraMap);
    }
}
