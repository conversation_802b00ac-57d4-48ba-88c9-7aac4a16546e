package com.xunge.model.basedata;

import cn.afterturn.easypoi.excel.annotation.Excel;

import java.util.Map;

/**
 * 综资与租电报账点
 */
public class ResourceEleRentBillAccount {
    @Excel(name = "审核状态",orderNum ="1",replace = {"审核通过_0","审核失败_8","审核中_9","未提交_-1","_null"},groupName = "资源信息")
    private String auditingState;
    private String baseResourceId;
    @Excel(name = "资源标识",orderNum ="2",groupName = "资源信息")
    private String baseResourceCuid;
    @Excel(name = "资源名称",orderNum ="3",groupName = "资源信息")
    private String baseResourceName;
    @Excel(name = "省份标识",orderNum ="4",groupName = "资源信息")
    private String prvSname;
    @Excel(name = "所属地市",orderNum ="5",groupName = "资源信息")
    private String pregName;
    @Excel(name = "所属区县",orderNum ="6",groupName = "资源信息")
    private String regName;
    @Excel(name = "所属部门",orderNum ="7",groupName = "资源信息")
    private String belongDept;
    @Excel(name = "位置点/机房类型",orderNum ="8",replace = {"_0","传输机房_1","无线机房_2","核心网机房_3","数据网机房_4","动力环境机房_5","用户机房_6","IDC机房_7","地下进线室_8","综合机房_9","接入机房_10","交换机房_11","网管机房_12","微波机房_13","MDC机房_14","MDF机房_15","新业务机房_16","室外综合机柜_17","备件机房_18","其他_19","_null"
    ,"室外无线位置点_20","室外传输位置点_21","全业务位置点_22","_null"},groupName = "资源信息")
    private String baseresourceCategory;

    private String baseresourceType;

    @Excel(name = "生命周期状态",orderNum ="9",replace = {"在网_1","工程_2","退网_3","删除_-1","_null"},groupName = "资源信息")
    private String baseresourceState;
    @Excel(name = "业务类型",orderNum ="10",replace = {"核心机房_1", "汇聚传输站点_2", "基站_3", "室分及WLAN_4",
            "家客集客_5", "IDC机房_6", "基地_7", "其他_8","_null"},groupName = "资源信息")
    private String serviceSiteType="";
    @Excel(name = "所属铁塔公司站点编码",orderNum ="11",groupName = "资源信息")
    private String towerSiteCode;
    @Excel(name = "站点cuid",orderNum ="12",groupName = "资源信息")
    private String basesiteCuid;
    @Excel(name = "资源地址",orderNum ="13",groupName = "资源信息")
    private String baseresourceAddress;
    @Excel(name = "经度",orderNum ="14",groupName = "资源信息")
    private String baseresourceLongitude;
    @Excel(name = "纬度",orderNum ="15",groupName = "资源信息")
    private String baseresourceLatitude;
    @Excel(name = "主设备总额定功率",orderNum ="16",groupName = "资源信息")
    private String equipmentPower;
    @Excel(name = "空调总额定功率",orderNum ="17",groupName = "资源信息")
    private String airconditionerPower;
    @Excel(name = "最近功率变动",orderNum ="18",groupName = "资源信息",suffix = "%")
    private String latestChangeRange;
    private String freeFlag;
    @Excel(name = "是否免电费",orderNum ="19",groupName = "资源信息")
    private String freeEleFlag;
    @Excel(name = "是否免租费",orderNum ="20",groupName = "资源信息")
    private String freeRentFlag;
    @Excel(name = "数据来源",orderNum ="21",replace = {"系统录入_0", "系统导入_1", "接口采集_2","_null"},groupName = "资源信息")
    private String dataFrom;
    @Excel(name = "入网时间",orderNum ="22",exportFormat = "yyyy-MM-dd",groupName = "资源信息")
    private String baseresourceOpendate;
    @Excel(name = "退网时间",orderNum ="23",exportFormat = "yyyy-MM-dd",groupName = "资源信息")
    private String baseresourceStopdate;
    @Excel(name = "产权性质",orderNum = "24", replace = {"自有（自建)_1", "自有（合建）_2",
            "自有（购买）_3", "租用_4", "用户所有_5", "其他_6","共建_7","置换_8","移动自有_9","移交铁塔_10","租用铁塔_11",
    "长租_12","借用_13","共享_14","_null"},groupName = "资源信息")
    private String roomProperty;
    @Excel(name = "产权单位",orderNum = "25", replace = {"中国铁塔_1", "中国移动_2", "中国联通_3",
            "中国电信_4", "中国铁通_5", "中国广电_6", "业主_7", "其他_8","_null"},groupName = "资源信息")
    private String roomOwner;
    @Excel(name = "备注",orderNum = "26",groupName = "资源信息")
    private String baseresourceNote;
    @Excel(name = "报账点类型",orderNum = "27",replace = {"租费报账点_0","自维电费报账点_1","铁塔电费报账点_2","代持电费报账点_3","_null"},groupName = "网络电费报账点信息")
    private String eleBillAccountType;
    @Excel(name = "报账点状态",orderNum = "28",replace = {"启用_0", "停用_9", "终止_10","_null"},groupName = "网络电费报账点信息")
    private String eleBillaccountState;
    @Excel(name = "报账点编码",orderNum = "29",groupName = "网络电费报账点信息")
    private String eleBillAccountCode;
    @Excel(name = "报账点名称",orderNum = "30",groupName = "网络电费报账点信息")
    private String eleBillAccountName;
    @Excel(name = "报账点审核状态",orderNum = "31", replace = {"审核通过_0", "未提交_-1", "删除审核中_3",
            "删除审核通过_4", "删除审核不通过_6", "审核不通过_8", "审核中_9","_null"},groupName = "网络电费报账点信息")
    private String eleAuditingState;
    @Excel(name = "所属成本中心",orderNum = "32",groupName = "网络电费报账点信息")
    private String eleCostCenterName;
    @Excel(name = "成本中心编码",orderNum = "33",groupName = "网络电费报账点信息")
    private String eleCostCenter;
    @Excel(name = "备注",orderNum = "34",groupName = "网络电费报账点信息")
    private String eleBillaccountNote;
    @Excel(name = "合同或固化编码",orderNum = "35",groupName = "网络电费报账点信息")
    private String eleContractCode;
    @Excel(name = "合同或固化名称",orderNum = "36",groupName = "网络电费报账点信息")
    private String eleContractName;
    @Excel(name = "合同或固化期始",orderNum = "37",groupName = "网络电费报账点信息")
    private String eleContractStartdate;
    @Excel(name = "合同或固化期终",orderNum = "38",groupName = "网络电费报账点信息")
    private String eleContractEnddate;
    @Excel(name = "供电类型",orderNum = "39",replace = {"转供电_2", "直供电_1","_null"},groupName = "网络电费报账点信息")
    private String eleSupplyMethod;
    @Excel(name = "是否包干",orderNum = "40",replace = {"否_0", "是_1","_null"},groupName = "网络电费报账点信息")
    private String eleIsIncludeAll;
    @Excel(name = "供应商名称",orderNum = "41",groupName = "网络电费报账点信息")
    private String eleSupplierName;
    @Excel(name = "供应商编码",orderNum = "42",groupName = "网络电费报账点信息")
    private String eleSupplierCode;
    private String eleBillAccountId;

    @Excel(name = "报账点类型",orderNum = "43",replace = {"租费报账点_0","电费特殊报账点_1","一站多路电特殊报账点_4","_null"},groupName = "特殊电费报账点信息")
    private String specialEleBillAccountType;
    @Excel(name = "报账点状态",orderNum = "44",replace = {"启用_0", "停用_9", "终止_10","_null"},groupName = "特殊电费报账点信息")
    private String specialEleBillaccountState;
    @Excel(name = "报账点编码",orderNum = "45",groupName = "特殊电费报账点信息")
    private String specialEleBillAccountCode;
    @Excel(name = "报账点名称",orderNum = "46",groupName = "特殊电费报账点信息")
    private String specialEleBillAccountName;
    @Excel(name = "报账点审核状态",orderNum = "47", replace = {"审核通过_0", "未提交_-1", "删除审核中_3", "删除审核通过_4",
            "删除审核不通过_6", "审核不通过_8", "审核中_9","_null"},groupName = "特殊电费报账点信息")
    private String specialEleAuditingState;
    @Excel(name = "所属成本中心",orderNum = "48",groupName = "特殊电费报账点信息")
    private String specialEleCostCenterName;
    @Excel(name = "成本中心编码",orderNum = "49",groupName = "特殊电费报账点信息")
    private String specialEleCostCenter;
    @Excel(name = "备注",orderNum = "50",groupName = "特殊电费报账点信息")
    private String specialEleBillaccountNote;
    @Excel(name = "合同或固化编码",orderNum = "51",groupName = "特殊电费报账点信息")
    private String specialEleContractCode;
    @Excel(name = "合同或固化名称",orderNum = "52",groupName = "特殊电费报账点信息")
    private String specialEleContractName;
    @Excel(name = "合同或固化期始",orderNum = "53",groupName = "特殊电费报账点信息")
    private String specialEleContractStartdate;
    @Excel(name = "合同或固化期终",orderNum = "54",groupName = "特殊电费报账点信息")
    private String specialEleContractEnddate;
    @Excel(name = "供电类型",orderNum = "55",replace = {"转供电_2", "直供电_1","_null"},groupName = "特殊电费报账点信息")
    private String specialEleSupplyMethod;
    @Excel(name = "是否包干",orderNum = "56",replace = {"否_0", "是_1","_null"},groupName = "特殊电费报账点信息")
    private String specialEleIsIncludeAll;
    @Excel(name = "供应商名称",orderNum = "57",groupName = "特殊电费报账点信息")
    private String specialEleSupplierName;
    @Excel(name = "供应商编码",orderNum = "58",groupName = "特殊电费报账点信息")
    private String specialEleSupplierCode;

    private String specialEleBillAccountId;

    @Excel(name = "报账点类型",orderNum = "59",replace = {"租费报账点_0","自维电费报账点_1","铁塔电费报账点_2","_null"},groupName = "塔维电费报账点信息")
    private String teleBillAccountType;
    @Excel(name = "报账点状态",orderNum = "60",replace = {"启用_0", "停用_9", "终止_10","_null"},groupName = "塔维电费报账点信息")
    private String teleBillaccountState;
    @Excel(name = "报账点编码",orderNum = "61",groupName = "塔维电费报账点信息")
    private String teleBillAccountCode;
    @Excel(name = "报账点名称",orderNum = "62",groupName = "塔维电费报账点信息")
    private String teleBillAccountName;
    @Excel(name = "报账点审核状态",orderNum = "63", replace = {"审核通过_0", "未提交_-1", "删除审核中_3",
            "删除审核通过_4", "删除审核不通过_6", "审核不通过_8", "审核中_9","_null"},groupName = "塔维电费报账点信息")
    private String teleAuditingState;
    @Excel(name = "所属成本中心",orderNum = "64",groupName = "塔维电费报账点信息")
    private String teleCostCenterName;
    @Excel(name = "成本中心编码",orderNum = "65",groupName = "塔维电费报账点信息")
    private String teleCostCenter;
    @Excel(name = "备注",orderNum = "66",groupName = "塔维电费报账点信息")
    private String teleBillaccountNote;
    @Excel(name = "合同或固化编码",orderNum = "67",groupName = "塔维电费报账点信息")
    private String teleContractCode;
    @Excel(name = "合同或固化名称",orderNum = "68",groupName = "塔维电费报账点信息")
    private String teleContractName;
    @Excel(name = "合同或固化期始",orderNum = "69",groupName = "塔维电费报账点信息")
    private String teleContractStartdate;
    @Excel(name = "合同或固化期终",orderNum = "70",groupName = "塔维电费报账点信息")
    private String teleContractEnddate;
    @Excel(name = "供电类型",orderNum = "71",replace = {"转供电_2", "直供电_1","_null"},groupName = "塔维电费报账点信息")
    private String teleSupplyMethod;
    @Excel(name = "是否包干",orderNum = "72",replace = {"否_0", "是_1","_null"},groupName = "塔维电费报账点信息")
    private String teleIsIncludeAll;
    @Excel(name = "供应商名称",orderNum = "73",groupName = "塔维电费报账点信息")
    private String teleSupplierName;
    @Excel(name = "供应商编码",orderNum = "74",groupName = "塔维电费报账点信息")
    private String teleSupplierCode;
    private String teleBillAccountId;

    @Excel(name = "报账点类型",orderNum = "75",replace = {"塔维租费报账点_0","自维租费报账点_1","三方塔服务费报账点_2","_null"},groupName = "租费报账点信息")
    private String rentBillAccountType;
    @Excel(name = "报账点状态",orderNum = "76",replace = {"启用_0", "停用_9", "终止_10","_null"},groupName = "租费报账点信息")
    private String rentBillaccountState;
    @Excel(name = "报账点编码",orderNum = "77",groupName = "租费报账点信息")
    private String rentBillAccountCode;
    @Excel(name = "报账点名称",orderNum = "78",groupName = "租费报账点信息")
    private String rentBillAccountName;
    @Excel(name = "报账点审核状态",orderNum = "79", replace = {"审核通过_0", "未提交_-1", "删除审核中_3", "删除审核通过_4",
            "删除审核不通过_6", "审核不通过_8", "审核中_9","_null"},groupName = "租费报账点信息")
    private String rentAuditingState;
    @Excel(name = "所属成本中心",orderNum = "80",groupName = "租费报账点信息")
    private String rentCostCenterName;
    @Excel(name = "成本中心编码",orderNum = "81",groupName = "租费报账点信息")
    private String rentCostCenter;
    @Excel(name = "备注",orderNum = "82",groupName = "租费报账点信息")
    private String rentBillaccountNote;
    @Excel(name = "合同或固化编码",orderNum = "83",groupName = "租费报账点信息")
    private String rentContractCode;
    @Excel(name = "合同或固化名称",orderNum = "84",groupName = "租费报账点信息")
    private String rentContractName;
    @Excel(name = "合同或固化期始",orderNum = "85",groupName = "租费报账点信息")
    private String rentContractStartdate;
    @Excel(name = "合同或固化期终",orderNum = "86",groupName = "租费报账点信息")
    private String rentContractEnddate;
    @Excel(name = "供电类型",orderNum = "87",replace = {"转供电_2", "直供电_1","_null"},groupName = "租费报账点信息")
    private String rentSupplyMethod;
    @Excel(name = "是否包干",orderNum = "88",replace = {"否_0", "是_1","_null"},groupName = "租费报账点信息")
    private String rentIsIncludeAll;
    @Excel(name = "供应商名称",orderNum = "89",groupName = "租费报账点信息")
    private String rentSupplierName;
    @Excel(name = "供应商编码",orderNum = "90",groupName = "租费报账点信息")
    private String rentSupplierCode;
    private String rentBillAccountId;

    @Excel(name = "报账点类型",orderNum = "91",replace = {"塔维租费报账点_0","租费特殊报账点_1","三方塔特殊报账点_2","一站多合同特殊报账点_3","_null"},groupName = "特殊租费报账点信息")
    private String specialRentBillAccountType;
    @Excel(name = "报账点状态",orderNum = "92",replace = {"启用_0", "停用_9", "终止_10","_null"},groupName = "特殊租费报账点信息")
    private String specialRentBillaccountState;
    @Excel(name = "报账点编码",orderNum = "93",groupName = "特殊租费报账点信息")
    private String specialRentBillAccountCode;
    @Excel(name = "报账点名称",orderNum = "94",groupName = "特殊租费报账点信息")
    private String specialRentBillAccountName;
    @Excel(name = "报账点审核状态",orderNum = "95", replace = {"审核通过_0", "未提交_-1", "删除审核中_3",
            "删除审核通过_4", "删除审核不通过_6", "审核不通过_8", "审核中_9","_null"},groupName = "特殊租费报账点信息")
    private String specialRentAuditingState;
    @Excel(name = "所属成本中心",orderNum = "96",groupName = "特殊租费报账点信息")
    private String specialRentCostCenterName;
    @Excel(name = "成本中心编码",orderNum = "97",groupName = "特殊租费报账点信息")
    private String specialRentCostCenter;
    @Excel(name = "备注",orderNum = "98",groupName = "特殊租费报账点信息")
    private String specialRentBillaccountNote;
    @Excel(name = "合同或固化编码",orderNum = "99",groupName = "特殊租费报账点信息")
    private String specialRentContractCode;
    @Excel(name = "合同或固化名称",orderNum = "100",groupName = "特殊租费报账点信息")
    private String specialRentContractName;
    @Excel(name = "合同或固化期始",orderNum = "101",groupName = "特殊租费报账点信息")
    private String specialRentContractStartdate;
    @Excel(name = "合同或固化期终",orderNum = "102",groupName = "特殊租费报账点信息")
    private String specialRentContractEnddate;
    @Excel(name = "供电类型",orderNum = "103",replace = {"转供电_2", "直供电_1","_null"},groupName = "特殊租费报账点信息")
    private String specialRentSupplyMethod;
    @Excel(name = "是否包干",orderNum = "104",replace = {"否_0", "是_1","_null"},groupName = "特殊租费报账点信息")
    private String specialRentIsIncludeAll;
    @Excel(name = "供应商名称",orderNum = "105",groupName = "特殊租费报账点信息")
    private String specialRentSupplierName;
    @Excel(name = "供应商编码",orderNum = "106",groupName = "特殊租费报账点信息")
    private String specialRentSupplierCode;
    private String specialRentBillAccountId;

    public String getBaseresourceType() {
        return baseresourceType;
    }

    public void setBaseresourceType(String baseresourceType) {
        this.baseresourceType = baseresourceType;
    }
    public String getEleBillAccountType() {
        return eleBillAccountType;
    }

    public void setEleBillAccountType(String eleBillAccountType) {
        this.eleBillAccountType = eleBillAccountType;
    }

    public String getEleIsIncludeAll() {
        return eleIsIncludeAll;
    }

    public void setEleIsIncludeAll(String eleIsIncludeAll) {
        this.eleIsIncludeAll = eleIsIncludeAll;
    }

    public String getEleSupplyMethod() {
        return eleSupplyMethod;
    }

    public void setEleSupplyMethod(String eleSupplyMethod) {
        this.eleSupplyMethod = eleSupplyMethod;
    }

    public String getSpecialEleBillAccountType() {
        return specialEleBillAccountType;
    }

    public void setSpecialEleBillAccountType(String specialEleBillAccountType) {
        this.specialEleBillAccountType = specialEleBillAccountType;
    }

    public String getSpecialEleIsIncludeAll() {
        return specialEleIsIncludeAll;
    }

    public void setSpecialEleIsIncludeAll(String specialEleIsIncludeAll) {
        this.specialEleIsIncludeAll = specialEleIsIncludeAll;
    }

    public String getSpecialEleSupplyMethod() {
        return specialEleSupplyMethod;
    }

    public void setSpecialEleSupplyMethod(String specialEleSupplyMethod) {
        this.specialEleSupplyMethod = specialEleSupplyMethod;
    }

    public String getTeleBillAccountType() {
        return teleBillAccountType;
    }

    public void setTeleBillAccountType(String teleBillAccountType) {
        this.teleBillAccountType = teleBillAccountType;
    }

    public String getTeleIsIncludeAll() {
        return teleIsIncludeAll;
    }

    public void setTeleIsIncludeAll(String teleIsIncludeAll) {
        this.teleIsIncludeAll = teleIsIncludeAll;
    }

    public String getTeleSupplyMethod() {
        return teleSupplyMethod;
    }

    public void setTeleSupplyMethod(String teleSupplyMethod) {
        this.teleSupplyMethod = teleSupplyMethod;
    }

    public String getRentBillAccountType() {
        return rentBillAccountType;
    }

    public void setRentBillAccountType(String rentBillAccountType) {
        this.rentBillAccountType = rentBillAccountType;
    }

    public String getRentIsIncludeAll() {
        return rentIsIncludeAll;
    }

    public void setRentIsIncludeAll(String rentIsIncludeAll) {
        this.rentIsIncludeAll = rentIsIncludeAll;
    }

    public String getRentSupplyMethod() {
        return rentSupplyMethod;
    }

    public void setRentSupplyMethod(String rentSupplyMethod) {
        this.rentSupplyMethod = rentSupplyMethod;
    }

    public String getSpecialRentBillAccountType() {
        return specialRentBillAccountType;
    }

    public void setSpecialRentBillAccountType(String specialRentBillAccountType) {
        this.specialRentBillAccountType = specialRentBillAccountType;
    }

    public String getSpecialRentIsIncludeAll() {
        return specialRentIsIncludeAll;
    }

    public void setSpecialRentIsIncludeAll(String specialRentIsIncludeAll) {
        this.specialRentIsIncludeAll = specialRentIsIncludeAll;
    }

    public String getSpecialRentSupplyMethod() {
        return specialRentSupplyMethod;
    }

    public void setSpecialRentSupplyMethod(String specialRentSupplyMethod) {
        this.specialRentSupplyMethod = specialRentSupplyMethod;
    }

    public String getSpecialEleBillAccountCode() {
        return specialEleBillAccountCode;
    }

    public void setSpecialEleBillAccountCode(String specialEleBillAccountCode) {
        this.specialEleBillAccountCode = specialEleBillAccountCode;
    }

    public String getSpecialEleSupplierCode() {
        return specialEleSupplierCode;
    }

    public void setSpecialEleSupplierCode(String specialEleSupplierCode) {
        this.specialEleSupplierCode = specialEleSupplierCode;
    }

    public String getSpecialRentBillAccountCode() {
        return specialRentBillAccountCode;
    }

    public void setSpecialRentBillAccountCode(String specialRentBillAccountCode) {
        this.specialRentBillAccountCode = specialRentBillAccountCode;
    }

    public String getSpecialRentSupplierCode() {
        return specialRentSupplierCode;
    }

    public void setSpecialRentSupplierCode(String specialRentSupplierCode) {
        this.specialRentSupplierCode = specialRentSupplierCode;
    }

    public String getTeleBillAccountCode() {
        return teleBillAccountCode;
    }

    public void setTeleBillAccountCode(String teleBillAccountCode) {
        this.teleBillAccountCode = teleBillAccountCode;
    }

    public String getTeleSupplierCode() {
        return teleSupplierCode;
    }

    public void setTeleSupplierCode(String teleSupplierCode) {
        this.teleSupplierCode = teleSupplierCode;
    }

    public String getTeleBillAccountName() {
        return teleBillAccountName;
    }

    public void setTeleBillAccountName(String teleBillAccountName) {
        this.teleBillAccountName = teleBillAccountName;
    }

    public String getTeleSupplierName() {
        return teleSupplierName;
    }

    public void setTeleSupplierName(String teleSupplierName) {
        this.teleSupplierName = teleSupplierName;
    }

    public String getSpecialEleBillAccountName() {
        return specialEleBillAccountName;
    }

    public void setSpecialEleBillAccountName(String specialEleBillAccountName) {
        this.specialEleBillAccountName = specialEleBillAccountName;
    }

    public String getSpecialEleSupplierName() {
        return specialEleSupplierName;
    }

    public void setSpecialEleSupplierName(String specialEleSupplierName) {
        this.specialEleSupplierName = specialEleSupplierName;
    }

    public String getSpecialRentBillAccountName() {
        return specialRentBillAccountName;
    }

    public void setSpecialRentBillAccountName(String specialRentBillAccountName) {
        this.specialRentBillAccountName = specialRentBillAccountName;
    }

    public String getSpecialRentSupplierName() {
        return specialRentSupplierName;
    }

    public void setSpecialRentSupplierName(String specialRentSupplierName) {
        this.specialRentSupplierName = specialRentSupplierName;
    }


    public String getSpecialEleBillAccountId() {
        return specialEleBillAccountId;
    }

    public void setSpecialEleBillAccountId(String specialEleBillAccountId) {
        this.specialEleBillAccountId = specialEleBillAccountId;
    }

    public String getSpecialRentBillAccountId() {
        return specialRentBillAccountId;
    }

    public void setSpecialRentBillAccountId(String specialRentBillAccountId) {
        this.specialRentBillAccountId = specialRentBillAccountId;
    }

    public String getTeleBillAccountId() {
        return teleBillAccountId;
    }

    public void setTeleBillAccountId(String teleBillAccountId) {
        this.teleBillAccountId = teleBillAccountId;
    }


    public String getEleSupplierCode() {
        return eleSupplierCode;
    }

    public void setEleSupplierCode(String eleSupplierCode) {
        this.eleSupplierCode = eleSupplierCode;
    }

    public String getRentSupplierCode() {
        return rentSupplierCode;
    }

    public void setRentSupplierCode(String rentSupplierCode) {
        this.rentSupplierCode = rentSupplierCode;
    }

    public String getEleBillAccountId() {
        return eleBillAccountId;
    }

    public void setEleBillAccountId(String eleBillAccountId) {
        this.eleBillAccountId = eleBillAccountId;
    }

    public String getRentBillAccountId() {
        return rentBillAccountId;
    }

    public void setRentBillAccountId(String rentBillAccountId) {
        this.rentBillAccountId = rentBillAccountId;
    }

    public String getBaseResourceId() {
        return baseResourceId;
    }

    public void setBaseResourceId(String baseResourceId) {
        this.baseResourceId = baseResourceId;
    }

    public String getBaseResourceCuid() {
        return baseResourceCuid;
    }

    public void setBaseResourceCuid(String baseResourceCuid) {
        this.baseResourceCuid = baseResourceCuid;
    }

    public String getPregName() {
        return pregName;
    }

    public void setPregName(String pregName) {
        this.pregName = pregName;
    }

    public String getRegName() {
        return regName;
    }

    public void setRegName(String regName) {
        this.regName = regName;
    }

    public String getBaseResourceName() {
        return baseResourceName;
    }

    public void setBaseResourceName(String baseResourceName) {
        this.baseResourceName = baseResourceName;
    }

    public String getEleBillAccountCode() {
        return eleBillAccountCode;
    }

    public void setEleBillAccountCode(String eleBillAccountCode) {
        this.eleBillAccountCode = eleBillAccountCode;
    }

    public String getRentBillAccountCode() {
        return rentBillAccountCode;
    }

    public void setRentBillAccountCode(String rentBillAccountCode) {
        this.rentBillAccountCode = rentBillAccountCode;
    }

    public String getEleBillAccountName() {
        return eleBillAccountName;
    }

    public void setEleBillAccountName(String eleBillAccountName) {
        this.eleBillAccountName = eleBillAccountName;
    }

    public String getEleSupplierName() {
        return eleSupplierName;
    }

    public void setEleSupplierName(String eleSupplierName) {
        this.eleSupplierName = eleSupplierName;
    }



    public String getRentBillAccountName() {
        return rentBillAccountName;
    }

    public void setRentBillAccountName(String rentBillAccountName) {
        this.rentBillAccountName = rentBillAccountName;
    }

    public String getRentSupplierName() {
        return rentSupplierName;
    }

    public void setRentSupplierName(String rentSupplierName) {
        this.rentSupplierName = rentSupplierName;
    }

    public String getEleBillaccountState() {
        return eleBillaccountState;
    }

    public void setEleBillaccountState(String eleBillaccountState) {
        this.eleBillaccountState = eleBillaccountState;
    }

    public String getEleAuditingState() {
        return eleAuditingState;
    }

    public void setEleAuditingState(String eleAuditingState) {
        this.eleAuditingState = eleAuditingState;
    }

    public String getEleCostCenter() {
        return eleCostCenter;
    }

    public void setEleCostCenter(String eleCostCenter) {
        this.eleCostCenter = eleCostCenter;
    }

    public String getEleBillaccountNote() {
        return eleBillaccountNote;
    }

    public void setEleBillaccountNote(String eleBillaccountNote) {
        this.eleBillaccountNote = eleBillaccountNote;
    }

    public String getEleContractStartdate() {
        return eleContractStartdate;
    }

    public void setEleContractStartdate(String eleContractStartdate) {
        this.eleContractStartdate = eleContractStartdate;
    }

    public String getEleContractEnddate() {
        return eleContractEnddate;
    }

    public void setEleContractEnddate(String eleContractEnddate) {
        this.eleContractEnddate = eleContractEnddate;
    }

    public String getSpecialEleBillaccountState() {
        return specialEleBillaccountState;
    }

    public void setSpecialEleBillaccountState(String specialEleBillaccountState) {
        this.specialEleBillaccountState = specialEleBillaccountState;
    }

    public String getSpecialEleAuditingState() {
        return specialEleAuditingState;
    }

    public void setSpecialEleAuditingState(String specialEleAuditingState) {
        this.specialEleAuditingState = specialEleAuditingState;
    }

    public String getSpecialEleCostCenter() {
        return specialEleCostCenter;
    }

    public void setSpecialEleCostCenter(String specialEleCostCenter) {
        this.specialEleCostCenter = specialEleCostCenter;
    }

    public String getSpecialEleBillaccountNote() {
        return specialEleBillaccountNote;
    }

    public void setSpecialEleBillaccountNote(String specialEleBillaccountNote) {
        this.specialEleBillaccountNote = specialEleBillaccountNote;
    }

    public String getSpecialEleContractStartdate() {
        return specialEleContractStartdate;
    }

    public void setSpecialEleContractStartdate(String specialEleContractStartdate) {
        this.specialEleContractStartdate = specialEleContractStartdate;
    }

    public String getSpecialEleContractEnddate() {
        return specialEleContractEnddate;
    }

    public void setSpecialEleContractEnddate(String specialEleContractEnddate) {
        this.specialEleContractEnddate = specialEleContractEnddate;
    }

    public String getTeleBillaccountState() {
        return teleBillaccountState;
    }

    public void setTeleBillaccountState(String teleBillaccountState) {
        this.teleBillaccountState = teleBillaccountState;
    }

    public String getTeleAuditingState() {
        return teleAuditingState;
    }

    public void setTeleAuditingState(String teleAuditingState) {
        this.teleAuditingState = teleAuditingState;
    }

    public String getTeleCostCenter() {
        return teleCostCenter;
    }

    public void setTeleCostCenter(String teleCostCenter) {
        this.teleCostCenter = teleCostCenter;
    }

    public String getTeleBillaccountNote() {
        return teleBillaccountNote;
    }

    public void setTeleBillaccountNote(String teleBillaccountNote) {
        this.teleBillaccountNote = teleBillaccountNote;
    }

    public String getTeleContractStartdate() {
        return teleContractStartdate;
    }

    public void setTeleContractStartdate(String teleContractStartdate) {
        this.teleContractStartdate = teleContractStartdate;
    }

    public String getTeleContractEnddate() {
        return teleContractEnddate;
    }

    public void setTeleContractEnddate(String teleContractEnddate) {
        this.teleContractEnddate = teleContractEnddate;
    }

    public String getRentBillaccountState() {
        return rentBillaccountState;
    }

    public void setRentBillaccountState(String rentBillaccountState) {
        this.rentBillaccountState = rentBillaccountState;
    }

    public String getRentAuditingState() {
        return rentAuditingState;
    }

    public void setRentAuditingState(String rentAuditingState) {
        this.rentAuditingState = rentAuditingState;
    }

    public String getRentCostCenter() {
        return rentCostCenter;
    }

    public void setRentCostCenter(String rentCostCenter) {
        this.rentCostCenter = rentCostCenter;
    }

    public String getRentBillaccountNote() {
        return rentBillaccountNote;
    }

    public void setRentBillaccountNote(String rentBillaccountNote) {
        this.rentBillaccountNote = rentBillaccountNote;
    }

    public String getRentContractStartdate() {
        return rentContractStartdate;
    }

    public void setRentContractStartdate(String rentContractStartdate) {
        this.rentContractStartdate = rentContractStartdate;
    }

    public String getRentContractEnddate() {
        return rentContractEnddate;
    }

    public void setRentContractEnddate(String rentContractEnddate) {
        this.rentContractEnddate = rentContractEnddate;
    }

    public String getSpecialRentBillaccountState() {
        return specialRentBillaccountState;
    }

    public void setSpecialRentBillaccountState(String specialRentBillaccountState) {
        this.specialRentBillaccountState = specialRentBillaccountState;
    }

    public String getSpecialRentAuditingState() {
        return specialRentAuditingState;
    }

    public void setSpecialRentAuditingState(String specialRentAuditingState) {
        this.specialRentAuditingState = specialRentAuditingState;
    }

    public String getSpecialRentCostCenter() {
        return specialRentCostCenter;
    }

    public void setSpecialRentCostCenter(String specialRentCostCenter) {
        this.specialRentCostCenter = specialRentCostCenter;
    }

    public String getSpecialRentBillaccountNote() {
        return specialRentBillaccountNote;
    }

    public void setSpecialRentBillaccountNote(String specialRentBillaccountNote) {
        this.specialRentBillaccountNote = specialRentBillaccountNote;
    }

    public String getSpecialRentContractStartdate() {
        return specialRentContractStartdate;
    }

    public void setSpecialRentContractStartdate(String specialRentContractStartdate) {
        this.specialRentContractStartdate = specialRentContractStartdate;
    }

    public String getSpecialRentContractEnddate() {
        return specialRentContractEnddate;
    }

    public void setSpecialRentContractEnddate(String specialRentContractEnddate) {
        this.specialRentContractEnddate = specialRentContractEnddate;
    }

    public String getAuditingState() {
        return auditingState;
    }

    public void setAuditingState(String auditingState) {
        this.auditingState = auditingState;
    }

    public String getPrvSname() {
        return prvSname;
    }

    public void setPrvSname(String prvSname) {
        this.prvSname = prvSname;
    }

    public String getBelongDept() {
        return belongDept;
    }

    public void setBelongDept(String belongDept) {
        this.belongDept = belongDept;
    }

    public String getBaseresourceCategory() {
        return baseresourceCategory;
    }

    public void setBaseresourceCategory(String baseresourceCategory) {
        this.baseresourceCategory = baseresourceCategory;
    }

    public String getBaseresourceState() {
        return baseresourceState;
    }

    public void setBaseresourceState(String baseresourceState) {
        this.baseresourceState = baseresourceState;
    }

    public String getServiceSiteType() {
        return serviceSiteType;
    }

    public void setServiceSiteType(String serviceSiteType) {
        this.serviceSiteType = serviceSiteType;
    }

    public String getTowerSiteCode() {
        return towerSiteCode;
    }

    public void setTowerSiteCode(String towerSiteCode) {
        this.towerSiteCode = towerSiteCode;
    }

    public String getBasesiteCuid() {
        return basesiteCuid;
    }

    public void setBasesiteCuid(String basesiteCuid) {
        this.basesiteCuid = basesiteCuid;
    }

    public String getBaseresourceAddress() {
        return baseresourceAddress;
    }

    public void setBaseresourceAddress(String baseresourceAddress) {
        this.baseresourceAddress = baseresourceAddress;
    }

    public String getBaseresourceLongitude() {
        return baseresourceLongitude;
    }

    public void setBaseresourceLongitude(String baseresourceLongitude) {
        this.baseresourceLongitude = baseresourceLongitude;
    }

    public String getBaseresourceLatitude() {
        return baseresourceLatitude;
    }

    public void setBaseresourceLatitude(String baseresourceLatitude) {
        this.baseresourceLatitude = baseresourceLatitude;
    }

    public String getEquipmentPower() {
        return equipmentPower;
    }

    public void setEquipmentPower(String equipmentPower) {
        this.equipmentPower = equipmentPower;
    }

    public String getAirconditionerPower() {
        return airconditionerPower;
    }

    public void setAirconditionerPower(String airconditionerPower) {
        this.airconditionerPower = airconditionerPower;
    }

    public String getLatestChangeRange() {
        return latestChangeRange;
    }

    public void setLatestChangeRange(String latestChangeRange) {
        this.latestChangeRange = latestChangeRange;
    }

    public String getFreeEleFlag() {
        return freeEleFlag;
    }

    public void setFreeEleFlag(String freeEleFlag) {
        this.freeEleFlag = freeEleFlag;
    }

    public String getFreeRentFlag() {
        return freeRentFlag;
    }

    public void setFreeRentFlag(String freeRentFlag) {
        this.freeRentFlag = freeRentFlag;
    }

    public String getDataFrom() {
        return dataFrom;
    }

    public void setDataFrom(String dataFrom) {
        this.dataFrom = dataFrom;
    }

    public String getBaseresourceOpendate() {
        return baseresourceOpendate;
    }

    public void setBaseresourceOpendate(String baseresourceOpendate) {
        this.baseresourceOpendate = baseresourceOpendate;
    }

    public String getBaseresourceStopdate() {
        return baseresourceStopdate;
    }

    public void setBaseresourceStopdate(String baseresourceStopdate) {
        this.baseresourceStopdate = baseresourceStopdate;
    }

    public String getRoomProperty() {
        return roomProperty;
    }

    public void setRoomProperty(String roomProperty) {
        this.roomProperty = roomProperty;
    }

    public String getRoomOwner() {
        return roomOwner;
    }

    public void setRoomOwner(String roomOwner) {
        this.roomOwner = roomOwner;
    }

    public String getBaseresourceNote() {
        return baseresourceNote;
    }

    public void setBaseresourceNote(String baseresourceNote) {
        this.baseresourceNote = baseresourceNote;
    }

    public String getEleCostCenterName() {
        return eleCostCenterName;
    }

    public void setEleCostCenterName(String eleCostCenterName) {
        this.eleCostCenterName = eleCostCenterName;
    }

    public String getEleContractCode() {
        return eleContractCode;
    }

    public void setEleContractCode(String eleContractCode) {
        this.eleContractCode = eleContractCode;
    }

    public String getEleContractName() {
        return eleContractName;
    }

    public void setEleContractName(String eleContractName) {
        this.eleContractName = eleContractName;
    }

    public String getSpecialEleCostCenterName() {
        return specialEleCostCenterName;
    }

    public void setSpecialEleCostCenterName(String specialEleCostCenterName) {
        this.specialEleCostCenterName = specialEleCostCenterName;
    }

    public String getSpecialEleContractCode() {
        return specialEleContractCode;
    }

    public void setSpecialEleContractCode(String specialEleContractCode) {
        this.specialEleContractCode = specialEleContractCode;
    }

    public String getSpecialEleContractName() {
        return specialEleContractName;
    }

    public void setSpecialEleContractName(String specialEleContractName) {
        this.specialEleContractName = specialEleContractName;
    }

    public String getTeleCostCenterName() {
        return teleCostCenterName;
    }

    public void setTeleCostCenterName(String teleCostCenterName) {
        this.teleCostCenterName = teleCostCenterName;
    }

    public String getTeleContractCode() {
        return teleContractCode;
    }

    public void setTeleContractCode(String teleContractCode) {
        this.teleContractCode = teleContractCode;
    }

    public String getTeleContractName() {
        return teleContractName;
    }

    public void setTeleContractName(String teleContractName) {
        this.teleContractName = teleContractName;
    }

    public String getRentCostCenterName() {
        return rentCostCenterName;
    }

    public void setRentCostCenterName(String rentCostCenterName) {
        this.rentCostCenterName = rentCostCenterName;
    }

    public String getRentContractCode() {
        return rentContractCode;
    }

    public void setRentContractCode(String rentContractCode) {
        this.rentContractCode = rentContractCode;
    }

    public String getRentContractName() {
        return rentContractName;
    }

    public void setRentContractName(String rentContractName) {
        this.rentContractName = rentContractName;
    }

    public String getSpecialRentCostCenterName() {
        return specialRentCostCenterName;
    }

    public void setSpecialRentCostCenterName(String specialRentCostCenterName) {
        this.specialRentCostCenterName = specialRentCostCenterName;
    }

    public String getSpecialRentContractCode() {
        return specialRentContractCode;
    }

    public void setSpecialRentContractCode(String specialRentContractCode) {
        this.specialRentContractCode = specialRentContractCode;
    }

    public String getSpecialRentContractName() {
        return specialRentContractName;
    }

    public void setSpecialRentContractName(String specialRentContractName) {
        this.specialRentContractName = specialRentContractName;
    }

    public String getFreeFlag() {
        return freeFlag;
    }

    public void setFreeFlag(String freeFlag) {
        this.freeFlag = freeFlag;
    }
}
