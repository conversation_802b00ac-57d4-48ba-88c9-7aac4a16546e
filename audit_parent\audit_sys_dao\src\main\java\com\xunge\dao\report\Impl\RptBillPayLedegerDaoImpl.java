package com.xunge.dao.report.Impl;

import com.xunge.dao.AbstractBaseDao;
import com.xunge.dao.report.IrptBillPayLedgerDao;
import com.xunge.model.report.RptBillPayLedgerVO;

import java.util.List;

public class RptBillPayLedegerDaoImpl extends AbstractBaseDao implements IrptBillPayLedgerDao {

    private final String Namespace = "com.xunge.mapping.RptBillPayLedgerVOMapper.";

    @Override
    public List<RptBillPayLedgerVO> queryBillaccount(RptBillPayLedgerVO rptBillPayLedgerVO) {
        return this.getSqlSession().selectList(Namespace + "queryBillaccount", rptBillPayLedgerVO);
    }

    @Override
    public List<RptBillPayLedgerVO> queryBillPayLedger(RptBillPayLedgerVO rptBillPayLedgerVO) {
        return this.getSqlSession().selectList(Namespace + "queryBillPayLedger", rptBillPayLedgerVO);
    }

    @Override
    public int queryBillaccountCount(RptBillPayLedgerVO rptBillPayLedgerVO) {
        return this.getSqlSession().selectOne(Namespace + "queryBillaccountCount", rptBillPayLedgerVO);
    }

    @Override
    public int queryBillPayLedgerCount(RptBillPayLedgerVO rptBillPayLedgerVO) {
        return this.getSqlSession().selectOne(Namespace + "queryBillPayLedgerCount", rptBillPayLedgerVO);
    }

}