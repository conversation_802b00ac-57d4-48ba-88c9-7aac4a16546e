package com.xunge.dao.app.impl;

import com.xunge.core.page.Page;
import com.xunge.dao.AbstractBaseDao;
import com.xunge.dao.app.IAppDao;
import com.xunge.filter.PageInterceptor;
import com.xunge.model.app.AppPermission;

import java.util.Map;

/**
 *
 */
public class AppDaoImpl extends AbstractBaseDao implements IAppDao {

    final String nameSpace = "com.xunge.dao.app.impl.AppDaoMapper.";

    @Override
    public int addAppPermission(Map<String, Object> params) {
        return this.getSqlSession().insert(nameSpace + "addAppPermission", params);
    }

    @Override
    public int stopServe(Map<String, Object> params) {
        return this.getSqlSession().update(nameSpace + "stopServe", params);
    }

    @Override
    public Page<AppPermission> queryAllAppPermission(Map<String, Object> params) {
        PageInterceptor.startPage(Integer.parseInt(params.get("pageNumber").toString()), Integer.parseInt(params.get("pageSize").toString()));
        this.getSqlSession().selectList(nameSpace + "queryAllAppPermission", params);
        return PageInterceptor.endPage();
    }
}
