package com.xunge.dao.selfelec;

import com.xunge.model.selfelec.EleSecondBillamount;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface EleSecondBillamountMapper {

    Map<String, Object> queryMaxCode(Map<String, Object> param);

    void insertList(@Param("eleSecondBillamounts") List<EleSecondBillamount> eleSecondBillamounts);

    void inserVerBillamounttList(@Param("eleSecondBillamounts") List<EleSecondBillamount> eleSecondBillamounts);

    Map<String, Object> queryMaxVerCode(Map<String, Object> param);

    Map<String, Object> queryMaxRentCode(Map<String, Object> param);

    void insertRentBillamountList(@Param("eleSecondBillamounts") List<EleSecondBillamount> eleSecondBillamounts);


}