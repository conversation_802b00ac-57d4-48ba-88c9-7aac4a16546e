package com.xunge.dao.account;

import com.xunge.model.system.log.TaskHistoryInfoVO;

import java.util.List;
import java.util.Map;

public interface IAccountStateDao {

    List<Map<String, Object>> getEleBillaMount(String prvId);

    List<Map<String, Object>> getTeleBillaMount(String prvId);

    List<Map<String, Object>> getRentBillamount(String prvId);

    List<Map<String, Object>> getEleVerificationBillamount(String prvId);

    List<Map<String, Object>> getEleLoanBillamount(String prvId);

    List<Map<String, Object>> getDsEleBillaMount(String prvId);

    void upEleBillaMount(Map<String, Object> map);

    void upTeleBillaMount(Map<String, Object> map);

    void upRentBillamount(Map<String, Object> map);

    void upEleVerificationBillamount(Map<String, Object> map);

    void upEleLoanBillamount(Map<String, Object> map);

    void upDsEleBillaMount(Map<String, Object> map);

    void upStatus(String taskId);

    void inLog(TaskHistoryInfoVO taskHistory);

    List<Map<String, Object>> getAccrualBillamount(String prv_id);

    void editPayment(Map<String, Object> map);
}

