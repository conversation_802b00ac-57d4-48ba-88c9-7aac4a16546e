package com.xunge.dao.report;

import java.util.List;
import java.util.Map;

public interface IrptElectricmeterDao {
    /**
     * 查询电表报表信息通过省份
     *
     * @return
     * <AUTHOR>
     */
    public List<Map<String, Object>> queryElectricmeterByPrv(Map<String, Object> map);

    /**
     * 查询电表报表信息通过地市
     *
     * @return
     * <AUTHOR>
     */
    public List<Map<String, Object>> queryElectricmeterByPreg(Map<String, Object> map);

    public List<Map<String, Object>> queryElectricmeter(Map<String, Object> map);
}