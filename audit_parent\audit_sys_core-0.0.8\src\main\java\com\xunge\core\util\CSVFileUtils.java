package com.xunge.core.util;


import com.alibaba.druid.util.StringUtils;
import lombok.extern.slf4j.Slf4j;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * @Version: 1.0.0
 * @Author: <PERSON>
 * @Email: <EMAIL>
 * @Time: 2020-04-09 10:08
 * @Description: CSV格式文件导出
 */
@Slf4j
public class CSVFileUtils {
    /**
     * 功能说明：获取UTF-8编码文本文件开头的BOM签名。
     * BOM(Byte Order Mark)，是UTF编码方案里用于标识编码的标准标记。例：接收者收到以EF BB BF开头的字节流，就知道是UTF-8编码。
     *
     * @return UTF-8编码文本文件开头的BOM签名
     */
    public static String getBOM() {

        byte b[] = {(byte) 0xEF, (byte) 0xBB, (byte) 0xBF};
        return new String(b);
    }

    /**
     * 生成CVS文件
     *
     * @param exportData 源数据List
     * @param map        csv文件的列表头map
     * @param fileName   文件名称
     * @return
     */
    @SuppressWarnings("rawtypes")
    public static File createCSVFile(List exportData, LinkedHashMap map, String filePath, String fileName) {
        File csvFile = null;
        BufferedWriter csvFileOutputStream = null;
        try {
            //定义文件名格式并创建
            File tmpZip = new File(filePath);
            if (!tmpZip.exists()) {
                tmpZip.mkdirs();
            }
            csvFile = new File(tmpZip + "/" + fileName + ".csv");
            if (!csvFile.exists()) {
                csvFile.createNewFile();
            }
            // UTF-8使正确读取分隔符","
            //如果生产文件乱码，windows下用gbk，linux用UTF-8
            csvFileOutputStream = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(
                    csvFile), "gbk"), 1024);

            //写入前段字节流，防止乱码
            //csvFileOutputStream.write(getBOM());
            // 写入文件头部
            for (Iterator propertyIterator = map.entrySet().iterator(); propertyIterator.hasNext(); ) {
                Map.Entry propertyEntry = (Map.Entry) propertyIterator.next();
                csvFileOutputStream.write((String) propertyEntry.getValue() != null ? (String) propertyEntry.getValue() : "");
                if (propertyIterator.hasNext()) {
                    csvFileOutputStream.write(",");
                }
            }
            csvFileOutputStream.newLine();
            // 写入文件内容
            for (Iterator iterator = exportData.iterator(); iterator.hasNext(); ) {
                Object row = (Object) iterator.next();
                for (Iterator propertyIterator = map.entrySet().iterator(); propertyIterator.hasNext(); ) {
                    Map.Entry propertyEntry = (Map.Entry) propertyIterator
                            .next();
                    String str = row != null ? ((String) ((Map) row).get(propertyEntry.getKey())) : "";

                    if (StringUtils.isEmpty(str)) {
                        str = "";
                    } else {
                        str = str.replaceAll("\"", "\"\"");
                        if (str.indexOf(",") >= 0) {
                            str = "\"" + str + "\"";
                        }
                        Object value = propertyEntry.getValue();
                        if ("必填备注".equals(value)){
                            //前后拼接一个"
                            str = "\"" + str + "\"";
                        }
                    }
                    csvFileOutputStream.write(str);
                    if (propertyIterator.hasNext()) {
                        csvFileOutputStream.write(",");
                    }
                }
                if (iterator.hasNext()) {
                    csvFileOutputStream.newLine();
                }
            }
            csvFileOutputStream.flush();
        } catch (Exception e) {
            log.error("CSVFileUtils 出错", e);

        } finally {
            try {
                csvFileOutputStream.close();
            } catch (IOException e) {
                log.error("CSVFileUtils 出错", e);
            }
        }
        return csvFile;
    }


    /**
     * 删除该目录filePath下的所有文件
     *
     * @param filePath 文件目录路径
     */
    public static void deleteFiles(String filePath) {
        File file = new File(filePath);
        if (file.exists()) {
            File[] files = file.listFiles();
            for (int i = 0; i < files.length; i++) {
                if (files[i].isFile()) {
                    boolean deleteResult=files[i].delete();
                    if (!deleteResult){
                        log.error("删除文件失败！");
                    }
                }
            }
        }
    }

    /**
     * 删除单个文件
     *
     * @param filePath 文件目录路径
     * @param fileName 文件名称
     */
    public static void deleteFile(String filePath, String fileName) {
        File file = new File(filePath);
        if (file.exists()) {
            File[] files = file.listFiles();
            for (int i = 0; i < files.length; i++) {
                if (files[i].isFile()) {
                    if (files[i].getName().equals(fileName)) {
                        boolean deleteResult=files[i].delete();
                        if (!deleteResult){
                            log.error("删除文件失败！");
                        }
                        return;
                    }
                }
            }
        }
    }

    public static void zipCsv(HttpServletRequest request, HttpServletResponse response, List<File> fileList, List<String> filename, String zipName) {
        ZipOutputStream zos = null;
        InputStream fis = null;
        try {
            //如果不以压缩包的形式导出，下面的代码就完全没有必要要了。上面的部分，系统会以CSV格式导出到本地。
            response.setContentType("application/zip");
            response.reset();
            response.setCharacterEncoding("GBK");
            //String userName = (String) request.getSession().getAttribute("User");
            response.setHeader("Content-Disposition", "attachment; filename=" + new String(zipName.getBytes("GBK"), "ISO8859-1") + ".zip");
            //文件压缩
            int leng = 0;
            zos = new ZipOutputStream(response.getOutputStream());
            //zos =new ZipOutputStream(new File("/tmp/upload/"+zipName+".zip"));
            //zos.setEncoding("GBK");
            File delFile = null;
            //获取用户名称
            for (int l = 0; l < fileList.size(); l++) {
                // fis = new FileInputStream(request.getRealPath("upload/")+File.separator+filename.get(l) + ".csv");
                fis = new FileInputStream(fileList.get(l));
                ZipEntry z1 = new ZipEntry(filename.get(l) + ".csv");
                //z1.setUnixMode(644);//linux 下设置，防止乱码
                // zos.setComment(zipName);
                zos.putNextEntry(z1);
                byte[] b = new byte[1024];
                while ((leng = fis.read(b)) != -1) {
                    zos.write(b, 0, leng);
                }
                fis.close();
            }
            zos.close();
        } catch (Exception e) {
            log.error("CSVFileUtils 出错", e);
        } finally {
            if (zos != null) {
                try {
                    zos.close();
                } catch (IOException e) {
                    log.error("CSVFileUtils 出错", e);
                }
                zos = null;
            }
            if (fis != null) {
                try {
                    fis.close();
                } catch (IOException e) {
                    log.error("CSVFileUtils 出错", e);
                }
                fis = null;
            }
        }

    }

}
