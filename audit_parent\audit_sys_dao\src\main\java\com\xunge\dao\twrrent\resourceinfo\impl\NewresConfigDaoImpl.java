package com.xunge.dao.twrrent.resourceinfo.impl;

import com.xunge.dao.AbstractBaseDao;
import com.xunge.dao.twrrent.resourceinfo.INewresConfigDao;
import com.xunge.model.towerrent.rentmanager.TwrNewresConfig;
import org.springframework.stereotype.Repository;

import java.util.Map;

/**
 * TODO：
 *
 * <AUTHOR>
 * @version 1.0 2018-06-27/15:32
 */
@Repository("newresConfigDao")
public class NewresConfigDaoImpl extends AbstractBaseDao implements INewresConfigDao {

    final String Namespace = "com.xunge.dao.towerrent.rentmanager.TowerNewresConfigMapper.";

    @Override
    public int startServer(Map<String, Object> param) {
        return this.getSqlSession().update(Namespace + "startServer", param);
    }

    @Override
    public TwrNewresConfig getTwrNewresConfig(String prvId) {
        return this.getSqlSession().selectOne(Namespace + "getTwrNewresConfig", prvId);
    }
}
