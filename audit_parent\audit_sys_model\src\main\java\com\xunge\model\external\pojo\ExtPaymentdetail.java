package com.xunge.model.external.pojo;

import java.io.Serializable;
import java.math.BigDecimal;

public class ExtPaymentdetail implements Serializable {

    private static final long serialVersionUID = 7695768748151070700L;
    /*主键*/
    private String id;
    /*地市*/
    private String city;                            //根据电表户号获取三费系统内地市
    /*区县*/
    private String region;                          //根据电表户号获取三费系统内区县名
    /*所属供电所*/
    private String eleCompany;
    /*用电户号*/
    private String accountNumber;
    /*用电地址*/
    private String address;
    /*抄表方式*/
    private int readMethod;                        //枚举值：0人工抄表， 1远程抄表， 2无法抄表
    /*付费类型*/
    private int payMethod;                         //枚举值：0后付费，1预付费
    /*电表类型*/
    private int meterType;                         //枚举值：0普通表，1平峰谷表
    /*报账类型*/
    private int billType;                          //枚举值：0转账，1托收
    /*票据类型*/
    private int invoiceType;                       //枚举值：0增值税普票 1收据 2增值税专票
    /*税率*/
    private BigDecimal rate;                       //如0.17，普票、收据可传0
    /*预付费类型*/
    private int loanType;                          //枚举值：0预付费，1 IC卡,是预付费时必填
    /*抄表周期*/
    private int readPeriod;                        //转换为枚举值：0按月，1按季，2按年
    /*电费月份*/
    private String eleMon;                         //根据“计费周期”取年月，YYYYMM,如201904
    /*抄表期始*/
    private String billamountStartdate;            //YYYYMMDD,如20190315
    /*抄表期终*/
    private String billamountEnddate;              //YYYYMMDD,如20190315
    /*本期读数*/
    private BigDecimal nowReadnum;                 //1、后付费：电表本期抄表读数。2、预付费：本期充值前电表读数。
    /*上期读数*/
    private BigDecimal lastReadnum;                //1、后付费：电表上期抄表读数。 2、预付费：上期充值后电表读数。
    /*表资产号*/
    private String meterCode;                      // 根据户号查找系统内电表，获取对应表资产号；也可以为电表编码，为电表的唯一标识
    /*更换后电表编码*/
    private String newMeterCode;
    /*本期度数*/
    private BigDecimal nowDegree;                  //1、如期间电表未归零，本期度数=本期读数-上期读数。 2、如期间电表归零，本期度数=本期读数+（归23零前电表读数-上期读数）。 3、预付费：本期度数=上期充值后电表读数-本期充值前电表读数。
    /*电表倍率*/
    private int meterRate;
    /*抄见电量*/
    private BigDecimal consumeDegree;              //单位:度，本期度数*电表倍率
    /*换表电量*/
    private BigDecimal changeDegree;               //单位:度
    /*退补电量*/
    private BigDecimal retrieveDegree;             //单位:度
    /*变损电量*/
    private BigDecimal tranformerLoos;             //等于页面的变损电量+线损电量，单位:度
    /*公摊电量*/
    private BigDecimal shareDegree;                // 单位:度
    /*免费电量*/
    private BigDecimal freeDegree;                 // 单位:度
    /*分表电量*/
    private BigDecimal subDegree;                  // 单位:度
    /*合计电量*/
    private BigDecimal totalDegree;                // 单位:度
    /*电价*/
    private BigDecimal degreePrice;                // 单位:元/度
    /*应付电费*/
    private BigDecimal payableDegree;              // 作为实际电费不含税+电费税金，单位：元
    /*功率因数标准*/
    private BigDecimal powerFactorStd;
    /*功率因数*/
    private BigDecimal powerFactor;
    /*调整系数*/
    private BigDecimal adjustFactor;
    /*调整电费*/
    private BigDecimal adjustFee;                  //单位：元
    /*用电容量*/
    private BigDecimal eleCapacity;
    /*基本电费单价*/
    private BigDecimal basePrice;                  //单位:元/度
    /*基本电费*/
    private BigDecimal baseFee;                    //单位：元
    /*退补电费*/
    private BigDecimal retrieveFee;                //单位：元
    /*收费金额合计*/
    private BigDecimal totalFee;                   //作为实际报账金额，单位：元
    /*其他费用*/
    private String otherFee;                       //费用名称为阶段性减免电费，税率、发票用默认值；格式如：[{费用名称1,金额1,税率1,发票类型1}{费用名称2,金额2,税率2,发票类型2}]多个其他费用用英文大括号隔开，费用明细间用英文逗号隔开。所有其他费用放在英文中括号中
    /*预留字段*/
    private String extData;                        //预留字段，用于后续接口拓展。使用时格式为json字符串。

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getRegion() {
        return region;
    }

    public void setRegion(String region) {
        this.region = region;
    }

    public String getEleCompany() {
        return eleCompany;
    }

    public void setEleCompany(String eleCompany) {
        this.eleCompany = eleCompany;
    }

    public String getAccountNumber() {
        return accountNumber;
    }

    public void setAccountNumber(String accountNumber) {
        this.accountNumber = accountNumber;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public int getReadMethod() {
        return readMethod;
    }

    public void setReadMethod(int readMethod) {
        this.readMethod = readMethod;
    }

    public int getPayMethod() {
        return payMethod;
    }

    public void setPayMethod(int payMethod) {
        this.payMethod = payMethod;
    }

    public int getMeterType() {
        return meterType;
    }

    public void setMeterType(int meterType) {
        this.meterType = meterType;
    }

    public int getBillType() {
        return billType;
    }

    public void setBillType(int billType) {
        this.billType = billType;
    }

    public int getInvoiceType() {
        return invoiceType;
    }

    public void setInvoiceType(int invoiceType) {
        this.invoiceType = invoiceType;
    }

    public BigDecimal getRate() {
        return rate;
    }

    public void setRate(BigDecimal rate) {
        this.rate = rate;
    }

    public int getLoanType() {
        return loanType;
    }

    public void setLoanType(int loanType) {
        this.loanType = loanType;
    }

    public int getReadPeriod() {
        return readPeriod;
    }

    public void setReadPeriod(int readPeriod) {
        this.readPeriod = readPeriod;
    }

    public String getEleMon() {
        return eleMon;
    }

    public void setEleMon(String eleMon) {
        this.eleMon = eleMon;
    }

    public String getBillamountStartdate() {
        return billamountStartdate;
    }

    public void setBillamountStartdate(String billamountStartdate) {
        this.billamountStartdate = billamountStartdate;
    }

    public String getBillamountEnddate() {
        return billamountEnddate;
    }

    public void setBillamountEnddate(String billamountEnddate) {
        this.billamountEnddate = billamountEnddate;
    }

    public BigDecimal getNowReadnum() {
        return nowReadnum;
    }

    public void setNowReadnum(BigDecimal nowReadnum) {
        this.nowReadnum = nowReadnum;
    }

    public BigDecimal getLastReadnum() {
        return lastReadnum;
    }

    public void setLastReadnum(BigDecimal lastReadnum) {
        this.lastReadnum = lastReadnum;
    }

    public String getMeterCode() {
        return meterCode;
    }

    public void setMeterCode(String meterCode) {
        this.meterCode = meterCode;
    }

    public String getNewMeterCode() {
        return newMeterCode;
    }

    public void setNewMeterCode(String newMeterCode) {
        this.newMeterCode = newMeterCode;
    }

    public BigDecimal getNowDegree() {
        return nowDegree;
    }

    public void setNowDegree(BigDecimal nowDegree) {
        this.nowDegree = nowDegree;
    }

    public int getMeterRate() {
        return meterRate;
    }

    public void setMeterRate(int meterRate) {
        this.meterRate = meterRate;
    }

    public BigDecimal getConsumeDegree() {
        return consumeDegree;
    }

    public void setConsumeDegree(BigDecimal consumeDegree) {
        this.consumeDegree = consumeDegree;
    }

    public BigDecimal getChangeDegree() {
        return changeDegree;
    }

    public void setChangeDegree(BigDecimal changeDegree) {
        this.changeDegree = changeDegree;
    }

    public BigDecimal getRetrieveDegree() {
        return retrieveDegree;
    }

    public void setRetrieveDegree(BigDecimal retrieveDegree) {
        this.retrieveDegree = retrieveDegree;
    }

    public BigDecimal getTranformerLoos() {
        return tranformerLoos;
    }

    public void setTranformerLoos(BigDecimal tranformerLoos) {
        this.tranformerLoos = tranformerLoos;
    }

    public BigDecimal getShareDegree() {
        return shareDegree;
    }

    public void setShareDegree(BigDecimal shareDegree) {
        this.shareDegree = shareDegree;
    }

    public BigDecimal getFreeDegree() {
        return freeDegree;
    }

    public void setFreeDegree(BigDecimal freeDegree) {
        this.freeDegree = freeDegree;
    }

    public BigDecimal getSubDegree() {
        return subDegree;
    }

    public void setSubDegree(BigDecimal subDegree) {
        this.subDegree = subDegree;
    }

    public BigDecimal getTotalDegree() {
        return totalDegree;
    }

    public void setTotalDegree(BigDecimal totalDegree) {
        this.totalDegree = totalDegree;
    }

    public BigDecimal getDegreePrice() {
        return degreePrice;
    }

    public void setDegreePrice(BigDecimal degreePrice) {
        this.degreePrice = degreePrice;
    }

    public BigDecimal getPayableDegree() {
        return payableDegree;
    }

    public void setPayableDegree(BigDecimal payableDegree) {
        this.payableDegree = payableDegree;
    }

    public BigDecimal getPowerFactorStd() {
        return powerFactorStd;
    }

    public void setPowerFactorStd(BigDecimal powerFactorStd) {
        this.powerFactorStd = powerFactorStd;
    }

    public BigDecimal getPowerFactor() {
        return powerFactor;
    }

    public void setPowerFactor(BigDecimal powerFactor) {
        this.powerFactor = powerFactor;
    }

    public BigDecimal getAdjustFactor() {
        return adjustFactor;
    }

    public void setAdjustFactor(BigDecimal adjustFactor) {
        this.adjustFactor = adjustFactor;
    }

    public BigDecimal getAdjustFee() {
        return adjustFee;
    }

    public void setAdjustFee(BigDecimal adjustFee) {
        this.adjustFee = adjustFee;
    }

    public BigDecimal getEleCapacity() {
        return eleCapacity;
    }

    public void setEleCapacity(BigDecimal eleCapacity) {
        this.eleCapacity = eleCapacity;
    }

    public BigDecimal getBasePrice() {
        return basePrice;
    }

    public void setBasePrice(BigDecimal basePrice) {
        this.basePrice = basePrice;
    }

    public BigDecimal getBaseFee() {
        return baseFee;
    }

    public void setBaseFee(BigDecimal baseFee) {
        this.baseFee = baseFee;
    }

    public BigDecimal getRetrieveFee() {
        return retrieveFee;
    }

    public void setRetrieveFee(BigDecimal retrieveFee) {
        this.retrieveFee = retrieveFee;
    }

    public BigDecimal getTotalFee() {
        return totalFee;
    }

    public void setTotalFee(BigDecimal totalFee) {
        this.totalFee = totalFee;
    }

    public String getOtherFee() {
        return otherFee;
    }

    public void setOtherFee(String otherFee) {
        this.otherFee = otherFee;
    }

    public String getExtData() {
        return extData;
    }

    public void setExtData(String extData) {
        this.extData = extData;
    }
}