package com.xunge.dao.selfelec.accrual;

import java.time.LocalDate;
import java.util.Date;
import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.xunge.model.selfelec.accrual.AccrualoffsetSucclog;

public interface AccrualoffsetSucclogMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(AccrualoffsetSucclog record);

    int insertSelective(AccrualoffsetSucclog record);

    AccrualoffsetSucclog selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(AccrualoffsetSucclog record);

    int updateByPrimaryKey(AccrualoffsetSucclog record);

	/**获取全量任务时日期最大一条
	 * @param code
	 * @return
	 */
	Date getInitMaxFinanceDayByAllCode(String type);

	/**根据类型获取大于等于日期得数据
	 * @param beforeDate
	 * @param code
	 * @return
	 */
	List<Date> getFinanceDateByTypeAndDate(@Param("date")LocalDate beforeDate, @Param("type")String code);
}