package com.xunge.model.finance.ext.accClaim.accwsdl;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import java.util.ArrayList;
import java.util.List;


/**
 * &lt;p&gt;WITHDRAWAL_LINE complex type的 Java 类。
 * <p>
 * &lt;p&gt;以下模式片段指定包含在此类中的预期内容。
 * <p>
 * &lt;pre&gt;
 * &amp;lt;complexType name="WITHDRAWAL_LINE"&amp;gt;
 * &amp;lt;complexContent&amp;gt;
 * &amp;lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&amp;gt;
 * &amp;lt;sequence&amp;gt;
 * &amp;lt;element name="WITHDRAWAL_LINE_ITEM" type="{http://soa.cmcc.com/OSB_RBS_CMF_HQ_ImportBatchAccruedClaimDocSrv}WITHDRAWAL_LINE_ITEM" maxOccurs="unbounded" minOccurs="0"/&amp;gt;
 * &amp;lt;/sequence&amp;gt;
 * &amp;lt;/restriction&amp;gt;
 * &amp;lt;/complexContent&amp;gt;
 * &amp;lt;/complexType&amp;gt;
 * &lt;/pre&gt;
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "WITHDRAWAL_LINE", propOrder = {
        "withdrawallineitem"
})
public class WITHDRAWALLINE {

    @XmlElement(name = "WITHDRAWAL_LINE_ITEM")
    protected List<WITHDRAWALLINEITEM> withdrawallineitem;

    public void setWithdrawallineitem(List<WITHDRAWALLINEITEM> withdrawallineitem) {
        this.withdrawallineitem = withdrawallineitem;
    }

    /**
     * Gets the value of the withdrawallineitem property.
     * <p>
     * &lt;p&gt;
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a &lt;CODE&gt;set&lt;/CODE&gt; method for the withdrawallineitem property.
     * <p>
     * &lt;p&gt;
     * For example, to add a new item, do as follows:
     * &lt;pre&gt;
     * getWITHDRAWALLINEITEM().add(newItem);
     * &lt;/pre&gt;
     * <p>
     * <p>
     * &lt;p&gt;
     * Objects of the following type(s) are allowed in the list
     * {@link WITHDRAWALLINEITEM }
     */


    public List<WITHDRAWALLINEITEM> getWITHDRAWALLINEITEM() {
        if (withdrawallineitem == null) {
            withdrawallineitem = new ArrayList<WITHDRAWALLINEITEM>();
        }
        return this.withdrawallineitem;
    }

}
