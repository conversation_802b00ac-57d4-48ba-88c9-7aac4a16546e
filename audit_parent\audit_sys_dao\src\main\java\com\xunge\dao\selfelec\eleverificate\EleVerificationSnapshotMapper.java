package com.xunge.dao.selfelec.eleverificate;

import com.xunge.model.selfelec.eleverificate.EleVerificationSnapshot;

/**
* <AUTHOR>
* @description 针对表【ele_verification_snapshot(核销快照表)】的数据库操作Mapper
* @createDate 2024-10-09 11:00:33
* @Entity generator.domain.EleVerificationSnapshot
*/
public interface EleVerificationSnapshotMapper {

    int deleteByPrimaryKey(Long id);
    int deleteByVerificationId(String verificationId);

    int insert(EleVerificationSnapshot record);

    int insertSelective(EleVerificationSnapshot record);

    EleVerificationSnapshot selectByPrimaryKey(Long id);

    EleVerificationSnapshot selectByVerificationId(String verificationId);

    int updateByPrimaryKeySelective(EleVerificationSnapshot record);

    int updateByPrimaryKey(EleVerificationSnapshot record);

}
