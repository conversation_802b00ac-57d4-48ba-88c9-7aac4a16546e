package com.xunge.dao.selfelec.elecaudit;

import com.xunge.core.page.Page;
import com.xunge.model.selfelec.vo.HistoryPaymentModelVO;

import java.util.List;
import java.util.Map;

public interface IHistoryPaymentModelDao {
    public Page<HistoryPaymentModelVO> queryAll(Map<String, Object> paramMap);

    public List<HistoryPaymentModelVO> queryAllNoPage(Map<String, Object> paramMap);

    public void export();
}
