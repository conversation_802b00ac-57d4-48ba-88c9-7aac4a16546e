package com.xunge.dao.sysSmsSendHistroy.impl;

import com.xunge.dao.AbstractBaseDao;
import com.xunge.dao.sysSmsSendHistroy.ISysSmsConfigDao;
import com.xunge.model.smsSendHistroy.SysSmsConfigVO;

import java.util.Map;

public class SysSmsConfigDaoImpl extends AbstractBaseDao implements ISysSmsConfigDao {

    final String Namespace = "com.xunge.dao.SysSmsConfigVOMapper.";

    @Override
    public SysSmsConfigVO querySmsModelMsg(Map<String, Object> paraMap) {
        return this.getSqlSession().selectOne(Namespace + "querySmsModelMsg", paraMap);
    }

    @Override
    public String queryOrderCode(Map<String, String> param) {
        return this.getSqlSession().selectOne(Namespace + "queryOrderCode", param);
    }

    @Override
    public String queryOrderCodeOnRentContract(Map<String, String> param) {
        return this.getSqlSession().selectOne(Namespace + "queryOrderCodeOnRentContract", param);
    }
}
