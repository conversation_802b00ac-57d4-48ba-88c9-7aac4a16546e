package com.xunge.dao.selfelec.accrual;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

import com.alibaba.fastjson.JSONObject;
import com.xunge.model.activity.ActHistoicFlow;
import com.xunge.model.selfelec.accrual.AccrualAmount;
import com.xunge.model.selfelec.accrual.AccrualParam;
import com.xunge.model.selfelec.accrual.EleAccrualRegAmount;


public interface AccrualConfigMapper {

	AccrualParam getAccrualParam(Map<String, Object> map);

	void insertParam(AccrualParam accrualParam);

	void updateParam(AccrualParam accrualParam);

	List<AccrualAmount> queryList(Map<String, Object> map);

	List<AccrualAmount> queryPreg(Map<String, Object> map);

	List<AccrualAmount> queryReg(Map<String, Object> map);

	void updateAmountConfig(AccrualAmount accrualAmount);

	List<EleAccrualRegAmount> queryAmount(List<JSONObject> list);

	List<ActHistoicFlow> querExisitAudit(String accrualId);

	void updateAccrualAuditInfo(ActHistoicFlow ahf);

	void insertAccrualAuditInfo(ActHistoicFlow newAf);

	void deleteAccrualAudit(String accrualId);

	BigDecimal getAmortization(@Param("pregId")String pregId, @Param("regId")String regId, @Param("year")Integer year, @Param("month")Integer month);

	List<EleAccrualRegAmount> queryAmountByPreg(List<String> list);

	List<EleAccrualRegAmount> getAccrualRegAmount(String prvId);

	BigDecimal queryAccrualAmount(Map<String, Object> amountMap);

}
