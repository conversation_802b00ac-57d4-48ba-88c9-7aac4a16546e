package com.xunge.model.external.entity;


import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

import java.io.Serializable;

/**
 * @Description 签名基础类
 * <AUTHOR>
 * @Date 2021/1/26
 * @modifier ZXX
 * @date 2021/1/26
 * @Version 1.0
 **/
@SuppressWarnings("deprecation")
@Data
public class BaseSignRo implements Serializable {
    private static final long serialVersionUID = 8126572563688838556L;
    //签名
    @NotEmpty(message = "签名不能为空")
    private String sign;

    //密钥ID
    @NotEmpty(message = "密钥ID不能为空")
    private String clientId;

    //访问时间
    // @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    // @JsonDeserialize(using = DateJsonDeserializer.class)
    @NotEmpty(message = "访问时间不能为空")
    private String accessDate;

    //省市code
    @NotEmpty(message = "省市code不能为空")
    private String prvCode;
}
