package com.xunge.model.activity;

import java.io.Serializable;
import java.util.Date;

/**
 * 工作流Entity
 *
 * <AUTHOR>
 * @version 2013-11-03
 */
public class SuperAct implements Serializable, Comparable<SuperAct> {

    private String taskId;
    private String comment; // 任务意见
    private Date startTime;//提交审核时间
    private Date endTime;//审核时间

    private String PROC_INST_ID_;
    private String assigneeName; // 上审核人
    private String ACT_TYPE_;
    private String auditLink;//审核环节

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public String getPROC_INST_ID_() {
        return PROC_INST_ID_;
    }

    public void setPROC_INST_ID_(String PROC_INST_ID_) {
        this.PROC_INST_ID_ = PROC_INST_ID_;
    }

    public String getAssigneeName() {
        return assigneeName;
    }

    public void setAssigneeName(String assigneeName) {
        this.assigneeName = assigneeName;
    }

    public String getACT_TYPE_() {
        return ACT_TYPE_;
    }

    public void setACT_TYPE_(String ACT_TYPE_) {
        this.ACT_TYPE_ = ACT_TYPE_;
    }

    public String getAuditLink() {
        return auditLink;
    }

    public void setAuditLink(String auditLink) {
        this.auditLink = auditLink;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    @Override
    public int compareTo(SuperAct o) {
        return this.getStartTime().compareTo(o.getStartTime());
    }

}
