package com.xunge.model.activity;

import cn.afterturn.easypoi.excel.annotation.Excel;

import java.io.Serializable;

/**
 * @ClassName MyWorkExportVo
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/3/10 14:16
 */
public class MyWorkExportVo implements Serializable {
    private static final long serialVersionUID = 7231730481864028595L;
    @Excel(name = "序号", orderNum = "0")
    private Integer serialNum;
    @Excel(name = "工单类别", orderNum = "1", replace = {"-_null"})
    private String title;
    @Excel(name = "工单名称", orderNum = "2", replace = {"-_null"})
    private String name;
    @Excel(name = "审核环节", orderNum = "3", replace = {"-_null"})
    private String superAuditLink;
    @Excel(name = "上级审核人", orderNum = "4", replace = {"-_null"})
    private String superiorAssigneeName;
    @Excel(name = "上级审核意见", orderNum = "5", replace = {"-_null"})
    private String comment;
    @Excel(name = "审核时间", orderNum = "6", replace = {"-_null"})
    private String superiorAssigneeTime;
    @Excel(name = "产生时间", orderNum = "7", replace = {"-_null"})
    private String beginDate;
    @Excel(name = "审核状态", orderNum = "8", replace = {"未提交_-1", "审核中_9", "审核不通过_8", "审核通过_0", "-_null"})
    private String status;

    public Integer getSerialNum() {
        return serialNum;
    }

    public void setSerialNum(Integer serialNum) {
        this.serialNum = serialNum;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getSuperAuditLink() {
        return superAuditLink;
    }

    public void setSuperAuditLink(String superAuditLink) {
        this.superAuditLink = superAuditLink;
    }

    public String getSuperiorAssigneeName() {
        return superiorAssigneeName;
    }

    public void setSuperiorAssigneeName(String superiorAssigneeName) {
        this.superiorAssigneeName = superiorAssigneeName;
    }

    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }

    public String getSuperiorAssigneeTime() {
        return superiorAssigneeTime;
    }

    public void setSuperiorAssigneeTime(String superiorAssigneeTime) {
        this.superiorAssigneeTime = superiorAssigneeTime;
    }

    public String getBeginDate() {
        return beginDate;
    }

    public void setBeginDate(String beginDate) {
        this.beginDate = beginDate;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
}
