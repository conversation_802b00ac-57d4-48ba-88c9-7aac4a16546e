package com.xunge.comm.utils;

import com.xunge.comm.FilePathEnum;
import com.xunge.comm.ProvinceEnum;
import com.xunge.comm.system.PromptMessageComm;
import com.xunge.core.exception.BusinessException;
import com.xunge.core.util.PropertiesLoader;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.concurrent.atomic.AtomicReference;

/**
 * 文件路径工具类，用于生成各种业务场景的文件路径
 * 静态工具类设计，直接通过类名调用方法
 * 根路径从配置文件读取，支持自定义设置
 */
@Component
public class FilePathUtils {

    private static final Logger log = LoggerFactory.getLogger(FilePathUtils.class);

    // 使用AtomicReference保证线程安全
    private static final AtomicReference<String> ROOT_PATH = new AtomicReference<>();


    // 根路径，从配置文件读取
    private static String configRootPath;

    // 静态初始化块，加载根路径配置
    static {
        try {
            PropertiesLoader prop = new PropertiesLoader(PromptMessageComm.URL_SYSCONFIG);
            configRootPath = prop.getProperty("UploadUrls");
            if (configRootPath == null || configRootPath.isEmpty()) {
                throw new BusinessException("无法从配置文件获取UploadUrls路径");
            }
            ROOT_PATH.set(configRootPath);
            log.info("文件路径根目录初始化: {}", configRootPath);
        } catch (Exception e) {
            throw new RuntimeException("初始化文件路径工具失败: " + e.getMessage(), e);
        }
    }


    // 私有构造函数，防止手动实例化
    private FilePathUtils() {
        // 工具类应当通过Spring容器管理
    }

    /**
     * 设置自定义根路径（用于测试或特殊场景）
     *
     * @param customRootPath 自定义根路径
     */
    public static void setRootPath(String customRootPath) {
        if (customRootPath != null && !customRootPath.isEmpty()) {
            ROOT_PATH.set(customRootPath);
            log.info("文件路径根目录已设置为: {}", customRootPath);
        } else {
            log.warn("尝试设置空的根路径，操作被忽略");
        }
    }

    /**
     * 获取当前根路径
     *
     * @return 当前设置的根路径
     * @throws IllegalStateException 如果根路径未初始化
     */
    public static String getRootPath() {
        String path = ROOT_PATH.get();
        if (path == null || path.isEmpty()) {
            throw new IllegalStateException("根路径未初始化，请确保Spring容器已启动");
        }
        return path;
    }

    /**
     * 静态方法，获取完整的文件路径
     * 将根路径、枚举固定路径和可变参数部分组合成完整路径
     *
     * 调用示例:
     * FilePathUtils.getPath(FilePathEnum.FILES, "GD", "202405");
     *
     * @param uploadPath 路径枚举，提供固定的基础路径部分
     * @param pathParts 可变参数，如省份编码、日期等动态路径部分，按传入顺序拼接
     * @return 完整的文件系统路径字符串
     * @throws IllegalStateException 如果根路径未初始化
     */
    public static String getPath(FilePathEnum uploadPath, String... pathParts) {
        // 获取根路径（会检查是否初始化）
        String rootPathStr = getRootPath();

        // 构建路径
        Path result = Paths.get(rootPathStr);

        // 添加固定路径部分
        String basePathStr = uploadPath.getBasePath();
        if (basePathStr != null && !basePathStr.isEmpty()) {
            result = result.resolve(basePathStr);
        }

        // 按顺序添加所有路径部分
        if (pathParts != null) {
            for (String part : pathParts) {
                if (part != null && !part.isEmpty()) {
                    result = result.resolve(part);
                }
            }
        }

        return result.toString();
    }

    /**
     * 结构化路径的方法，专门处理包含省份和日期的场景
     * 将根路径、枚举固定路径和可变参数部分组合成完整路径
     *
     * 调用示例:
     * FilePathUtils.getStructuredPath(FilePathEnum.FILES, "GD", "202405", "subfolder", "filename.txt");
     *
     * @param uploadPath 路径枚举，提供固定的基础路径部分
     * @param prvCode 省份编码或省份ID，可以为空
     * @param date 日期，可以为空
     * @param pathParts 可变参数，其他动态路径部分，按传入顺序拼接
     * @return 完整的文件系统路径字符串
     * @throws IllegalArgumentException 如果省份编码或省份ID无效
     * @throws IllegalStateException 如果根路径未初始化
     */
    public static String getStructuredPath(FilePathEnum uploadPath, String prvCode, String date, String... pathParts) {
        // 获取根路径（会检查是否初始化）
        String rootPathStr = getRootPath();

        // 处理省份代码或省份ID
        if (prvCode != null && !prvCode.isEmpty()) {
            // 检查是否为省份ID（6位数字）
            if (prvCode.matches("\\d{6}")) {
                // 检查是否为有效的省份ID
                if (ProvinceEnum.isValidId(prvCode)) {
                    // 转换为省份代码
                    prvCode = ProvinceEnum.getCodeById(prvCode);
                } else {
                    throw new IllegalArgumentException("无效的省份ID: " + prvCode);
                }
            }
            // 检查是否为有效的省份代码
            else if (!ProvinceEnum.isValidCode(prvCode)) {
                throw new IllegalArgumentException("无效的省份代码: " + prvCode);
            }
        }

        // 构建路径
        Path result = Paths.get(rootPathStr);

        // 添加固定路径部分
        String basePathStr = uploadPath.getBasePath();
        if (basePathStr != null && !basePathStr.isEmpty()) {
            result = result.resolve(basePathStr);
        }

        // 添加省份编码（如果不为空）
        if (prvCode != null && !prvCode.isEmpty()) {
            result = result.resolve(prvCode);
        }

        // 添加日期（如果不为空）
        if (date != null && !date.isEmpty()) {
            result = result.resolve(date);
        }

        // 按顺序添加其他路径部分
        if (pathParts != null) {
            for (String part : pathParts) {
                if (part != null && !part.isEmpty()) {
                    result = result.resolve(part);
                }
            }
        }

        return result.toString();
    }

    /**
     * 检查并创建指定文件路径的目录
     * 如果目录不存在，则递归创建
     *
     * @param filePath 文件或目录的完整路径
     * @throws RuntimeException 如果目录创建失败
     */
    public static void checkAndCreateFilePath(String filePath) {
        if (filePath == null || filePath.isEmpty()) {
            throw new IllegalArgumentException("文件路径不能为空");
        }

        Path path = Paths.get(filePath);
        Path parentDir = path.getParent();

        // 如果没有父目录（例如根目录），则无需创建
        if (parentDir == null) {
            log.debug("路径没有父目录，无需创建: {}", filePath);
            return;
        }

        // 检查父目录是否存在，不存在则创建
        if (!Files.exists(parentDir)) {
            try {
                Files.createDirectories(parentDir); // 自动递归创建父目录
                log.info("目录创建成功: {}", parentDir);
            } catch (IOException e) {
                String errorMsg = String.format("创建目录失败: %s", parentDir);
                log.error(errorMsg, e);
                throw new RuntimeException(errorMsg, e);
            }
        }

    }

    /**
     * 检查并创建目录（不是文件）
     * 如果目录不存在，则递归创建
     *
     * @param dirPath 目录的完整路径
     * @throws RuntimeException 如果目录创建失败
     */
    public static void checkAndCreateDirPath(String dirPath) {
        if (dirPath == null || dirPath.isEmpty()) {
            throw new IllegalArgumentException("目录路径不能为空");
        }

        Path path = Paths.get(dirPath);

        // 检查目录是否存在，不存在则创建
        if (!Files.exists(path)) {
            try {
                Files.createDirectories(path); // 自动递归创建目录
                log.info("目录创建成功: {}", path);
            } catch (IOException e) {
                String errorMsg = String.format("创建目录失败: %s", path);
                log.error(errorMsg, e);
                throw new RuntimeException(errorMsg, e);
            }
        } else if (!Files.isDirectory(path)) {
            // 路径存在但不是目录
            String errorMsg = String.format("路径已存在但不是目录: %s", path);
            log.error(errorMsg);
            throw new IllegalArgumentException(errorMsg);
        }
    }
} 