package com.xunge.dao.report.Impl;

import com.xunge.dao.AbstractBaseDao;
import com.xunge.dao.report.IRptRentExecutionProgressDao;
import com.xunge.model.report.RptRentExecutionProgressVO;

import java.util.List;
import java.util.Map;

/**
 * @创建人 LiangCheng
 * @创建时间 2018/12/27 0027
 * @描述：
 */
public class RptRentExecutionProgressDaoImpl extends AbstractBaseDao implements IRptRentExecutionProgressDao {

    final String Namespace = "com.xunge.mapping.report.RptRentExecutionProgressVOMapper.";

    @Override
    public List<RptRentExecutionProgressVO> queryRentExecutionProgressProvince(Map<String, Object> map) {
        return this.getSqlSession().selectList(Namespace + "queryRentExecutionProgressProvince", map);
    }

    @Override
    public List<RptRentExecutionProgressVO> queryRentExecutionProgressCity(Map<String, Object> map) {
        return this.getSqlSession().selectList(Namespace + "queryRentExecutionProgressCity", map);
    }

    @Override
    public List<RptRentExecutionProgressVO> queryRentExecutionProgressGroup(Map<String, Object> map) {
        return this.getSqlSession().selectList(Namespace + "queryRentExecutionProgressGroup", map);
    }

    @Override
    public List<RptRentExecutionProgressVO> queryRentPayableExecutionProgressProvince(Map<String, Object> map) {
        return this.getSqlSession().selectList(Namespace + "queryRentPayableExecutionProgressProvince", map);
    }

    @Override
    public List<RptRentExecutionProgressVO> queryRentPayableExecutionProgressCity(Map<String, Object> map) {
        return this.getSqlSession().selectList(Namespace + "queryRentPayableExecutionProgressCity", map);
    }

    @Override
    public List<RptRentExecutionProgressVO> queryRentPayableExecutionProgressGroup(Map<String, Object> map) {
        return this.getSqlSession().selectList(Namespace + "queryRentPayableExecutionProgressGroup", map);
    }

}
