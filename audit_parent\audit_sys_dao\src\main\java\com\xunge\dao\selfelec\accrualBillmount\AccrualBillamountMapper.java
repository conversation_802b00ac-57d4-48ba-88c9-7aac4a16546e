package com.xunge.dao.selfelec.accrualBillmount;

import com.xunge.model.selfelec.accrualBillamount.AccrualBillamount;
import com.xunge.model.selfelec.accrualBillamount.AccrualSecondBillamount;
import com.xunge.model.selfelec.accrualBillamount.EleAccrualoffsetHistory;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

public interface AccrualBillamountMapper {
    
    int deleteByPrimaryKey(AccrualBillamount key);

    
    int insert(AccrualBillamount record);

    
    int insertSelective(AccrualBillamount record);

    
    AccrualBillamount selectByPrimaryKey(AccrualBillamount key);

    
    int updateByPrimaryKeySelective(AccrualBillamount record);

    
    int updateByPrimaryKey(AccrualBillamount record);

    List<AccrualBillamount> queryBillamountPage(Map<String, Object> paraMap);

    List<AccrualBillamount> queryAccrualNum(List<String> list);

    List<AccrualBillamount> queryAccrualHisAmount(List<String> list);

    List<AccrualBillamount> queryClaimType(List<String> list);

    List<AccrualBillamount> queryBillamountMainPage(Map<String, Object> paraMap);

    List<AccrualBillamount> queryBillamountMainPage1(Map<String, Object> paraMap);

    int deleteByPrimaryKeys(List<String> accrualIdList);

    int updateEleAccrualPushStateByBillamountId(Map<String, Object> map);

    int deleteSecondBillamount(Map<String, Object> map);

    int updateEleAccrualPushStateByBillamountIdSnapShot(Map<String, Object> map);

    int updateBillamountState(@Param("billamountId") String billamountId,
                              @Param("billType") int billType,
                              @Param("billamountCode") String billamountCode);

    List<AccrualSecondBillamount> selectByBillAmountId(@Param("billamountId") String billamountId);

    int updateSecondBillamountByBillamountId(List<AccrualSecondBillamount> accrualSecondBillamounts);

    List<EleAccrualoffsetHistory> selectHisByBillAmountId(@Param("billamountId") String billamountId);

    int updateHisBillamountByBillamountId(EleAccrualoffsetHistory eleAccrualoffsetHistory);


	List<AccrualBillamount> querySecondCount(List<String> queryList2);

    /**
     * 更新二次汇总单财务计提行
     * @param paramMap
     * @return
     */
    int updateAccrualSecondBillamount(Map<String,Object> paramMap);

    /**
     * 更新历史汇总单财务计提行
     * @param paramMap
     * @return
     */
    int updateAccrualBillamountHis(Map<String,Object> paramMap);

    /**
     * 更新计提汇总单报账公司编码
     * @param paramMap
     * @return
     */
    int updateAccrualBillamountCompany(Map<String,Object> paramMap);


    List<AccrualBillamount> selectBillamountByIds(Map<String, Object> cond);

    int updateFinanceAmountEle(@Param("accountsummaryCode") String accountsummaryCode,
                               @Param("parentExpenseLineId") String parentExpenseLineId,
                               @Param("financeAmount") BigDecimal financeAmount,
                               @Param("reverseAmount") BigDecimal reverseAmount);

    int updateFinanceAmountHisEle(@Param("accountsummaryCode") String accountsummaryCode,
                                  @Param("parentExpenseLineId") String parentExpenseLineId,
                                  @Param("financeAmount") BigDecimal financeAmount,
                                  @Param("reverseAmount") BigDecimal reverseAmount);

    int updateEleAccrualBillamount(@Param("billamountCode") String billamountCode,
                                   @Param("offsDate") Date offsDate,
                                   @Param("offsAmount") BigDecimal offsAmount,
                                   @Param("leftOffsAmount") BigDecimal leftOffsAmount);


	List<AccrualBillamount> querySecondNum(List<String> list);


	/**
	* 保存5g随e签配置信息
	* <AUTHOR>   
	* @date 2024年11月13日 上午9:30:01 
	* @param sign 
	 */
	void save5gSign(AccrualBillamount sign);


	/** 
	* @Description: 查询5g随e签配置
	* <AUTHOR>   
	* @date 2024年11月13日 下午2:11:43 
	* @param sign
	* @return  
	*/ 
	AccrualBillamount query5gSign(AccrualBillamount sign);


	/** 
	* @Description: 更新5g随e签配置
	* <AUTHOR>   
	* @date 2024年11月13日 下午2:12:09 
	* @param sign  
	*/ 
	void update5gSign(AccrualBillamount sign);


	/** 
	* @Description: 根据报账人查询最新签署信息
	* <AUTHOR>   
	* @date 2024年11月14日 上午9:40:11 
	* @param oprUserId
	* @return  
	*/ 
	AccrualBillamount queryLastSignType(String oprUserId);
}