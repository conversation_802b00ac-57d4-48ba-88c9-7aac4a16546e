package com.xunge.dao.monthlyreportprocess.impl;

import com.xunge.dao.AbstractBaseDao;
import com.xunge.dao.monthlyreportprocess.MonthlyReportProcessDao;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public class MonthlyReportProcessD<PERSON><PERSON>mpl extends AbstractBaseDao implements MonthlyReportProcessDao {

    final String Namespace = "com.xunge.mapping.MonthlyReportProcessMapper.";

    @Override
    public Integer updateKeyIndexCope(Map<String, Object> paraMap) {
        return this.getSqlSession().insert(Namespace + "updateKeyIndexCope", paraMap);
    }

    @Override
    public Integer updateKeyIndexAm(Map<String, Object> paraMap) {
        return this.getSqlSession().insert(Namespace + "updateKeyIndexAm", paraMap);
    }

    @Override
    public Integer deleteRentKeyIndex(Map<String, Object> paraMap) {
        return this.getSqlSession().delete(Namespace + "deleteRentKeyIndex", paraMap);
    }

    @Override
    public Integer insertKeyIndexCope(List<Map<String, Object>> listMap) {
        return this.getSqlSession().insert(Namespace + "insertKeyIndexCope", listMap);
    }

    @Override
    public Integer updateKeyIndexCopeNew(Map<String, Object> paraMap) {
        return this.getSqlSession().update(Namespace + "updateKeyIndexCopeNew", paraMap);
    }

    @Override
    public Integer updateKeyIndexAmNew(Map<String, Object> paraMap) {
        return this.getSqlSession().update(Namespace + "updateKeyIndexAmNew", paraMap);
    }
}
