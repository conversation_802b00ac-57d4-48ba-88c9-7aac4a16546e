package com.xunge.dao.elecbill;

import com.xunge.model.selfrent.billamount.RentBillamountVO;

import java.util.List;
import java.util.Map;

public interface RentBillamountVOMapper {
    int deleteByPrimaryKey(String billamountId);

    int insert(RentBillamountVO record);

    int insertSelective(RentBillamountVO record);

    RentBillamountVO selectByPrimaryKey(String billamountId);

    int updateByPrimaryKeySelective(RentBillamountVO record);

    int updateByPrimaryKey(RentBillamountVO record);

    boolean updateStatus(RentBillamountVO record);

    boolean updateStatusJob(com.xunge.model.selfrent.billamount.RentBillamountVO record);

    int updateTwrPushdState(Map<String, Object> map);

    com.xunge.model.selfrent.billamount.RentBillamountVO selectByCode(String billamountCode);

    List<String> queryActivityDistinctListByBillamountId(String billamountId);

}