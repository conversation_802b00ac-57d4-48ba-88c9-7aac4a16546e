package com.xunge.core.aop;

import com.xunge.core.Interceptor.TenantContextHolder;
import com.xunge.core.exception.BusinessException;
import com.xunge.core.model.UserLoginInfo;

import com.xunge.core.util.MD5Util;
import com.xunge.core.util.PropertiesLoader;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.data.redis.core.Cursor;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ScanOptions;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;

import javax.servlet.http.HttpSession;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * Created by mark on 16/11/29.
 */
@Aspect
@Component
@Order(1)
@Slf4j
public class RedisAspect {

    @Autowired
    private RedisTemplate<String,Object> redisTemplate;

    public static final Integer defaultTTL;
    /**
     * 分隔符 生成key 格式为 类全类名:方法名:参数所属类全类名
     **/
    private static final String DELIMITER = ":";
    /**
     * 前缀
     */
    private static final String PREFIX = "serviceCache:";

    static {
        PropertiesLoader prop = new PropertiesLoader("\\properties\\redis.properties");
        defaultTTL = Integer.parseInt(prop.getProperty("RedisCacheDefaultTTL"));
    }

    /**
     * Service层切点 使用到了我们定义的 RedisCache 作为切点表达式。 而且我们可以看出此表达式基于 annotation。 并且用于内建属性为查询的方法之上
     */
    @Pointcut("@annotation(com.xunge.core.annotaion.redis.RedisCache)")
    public void redisCacheAspect() {
    }

    /**
     * Service层切点 使用到了我们定义的 RedisEvict 作为切点表达式。 而且我们可以看出此表达式是基于 annotation 的。
     * 并且用于内建属性为非查询的方法之上，用于更新表
     */
    @Pointcut("@annotation(com.xunge.core.annotaion.redis.RedisEvict)")
    public void redisCacheEvict() {
    }

    @Around("redisCacheAspect()")
    public Object cache(ProceedingJoinPoint joinPoint) {
        // 得到类名、方法名和参数
        String clazzName = joinPoint.getTarget().getClass().getSimpleName();
        String methodName = joinPoint.getSignature().getName();
        Object[] args = joinPoint.getArgs();

        // 根据类名、方法名和参数生成Key
        String redisKey = getKey(clazzName, methodName, args);

        Object obj = null;
        try {
            UserLoginInfo user = null;
            RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
            if (requestAttributes != null) {
                HttpSession session = (HttpSession) requestAttributes.resolveReference("session");
                if (session != null) {
                    user = (UserLoginInfo) session.getAttribute("user");
                }
            }
            if (user == null){
                //如果是还没登录，系统在登录的时候有查字典表，这时候session用户是空的，要查数据库
                log.debug("**********用户登录，不从Redis查到数据，开始从MySQL查询数据**********");
                obj = joinPoint.proceed();
                return obj;
            }
            String prvCode = user.getPrv_code();
            //分省缓存
            redisKey = prvCode + ":" + redisKey;
            log.info("生成key: " + redisKey);
            obj = redisTemplate.opsForValue().get(redisKey);
//            obj = strjedis.getSerialize(redisKey.getBytes());

            if (obj != null) {
                log.debug("**********从Redis中查到了数据**********");
                log.info("------->从Redis中查到了数据:" + redisKey);
                return obj;
            }
            log.debug("**********没有从Redis查到数据，开始从MySQL查询数据**********");
            obj = joinPoint.proceed();
            //后置：将数据库查到的数据保存到Redis
            redisTemplate.opsForValue().set(redisKey,obj,defaultTTL, TimeUnit.SECONDS);
//            String code = strjedis.setEx(redisKey, defaultTTL, obj);
            log.info("------->更新了REDIS的KEY值:" + redisKey);
        } catch (Throwable e) {
            log.error("RedisAspect 出错", e);
            if (e.getCause() != null && e.getCause().getMessage().contains("BusinessException")) {
                if (e.getCause().getMessage() != null) {
                    String errmsg = e.getCause().getMessage();
                    throw new BusinessException(errmsg.substring(errmsg.lastIndexOf(':') + 1, errmsg.length()));
                } else {
                    throw new BusinessException(e.getMessage());
                }
            }
            throw new BusinessException(e.getMessage());
        }
        return obj;
    }

    /**
     * * 在方法调用前清除缓存，然后调用业务方法 * @param joinPoint * @return * @throws Throwable
     */
    @Around("redisCacheEvict()")
    public Object evictCache(ProceedingJoinPoint joinPoint) throws Throwable {
        try {
            UserLoginInfo user = null;
            RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
            if (requestAttributes != null) {
                HttpSession session = (HttpSession) requestAttributes.resolveReference("session");
                if (session != null) {
                    user = (UserLoginInfo) session.getAttribute("user");
                }
            }
            //自己系统用session中的省份，外部调用（app调用）用数据源中的省份
            String prvCode = user == null ? TenantContextHolder.getTenant() : user.getPrv_code();

//            JedisUtil jedisUtil = JedisUtil.getInstance();
//            JedisUtil.Keys strjedis = jedisUtil.new Keys();
            // 得到类名、方法名和参数
            String clazzName = joinPoint.getTarget().getClass().getSimpleName();
            //只更新自己省的缓存 && 区分塔维和后付费
            StringBuilder keyBuilder = new StringBuilder(prvCode + ":" + PREFIX);
            keyBuilder.append(clazzName);
            String key = keyBuilder.toString();
            log.info("清空缓存 ====> " + key);
            // 清除对应缓存
//            Set<String> keys = redisTemplate.keys("*" + key + "*");
//            redisTemplate.delete(keys);
//            strjedis.batchDel(key);
            List<String> keysToDelete = new ArrayList<>();
            try(Cursor<String> cursor = redisTemplate.scan(
                    ScanOptions.scanOptions()
                            .match("*" + key + "*")
                            .count(10000)
                            .build()
            )){
                while (cursor.hasNext()) {
                    keysToDelete.add(cursor.next());
                    // 批量删除，避免多次网络往返
                    if (keysToDelete.size() >= 100) {
                        redisTemplate.delete(keysToDelete);
                        keysToDelete.clear();
                    }
                }
            }finally {
                if (!keysToDelete.isEmpty()) {
                    redisTemplate.delete(keysToDelete);
                }
            }
        } catch (Exception e) {
            log.error("RedisAspect 出错", e);
            if (e.getCause() != null && e.getCause().getMessage().contains("BusinessException")) {
                if (e.getCause().getMessage() != null) {
                    String errmsg = e.getCause().getMessage();
                    throw new BusinessException(errmsg.substring(errmsg.lastIndexOf(':') + 1, errmsg.length()));
                } else {
                    throw new BusinessException(e.getMessage());
                }
            }
            throw new BusinessException(e.getMessage());
        }
        return joinPoint.proceed(joinPoint.getArgs());
    }

    /**
     * 根据类名、方法名和参数生成Key
     *
     * @param clazzName
     * @param methodName
     * @param args
     * @return key格式：全类名:方法名:参数
     */
    private String getKey(String clazzName, String methodName, Object[] args) {
        StringBuilder key = new StringBuilder(PREFIX);
        key.append(clazzName);
        key.append(DELIMITER);
        key.append(methodName);
        key.append(DELIMITER);
        StringBuilder param = new StringBuilder();
        for (Object obj : args) {
            param.append(obj);
            param.append(DELIMITER);
        }
        try {
            String paramMd5 = MD5Util.encode(param.toString());
            key.append(paramMd5);
        } catch (Exception e) {
            log.error("生成key失败", e);
            key.append(param);
        }

        return key.toString();
    }
}