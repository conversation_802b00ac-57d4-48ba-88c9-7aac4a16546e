package com.xunge.dao.twrrent.settlement;

import com.xunge.core.page.Page;
import com.xunge.model.towerrent.settlement.*;

import java.util.List;
import java.util.Map;

public interface ITowerMobileBillCheckDao {
    /**
     * 查询铁塔侧和移动侧账单数据
     *
     * @param paraMap
     * @return 2017年7月10日 lpw
     */
    Page<List<TowerAndMobileBillVO>> queryTowerAndMobileFee(Map<String, Object> paraMap, int pageNumber, int pageSize);

    Page<List<TowerAndMobileBillVO>> queryTowerAndMobileFeeConfig(Map<String, Object> paraMap, int pageNumber, int pageSize);

    /**
     * 对账查询
     *
     * @param paraMap
     * @param pageNumber
     * @param pageSize
     * @return
     */
    List<TowerAndMobileBillVO> queryCheckTowerAndMobileFee(Map<String, Object> paraMap, int pageNumber, int pageSize);

    List<TowerAndMobileBillVO> queryCheckTowerAndMobileFeeConfig(Map<String, Object> paraMap, int pageNumber, int pageSize);

    /**
     * 将对账结果更新入铁塔侧账单表中
     *
     * @param towerAndMobileBillVO
     * @return
     */
    int updateCompareResult(TowerAndMobileBillVO towerAndMobileBillVO);

    /**
     * 修改确认结果
     *
     * @param vo
     * @return
     */
    public int updateTowerMobileBillState(TowerBillbalanceVO vo);

    public int updateTowerMobileBillStateConfig(TowerBillbalanceVO vo);

    /**
     * 确认结果
     *
     * @param vo
     * @return
     */
    public int updateTowerMobileBillConfirmState(TowerBillbalanceVO vo);

    //批量修改交维状态
    public int crossStateUpdate(TowerBillbalanceVO vo);

    public int updateTowerMobileBillConfirmStateConfig(TowerBillbalanceVO vo);

    /**
     * 取消确认
     *
     * @param paraMap
     * @return
     */
    int updateCancleConfirmState(Map<String, Object> paraMap);

    /**
     * 查询所有数据
     *
     * @param map
     * @return
     */
    List<TowerAndMobileBillVO> queryAllTowerAndMobileFee(Map<String, Object> map);

    /**
     * 查询所有数据
     *
     * @param map
     * @return
     */
    List<TowerAndMobileBillVO> selectAllTowerAndMobileFee(Map<String, Object> map);

    List<TowerAndMobileBillVO> selectAllTowerAndMobileFeeConfig(Map<String, Object> map);

    /**
     * 查询账单确认数据
     *
     * @param paraMap
     * @return
     */
    List<TowerAndMobileBillConfirmVO> queryTowerAndMobileConfirmBill(
            Map<String, Object> paraMap);

    /**
     * 查询账单确认数据
     *
     * @param paraMap
     * @return
     */
    List<TowerAndMobileBillConfirmVO> selectTowerAndMobileConfirmBill(
            Map<String, Object> paraMap);

    List<TowerAndMobileBillConfirmVO> selectTowerAndMobileConfirmBillConfig(
            Map<String, Object> paraMap);

    /**
     * 导出未确认账单数据查询
     *
     * @param paraMap
     * @return
     */
    List<TheBillConfirmVO> selectTheBillConfirmVO(
            Map<String, Object> paraMap);

    /**
     * 查询账单确认数据
     *
     * @param paraMap
     * @param pageNumber
     * @param pageSize
     * @return
     */
    Page<List<TowerAndMobileBillConfirmVO>> queryTowerAndMobileConfirmBalance(
            Map<String, Object> paraMap, int pageNumber, int pageSize);

    /**
     * 查询账单确认数据（带状态）
     *
     * @param paraMap
     * @param pageNumber
     * @param pageSize
     * @return
     */
    Page<TowerAndMobileBillConfirmVO> queryTowerAndMobileConfirmBalanceState(
            Map<String, Object> paraMap, int pageNumber, int pageSize);

    Page<List<TowerAndMobileBillConfirmVO>> queryTowerAndMobileConfirmBalanceConfig(
            Map<String, Object> paraMap, int pageNumber, int pageSize);

    Page<List<TowerAndMobileBillConfirmVO>> queryTowerAndMobileConfirmBalanceConfigState(
            Map<String, Object> paraMap, int pageNumber, int pageSize);


    /**
     * 根据账单id集合查询对应的账单
     *
     * @param paraMap
     * @return
     * <AUTHOR>
     */
    public Page<List<TowerBillbalanceVO>> queryTowerBillbalanceByIds(Map<String, Object> paraMap);

    public Page<List<TowerBillbalanceVO>> queryTowerBillbalanceByIdsConfig(Map<String, Object> paraMap);

    /**
     * 查询铁塔服务费台账列表
     *
     * @param map
     * @return
     */
    public List<ExportExcelServiceChargeVo> exportExcelServiceCharge(Map<String, Object> map);

    /**
     * 查询未确认的铁塔侧账单
     *
     * @param map
     * @return
     */
    public List<TowerAndMobileBillConfirmVO> selectTowerBillbalanceConfirmBill(Map<String, Object> map);

    public List<TowerAndMobileBillConfirmVO> selectTowerBillbalanceConfirmBillConfig(Map<String, Object> map);

    public List<TowerBillbalanceVO> selectTowerBillbalanceCross(Map<String, Object> map);


    public int queryAccountsummaryCountById(Map<String, Object> map);

    int comfirmStateUpdate(Map<String, Object> param);

    Page<List<MobileBillbalanceChangeVO>> queryMobileChangeList(
            Map<String, Object> paraMap, int pageNumber, int pageSize);

    List<MobileBillbalanceChangeVO> selectMobileChangeList(
            Map<String, Object> paraMap);

    int insertConfirmNote(Map<String, Object> map);

    Page<List<TheConfirmNoteVO>> queryBillConfirmNote(Map<String, Object> paraMap, int pageNumber, int pageSize);

    List<BillCheckPraceFeeVO> checkPraceFee(Map<String, Object> paraMap);
}
