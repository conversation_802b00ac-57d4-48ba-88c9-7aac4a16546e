package com.xunge.dao.twrrent.monthlyReport.impl;

import com.xunge.comm.system.RESULT;
import com.xunge.dao.AbstractBaseDao;
import com.xunge.dao.twrrent.monthlyReport.IThreeCaseStudyDao;
import com.xunge.model.towerrent.monthlyReport.RptThreeCaseStudy;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @description: 三方塔DAO层
 * @author: dxd
 * @create: 2019-08-05 14:03
 **/
@Repository("threeCaseStudyDao")
public class ThreeCaseStudyDaoImpl extends AbstractBaseDao implements IThreeCaseStudyDao {

    private final String namespace = "com.xunge.dao.twrrent.monthlyReport.TwrThreeCaseStudyMapper.";

    @Override
    public List<RptThreeCaseStudy> queryThreeCaseData(Map<String, Object> paraMap) {
        return this.getSqlSession().selectList(namespace + "queryThreeCaseData", paraMap);
    }

    @Override
    public String insertThreeById(RptThreeCaseStudy threeObj) {
        int result = this.getSqlSession().insert(namespace + "insertThreeById", threeObj);
        return (result == 0) ? RESULT.FAIL_0 : RESULT.SUCCESS_1;
    }

    @Override
    public String deleteThreeById(Map<String, Object> map) {
        int result = this.getSqlSession().delete(namespace + "deleteThreeById", map);
        return (result == 0) ? RESULT.FAIL_0 : RESULT.SUCCESS_1;
    }

    @Override
    public String updateThreeById(RptThreeCaseStudy threeObj) {
        int result = this.getSqlSession().update(namespace + "updateThreeById", threeObj);
        return (result == 0) ? RESULT.FAIL_0 : RESULT.SUCCESS_1;
    }

    @Override
    public List<RptThreeCaseStudy> findThreeName(Map<String, Object> map) {
        return this.getSqlSession().selectList(namespace + "queryThreeCaseData", map);
    }

    @Override
    public String findPrvNameById(String prvId) {
        return this.getSqlSession().selectOne(namespace + "findPrvNameById", prvId);
    }

    @Override
    public List<String> queryFiles(String businessId, String businessType) {
        Map<String, String> map = new HashMap<>();
        map.put("businessId", businessId);
        map.put("businessType", businessType);
        return this.getSqlSession().selectList(namespace + "queryFiles", map);
    }

    @Override
    public List<RptThreeCaseStudy> queryGroupThreeCaseData(Map<String, Object> paraMap) {
        return this.getSqlSession().selectList(namespace + "queryGroupThreeCaseData", paraMap);
    }

    @Override
    public List<Map<String, Object>> selecThreeCasetUser() {
        Map<String, String> map = new HashMap<>();
        /*map.put("businessId", businessId);
        map.put("businessType", businessType);*/
        return this.getSqlSession().selectList(namespace + "selecThreeCasetUser", map);
    }

    @Override
    public List<RptThreeCaseStudy> queryThreeCaseDataHis(Map<String, Object> paraMap) {
        return this.getSqlSession().selectList(namespace + "queryThreeCaseDataHis", paraMap);
    }
}
