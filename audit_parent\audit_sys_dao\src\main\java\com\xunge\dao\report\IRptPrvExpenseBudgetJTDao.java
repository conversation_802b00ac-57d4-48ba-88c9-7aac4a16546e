package com.xunge.dao.report;

import com.xunge.model.report.RptPrvExpenseBudgetApplyVO;

import java.util.List;
import java.util.Map;


public interface IRptPrvExpenseBudgetJTDao {
    List<RptPrvExpenseBudgetApplyVO> queryExpenseBudgetApplyList(Map<String, Object> paraMap);

    List<RptPrvExpenseBudgetApplyVO> queryExpenseBudgetApply(Map<String, Object> map);


    List<Map<String, Object>> export(Map<String, Object> map);

}
