package com.xunge.dao.system.user.impl;

import com.xunge.core.page.Page;
import com.xunge.dao.AbstractBaseDao;
import com.xunge.dao.system.user.ISysUserDao;
import com.xunge.filter.PageInterceptor;
import com.xunge.model.system.portaluser.ExportUser;
import com.xunge.model.system.portaluser.ImportUser;
import com.xunge.model.system.region.SysRegionVO;
import com.xunge.model.system.roleuser.RoleUserVO;
import com.xunge.model.system.user.ReimburserVO;
import com.xunge.model.system.user.SysUserPasswdRecord;
import com.xunge.model.system.user.SysUserVO;
import com.xunge.model.towerrent.accountsummary.TwrReimburserVO;
import com.xunge.model.towerrent.accountsummary.query.TwrReimburserQueryDto;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 用户管理dao实现类
 */
public class SysUserDaoImpl extends AbstractBaseDao implements ISysUserDao {

    final String Namespace = "com.xunge.dao.system.SysUserMapper.";

    @Override
    public SysUserVO queryUserInfoBySmapId(String smapId) {
        return (SysUserVO) this.getSqlSession().selectOne(Namespace + "queryUserInfoBySmapId", smapId);
    }

    /**
     * 根据登陆用户名查找用户信息
     */
    @Override
    public SysUserVO getByUserLoginName(String userLoginname) {
        return (SysUserVO) this.getSqlSession().selectOne(Namespace + "getUserByLoginName", userLoginname);
    }

    @Override
    public SysUserVO queryUserByUserLoginName(String userLoginname) {
        return (SysUserVO) this.getSqlSession().selectOne(Namespace + "getUserInfoByLoginName", userLoginname);
    }

    /**
     * 根据登陆用户名查找用户信息列表
     */
    @Override
    public List<SysUserVO> getUserListByUserLoginName(String userLoginname) {
        return this.getSqlSession().selectList(Namespace + "getUserListByUserLoginName", userLoginname);
    }

    @Override
    public SysUserVO getByUserLoginNamePrvFlag(Map<String, Object> map) {

        return (SysUserVO) this.getSqlSession().selectOne(Namespace + "getByUserLoginNamePrvFlag", map);
    }
    /**
     * 根据登陆用户ID找到用户信息
     */
    @Override
    public SysUserVO getByUserId(String userId) {
        return (SysUserVO) this.getSqlSession().selectOne(Namespace + "getUserById", userId);
    }

    /**
     * 添加用户
     */
    @Override
    public int insertSysUser(SysUserVO sysUser) {
        return this.getSqlSession().insert(Namespace + "addSysUser", sysUser);
    }

    /**
     * 修改用户
     */
    @Override
    public int updateSysUserByUserId(SysUserVO sysUser) {
        return this.getSqlSession().update(Namespace + "updateSysUserById", sysUser);
    }

    /**
     * 查询全部用户信息
     */
    @Override
    public Page<List<SysUserVO>> querySysUser(Map<String, Object> paramMap, int cur_page_num, int page_count) {
        PageInterceptor.startPage(cur_page_num, page_count);
        this.getSqlSession().selectList(Namespace + "queryUserMsg", paramMap);
        return PageInterceptor.endPage();
    }

    /**
     * 根据登陆用户名，用户姓名模糊查找用户信息
     */
    @Override
    public Page<List<SysUserVO>> querySysUserByname(Map<String, Object> paramMap, int cur_page_num, int page_count) {
        PageInterceptor.startPage(cur_page_num, page_count);
        this.getSqlSession().selectList(Namespace + "queryUserPrvRegMsg", paramMap);

        return PageInterceptor.endPage();
    }

    /**
     * 修改用户状态
     */
    @Override
    public int updateUserStateBatch(Map<String, Object> paramMap) {
        return this.getSqlSession().update(Namespace + "updateUserState", paramMap);
    }

    /**
     * 根据登陆用户ID查找省份编码信息
     */
    @Override
    public SysRegionVO queryPrvIdByUser(SysUserVO user) {
        return this.getSqlSession().selectOne(Namespace + "queryPrvIdByUser", user);
    }

    @Override
    public List<String> queryRoleIdsByUser(SysUserVO user) {
        return this.getSqlSession().selectList(Namespace + "queryRoleIdsByUser", user);
    }

    public List<String> queryRoleCodesByUser(SysUserVO user) {
        return this.getSqlSession().selectList(Namespace + "queryRoleCodesByUser", user);
    }

    /**
     * 添加用户角色信息
     */
    @Override
    public int insertRoleUser(Map map) {
        return this.getSqlSession().insert(Namespace + "patch", map);
    }

    /**
     * 根据用户ID查找所有角色信息
     */
    @Override
    public List<String> queryUserRole(Map map) {
        return this.getSqlSession().selectList(Namespace + "queryUserRole", map);
    }

    /**
     * 根据用户ID删除所有角色信息
     */
    @Override
    public void deleteRoleByUsreID(String userId) {
        this.getSqlSession().delete(Namespace + "deleteRoleByUsreID", userId);
    }

    /**
     * 增加用户部门关系
     */
    @Override
    public int insertDepartmentUser(Map map) {
        return this.getSqlSession().insert(Namespace + "insertDeptUser", map);
    }

    @Override
    public List<String> queryUserDepartment(Map map) {
        return this.getSqlSession().selectList(Namespace + "queryUserDept", map);
    }

    @Override
    public void deleteDepartmentByUsreID(String userId) {
        this.getSqlSession().delete(Namespace + "deleteDeptByUsreID", userId);
    }

    @Override
    public int insertUserRegion(Map<String, Object> insertUserRegionMap) {
        try {
            this.getSqlSession().insert(Namespace + "insertUserRegion", insertUserRegionMap);
            return 1;
        } catch (Exception e) {
            return 0;
        }
    }

    @Override
    public List<String> queryRegionId(Map<String, Object> paraMap) {
        return this.getSqlSession().selectList(Namespace + "queryRegionId", paraMap);
    }

    //修改用户区县关系状态
    @Override
    public int updateUserRegion(Map<String, Object> updateUserRegionMap) {
        return this.getSqlSession().update(Namespace + "updateUserRegion", updateUserRegionMap);
    }

    @Override
    public SysUserVO queryAllUser(String userId) {
        SysUserVO sysUserVO = this.getSqlSession().selectOne(Namespace + "queryAllUser", userId);
        return sysUserVO;
    }


    @Override
    public Page<List<SysUserVO>> queryAllUserByRoleId(Map<String, Object> paraMap, int pageNumber, int pageSize) {
        PageInterceptor.startPage(pageNumber, pageSize);
        this.getSqlSession().selectList(Namespace + "queryAllUserByRoleId", paraMap);
        return PageInterceptor.endPage();
    }

    @Override
    public Page<List<SysUserVO>> queryUserAll(Map<String, Object> paraMap, int pageNumber, int pageSize) {
        PageInterceptor.startPage(pageNumber, pageSize);
        this.getSqlSession().selectList(Namespace + "queryUserAll", paraMap);
        return PageInterceptor.endPage();
    }

    @Override
    public int updateUserRoleState(Map<String, Object> map) {
        return this.getSqlSession().update(Namespace + "updateUserRoleState", map);
    }

    @Override
    public int updateUserDeptState(Map<String, Object> map) {
        return this.getSqlSession().update(Namespace + "updateUserDeptState", map);
    }

    @Override
    public List<RoleUserVO> queryUserRoleVOByUserId(Map<String, Object> map) {
        return this.getSqlSession().selectList(Namespace + "queryUserRoleVO", map);
    }

    @Override
    public SysUserVO queryUserInfo(Map<String, Object> paraMap) {
        return this.getSqlSession().selectOne(Namespace + "queryUserInfo", paraMap);
    }
    @Override
    public SysUserVO queryUserInfoForRobot(String userId) {
        return this.getSqlSession().selectOne(Namespace + "queryUserInfoForRobot", userId);
    }
    @Override
    public int updateUserInfo(Map<String, Object> paraMap) {
        return this.getSqlSession().update(Namespace + "updateUserInfo", paraMap);
    }

    @Override
    public List<SysUserVO> queryUserByRole(Map<String, Object> paraMap) {
        return this.getSqlSession().selectList(Namespace + "queryUserByRole", paraMap);
    }

    @Override
    public SysUserVO queryUserByUserId(Map<String, Object> paraMap) {
        return this.getSqlSession().selectOne(Namespace + "queryUserByUserId", paraMap);
    }

    @Override
    public SysUserVO queryUserById(String userId) {
        return this.getSqlSession().selectOne(Namespace + "queryUserById", userId);
    }

    @Override
    public SysUserVO queryUserIdByUserId(Map<String, Object> paraMap) {
        return this.getSqlSession().selectOne(Namespace + "queryUserIdByUserId", paraMap);
    }

    @Override
    public List<SysUserVO> queryUserByLoginNameAndPrvId(Map<String, Object> paraMap) {
        return this.getSqlSession().selectList(Namespace + "queryUserByLoginNameAndPrvId", paraMap);
    }

    @Override
    public List<SysUserVO> queryUserBySmapIdAndPrvId(Map<String, Object> paraMap) {
        return this.getSqlSession().selectList(Namespace + "queryUserBySmapIdAndPrvId", paraMap);
    }

    @Override
    public List<SysUserVO> queryUserByProjectName(Map<String, Object> paraMap) {
        // TODO Auto-generated method stub
        return this.getSqlSession().selectList(Namespace + "queryUserByProjectName", paraMap);
    }

    @Override
    public SysUserVO provinceWhetherOpen(Map<String, Object> paraMap) {
        return this.getSqlSession().selectOne(Namespace + "provinceWhetherOpen", paraMap);
    }

    @Override
    public Page<Map<String, String>> queryAllSmapUser(Map<String, Object> paraMap, int pageNumber, int pageSize) {
        PageInterceptor.startPage(pageNumber, pageSize);
        this.getSqlSession().selectList(Namespace + "queryAllSmapUser", paraMap);
        return PageInterceptor.endPage();
    }

    @Override
    public Page<List<SysUserVO>> queryAllUserByRoleName(Map<String, Object> paraMap, int pageNumber, int pageSize) {
        PageInterceptor.startPage(pageNumber, pageSize);
        this.getSqlSession().selectList(Namespace + "queryAllUserByRoleName", paraMap);
        return PageInterceptor.endPage();
    }

    @Override
    public String queryUserIdByUserSampId(String sampId) {
        return this.getSqlSession().selectOne(Namespace + "queryUserIdByUserSampId", sampId);
    }

    @Override
    public SysUserVO querySmapInfoByUserId(String userId) {
        return this.getSqlSession().selectOne(Namespace + "querySmapInfoByUserId", userId);
    }

    @Override
    public SysUserVO getByUid(String uid) {
        return this.getSqlSession().selectOne(Namespace + "getByUid", uid);
    }


    @Override
    public SysUserVO getUserInfo(SysUserVO user) {
        return this.getSqlSession().selectOne(Namespace + "getUserInfo", user);
    }

    @Override
    public int resetPassword(Map<String, Object> map) {
        return this.getSqlSession().update(Namespace + "resetPassword", map);
    }

    @Override
    public SysUserVO queryInfoByUserName(Map<String, Object> map) {
        return this.getSqlSession().selectOne(Namespace + "queryInfoByUserName", map);
    }

    @Override
    public SysUserVO queryUserInfoByparam(Map<String, Object> map) {
        return this.getSqlSession().selectOne(Namespace + "queryUserInfoByparam", map);
    }

    @Override
    public SysUserVO queryUserInfoByparamLoginname(Map<String, Object> map) {
        return this.getSqlSession().selectOne(Namespace + "queryUserInfoByparamLoginname", map);
    }

    @Override
    public int updateCompanyType(SysUserVO userCompany) {
        return this.getSqlSession().update(Namespace + "updateCompanyType", userCompany);
    }

    @Override
    public int insertCompanyType(SysUserVO userCompany) {
        return this.getSqlSession().insert(Namespace + "insertCompanyType", userCompany);
    }

    @Override
    public List<SysUserVO> selectAccUserCompanyType(SysUserVO userCompany) {
        return this.getSqlSession().selectList(Namespace + "selectAccUserCompanyType", userCompany);
    }

    @Override
    public List<SysUserVO> selectUserCompanyType(SysUserVO userCompany) {
        return this.getSqlSession().selectList(Namespace + "selectUserCompanyType", userCompany);

    }

    @Override
    public SysUserVO getByPortalUserId(String pid) {
        return (SysUserVO) this.getSqlSession().selectOne(Namespace + "getByPortalUserId", pid);
    }

    @Override
    public List<SysUserVO> queryInfoByUserPhone(String userPhone) {
        return this.getSqlSession().selectList(Namespace + "queryInfoByUserPhone", userPhone);
    }

    @Override
    public List<ExportUser> queryUserAllAvailable(Map<String, Object> paraMap) {
        this.getSqlSession().clearCache();
        return this.getSqlSession().selectList(Namespace + "queryUserAllAvailable", paraMap);
    }

    @Override
    public List<SysUserVO> queryAllUserBySmapId(String smapId) {
        return this.getSqlSession().selectList(Namespace + "queryAllUserBySmapId", smapId);
    }

    @Override
    public void rmoveUserPortalId(String pid) {
        this.getSqlSession().update(Namespace + "rmoveUserPortalId", pid);
    }

    @Override
    public void updateUserPid(ImportUser u) {
        this.getSqlSession().update(Namespace + "updateUserPid", u);
    }

    @Override
    public int ifKnowRobot(String userId) {
        return this.getSqlSession().selectOne(Namespace+"ifKnowRobot", userId);
    }

    @Override
    public int addNoticeUser(String userId){
        return this.getSqlSession().insert(Namespace+"addNoticeUser", userId);
    }

    @Override
    public String selectStaffCodeByUserId(String userId){
        return this.getSqlSession().selectOne(Namespace + "selectStaffCodeByUserId", userId);
    }

    @Override
    public SysUserVO getBySmapIdPrvFlag(Map<String, Object> map) {

        return (SysUserVO) this.getSqlSession().selectOne(Namespace + "getBySmapIdPrvFlag", map);
    }

    @Override
    public int updateCompanyByUserId(SysUserVO sysUser) {
        return this.getSqlSession().update(Namespace + "updateCompanyByUserId", sysUser);
    }

    @Override
    public List<ReimburserVO> queryReimburserList(ReimburserVO reimburserVO) {
        return this.getSqlSession().selectList(Namespace + "queryReimburserList", reimburserVO);
    }

    @Override
    public List<TwrReimburserVO> queryUserListByUserId(String createUserId) {
        return this.getSqlSession().selectList(Namespace + "queryUserListByUserId", createUserId);
    }

    @Override
    public List<TwrReimburserVO> queryTwrReimburserList(TwrReimburserQueryDto reimburserVO) {
        return this.getSqlSession().selectList(Namespace + "queryTwrReimburserList", reimburserVO);
    }

    @Override
    public ReimburserVO queryPushUserCompanyName(ReimburserVO reimburserVO) {
        return (ReimburserVO) this.getSqlSession().selectOne(Namespace + "queryPushUserCompanyName", reimburserVO);
    }

    @Override
    public List<SysUserPasswdRecord> queryLastThreeModifyRecords(String userName) {
        return  this.getSqlSession().selectList(Namespace + "queryLastThreeModifyRecords", userName);
    }

    @Override
    public int saveModifyRecords(SysUserPasswdRecord record){
        return  this.getSqlSession().insert(Namespace + "saveModifyRecords", record);
    }
}
