package com.xunge.dao.selfrent.accrual;

import com.xunge.model.selfrent.accrual.RentAccrualSummaryVO;
import com.xunge.model.selfrent.accrual.TowerAccrualQueryDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface IRentAccrualSummaryMapper {

    List<String> queryRentAccrualSupplierIdAll(Map<String, Object> map);

    int saveRentAccrualSummary(RentAccrualSummaryVO vo);

    List<RentAccrualSummaryVO> queryRentAccrualSummaryVO(Map<String, Object> map);

    List<RentAccrualSummaryVO> queryRentAccrualSummaryVOByIds(@Param("ids") List<String> ids);

    RentAccrualSummaryVO queryRentAccrualSummaryById(Map<String, Object> map);

    int editRentAccrualSummary(RentAccrualSummaryVO vo);

    int delRentAccrualSummary(RentAccrualSummaryVO vo);

    List<RentAccrualSummaryVO> queryRentAccrualUpper(Map<String, Object> map);


    List<String> queryRentAccrualSupplierIdByDto(TowerAccrualQueryDto dto);

    /**
     * 根据rentAccrualSummaryId获取rentAccrualType
     * @param summaryId
     * @return
     */
    Integer getAccrualTypeBySummaryId(@Param("summaryId") String summaryId);

    /**
     * 查询汇总单包含的所有业务活动编码（不重复）
     * @param summaryId
     * @return
     */
    List<String> getDistinctActivityCodesBySummaryId(@Param("summaryId") String summaryId);

    /**
     * 查询租费汇总单包含的所有业务活动编码（不重复）
     * @param summaryId
     * @return
     */
    List<String> getDistinctRentActivityCodesBySummaryId(@Param("summaryId") String summaryId);

}
