package com.xunge.dao.report.Impl;

import com.xunge.core.page.Page;
import com.xunge.dao.AbstractBaseDao;
import com.xunge.dao.report.SupplyMethodChangeDao;
import com.xunge.filter.PageInterceptor;
import com.xunge.model.report.SupplyMethodChangeCountVO;
import com.xunge.model.report.SupplyMethodChangeVO;

import java.util.List;
import java.util.Map;

/**
 * @创建人 LiangCheng
 * @创建时间 2020/1/19 0019
 * @描述：
 */
public class SupplyMethodChangeDaoImpl extends AbstractBaseDao implements SupplyMethodChangeDao {


    final String Namespace = "com.xunge.mapping.report.SupplyMethodChangeMapper.";

    @Override
    public void saveImportSupplyChangeData(List<SupplyMethodChangeVO> list) {
        this.getSqlSession().insert(Namespace + "saveImportSupplyChangeData", list);
    }

    @Override
    public Page<List<SupplyMethodChangeVO>> queryAllSupplyChange(Map<String, Object> map, int pageNumber, int pageSize) {
        PageInterceptor.startPage(pageNumber, pageSize);
        this.getSqlSession().selectList(Namespace + "queryAllSupplyChange", map);
        return PageInterceptor.endPage();
    }

    @Override
    public List<SupplyMethodChangeVO> queryAllSupplyChangeList(Map<String, Object> map) {
        return this.getSqlSession().selectList(Namespace + "queryAllSupplyChange", map);
    }

    @Override
    public Page<List<SupplyMethodChangeCountVO>> queryAllSupplyChangeCount(Map<String, Object> map, int pageNumber, int pageSize) {
        PageInterceptor.startPage(pageNumber, pageSize);
        this.getSqlSession().selectList(Namespace + "queryAllSupplyChangeCount", map);
        return PageInterceptor.endPage();
    }

    @Override
    public List<SupplyMethodChangeCountVO> queryAllSupplyChangeCountList(Map<String, Object> map) {
        return this.getSqlSession().selectList(Namespace + "queryAllSupplyChangeCount", map);
    }

    @Override
    public Page<List<SupplyMethodChangeVO>> queryAllSupplyChangeJT(Map<String, Object> map, int pageNumber, int pageSize) {
        PageInterceptor.startPage(pageNumber, pageSize);
        this.getSqlSession().selectList(Namespace + "queryAllSupplyChangeJT", map);
        return PageInterceptor.endPage();
    }

    @Override
    public List<SupplyMethodChangeVO> queryAllSupplyChangeListJT(Map<String, Object> map) {
        return this.getSqlSession().selectList(Namespace + "queryAllSupplyChangeJT", map);
    }

    @Override
    public Page<List<SupplyMethodChangeCountVO>> queryAllSupplyChangeCountJT(Map<String, Object> map, int pageNumber, int pageSize) {
        PageInterceptor.startPage(pageNumber, pageSize);
        this.getSqlSession().selectList(Namespace + "queryAllSupplyChangeCountJT", map);
        return PageInterceptor.endPage();
    }

    @Override
    public List<SupplyMethodChangeCountVO> queryAllSupplyChangeCountListJT(Map<String, Object> map) {
        return this.getSqlSession().selectList(Namespace + "queryAllSupplyChangeCountJT", map);
    }
}
