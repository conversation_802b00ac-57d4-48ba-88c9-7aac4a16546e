package com.xunge.dao.system.template;

import com.xunge.model.system.template.NcmsField;
import com.xunge.model.system.template.NcmsTemplate;

import java.util.List;
import java.util.Map;

/**
 * 描述：
 * Created on 2019/11/5.
 * <p>Title:</p>
 * <p>Copyright:Copyright (c) 2017</p>
 * <p>Company:安徽科大国创</p>
 * <p>Department:西南二区BU</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @update
 */
public interface NcmsTemplateDao {

    NcmsTemplate queryByClassPath(String classPath);

    int updateFieldByFieldId(NcmsField field);

    int saveFields(List<NcmsField> classNcmsFields);

    int saveTemplate(NcmsTemplate template);

    List<NcmsTemplate> queryByParam(Map<String, Object> param);

    List<NcmsField> queryFieldsByParam(Map<String, Object> param);

    List<Map<String, Object>> queryTableFieldsByParam(Map<String, Object> param);

    int setRelativeField(Map<String, Object> param);

    int opById(Map<String, Object> param);

    int startCompareByFieldId(Map<String, Object> param);
}
