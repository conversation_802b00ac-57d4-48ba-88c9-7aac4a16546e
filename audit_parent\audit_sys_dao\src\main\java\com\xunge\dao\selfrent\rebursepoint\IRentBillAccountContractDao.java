package com.xunge.dao.selfrent.rebursepoint;

import com.xunge.model.selfrent.rebursepoint.RentBillAccountContractVO;

import java.util.Map;

/**
 * 报账点合同关系Dao
 *
 * <AUTHOR> 2017年6月25日
 */
public interface IRentBillAccountContractDao {
    /**
     * 添加报账点合同关系Dao
     *
     * @param rentBillAccountContract
     */
    void insertBillAccountContract(RentBillAccountContractVO rentBillAccountContract);

    /**
     * 修改合同报账点关系
     *
     * @param billAccountContractId
     */
    int updateBillAccountContract(Map<String, Object> paraMap);

    /**
     * 删除合同报账点关系
     *
     * @param billAccountContractId
     */
    void deleteBillAccountContract(Map<String, Object> paraMap);

    /**
     * 查询合同与是否已绑定报账点
     *
     * @param rentcontractId
     * @return
     */
    RentBillAccountContractVO queryContractBindBillacc(String rentcontractId);

    /**
     * 根据报账点Id查询报账点关联的合同信息
     *
     * @param billaccountId
     * @return
     */
    public RentBillAccountContractVO queryBillaccountContractById(Map<String, Object> parMap);


    /**
     * @param @param paraMap    设定文件
     * @return void    返回类型
     * @throws
     * @Title: updateComtractInfo
     * @Description: TODO(这里用一句话描述这个方法的作用)
     */

    int updateComtractInfo(Map<String, Object> paraMap);

}
