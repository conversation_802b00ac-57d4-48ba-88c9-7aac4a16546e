package com.xunge.dao.selfrent.billaccount;

import com.xunge.model.selfrent.billAccount.VBaseresourcePaymentdetailVO;

import java.util.List;
import java.util.Map;

public interface IVBaseresourcePaymentdetailDao {
    /**
     * 查询资源点以及资源点缴费记录
     *
     * @param billaccountId
     * @return
     * <AUTHOR>
     */
    public List<VBaseresourcePaymentdetailVO> queryBaseresourcePaymentdetail(Map<String, Object> map);

    /**
     * 查询铁塔 和铁塔缴费记录
     *
     * @param map
     * @return
     */
    public List<VBaseresourcePaymentdetailVO> queryBaseTowerPaymentdetail(Map<String, Object> map);
}