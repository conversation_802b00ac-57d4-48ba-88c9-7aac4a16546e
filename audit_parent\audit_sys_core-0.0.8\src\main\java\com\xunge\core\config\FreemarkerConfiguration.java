package com.xunge.core.config;

import freemarker.template.Configuration;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

import static freemarker.template.Configuration.VERSION_2_3_30;

/**
 * <AUTHOR>
 * @version 1.0
 * @className FreemarkerConfiguration
 * @date 2020-07-30 09:56
 **/
@Component
@org.springframework.context.annotation.Configuration
public class FreemarkerConfiguration {

    @Bean
    public Configuration configuration() {
        Configuration configuration = new Configuration(VERSION_2_3_30);
        configuration.setDefaultEncoding("UTF-8");
        configuration.setClassForTemplateLoading(this.getClass(), "/template");
        return configuration;
    }

}
