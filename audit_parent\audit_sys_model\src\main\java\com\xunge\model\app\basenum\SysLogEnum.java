package com.xunge.model.app.basenum;

/**
 * @Auther: hxh
 * @Date: 2018/9/2 20:01
 * @Description:
 */
public enum SysLogEnum {
    //错误记录后缀
    error_suffix("一般错误", 1),

    log_operate("操作日志", 10),

    log_error("错误日志", 20),

    log_login("登陆日志", 30),

    log_exception("系统异常日志", 99);

    private String strValue;

    private int intValue;

    SysLogEnum(String strValue, int intValue) {
        this.strValue = strValue;
        this.intValue = intValue;
    }

    public String getStrValue() {
        return strValue;
    }

    public void setStrValue(String strValue) {
        this.strValue = strValue;
    }

    public int getIntValue() {
        return intValue;
    }

    public void setIntValue(int intValue) {
        this.intValue = intValue;
    }
}
