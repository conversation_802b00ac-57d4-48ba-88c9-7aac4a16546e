package com.xunge.dao.report;

import com.xunge.model.report.RptSiteInfoStatisticsVO;

import java.util.List;
import java.util.Map;

/**
 * @Auther: LinFei Li
 * @Date: 2018/12/13 16:58
 * @Description:
 */
public interface IRptSiteInfoStatisticsDao {

    /**
     * 查询站点数据
     *
     * @param map
     * @return
     */
    List<RptSiteInfoStatisticsVO> querySiteData(Map<String, Object> map);

    /**
     * 查询地市站点数据
     *
     * @param map
     * @return
     */
    List<RptSiteInfoStatisticsVO> querySiteDataByPregId(Map<String, Object> map);

    /**
     * 查询区县站点数据
     *
     * @param map
     * @return
     */
    List<RptSiteInfoStatisticsVO> querySiteDataByRegId(Map<String, Object> map);

}
