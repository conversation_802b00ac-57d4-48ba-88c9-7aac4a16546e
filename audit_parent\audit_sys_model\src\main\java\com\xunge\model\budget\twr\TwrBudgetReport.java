package com.xunge.model.budget.twr;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2022/7/28
 * @description
 */
@Data
@ToString
@NoArgsConstructor
public class TwrBudgetReport {
    /**
     *
     */
    private String id;

    private String workOrderId;

    private String budgetTime;

    private Integer flowType;
    /**
     * 省份ID
     */
    private String prvId;

    /**
     * 省份名称
     */
    private String prvName;
    /**
     * 年份
     */
    private Integer onYear;

    /**
     * 月份
     */
    private Integer onMonth;
    /**
     * 业务类型({塔类,1}, {室分,2}, {微站,3}, {传输,4}, {非标,5}, {合计,6})
     */
    private Integer productType;
    /**
     * 本年账期年份1月-账期月份存量订单在新年度的费用
     */
    private BigDecimal stockOrderNewFee;
    /**
     * 本年起租订单在新年补足费用
     */
    private BigDecimal complementNewFee;
    /**
     * 本年账期月份-12月新起租订单在新年度的费用
     */
    private BigDecimal rentOrderNewFee;
    /**
     * 新年新建站在新年的费用
     */
    private BigDecimal newSiteFee;
    /**
     * 预算调整补足费用
     */
    private BigDecimal budgetSupplementFee;
    /**
     * 油机发电费（非包干）
     */
    private BigDecimal oilFee;
    /**
     * 降本增效压降费用-提升共享范围
     */
    private BigDecimal promoteSharedFee;
    /**
     * 降本增效压降费用-站址退租退网
     */
    private BigDecimal hireLogoutFee;
    /**
     * 降本增效压降费用-免除普遍服务站址费用
     */
    private BigDecimal universalServiceFee;
    /**
     * 预算总览-预算金额
     */
    private BigDecimal pandectBudgetFee;
    /**
     * 预算总览-核减金额
     */
    private BigDecimal pandectSubtractFee;

    /**
     * 预算总览-核减金额后金额
     */
    private BigDecimal pandectSubtractFeeAfter;

    /**
     * 最新账期累计摊销值
     */
    private BigDecimal stockOrderAmoritzeFee;
    /**
     * 最新账期计提金额
     */
    private BigDecimal stockOrderAccrualFee;
    /**
     * 剩余月份应付费用
     */
    private BigDecimal stockOrderCopeFee;
    /**
     * 本年账期年份1月-账期月份存量订单在新年度的费用-预算金额
     */
    private BigDecimal stockOrderBudgetFee;
    /**
     * 本年账期年份1月-账期月份存量订单在新年度的费用-核减金额
     */
    private BigDecimal stockOrderSubtractFee;

    private BigDecimal stockOrderSubtractFeeAfter;
    /**
     * 本年起租订单在新年补足费用
     */
    private BigDecimal complementNewRentFee;
    /**
     * 本年起租订单在新年补足费用-预算金额
     */
    private BigDecimal complementNewBudgetFee;
    /**
     * 本年起租订单在新年补足费用-核减金额
     */
    private BigDecimal complementNewSubtractFee;

    private BigDecimal complementNewSubtractFeeAfter;
    /**
     * 单订单服务费（本年新址新建订单年均值）
     */
    private BigDecimal rentOrderNewSiteFee;
    /**
     * 单订单服务费（本年共址订单年均值）
     */
    private BigDecimal rentOrderShareSiteFee;
    /**
     * 本年共址改造订单平均产品单元数
     */
    private String rentOrderCurrentUnitNumber;
    /**
     * 年底起租的新址新建订单数
     */
    private Integer rentOrderSiteEndNumber;
    /**
     * 年底起租的共址改造订单数
     */
    private Integer rentOrderShareEndNumber;
    /**
     * 新年共址改造站址平均产品单元数
     */
    private BigDecimal rentOrderNewUnitNumber;
    /**
     * 本年账期月份-12月新起租订单在新年度的费用-预算金额
     */
    private BigDecimal rentOrderBudgetFee;
    /**
     * 本年账期月份-12月新起租订单在新年度的费用-核减金额
     */
    private BigDecimal rentOrderSubtractFee;

    private BigDecimal rentOrderSubtractFeeAfter;
    /**
     * 单站服务费（本年新建站年均值）
     */
    private BigDecimal newSiteCurrentYearFee;
    /**
     * 单订单服务费（共址订单年均值）
     */
    private BigDecimal newSiteShareYearFee;
    /**
     * 本年共址改造订单平均产品单元数
     */
    private BigDecimal newSiteCurrentUnitNumber;
    /**
     * 新年新建站址数
     */
    private Integer newSiteNumber;
    /**
     * 新年共址改造订单数
     */
    private Integer newSiteShareOrderNumber;
    /**
     * 月份数（新址新建）
     */
    private BigDecimal newSiteCurrentMonthNumber;
    /**
     * 月份数（共址改造）
     */
    private BigDecimal newSiteShareMonthNumber;
    /**
     * 新年共址改造站址平均产品单元数
     */
    private BigDecimal newSiteShareUnitNumber;
    /**
     * 新年新建站在新年的费用-预算金额
     */
    private BigDecimal newSiteBudgetFee;
    /**
     * 新年新建站在新年的费用-核减金额
     */
    private BigDecimal newSiteSubtractFee;

    private BigDecimal newSiteSubtractFeeAfter;
    /**
     * 新年度总站址数
     */
    private Integer promoteSharedSiteNumber;

    /**
     * 单站服务费（当年独享站年化均值）
     */
    private BigDecimal promoteSharedSiteFee;
    /**
     * 共享率提升百分比
     */
    private BigDecimal promoteSharedSiteRadio;
    /**
     * 月份数
     */
    private BigDecimal promoteSharedMonthNumber;
    /**
     * 提升共享范围-预算金额
     */
    private BigDecimal promoteSharedBudgetFee;
    /**
     * 提升共享范围-核减金额
     */
    private BigDecimal promoteSharedSubtractFee;

    private BigDecimal promoteSharedSubtractFeeAfter;
    /**
     * 单站服务费（年化均值）
     */
    private BigDecimal hireLogoutSiteFee;
    /**
     * 单订单服务费（年化均值）
     */
    private BigDecimal hireLogoutOrderFee;
    /**
     * 退租站址数
     */
    private Integer hireLogoutBackSiteNumber;
    /**
     * 退租订单数
     */
    private Integer hireLogoutBackOrderNumber;
    /**
     * 月份数（退租站址）
     */
    private BigDecimal hireLogoutSiteMonthNumber;
    /**
     * 月份数（退租订单）
     */
    private BigDecimal hireLogoutOrderMonthNumber;
    /**
     * 站址退租退网-预算金额
     */
    private BigDecimal hireLogoutBudgetFee;
    /**
     * 站址退租退网-核减金额
     */
    private BigDecimal hireLogoutSubtractFee;

    private BigDecimal hireLogoutSubtractFeeAfter;
    /**
     * 单站塔租（年化均值）
     */
    private BigDecimal universalServiceSiteFee;
    /**
     * 新增压降站点
     */
    private Integer universalServiceDropSite;
    /**
     * 塔租折扣
     */
    private BigDecimal universalServiceRentRadio;
    /**
     * 月份数
     */
    private BigDecimal universalServiceMonthNumber;
    /**
     * 免除普遍服务站址-预算金额
     */
    private BigDecimal universalServiceBudgetFee;
    /**
     * 免除普遍服务站址-核减金额
     */
    private BigDecimal universalServiceSubtractFee;

    private BigDecimal universalServiceSubtractFeeAfter;
    /**
     * 预算调整补足-预算金额
     */
    private BigDecimal budgetSupplementBudgetFee;
    /**
     * 预算调整补足-核减金额
     */
    private BigDecimal budgetSupplementSubtractFee;

    private BigDecimal budgetSupplementSubtractFeeAfter;
    /**
     * 油机发电（非包干）-预算金额
     */
    private BigDecimal oilBudgetFee;
    /**
     * 油机发电（非包干）-核减金额
     */
    private BigDecimal oilSubtractFee;

    private BigDecimal oilSubtractFeeAfter;

    /**
     * 调整金额
     */
    private BigDecimal stockOrderAdjustFee;
    private BigDecimal complementNewAdjustFee;
    private BigDecimal rentOrderAdjustFee;
    private BigDecimal newSiteAdjustFee;
    private BigDecimal promoteSharedAdjustFee;
    private BigDecimal hireLogoutAdjustFee;
    private BigDecimal universalServiceAdjustFee;
    private BigDecimal budgetSupplementAdjustFee;
    private BigDecimal oilAdjustFee;

    private BigDecimal stockOrderAdjustFeeAfter;
    private BigDecimal complementNewAdjustFeeAfter;
    private BigDecimal rentOrderAdjustFeeAfter;
    private BigDecimal newSiteAdjustFeeAfter;
    private BigDecimal promoteSharedAdjustFeeAfter;
    private BigDecimal hireLogoutAdjustFeeAfter;
    private BigDecimal universalServiceAdjustFeeAfter;
    private BigDecimal budgetSupplementAdjustFeeAfter;
    private BigDecimal oilAdjustFeeAfter;

    private String stockOrderRemark;
    private String complementNewRemark;
    private String rentOrderRemark;
    private String newSiteRemark;
    private String promoteSharedRemark;
    private String hireLogoutRemark;
    private String universalServiceRemark;
    private String budgetSupplementRemark;
    private String oilRemark;

    /**
     * 单订单服务费（本年新址新建订单年均值）订单数
     */
    private BigDecimal rentOrderNewOrderAmount;

    /**
     * 单订单服务费（本年共址订单年均值）订单数
     */
    private BigDecimal rentOrderShareOrderAmount;

    /**
     * 单站服务费（本年新建站年均值）站址数
     */
    private BigDecimal newSiteCurrentSiteAmount;

    /**
     * 单订单服务费（共址订单年均值）订单数
     */
    private BigDecimal newSiteShareOrderAmount;

    /**
     * 单站服务费（当年独享站年化均值）站址刷量
     */
    private BigDecimal promoteSharedSiteAmount;


    /**
     * 单站服务费（年化均值）站址数量
     */
    private BigDecimal hireLogoutSiteAmount;

    /**
     * 单订单服务费（年化均值）订单数
     */
    private BigDecimal hireLogoutOrderAmount;

    /**
     * 单站塔租（年化均值）站址数量
     */
    private BigDecimal universalServiceSiteAmount;

    /**
     * 快照数据对应流转记录表id
     */
    private String auditInfoId;

    private Integer promoteSharedCurrentNumber;

    /**
     * 预算总览调整
     */
    private BigDecimal pandectAfterFee;
    /**
     * 预算总览调整后费用
     */
    private BigDecimal pandectAfterFeeAfter;

    /**
     * 10-12月起租的新址新建订单在当年使用月数
     */
    private Integer rentOrderSiteMonth;

    /**
     * 10-12月起租的共址改造订单在当年的使用月数
     */
    private Integer rentOrderShareMonth;

    /**
     * 10-12月起租订单在当年执行金额
     */
    private BigDecimal rentOrderExecutionFee;

    // 预算调整补足-执行金额
    private BigDecimal budgetSupplementExecutionFee;

    // 预算调整补足-执行金额备注
    private String budgetSupplementExecutionRemark;

    // 油机发电（非包干）-执行金额
    private BigDecimal oilExecutionFee;

    // 油机发电（非包干）_执行金额备注
    private String oilExecutionRemark;

    // 前一年执行金额
    private BigDecimal historyOneExecutionFee;

    // 前一年租赁站址数
    private Integer historyOneSiteNumber;

    // 前一年单站服务费
    private BigDecimal historyOneSiteFee;

    // 前两年执行金额
    private BigDecimal historyTwoExecutionFee;

    // 前两年租赁站址数
    private Integer historyTwoSiteNumber;

    // 前两年单站服务费
    private BigDecimal historyTwoSiteFee;

    // 前三年执行金额
    private BigDecimal historyThreeExecutionFee;

    // 前三年租赁站址数
    private Integer historyThreeSiteNumber;

    // 前三年单站服务费
    private BigDecimal historyThreeSiteFee;

    // 当年起租站址到达数
    private Integer historyCurrentSiteNumber;

    // 当年单站服务费
    private BigDecimal historyCurrentSiteFee;

    // 下一年起租站址到达数
    private Integer historyNextSiteNumber;

    // 下一年单站服务费
    private BigDecimal historyNextSiteFee;

    private String historyRemark;

    private Integer historySiteNumber;

    private Integer historyMonthSiteNumber;
    public TwrBudgetReport(String prvId, String prvName, Integer productType) {
        this.prvId = prvId;
        this.prvName = prvName;
        this.productType = productType;
    }
}
