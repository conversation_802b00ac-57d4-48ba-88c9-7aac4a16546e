package com.xunge.model.converter;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;

import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.CellData;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;

/**
 * 布尔型转化器
 * <AUTHOR>
 * @date 2020-07-22 12:00
 */
public class BooleanConverter implements Converter<Integer> {

    private static final String YES = "是";
    private static final String NO = "否";
    private static final Integer YES_NUM = 1;
    private static final Integer NO_NUM = 0;

    @Override
    public Class supportJavaTypeKey() {
        return Integer.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }

    @Override
    public Integer convertToJavaData(ReadCellData cellData, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        String value = cellData.getStringValue();
        if (YES.equals(value)){
            return YES_NUM;
        }else if (NO.equals(value)){
            return NO_NUM;
        }
        return null;
    }

    @Override
    public WriteCellData convertToExcelData(Integer value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration)  {
        if (value == null){
            return new WriteCellData<>("");
        }else if (YES_NUM.equals(value)){
            return new WriteCellData<>(YES);
        }else if (NO_NUM.equals(value)){
            return new WriteCellData<>(NO);
        }
        return new WriteCellData<>(value.toString());
    }
}
