package com.xunge.dao.system.oauth;

import com.xunge.model.system.oauth.SysOauthClientVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/4/1
 */
public interface SysOauthClientDetailsMapper {
    SysOauthClientVO selectByPrimaryKey(String clientId);

    /**
     * 根据clientId查询外部系统的页面权限
     * @param clientId
     * @return
     */
    List<String> queryPageUrlByClientId(@Param("clientId") String clientId);
}
