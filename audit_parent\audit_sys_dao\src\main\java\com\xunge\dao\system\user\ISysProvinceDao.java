package com.xunge.dao.system.user;

import com.xunge.model.SysProvinceTreeVO;
import com.xunge.model.system.province.SysProvinceVO;

import java.util.List;
import java.util.Map;

public interface ISysProvinceDao {
    public List<SysProvinceTreeVO> queryOnePro(Map<String, Object> paraMap);

    /**
     * 集团收集表新增功能-查询所有省份
     *
     * @return
     * <AUTHOR>
     */
    public List<SysProvinceTreeVO> queryAllSimpleProvince();

    /**
     * 根据身份Code查询省份，Code也唯一
     */
    public List<SysProvinceVO> queryProvinceByCode(Map<String, Object> paraMap);

    public List<SysProvinceVO> queryProvinceById(Map<String, Object> paraMap);

}
