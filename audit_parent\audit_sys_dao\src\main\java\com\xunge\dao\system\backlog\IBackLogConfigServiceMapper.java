package com.xunge.dao.system.backlog;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.xunge.model.system.backlog.BackLogConfig;

/** 
* @ClassName: IAgentConfigServiceMapper 
* @Description: 代办/已办/工单配置
* @Author：tian
* @Date：2025年5月6日 
*/
public interface IBackLogConfigServiceMapper {

	/** 
	* @Description: 获取系统代办配置
	* <AUTHOR>   
	* @date 2025年5月6日 上午10:15:40 
	* @return  
	*/ 
	List<BackLogConfig> getBackLogConfig();

	/** 
	* @Description: 根据参数获取代办配置信息
	* <AUTHOR>   
	* @date 2025年5月12日 下午5:11:52 
	* @param backLogKey
	* @param procDefKey
	* @return  
	*/ 
	BackLogConfig getBackLogConfigByKeyAndProcDef(@Param("backLogKey")String backLogKey, @Param("procDefKey")String procDefKey);

}
