package com.xunge.model.app;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date
 */
public class ResultApp<T> implements Serializable {

    /**
     * 错误码
     */
    private String code;

    /**
     * 错误描述
     */
    private String msg;

    /**
     * 总的数据条数
     */
    private Long total;


    /**
     * 待办列表或者附件列表
     */
    private List<T> rows;

    /**
     * 详情
     */
    private Object detailObj;

    /**
     * 是否是大集中省份
     */
    private String isFinance;

    /**
     * 是否为直辖市
     */
    private Boolean isMuni;

    /**
     * 无参构造函数，默认成功，无数据
     */
    public ResultApp() {
        this(ErrorCode.SUCCESS, 0, 0, null);
    }

    /**
     * 构造方法
     *
     * @param errorCode 错误码枚举对象
     * @param total     符合查询条件的数据总数
     * @param count     返回数据总数
     * @param rows      数据行
     */
    public ResultApp(ErrorCode errorCode, long total, int count, List<T> rows) {
        super();
        set(errorCode, total, rows);
    }

    public Object getDetailObj() {
        return detailObj;
    }

    public void setDetailObj(Object detailObj) {
        this.detailObj = detailObj;
    }

    public String getIsFinance() {
        return isFinance;
    }

    public void setIsFinance(String isFinance) {
        this.isFinance = isFinance;
    }

    public Boolean getMuni() {
        return isMuni;
    }

    public boolean isMuni() {
        return isMuni;
    }

    public void setMuni(Boolean muni) {
        isMuni = muni;
    }

    public void setMuni(boolean muni) {
        isMuni = muni;
    }

    public void set(ErrorCode errorCode, List<T> rows) {
        set(errorCode, 0L, rows);
    }

    public void set(ErrorCode errorCode) {
        set(errorCode, null);
    }

    /**
     * 设置bean值
     *
     * @param total 符合条件数据总数
     * @param rows  符合条件的数据行
     */
    public void set(ErrorCode errorCode, long total, List<T> rows) {
        if (null == errorCode) {
            this.code = ErrorCode.SUCCESS.code;
            this.msg = ErrorCode.SUCCESS.msg;
        } else {
            this.code = errorCode.code;
            this.msg = errorCode.msg;
        }
        this.total = total;
        if (null == rows) {
            this.rows = new ArrayList<>();
        } else {
            this.rows = rows;
        }
    }

    /**
     * 设置bean值
     *
     * @param total 符合条件数据总数
     * @param row   符合条件的数据行
     */
    public void set(ErrorCode errorCode, long total, T row) {
        List<T> rows = new ArrayList<>();
        if (null != row) {
            rows.add(row);
        }
        set(errorCode, total, rows);
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public long getTotal() {
        return total;
    }

    public void setTotal(Long total) {
        this.total = total;
    }

    public void setTotal(long total) {
        this.total = total;
    }

    public List<T> getRows() {
        return rows;
    }

    public void setRows(List<T> rows) {
        this.rows = rows;
    }
}
