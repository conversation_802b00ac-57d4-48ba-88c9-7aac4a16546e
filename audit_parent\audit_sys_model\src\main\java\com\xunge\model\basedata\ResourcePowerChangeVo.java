package com.xunge.model.basedata;

import java.math.BigDecimal;
import java.util.Date;

public class ResourcePowerChangeVo {

    private String baseresourceId;

    private int baseresourceType;

    private BigDecimal mainEquipmentPower;

    private BigDecimal airConditionPower;

    private BigDecimal towerAirConditionPower;

    private Date changeDate;

    private BigDecimal changeRange;

    public String getBaseresourceId() {
        return baseresourceId;
    }

    public void setBaseresourceId(String baseresourceId) {
        this.baseresourceId = baseresourceId;
    }

    public int getBaseresourceType() {
        return baseresourceType;
    }

    public void setBaseresourceType(int baseresourceType) {
        this.baseresourceType = baseresourceType;
    }

    public BigDecimal getMainEquipmentPower() {
        return mainEquipmentPower;
    }

    public void setMainEquipmentPower(BigDecimal mainEquipmentPower) {
        this.mainEquipmentPower = mainEquipmentPower;
    }

    public BigDecimal getAirConditionPower() {
        return airConditionPower;
    }

    public void setAirConditionPower(BigDecimal airConditionPower) {
        this.airConditionPower = airConditionPower;
    }

    public BigDecimal getTowerAirConditionPower() {
        return towerAirConditionPower;
    }

    public void setTowerAirConditionPower(BigDecimal towerAirConditionPower) {
        this.towerAirConditionPower = towerAirConditionPower;
    }

    public Date getChangeDate() {
        return changeDate;
    }

    public void setChangeDate(Date changeDate) {
        this.changeDate = changeDate;
    }

    public BigDecimal getChangeRange() {
        return changeRange;
    }

    public void setChangeRange(BigDecimal changeRange) {
        this.changeRange = changeRange;
    }
}
