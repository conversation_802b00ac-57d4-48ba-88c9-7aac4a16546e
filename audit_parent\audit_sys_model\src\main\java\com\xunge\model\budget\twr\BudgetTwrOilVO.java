package com.xunge.model.budget.twr;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2022/7/28
 * @description 油机发电(非包干)
 */
@Data
public class BudgetTwrOilVO {

    /**
     * 省份ID
     */
    private String prvId;

    /**
     * 省份名称
     */
    private String prvName;
    /**
     * 业务类型({塔类,1}, {室分,2}, {微站,3}, {传输,4}, {非标,5}, {合计,6})
     */
    private Integer productType;

    /**
     * 油机发电（非包干）-预算金额
     */
    private BigDecimal oilBudgetFee;

    // 油机发电（非包干）-执行金额
    private BigDecimal oilExecutionFee;

    private String oilExecutionRemark;

    /**
     * 油机发电（非包干）-核减金额
     */
    private BigDecimal oilSubtractFee;

    /**
     * 调整金额
     */
    private BigDecimal oilAdjustFee;
    private BigDecimal oilAdjustFeeAfter;

    private String oilRemark;

}
