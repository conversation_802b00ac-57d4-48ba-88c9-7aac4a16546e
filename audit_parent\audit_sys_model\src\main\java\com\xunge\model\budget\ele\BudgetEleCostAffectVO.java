package com.xunge.model.budget.ele;

import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR> LiangCheng
 * @date : 2022-07-04
 * @desc 成本影响表
 */
@Data
@ToString
public class BudgetEleCostAffectVO implements Serializable {
    /**
     * 省份
     */
    private String prvId;

    /**
     * 省份名称
     */
    private String prvName;

    /**
     * 年份
     */
    private Integer onYear;

    /**
     * 工作名称
     */
    private String workName;

    /**
     * 具体描述
     */
    private String describe;

    /**
     * 是否已体现在成本校正表中(0：是 1：否)
     */
    private Integer embody;

    /**
     * 节约或增长(填-1或者1，对应填写的-和+)
     */
    private Integer frugalGrow;

    /**
     * 单位影响因子
     */
    private BigDecimal affectFactor;

    /**
     * 计量单位(元/站，元/机架)
     */
    private String affectUnit;

    /**
     * 数量
     */
    private Integer affectNum;

    /**
     * 合计影响,单位影响因子*数量
     */
    private BigDecimal totalAffect;

    /**
     * 说明
     */
    private String remark;

    /**
     * 节点
     */
    private Integer nodeType;

    private String workOrderId;
}

