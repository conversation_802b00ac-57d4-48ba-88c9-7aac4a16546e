package com.xunge.dao.selfelec;

import com.xunge.model.selfelec.EleBillamount;
import com.xunge.model.selfelec.EleBillamountExample;
import com.xunge.model.selfelec.EleBillamountVerificationDetail;
import com.xunge.model.selfelec.EleBillamountdetail;
import com.xunge.model.selfelec.billamount.AccrualBillamountDetailQueryDto;
import com.xunge.model.selfelec.billamount.AccrualBillamountQueryDto;
import com.xunge.model.selfelec.billamount.AccrualSecondBillamountDto;
import com.xunge.model.selfelec.billamount.PaymentOffsDetailDto;
import com.xunge.model.selfrent.billamount.EleBillamountDetailVO;
import com.xunge.model.selfrent.billamount.EleOtherBillamountDetailVO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.cursor.Cursor;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public interface EleBillamountMapper {
    int countByExample(EleBillamountExample example);

    int deleteByExample(EleBillamountExample example);

    int deleteByPrimaryKey(String billamountId);

    int insert(EleBillamount record);

    int insertSelective(EleBillamount record);

    List<EleBillamount> selectByExample(EleBillamountExample example);

    EleBillamount selectByPrimaryKey(String billamountId);

    int updateByExampleSelective(@Param("record") EleBillamount record, @Param("example") EleBillamountExample example);

    int updateByExample(@Param("record") EleBillamount record, @Param("example") EleBillamountExample example);

    int updateByPrimaryKeySelective(EleBillamount record);

    int updateByPrimaryKey(EleBillamount record);

    int updateBillamountIdById(Map<String, Object> map);

    int selectPushedBill(Map<String, Object> map);

    int selectPushBackBill(Map<String, Object> map);

    /**
     * 报账汇总查询
     */
    List<String> queryBillAmountIds(Map<String, Object> params);

    List<EleBillamount> queryBillAmount(@Param("billAmountIdList") List<String> billAmountIdList);

    List<EleBillamount> selectBillamountList(Map<String, Object> params);

    List<EleBillamountdetail> selectBillamountdetailList(Map<String, Object> params);

    List<EleBillamount> selectBillamountList2(Map<String, Object> params);

    EleBillamount queryBillamountById(Map<String, Object> maps);

    void updateBillamountIdIsNull(@Param("ids") List<String> ids);

    void updateBillamountIdIsNullToTele(@Param("ids") List<String> ids);

    int updateBillamountAdjustById(EleBillamount record);

    void updateBillamountIdIsNullByDetailId(@Param("billamountdetailId") String billamountdetailId);

    List<EleBillamount> selectBillamountExportList(Map<String, Object> params);
    List<EleBillamount> exportBillAmount(Map<String, Object> params);
    List<EleBillamount> exportBillAmountOld(Map<String, Object> params);

    List<String> queryInquiryClaimPmtBillamount(Map<String, Object> params);

    List<EleBillamountVerificationDetail> exportVerificationDetails(Map paramMap);

    void editPayment(EleBillamount record);

    List<EleBillamount> selectBillamountByIds(Map<String, Object> cond);

    List<EleBillamount> selectEleLoanBillamountByIds(Map<String, Object> cond);

    int updateBillamountFinanceState(@Param("billamountId") String billamountId, @Param("billType") Integer billType);

    int rebuildBillamount(EleBillamount record);

    int rebuildBillamountdetail(EleBillamount record);

    int rebuildBillamountPayment(EleBillamount record);

    List<EleBillamountDetailVO> exportEleBillamountDetail(Map<String, Object> params);

    List<String> selectBillamountidSForExport(Map<String, Object> params);

    List<String> selectTeleBillamountidsForExport(Map<String, Object> params);

    void editBillamountReimburser(Map<String, Object> map);

    List<AccrualSecondBillamountDto> selectAccrualSecondDetail(AccrualBillamountQueryDto accrualBillamountQueryDto);

    List<AccrualBillamountDetailQueryDto> selectAccrualCondition(String billamountId);

    /**
     * 更新计提二次汇总明细可冲销金额
     * @param secondBillamountId
     * @return
     */
    int updateLeftOffsAmount(String secondBillamountId);

    /**
     * 更新计提汇总单可冲销金额
     * @param billamountId 计提汇总单id
     * @param offsAmount 本次冲销金额
     * @param leftOffsAmount
     * @return
     */
    int updateAccrualLeftOffsAmount(@Param("billamountId") String billamountId,@Param("offsAmount") BigDecimal offsAmount,@Param("leftOffsAmount") BigDecimal leftOffsAmount);

    int addPaymentOffsDetail(List<PaymentOffsDetailDto> list);

    /**
     * 修改电费汇总单冲销金额同时修改计提二次汇总可冲销余额
     * @param dto
     * @return
     */
    int updatePaymentOffsDetail(PaymentOffsDetailDto dto);

    /**
     * 根据汇总单冲销明细id查询二次汇总id
     * @param id
     * @return
     */
    String selectAccrualSecondDetailId(String id);

    /**
     * 删除汇总单前先恢复计提二次汇总可冲销余额
     * @param dto
     * @return
     */
    int revertPaymentOffsDetail(PaymentOffsDetailDto dto);

    /**
     * 删除电费汇总单的冲销明细
     * @param id
     * @return
     */
    int deletePaymentOffsDetail(String id);

    PaymentOffsDetailDto selectLeftOffsAmount(String secondBillamountId);

    PaymentOffsDetailDto selectOldOffsAmount(String id);

    List<AccrualSecondBillamountDto> selectPaymentOffsDetail(AccrualBillamountQueryDto accrualBillamountQueryDto);
	BigDecimal getElePaymentOffsAmount(String billamountId);

    String checkAccrualModel(@Param("prvId") String prvId);

    BigDecimal queryAllOffsAmount(String secondBillamountId);

	/** 
	* @Description: 查询列账冲销金额
	* <AUTHOR>   
	* @date 2024年5月27日 下午3:17:28 
	* @param params
	* @return  
	*/ 
	BigDecimal selectPaymentOffsDetailAmount(AccrualBillamountQueryDto params);

    List<EleBillamount> selectBillamountById(String billamountId);

    List<EleBillamount> selectBillamountByIdForTw(String billamountId);

}
