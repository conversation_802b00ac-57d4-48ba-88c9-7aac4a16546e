package com.xunge.model.budget.twr;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @Description:预估执行总览
 * @Author: dxd
 * @Date: 2024/2/1 15:57
 */
@Data
public class BudgetEstimatedVo {

    /**
     * 省份ID
     */
    private String prvId;

    /**
     * 省份名称
     */
    private String prvName;
    /**
     * 业务类型({塔类,1}, {室分,2}, {微站,3}, {传输,4}, {非标,5}, {合计,6})
     */
    private Integer productType;

    private BigDecimal stockOrderBudgetFee;
    private BigDecimal rentOrderExecutionFee;
    private BigDecimal budgetSupplementExecutionFee;
    private BigDecimal oilExecutionFee;
    private BigDecimal estimatedPriceBudgetFee;
}
