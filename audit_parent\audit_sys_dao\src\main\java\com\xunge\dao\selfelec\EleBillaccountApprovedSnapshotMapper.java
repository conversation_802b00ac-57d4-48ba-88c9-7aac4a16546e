package com.xunge.dao.selfelec;


import com.xunge.model.selfelec.EleBillaccountApprovedSnapshot;

/**
 * <AUTHOR>
 * @description 针对表【ele_billaccount_approved_snapshot(电费报账点审核通过时的快照)】的数据库操作Mapper
 * @createDate 2024-03-05 10:17:24
 * @Entity generator.domain.EleBillaccountApprovedSnapshot
 */
public interface EleBillaccountApprovedSnapshotMapper {

    int deleteByPrimaryKey(String id);

    int insert(EleBillaccountApprovedSnapshot record);

    int insertSelective(EleBillaccountApprovedSnapshot record);

    EleBillaccountApprovedSnapshot selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(EleBillaccountApprovedSnapshot record);

    int updateByPrimaryKey(EleBillaccountApprovedSnapshot record);
    EleBillaccountApprovedSnapshot selectByBillaccountId(String billaccountId);
}
