package com.xunge.model.external.pojo;

import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @Description 从南网抓取记录日志表
 * <AUTHOR>
 * @Date 2021/1/28 14:04
 * @modifier ZXX
 * @date 2021/1/28 14:04
 * @Version 1.0
 **/
public class ExtCrawlerRecord implements Serializable {
    private static final long serialVersionUID = -3987094355985915338L;
    private Integer id; //主键
    private String operator; //操作用户
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date operateTime; //操作时间
    private String annual; //所选年度
    private String accountNumbers;////所选户号  ","分割
    private List<String> accountNumberList;//所选户号  ","分割
    private Integer totalCount;//总条数
    private Integer successCount;//成功条数
    private Integer isSuccess; //是否成功
    private String reason; //失败原因
    private Integer createPaymentState; //建单状态
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createPaymentTime; //建单时间

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public Date getOperateTime() {
        return operateTime;
    }

    public void setOperateTime(Date operateTime) {
        this.operateTime = operateTime;
    }

    public String getAnnual() {
        return annual;
    }

    public void setAnnual(String annual) {
        this.annual = annual;
    }

    public String getAccountNumbers() {
        return accountNumbers;
    }

    public void setAccountNumbers(String accountNumbers) {
        this.accountNumbers = accountNumbers;
    }

    public List<String> getAccountNumberList() {
        return accountNumberList;
    }

    public void setAccountNumberList(List<String> accountNumberList) {
        this.accountNumberList = accountNumberList;
    }

    public Integer getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(Integer totalCount) {
        this.totalCount = totalCount;
    }

    public Integer getSuccessCount() {
        return successCount;
    }

    public void setSuccessCount(Integer successCount) {
        this.successCount = successCount;
    }

    public Integer getIsSuccess() {
        return isSuccess;
    }

    public void setIsSuccess(Integer isSuccess) {
        this.isSuccess = isSuccess;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public Integer getCreatePaymentState() {
        return createPaymentState;
    }

    public void setCreatePaymentState(Integer createPaymentState) {
        this.createPaymentState = createPaymentState;
    }

    public Date getCreatePaymentTime() {
        return createPaymentTime;
    }

    public void setCreatePaymentTime(Date createPaymentTime) {
        this.createPaymentTime = createPaymentTime;
    }
}
