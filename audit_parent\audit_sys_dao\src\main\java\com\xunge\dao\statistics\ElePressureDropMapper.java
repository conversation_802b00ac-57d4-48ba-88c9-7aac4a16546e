package com.xunge.dao.statistics;

import com.xunge.model.statistics.ElePressuredropDetail;
import com.xunge.model.statistics.ElePressuredropStatistics;

import java.util.List;
import java.util.Map;

public interface ElePressureDropMapper {

    List<ElePressuredropDetail> queryDetailList(Map<String, Object> param);

    List<ElePressuredropStatistics> queryStatisticsList(Map<String, Object> param);

    ElePressuredropStatistics getTotalStatistics(Map<String, Object> param);

}
