package com.xunge.model.finance.ext.accClaim.accrual;

import com.xunge.core.model.UserLoginInfo;
import com.xunge.model.finance.ext.accClaim.accwsdl.OutputParameters;
import com.xunge.model.system.user.SysUserVO;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 计提汇总单推送返回数据
 */
@Data
@NoArgsConstructor
public class AccrualClaimDataRsp {

    private OutputParameters response;

    private UserLoginInfo loginInfo;

    private EleAccrualBillamount eleAccrualBillamount;

    private String inputJsonStr;

    private String msg;

    private SysUserVO sysUserVO;

    /**
     * 费用类型 0：电费 1：租费 2：三方塔
     */
    private Integer businessType;

}
