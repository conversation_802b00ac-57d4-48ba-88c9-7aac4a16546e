package com.xunge.dao.report.Impl;

import com.xunge.dao.AbstractBaseDao;
import com.xunge.dao.report.IRptEleprotocolDao;

import java.util.List;
import java.util.Map;

public class RptEleprotocolDaoImpl extends AbstractBaseDao implements IRptEleprotocolDao {

    final String Namespace = "com.xunge.mapping.RptEleprotocolVOMapper.";

    @Override
    public List<Map<String, Object>> queryRptEleprotocolByPrvid(Map<String, Object> map) {
        return this.getSqlSession().selectList(Namespace + "queryRptEleprotocolByPrvid", map);
    }

    @Override
    public List<Map<String, Object>> queryRptEleprotocolByPregid(Map<String, Object> map) {
        return this.getSqlSession().selectList(Namespace + "queryRptEleprotocolByPregid", map);
    }

    @Override
    public List<Map<String, Object>> queryRptEleprotocol(Map<String, Object> map) {
        return this.getSqlSession().selectList(Namespace + "queryRptEleprotocol", map);
    }

}
