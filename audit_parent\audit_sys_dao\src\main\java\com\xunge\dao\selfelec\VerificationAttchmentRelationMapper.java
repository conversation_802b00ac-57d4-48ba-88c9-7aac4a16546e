package com.xunge.dao.selfelec;

import com.xunge.model.selfelec.PaymentAttchmentRelation;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019年04月29日
 */
public interface VerificationAttchmentRelationMapper {

    /**
     * 根据条件查询数据
     *
     * @param paramMap
     * @return
     */
    List<PaymentAttchmentRelation> queryAttchmentRelationList(Map<String, Object> paramMap);

    /**
     * 新增数据
     *
     * @param paymentAttchmentRelation
     */
    void insertAttchmentRelation(PaymentAttchmentRelation paymentAttchmentRelation);

    /**
     * 删除数据
     *
     * @param paramMap
     */
    void delAttchmentRelationById(Map<String, Object> paramMap);

    /**
     * 批量新增
     *
     * @param relationList
     */
    void insertAttchmentRelationByBatch(List<PaymentAttchmentRelation> relationList);

    /**
     * 根据缴费单id删除附件
     *
     * @param paramMap
     */
    void delAttchmentRelationByPaymentId(Map<String, Object> paramMap);

    /**
     * 根据id更新附件的id值
     *
     * @param
     */
    void updatePaymentByPaymentdetailId(@Param("attachementBussinessId") String attachementBussinessId, @Param("billaccountpaymentdetailId") String billaccountpaymentdetailId);
}
