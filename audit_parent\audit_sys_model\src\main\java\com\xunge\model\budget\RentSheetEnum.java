package com.xunge.model.budget;

public enum RentSheetEnum {

    EXIST(1,"本年存量站在次年预算"),ADD(2,"次年新增站在次年预算"),DEL(3,"次年退网站在次年退网预算"),SUPPLEMENT(4,"本年底新增站在次年预算");

    private Integer sheetType;

    private String sheetName;

    RentSheetEnum(Integer sheetType, String sheetName) {
        this.sheetType = sheetType;
        this.sheetName = sheetName;
    }

    public Integer getSheetType() {
        return sheetType;
    }

    public String getSheetName() {
        return sheetName;
    }

    public static RentSheetEnum matchSheetType(Integer sheetType){
        for (RentSheetEnum singleEnum : RentSheetEnum.values()){
            if (sheetType != null && singleEnum.getSheetType().equals(sheetType)){
                return singleEnum;
            }
        }
        return null;
    }
}
