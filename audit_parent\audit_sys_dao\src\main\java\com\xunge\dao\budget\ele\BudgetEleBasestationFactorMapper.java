package com.xunge.dao.budget.ele;

import com.xunge.model.budget.ele.BudgetEleBasestationFactorVO;
import com.xunge.model.budget.ele.BudgetEleParamVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @author: LiangCheng
 * Date: 2022/7/4 15:15
 * Description:基站影响因子
 */
public interface BudgetEleBasestationFactorMapper {

    /**
     * 保存基站影响因子
     */
    void saveEleBasestationFactor(BudgetEleBasestationFactorVO basestationFactorVO);

    List<BudgetEleBasestationFactorVO> getEleBasestationFactor(BudgetEleParamVO budgetEleParamVO);

    void editEleBasestationFactorGroupDraft(BudgetEleBasestationFactorVO budgetEleBasestationFactorVO);

    void editEleBasestationFactor(BudgetEleBasestationFactorVO budgetEleBasestationFactorVO);

    void delBudgetEleData(BudgetEleParamVO budgetEleParamVO);

}
