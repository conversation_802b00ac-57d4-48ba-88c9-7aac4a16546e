package com.xunge.comm.elec;

/**
 * 标杆值范围枚举
 */
public enum BenchmarkRangeEnum {

    /**
     * 否
     */
    ZERO(0, "否"),

    /**
     * 高于上限超标
     */
    ONE(1, "低于下限超标"),

    /**
     *
     */
    TWO(2, "高于上限超标"),
    /**
     * 标杆值为0，负数时，的超标类型为3
     */
    THREE(3, "是");

    public final int code;
    public final String text;

    BenchmarkRangeEnum(int code, String text) {
        this.code = code;
        this.text = text;
    }

    /**
     * 获取枚举信息
     *
     * @param code 状态码
     * @return 结果
     */
    public static BenchmarkRangeEnum getBenchmarkRangeEnum(int code) {
        BenchmarkRangeEnum temp = null;
        for (BenchmarkRangeEnum benchmarkRangeEnum : BenchmarkRangeEnum.values()) {
            if (benchmarkRangeEnum.getCode() == code) {
                temp = benchmarkRangeEnum;
                break;
            }
        }
        return temp;
    }

    public int getCode() {
        return code;
    }

    public String getText() {
        return text;
    }
}
