package com.xunge.dao.report;

import com.xunge.model.selfrent.resource.ZeroFlowResourceVO;

import java.util.List;
import java.util.Map;

public interface IrptResourcesDao {
    /**
     * 查询资源点报表信息通过省份
     *
     * @return
     * <AUTHOR>
     */
    public List<Map<String, Object>> queryResourcesByPrv(Map<String, Object> map);

    /**
     * 查询资源点报表信息通过地市
     *
     * @return
     * <AUTHOR>
     */
    public List<Map<String, Object>> queryResourcesByPreg(Map<String, Object> map);

    public List<Map<String, Object>> queryResources(Map<String, Object> map);

    /**
     * 查询资源点报表信息通过省份
     *
     * @return
     * <AUTHOR>
     */
    public List<Map<String, Object>> queryResPointByPrv(Map<String, Object> map);

    /**
     * 查询资源点报表信息通过地市
     *
     * @return
     * <AUTHOR>
     */
    public List<Map<String, Object>> queryResPointByPreg(Map<String, Object> map);

    /**
     * 查询资源点报表信息通过地市
     *
     * @return
     * <AUTHOR>
     */
    public List<Map<String, Object>> queryResPoint(Map<String, Object> map);

    List<ZeroFlowResourceVO> queryRentZeroFlowResource(Map<String, Object> map);

    int updateZeroNote(ZeroFlowResourceVO vo);
}