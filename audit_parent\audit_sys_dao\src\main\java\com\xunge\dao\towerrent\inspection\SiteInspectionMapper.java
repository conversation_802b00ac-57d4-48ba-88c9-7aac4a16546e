package com.xunge.dao.towerrent.inspection;

import com.xunge.model.towerrent.inspection.ResourceInfoDto;
import com.xunge.model.towerrent.inspection.SiteCompareVO;
import com.xunge.model.towerrent.inspection.SiteInfoVO;

import java.util.List;
import java.util.Map;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2019/10/9.
 */
public interface SiteInspectionMapper {
    /**
     * 查询现场巡检信息
     *
     * @param param
     * @return
     */
    List<SiteInfoVO> qrySiteInspection(Map<String, Object> param);

    /**
     * 查询crm侧信息
     *
     * @param param
     * @return
     */
    List<SiteInfoVO> qryCrmInfo(Map<String, Object> param);

    /**
     * 查询综资侧信息
     *
     * @param param
     * @return
     */
    List<SiteInfoVO> qryResourceInfo(Map<String, Object> param);

    List<ResourceInfoDto> queryResourceInfo(Map<String, Object> param);

    List<ResourceInfoDto> queryResourceInfo1(Map<String, Object> param);

    /**
     * 根据inspectionId查询对应的比对信息
     *
     * @param param
     * @return
     */
    List<SiteCompareVO> qryInspectionCompare(Map<String, Object> param);

    /**
     * 保存导入信息
     *
     * @param siteInfoVO
     * @return
     */
    int addSiteInspection(SiteInfoVO siteInfoVO);

    /**
     * 校验用户录入地市区县是否存在
     *
     * @param map
     * @return
     */
    SiteInfoVO checkPrgNameAndRegName(Map<String, Object> map);

    int addCompareInfo(List<SiteCompareVO> list);

    /**
     * 查询CRM侧信息
     * @param param
     * @return
     */
    //List<SiteInfoVO> qryCrmInfo(Map<String, Object> param);

    /**
     * 巡检统计分析
     *
     * @param param
     * @return
     */
    List<SiteInfoVO> inspectionAnsalysis(Map<String, Object> param);

    /**
     * 巡检统计分析 查询总数
     *
     * @param param
     * @return
     */
    List<SiteInfoVO> queryInspectionAnalyseCount(Map<String, Object> param);

    /**
     * 巡检统计分析查询详情
     *
     * @param param
     * @return
     */
    List<SiteInfoVO> queryInspectionAnalyseDetail(Map<String, Object> param);

    List<SiteInfoVO> qryAllCount(Map<String, Object> param);

    int deleteInspection(String inspectionId);

    List<Map<String, Object>> qryAttachments(Map<String, Object> param);

    int addImgTempInfo(Map<String, Object> param);

    List<Map<String, Object>> qryImgTempInfo(Map<String, Object> param);

    int delImgTempInfo(Map<String, Object> param);
}
