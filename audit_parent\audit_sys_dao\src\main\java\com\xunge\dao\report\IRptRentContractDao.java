package com.xunge.dao.report;

import com.xunge.core.page.Page;
import com.xunge.model.report.RptTimelyVO;

import java.util.List;
import java.util.Map;

public interface IRptRentContractDao {
    /**
     * 根据省份id查询各地市租费合同数据
     *
     * @return
     * <AUTHOR>
     */
    public List<Map<String, Object>> queryRptRentcontractByPrvid(Map<String, Object> map);

    /**
     * 根据地市id查询区县租费合同数据
     *
     * @return
     * <AUTHOR>
     */
    public List<Map<String, Object>> queryRptRentcontractByPregid(Map<String, Object> map);

    public List<Map<String, Object>> queryRptRentcontract(Map<String, Object> map);

    Page<List<RptTimelyVO>> queryTimely(Map<String, Object> paraMap, int pageNumber, int pageSize);
}
