package com.xunge.model.budget;

import com.xunge.model.budget.tower.BudgetFiledValidate;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 三方塔预算表
 * @TableName three_tower_budget_report
 */
@Data
public class ThreeTowerBudgetReport extends BudgetBaseVo implements Serializable {
    /**
     * 主键
     */
    private String id;

    /**
     * 预算表类型名称
     */
    private String budgetSheetName;

    /**
     * 省份ID
     */
    private String prvId;

    /**
     * 省份名称
     */
    private String prvName;

    /**
     * 预算表类型，1：年底存量；2：明年新增；3：明年退网；0：总览
     */
    private Integer budgetSheetType;

    /**
     * 流转类型，代表各个节点1：工单或草稿；2：省侧提交或驳回；3：确认完结后维护；4：最终预算；5：预算测算
     */
    private Integer flowType;

    /**
     * 单站服务费（系统数据）
     */
    @BudgetFiledValidate(true)
    private BigDecimal avgSiteAmountSys;

    private Integer siteNum;

    /**
     * 规模（系统数据）
     */
    private Integer siteScaleSys;

    /**
     * 规模
     */
    private Integer siteScale;

    /**
     * 月数
     */
    @BudgetFiledValidate(true)
    private BigDecimal months;

    /**
     * 预算金额
     */
    private BigDecimal budgetAmount;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 核减金额（存量+新增+退网）
     */
    private BigDecimal accountAmount;

    /**
     * 总览计算核减后金额的核减金额值（存量+新增-退网）
     */
    private BigDecimal calAccountAmount;

    /**
     * 核减后金额
     */
    private BigDecimal afterAccountAmount;

    /**
     * 调整金额：各预算表每个省一份（存量+新增+退网）
     */
    private BigDecimal adjustAmount;

    /**
     * 总览计算调整后金额的调整金额值（存量+新增-退网）
     */
    private BigDecimal calAdjustAmount;

    /**
     * 调整后金额：各预算表每个省一份
     */
    private BigDecimal afterAdjustAmount;

    /**
     * 快照数据对应流转记录表id
     */
    private String auditInfoId;

    /**
     * 如果需要更改同一节点的信息则需要传递此字段
     */
    private Integer oldFlowType;

    /**
     * 总览表字段：预算金额（存量表）
     */
    private BigDecimal existBudgetAmount;

    /**
     * 总览表字段：预算金额（新增表）
     */
    private BigDecimal addBudgetAmount;

    /**
     * 总览表字段：预算金额（退网表）
     */
    private BigDecimal delBudgetAmount;

    public ThreeTowerBudgetReport(Integer budgetSheetType, String budgetSheetName, String prvId, String prvName) {
        this.budgetSheetType = budgetSheetType;
        this.budgetSheetName = budgetSheetName;
        this.prvId = prvId;
        this.prvName = prvName;
    }

    public ThreeTowerBudgetReport() {
        super();
    }

    private static final long serialVersionUID = 1L;
}