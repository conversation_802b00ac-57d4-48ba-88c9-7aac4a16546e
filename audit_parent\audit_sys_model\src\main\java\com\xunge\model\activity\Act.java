package com.xunge.model.activity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.xunge.core.model.UserLoginInfo;
import com.xunge.core.util.StrUtil;
import com.xunge.core.util.TimeUtils;

import cn.afterturn.easypoi.excel.annotation.Excel;

import org.activiti.engine.history.HistoricActivityInstance;
import org.activiti.engine.history.HistoricTaskInstance;
import org.activiti.engine.repository.ProcessDefinition;
import org.activiti.engine.runtime.ProcessInstance;
import org.activiti.engine.task.Task;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 工作流Entity
 *
 * <AUTHOR>
 * @version 2013-11-03
 */
public class Act implements Serializable, Comparable<Act> {

    /**
     *
     */
    private static final long serialVersionUID = -8263886550328167849L;
    private String taskId; // 任务编号
    @Excel(name = "审核环节", orderNum = "2", replace = {"-_null"})
    private String taskName; // 任务名称
    private String taskDefKey; // 任务定义Key（任务环节标识）

    private String procInsId; // 流程实例ID
    private String procDefId; // 流程定义ID
    private String procDefKey; // 流程定义Key（流程定义标识）
    private String regCode; // 流程所属区域(区分区域)

    private String businessTable; // 业务绑定Table
    private String businessId; // 业务绑定ID
    @Excel(name = "待办类别", orderNum = "1", replace = {"-_null"})
    private String title; // 任务标题
    @Excel(name = "已办类别", orderNum = "1", replace = {"-_null"})
    private String atitle; // 任务标题
    /**
     * 紧急程度
     */
    private String emergencyDegree;
    private UserLoginInfo loginInfo;
    @Excel(name = "处理状态", orderNum = "10", replace = {"已驳回_-1", "待处理_0", "已处理_1", "已签收_9", "已完结_11"})
    private String status; // 任务状态（todo/claim/finish）
    //	private String procExecUrl; 	// 流程执行（办理）RUL
    private String nextTaskStatus; // 任务状态（todo/claim/finish）
    @Excel(name = "上级审核意见", orderNum = "6", replace = {"-_null"})
    private String comment; // 任务意见
    private String flag; // 意见状态
    private transient Task task; // 任务对象
    private transient ProcessDefinition procDef; // 流程定义对象
    private transient ProcessInstance procIns; // 流程实例对象
    private transient HistoricTaskInstance histTask; // 历史任务
    private transient HistoricActivityInstance histIns; //历史活动任务
    private String assignee; // 任务执行人编号
    private String assigneeName; // 任务执行人名称
    private List<String> assigneeNameGroup; // 任务执行人角色
    @JsonIgnore
    private Variable vars; // 流程变量
    @Excel(name = "产生时间", orderNum = "8", exportFormat = "yyyy-MM-dd HH:mm:ss", replace = {"-_null"})
    private Date beginDate; // 开始查询日期
    @Excel(name = "处理时间", orderNum = "9", exportFormat = "yyyy-MM-dd HH:mm:ss" , replace = {"-_null"})
    private Date endDate; // 结束查询日期
    @Excel(name = "上级审核人", orderNum = "5", replace = {"-_null"})
    private String superiorAssigneeName; // 上审核人
    @Excel(name = "审核时间", orderNum = "7", exportFormat = "yyyy-MM-dd HH:mm:ss", replace = {"-_null"})
    private String superiorAssigneeTime; // 审核时间
    private String superAuditLink; ////上级审核环节
    //	private Variable taskVars; 	// 流程任务变量
    private Date superiorEndTime; // 上级审核时间
    private String userLoginName; // 用户登录账号
    private List<Act> list; // 任务列表
    private String category;
    @Excel(name = "工单名称", orderNum = "3", replace = {"-_null"})
    private String name;
    private String majorId;
    private String operateType;
    private String contractId;
    private String businessKey;
    private String dataState;//数据的状态
    private String draftType;
    private String PROC_INST_ID_;
    private Date dealStartTime;
    private Date dealEndTime;
    /**
     * 审核结果  开始，通过，驳回，撤回
     */
    private String auditMsg;
    /*其他信息，想存啥就存啥*/
    private Map<String, Object> otherInfo;
    @Excel(name = "报账金额", orderNum = "4", replace = {"-_null"})
    private BigDecimal billAmount;
    public Act() {
        super();
    }

    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    public String getEmergencyDegree() {
        return emergencyDegree;
    }

    public void setEmergencyDegree(String emergencyDegree) {
        this.emergencyDegree = emergencyDegree;
    }

    public UserLoginInfo getLoginInfo() {
        return loginInfo;
    }

    public void setLoginInfo(UserLoginInfo loginInfo) {
        this.loginInfo = loginInfo;
    }

    public String getSuperiorAssigneeName() {
        return superiorAssigneeName;
    }

    public void setSuperiorAssigneeName(String superiorAssigneeName) {
        this.superiorAssigneeName = superiorAssigneeName;
    }

    public String getTaskId() {
        if (taskId == null && task != null) {
            taskId = task.getId();
        }
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public String getAuditMsg() {
        return auditMsg;
    }

    public void setAuditMsg(String auditMsg) {
        this.auditMsg = auditMsg;
    }

    public String getTaskName() {
        if (taskName == null && task != null) {
            taskName = task.getName();
        }
        return taskName;
    }

    public void setTaskName(String taskName) {
        this.taskName = taskName;
    }

    public String getTaskDefKey() {
        if (taskDefKey == null && task != null) {
            taskDefKey = task.getTaskDefinitionKey();
        }
        return taskDefKey;
    }

    public void setTaskDefKey(String taskDefKey) {
        this.taskDefKey = taskDefKey;
    }

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    public Date getTaskCreateDate() {
        if (task != null) {
            return task.getCreateTime();
        }
        return null;
    }

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    public Date getTaskEndDate() {
        if (histTask != null) {
            return histTask.getEndTime();
        }
        return null;
    }

    @JsonIgnore
    public Task getTask() {
        return task;
    }

    public void setTask(Task task) {
        this.task = task;
    }

    @JsonIgnore
    public ProcessDefinition getProcDef() {
        return procDef;
    }

    public void setProcDef(ProcessDefinition procDef) {
        this.procDef = procDef;
    }

    @JsonIgnore
    public ProcessInstance getProcIns() {
        return procIns;
    }

    public void setProcIns(ProcessInstance procIns) {
        this.procIns = procIns;
        if (procIns != null && procIns.getBusinessKey() != null) {
            String[] ss = procIns.getBusinessKey().split(":");
            if (ss != null && ss.length == 2) {
                setBusinessTable(ss[0]);
                setBusinessId(ss[1]);
            }
        }
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    @JsonIgnore
    public HistoricTaskInstance getHistTask() {
        return histTask;
    }

    public void setHistTask(HistoricTaskInstance histTask) {
        this.histTask = histTask;
    }

    @JsonIgnore
    public HistoricActivityInstance getHistIns() {
        return histIns;
    }

    public void setHistIns(HistoricActivityInstance histIns) {
        this.histIns = histIns;
    }

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    public Date getBeginDate() {
        return beginDate;
    }

    public void setBeginDate(Date beginDate) {
        this.beginDate = beginDate;
    }

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }

    public String getFlag() {
        return flag;
    }

    public void setFlag(String flag) {
        this.flag = flag;
    }

    public String getProcDefId() {
        if (procDefId == null && task != null) {
            procDefId = task.getProcessDefinitionId();
        }
        return procDefId;
    }

    public void setProcDefId(String procDefId) {
        this.procDefId = procDefId;
    }

    public String getProcInsId() {
        if (procInsId == null && task != null) {
            procInsId = task.getProcessInstanceId();
        }
        return procInsId;
    }

    public void setProcInsId(String procInsId) {
        this.procInsId = procInsId;
    }

    public String getBusinessId() {
        return businessId;
    }

    public void setBusinessId(String businessId) {
        this.businessId = businessId;
    }

    public String getBusinessTable() {
        return businessTable;
    }

    public void setBusinessTable(String businessTable) {
        this.businessTable = businessTable;
    }

    public String getAssigneeName() {
        return assigneeName;
    }

    public void setAssigneeName(String assigneeName) {
        this.assigneeName = assigneeName;
    }

    public String getAssignee() {
        if (assignee == null && task != null) {
            assignee = task.getAssignee();
        }
        return assignee;
    }

    public void setAssignee(String assignee) {
        this.assignee = assignee;
    }

    public List<String> getAssigneeNameGroup() {
        return assigneeNameGroup;
    }

    public void setAssigneeNameGroup(List<String> assigneeNameGroup) {
        this.assigneeNameGroup = assigneeNameGroup;
    }

    public List<Act> getList() {
        return list;
    }

    public void setList(List<Act> list) {
        this.list = list;
    }

    @JsonIgnore
    public Variable getVars() {
        return vars;
    }

    public void setVars(Variable vars) {
        this.vars = vars;
    }

    /**
     * 通过Map设置流程变量值
     *
     * @param map
     */
    public void setVars(Map<String, Object> map) {
        this.vars = new Variable(map);
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getMajorId() {
        return majorId;
    }

    public void setMajorId(String majorId) {
        this.majorId = majorId;
    }

    public String getOperateType() {
        return operateType;
    }

    public void setOperateType(String operateType) {
        this.operateType = operateType;
    }

    //	public Variable getTaskVars() {
    //		return taskVars;
    //	}
    //
    //	public void setTaskVars(Variable taskVars) {
    //		this.taskVars = taskVars;
    //	}
    //
    //	/**
    //	 * 通过Map设置流程任务变量值
    //	 * @param map
    //	 */
    //	public void setTaskVars(Map<String, Object> map) {
    //		this.taskVars = new Variable(map);
    //	}

    /**
     * 获取流程定义KEY
     *
     * @return
     */
    public String getProcDefKey() {
        if (StrUtil.isBlank(procDefKey) && StrUtil.isNotBlank(procDefId)) {
            procDefKey = StrUtil.split(procDefId, ":")[0];
        }
        return procDefKey;
    }

    public void setProcDefKey(String procDefKey) {
        this.procDefKey = procDefKey;
    }

    /**
     * 获取过去的任务历时
     *
     * @return
     */
    public String getDurationTime() {
        if (histIns != null && histIns.getDurationInMillis() != null) {
            return TimeUtils.toTimeString(histIns.getDurationInMillis());
        }
        return "";
    }

    /**
     * 是否是一个待办任务
     *
     * @return
     */
    public boolean isTodoTask() {
        return "todo".equals(status) || "claim".equals(status);
    }

    /**
     * 是否是已完成任务
     *
     * @return
     */
    public boolean isFinishTask() {
        return "finish".equals(status) || StrUtil.isBlank(taskId);
    }

    public String getRegCode() {
        return regCode;
    }

    public void setRegCode(String regCode) {
        this.regCode = regCode;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((businessId == null) ? 0 : businessId.hashCode());
        result = prime * result + ((businessTable == null) ? 0 : businessTable.hashCode());
        return result;
    }


    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        Act act = (Act) o;
        if (businessId == null) {
            if (act.businessId != null) return false;
        } else if (!businessId.equals(act.businessId)) return false;
        if (businessTable == null) {
            if (act.businessTable != null) return false;
        } else if (!businessTable.equals(act.businessTable)) return false;
        return Objects.equals(taskId, act.taskId) &&
                Objects.equals(taskName, act.taskName) &&
                Objects.equals(taskDefKey, act.taskDefKey) &&
                Objects.equals(procInsId, act.procInsId) &&
                Objects.equals(procDefId, act.procDefId) &&
                Objects.equals(procDefKey, act.procDefKey) &&
                Objects.equals(regCode, act.regCode) &&
                Objects.equals(businessTable, act.businessTable) &&
                Objects.equals(businessId, act.businessId) &&
                Objects.equals(title, act.title) &&
                Objects.equals(emergencyDegree, act.emergencyDegree) &&
                Objects.equals(status, act.status) &&
                Objects.equals(comment, act.comment) &&
                Objects.equals(flag, act.flag) &&
                Objects.equals(assignee, act.assignee) &&
                Objects.equals(assigneeName, act.assigneeName) &&
                Objects.equals(assigneeNameGroup, act.assigneeNameGroup) &&
                Objects.equals(beginDate, act.beginDate) &&
                Objects.equals(endDate, act.endDate) &&
                Objects.equals(superiorAssigneeName, act.superiorAssigneeName) &&
                Objects.equals(superiorAssigneeTime, act.superiorAssigneeTime) &&
                Objects.equals(superAuditLink, act.superAuditLink) &&
                Objects.equals(superiorEndTime, act.superiorEndTime) &&
                Objects.equals(userLoginName, act.userLoginName) &&
                Objects.equals(category, act.category) &&
                Objects.equals(name, act.name) &&
                Objects.equals(majorId, act.majorId) &&
                Objects.equals(operateType, act.operateType) &&
                Objects.equals(contractId, act.contractId) &&
                Objects.equals(businessKey, act.businessKey) &&
                Objects.equals(dataState, act.dataState) &&
                Objects.equals(draftType, act.draftType) &&
                Objects.equals(PROC_INST_ID_, act.PROC_INST_ID_) &&
                Objects.equals(auditMsg, act.auditMsg);
    }

    public String getSuperiorAssigneeTime() {
        return superiorAssigneeTime;
    }

    public void setSuperiorAssigneeTime(String superiorAssigneeTime) {
        this.superiorAssigneeTime = superiorAssigneeTime;
    }

    @Override
    public int compareTo(Act o) {
        return this.getBeginDate().compareTo(o.getBeginDate());
    }

    public String getUserLoginName() {
        return userLoginName;
    }

    public void setUserLoginName(String userLoginName) {
        this.userLoginName = userLoginName;
    }

    public String getContractId() {
        return contractId;
    }

    public void setContractId(String contractId) {
        this.contractId = contractId;
    }

    public String getBusinessKey() {
        return businessKey;
    }

    public void setBusinessKey(String businessKey) {
        this.businessKey = businessKey;
    }

    public String getDataState() {
        return dataState;
    }

    public void setDataState(String dataState) {
        this.dataState = dataState;
    }

    public String getPROC_INST_ID_() {
        return PROC_INST_ID_;
    }

    public void setPROC_INST_ID_(String PROC_INST_ID_) {
        this.PROC_INST_ID_ = PROC_INST_ID_;
    }

    public String getDraftType() {
        return draftType;
    }

    public void setDraftType(String draftType) {
        this.draftType = draftType;
    }

    public String getSuperAuditLink() {
        return superAuditLink;
    }

    public void setSuperAuditLink(String superAuditLink) {
        this.superAuditLink = superAuditLink;
    }

    public Date getSuperiorEndTime() {
        return superiorEndTime;
    }

    public void setSuperiorEndTime(Date superiorEndTime) {
        this.superiorEndTime = superiorEndTime;
    }

    public Map<String, Object> getOtherInfo() {
        return otherInfo;
    }

    public void setOtherInfo(Map<String, Object> otherInfo) {
        this.otherInfo = otherInfo;
    }

    public String getNextTaskStatus() {
        return nextTaskStatus;
    }

    public void setNextTaskStatus(String nextTaskStatus) {
        this.nextTaskStatus = nextTaskStatus;
    }

    public BigDecimal getBillAmount() {
        return billAmount;
    }

    public void setBillAmount(BigDecimal billAmount) {
        this.billAmount = billAmount;
    }

	public Date getDealStartTime() {
		return dealStartTime;
	}

	public void setDealStartTime(Date dealStartTime) {
		this.dealStartTime = dealStartTime;
	}

	public Date getDealEndTime() {
		return dealEndTime;
	}

	public void setDealEndTime(Date dealEndTime) {
		this.dealEndTime = dealEndTime;
	}

	public String getAtitle() {
		return atitle;
	}

	public void setAtitle(String atitle) {
		this.atitle = atitle;
	}

}
