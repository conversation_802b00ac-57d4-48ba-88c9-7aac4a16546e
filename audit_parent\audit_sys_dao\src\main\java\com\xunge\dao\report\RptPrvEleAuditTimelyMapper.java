package com.xunge.dao.report;

import com.xunge.model.report.RptEleAuditTimelyDetailVO;
import com.xunge.model.report.RptPrvEleAuditTimelyVO;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Component
public interface RptPrvEleAuditTimelyMapper {

    long countByExample(RptPrvEleAuditTimelyVO example);

    List<RptPrvEleAuditTimelyVO> selectByPrimaryKey(RptPrvEleAuditTimelyVO key);

    List<RptEleAuditTimelyDetailVO> exportEleAuditTimelyDetail(Map<String,Object> param);
}