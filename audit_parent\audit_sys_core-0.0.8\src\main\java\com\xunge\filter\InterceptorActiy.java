package com.xunge.filter;

import com.xunge.core.Interceptor.AreaToDatabaseUtil;
import com.xunge.core.Interceptor.HistoryDataLocalHolder;
import com.xunge.core.Interceptor.ReflectHelper;
import org.apache.ibatis.executor.resultset.ResultSetHandler;
import org.apache.ibatis.executor.statement.RoutingStatementHandler;
import org.apache.ibatis.executor.statement.StatementHandler;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.plugin.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.util.Properties;

/**
 * activiti引擎processEngine数据库操作插件
 */
@Intercepts({@Signature(type = StatementHandler.class, method = "prepare", args = {Connection.class, Integer.class}),
        @Signature(type = ResultSetHandler.class, method = "handleResultSets", args = {java.sql.Statement.class})})
public class InterceptorActiy implements Interceptor {
    private final static Logger logger = LoggerFactory.getLogger(InterceptorActiy.class);
    private static String schamestart = "/*!mycat:schema=";
    private static String schameend = " */";

    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        String tenant = AreaToDatabaseUtil.getDatabaseByArea();

        if (invocation.getTarget() instanceof RoutingStatementHandler) {
            logger.info("工作流sql改写开始------------------");
            RoutingStatementHandler statementHandler = (RoutingStatementHandler) invocation.getTarget();
            StatementHandler delegate = (StatementHandler) ReflectHelper.getFieldValue(statementHandler, "delegate");
            BoundSql boundSql = delegate.getBoundSql();
            // 获取当前要执行的Sql语句，也就是我们直接在Mapper映射语句中写的Sql语句
            String sql = boundSql.getSql();

            if (HistoryDataLocalHolder.queryHistoryData(HistoryDataLocalHolder.HISTORY_ACTIVITY_TABLE)) {
                sql = sql.replaceAll("\\sACT_HI_ACTINST", " ACT_HI_ACTINST_STATIC");
                sql = sql.replaceAll("\\sACT_HI_ATTACHMENT", " ACT_HI_ATTACHMENT_STATIC");
                sql = sql.replaceAll("\\sACT_HI_COMMENT", " ACT_HI_COMMENT_STATIC");
                sql = sql.replaceAll("\\sACT_HI_DETAIL", " ACT_HI_DETAIL_STATIC");
                sql = sql.replaceAll("\\sACT_HI_IDENTITYLINK", " ACT_HI_IDENTITYLINK_STATIC");
                sql = sql.replaceAll("\\sACT_HI_PROCINST", " ACT_HI_PROCINST_STATIC");
                sql = sql.replaceAll("\\sACT_HI_TASKINST", " ACT_HI_TASKINST_STATIC");
                sql = sql.replaceAll("\\sACT_HI_VARINST", " ACT_HI_VARINST_STATIC");
            }
            // 给当前的page参数对象设置总记录数
            logger.info("activities处理之前" + sql);
            //对 sql 增加 mycat 注解

            sql = schamestart + tenant + schameend + sql;

            logger.info("activities加入处理后:" + sql);

            ReflectHelper.setFieldValue(boundSql, "sql", sql);

        }
        return invocation.proceed();
    }

    @Override
    public Object plugin(Object target) {
        // TODO Auto-generated method stub
        if (target instanceof StatementHandler) {
            return Plugin.wrap(target, this);
        } else {
            return target;
        }
    }

    @Override
    public void setProperties(Properties properties) {
        // TODO Auto-generated method stub

    }

}
