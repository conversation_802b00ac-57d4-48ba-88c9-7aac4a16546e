package com.xunge.dao.basedata.ring;

import com.xunge.dto.selfelec.AuthorityUser;
import com.xunge.model.basedata.ring.MeterPerfVO;
import com.xunge.model.basedata.ring.MeterPerfVOExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface MeterPerfVOMapper {

    int countByExample(MeterPerfVOExample example);

    int deleteByExample(MeterPerfVOExample example);

    int deleteByPrimaryKey(String meterId);

    int insert(MeterPerfVO record);

    int insertSelective(MeterPerfVO record);

    List<MeterPerfVO> selectByExample(MeterPerfVOExample example);

    MeterPerfVO selectByPrimaryKey(String meterId);

    int updateByExampleSelective(@Param("record") MeterPerfVO record, @Param("example") MeterPerfVOExample example);

    int updateByExample(@Param("record") MeterPerfVO record, @Param("example") MeterPerfVOExample example);

    int updateByPrimaryKeySelective(MeterPerfVO record);

    int updateByPrimaryKey(MeterPerfVO record);

    boolean batchInsert(List<MeterPerfVO> datas);

    boolean delByCuidsAndPrvid(Map<String, Object> map);

    List<MeterPerfVO> selectByCondition(@Param("author")AuthorityUser author, @Param("mseterPerfVO") MeterPerfVO mseterPerfVO);
}