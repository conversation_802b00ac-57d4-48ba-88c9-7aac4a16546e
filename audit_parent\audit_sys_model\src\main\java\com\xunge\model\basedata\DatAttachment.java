package com.xunge.model.basedata;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.Date;

public class DatAttachment {
    
    private String attachmentId;

    
    private Integer attachmentType;

    
    private String attachmentName;

    
    private String attachmentUrl;

    
    private String attachmentNote;

    
    private String businessType;

    
    private String businessId;

    private String businessIds;


    private String createUser;//创建人

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;//创建时间

    private String yearmonth;//附件所属月份

    private boolean isPs;//判断图片是否被处理过

    private boolean isImage;//判断是否图片

    private String md5Num;

    private Long fileSize;

    public String getYearmonth() {
        return yearmonth;
    }

    public void setYearmonth(String yearmonth) {
        this.yearmonth = yearmonth;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    
    public String getAttachmentId() {
        return attachmentId;
    }

    
    public void setAttachmentId(String attachmentId) {
        this.attachmentId = attachmentId == null ? null : attachmentId.trim();
    }

    
    public Integer getAttachmentType() {
        return attachmentType;
    }

    
    public void setAttachmentType(Integer attachmentType) {
        this.attachmentType = attachmentType;
    }

    
    public String getAttachmentName() {
        return attachmentName;
    }

    
    public void setAttachmentName(String attachmentName) {
        this.attachmentName = attachmentName == null ? null : attachmentName.trim();
    }

    
    public String getAttachmentUrl() {
        return attachmentUrl;
    }

    
    public void setAttachmentUrl(String attachmentUrl) {
        this.attachmentUrl = attachmentUrl == null ? null : attachmentUrl.trim();
    }

    
    public String getAttachmentNote() {
        return attachmentNote;
    }

    
    public void setAttachmentNote(String attachmentNote) {
        this.attachmentNote = attachmentNote == null ? null : attachmentNote.trim();
    }

    
    public String getBusinessType() {
        return businessType;
    }

    
    public void setBusinessType(String businessType) {
        this.businessType = businessType == null ? null : businessType.trim();
    }

    
    public String getBusinessId() {
        return businessId;
    }

    
    public void setBusinessId(String businessId) {
        this.businessId = businessId == null ? null : businessId.trim();
    }

    public String getBusinessIds() {
        return businessIds;
    }

    public void setBusinessIds(String businessIds) {
        this.businessIds = businessIds;
    }

    public boolean isPs() {
        return isPs;
    }

    public void setPs(boolean isPs) {
        this.isPs = isPs;
    }

    public boolean isImage() {
        return isImage;
    }

    public void setImage(boolean image) {
        isImage = image;
    }

    public String getMd5Num() {
        return md5Num;
    }

    public void setMd5Num(String md5Num) {
        this.md5Num = md5Num;
    }

    public Long getFileSize() {
        return fileSize;
    }

    public void setFileSize(Long fileSize) {
        this.fileSize = fileSize;
    }
}