package com.xunge.dao.system.finance.impl;

import com.xunge.core.page.Page;
import com.xunge.dao.AbstractBaseDao;
import com.xunge.dao.system.finance.ISysFinanceConfigDao;
import com.xunge.filter.PageInterceptor;
import com.xunge.model.system.finance.SysFinanceVo;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository("financeConfigDao")
public class SysFinanceConfigDaoImpl extends AbstractBaseDao implements ISysFinanceConfigDao {

    final String Namespace = "com.xunge.dao.system.FinanceMapper.";

    @Override
    public int insertSelective(SysFinanceVo record) {
        return this.getSqlSession().insert(Namespace + "insertSelective", record);
    }

    @Override
    public Page<List<SysFinanceVo>> queryFinanceByPage(Map<String, Object> paraMap, int pageNumber, int pageSize) {
        PageInterceptor.startPage(pageNumber, pageSize);
        this.getSqlSession().selectList(Namespace + "queryFinanceByPage", paraMap);
        return PageInterceptor.endPage();
    }

    @Override
    public int updateByPrimaryKeySelective(SysFinanceVo record) {
        return this.getSqlSession().update(Namespace + "updateByPrimaryKeySelective", record);
    }
    @Override
    public SysFinanceVo selectByPrimaryKey(Long sysFinanceId) {
        return this.getSqlSession().selectOne(Namespace + "selectByPrimaryKey", sysFinanceId);
    }

    @Override
    public SysFinanceVo queryFinanceByPrvCode(Map<String, Object> paraMap) {
        return this.getSqlSession().selectOne(Namespace + "queryFinanceByPrvCode", paraMap);
    }

    @Override
    public List<SysFinanceVo> queryClass(Map<String, Object> paraMap) {
        return this.getSqlSession().selectList(Namespace + "queryClass", paraMap);
    }

    @Override
    public int insertClass(Map<String, Object> map) {
        return this.getSqlSession().insert(Namespace + "insertClass", map);
    }

    @Override
    public int deleteClass() {
        return this.getSqlSession().delete(Namespace + "deleteClass");

    }

    @Override
    public List<SysFinanceVo> queryIsTowerByCode(Map<String, Object> paraMap) {
        return this.getSqlSession().selectList(Namespace + "queryIsTowerByCode", paraMap);
    }

}
