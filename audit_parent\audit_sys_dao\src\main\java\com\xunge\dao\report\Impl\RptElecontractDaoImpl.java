package com.xunge.dao.report.Impl;

import com.xunge.dao.AbstractBaseDao;
import com.xunge.dao.report.IRptElecontractDao;

import java.util.List;
import java.util.Map;

public class RptElecontractDaoImpl extends Abstract<PERSON>aseDao implements IRptElecontractDao {

    final String Namespace = "com.xunge.mapping.RptElecontractVOMapper.";

    @Override
    public List<Map<String, Object>> queryRptElecontractByPrvid(Map<String, Object> map) {
        return this.getSqlSession().selectList(Namespace + "queryRptElecontractByPrvid", map);
    }

    @Override
    public List<Map<String, Object>> queryRptElecontractByPregid(Map<String, Object> map) {
        return this.getSqlSession().selectList(Namespace + "queryRptElecontractByPregid", map);
    }

    @Override
    public List<Map<String, Object>> queryRptElecontract(Map<String, Object> map) {
        return this.getSqlSession().selectList(Namespace + "queryRptElecontract", map);
    }

}
