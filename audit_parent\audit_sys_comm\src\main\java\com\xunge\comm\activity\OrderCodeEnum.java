package com.xunge.comm.activity;

public enum OrderCodeEnum {
    RENT_CONTRACT("rent_contract", "contract_id,rentcontract_id"),
    RENT_PAYMENT("rent_payment", "rentPayment_code,payment_id"),
    RENT_BILLACCOUNT("rent_billaccount", "billaccount_code,billaccount_id"),
    ELE_BILLACCOUNT("ele_billaccount", "billaccount_code,billaccount_id"),
    DAT_CONTRACT("dat_contract", "contract_code,contract_id"),
    ELE_PAYMENT("ele_payment", "payment_code,billaccountpaymentdetail_id"),
    DAT_BASESITE("dat_basesite", "basesite_cuid,basesite_id"),
    DAT_BASERESOURCE("dat_baseresource", "baseresource_cuid,baseresource_id"),
    ELE_LOAN("ele_loan", "loan_code,loan_id"),
    ELE_VERIFICATION("ele_verification", "verification_code,verification_id"),
    DAT_BASETOWER("dat_basetower", "tower_cid,tower_id"),
    DAT_BASEANTENNA("dat_baseantenna", "antenna_cid,antenna_id"),
    RPT_PRV_EXPENSE_BUDGET_APPLY("rpt_prv_expense_budget_apply", "'-',apply_id"),
    ELE_RECOVER_PAYMENT("ele_recover_payment", "recover_code,recover_id"),
    RENT_ANOMALOUS_WARNIGNG("rent_anomalous_warning", "recovery_code,payment_id"),
    TWR_ACCOUNTSUMMARY("twr_accountsummary", "accountsummary_code,accountsummary_id"),
    /*TWR_REG_PUNISH("twr_reg_punish", ",twr_reg_punish_id"),
    TWR_PROVINCE_PUNISH("twr_province_punish", ""),
    TWR_RENTINFORMATION("twr_rentinformation", "business_confirm_number,rentinformation_id"),
    TWR_RENTINFORMATIONTOWER("twr_rentinformationtower", "business_confirm_number,rentinformationtower_id"),
    TWR_RENTINFORMATIONTOWER_ROOM("twr_rentinformationtower_room","business_confirm_number,rentinformationtower_room_id"),
    TWR_RENTINFORMATION_ROOM("twr_rentinformation_room","business_confirm_number,rentinformationroom_id"),
    TWR_RENTINFORMATIONTOWER_TRANS("twr_rentinformationtower_trans","business_confirm_number,rentinformationtower_trans_id"),
    TWR_RENTINFORMATION_TRANS("twr_rentinformation_trans","business_confirm_number,rentinformationtrans_id"),
    TWR_RENTINFORMATIONTOWER_TINY("twr_rentinformationtower_tiny","business_confirm_number,rentinformationtower_tiny_id"),
    TWR_RENTINFORMATION_TINY("twr_rentinformation_tiny","business_confirm_number,rentinformationtiny_id"),
    TWR_RENTINFORMATIONTOWER_NONSTAND("twr_rentinformationtower_nonstand","business_confirm_number,rentinformationtower_nonstand_id"),
    TWR_RENTINFORMATION_NONSTAND("twr_rentinformation_nonstand","business_confirm_number,rentinformationnonstand_id"),
    TWR_TOWERBILL_CHARGE_BACK("twr_towerbillbalance","business_confirm_number,towerbillbalance_id"),
    TWR_ROOMBILL_CHARGE_BACK("twr_towerbillbalance_room","business_confirm_number,tower_bill_balance_room_id"),
    TWR_TRANSBILL_CHARGE_BACK("twr_towerbillbalance_trans","business_confirm_number,tower_bill_balance_trans_id"),
    TWR_TINYBILL_CHARGE_BACK("twr_towerbillbalance_tiny","business_confirm_number,tower_bill_balance_tiny_id"),
    TWR_NONSTANDBILL_CHARGE_BACK("twr_towerbillbalance_nonstand","business_confirm_number,towerbillbalance_id"),*/
    OTHER_TABLE("empty", "");

    private String desc;
    private String value;

    private OrderCodeEnum(String desc, String value) {
        this.desc = desc;
        this.value = value;
    }

    public String getDesc() {
        return desc;
    }

    public static OrderCodeEnum setDesc(String desc) {
        switch (desc) {
            case "rent_contract":
                return RENT_CONTRACT;
            case "rent_payment":
                return RENT_PAYMENT;
            case "rent_billaccount":
                return RENT_BILLACCOUNT;
            case "twr_accountsummary":
                return TWR_ACCOUNTSUMMARY;
			/*case "twr_rentinformation":
				return TWR_RENTINFORMATION;
			case "twr_rentinformationtower":
				return TWR_RENTINFORMATIONTOWER;
			case "twr_province_punish":
				return TWR_PROVINCE_PUNISH;
			case "twr_reg_punish":
				return TWR_REG_PUNISH;*/
            case "ele_billaccount":
                return ELE_BILLACCOUNT;
            case "dat_contract":
                return DAT_CONTRACT;
            case "ele_payment":
                return ELE_PAYMENT;
            case "dat_basesite":
                return DAT_BASESITE;
            case "dat_baseresource":
                return DAT_BASERESOURCE;
            case "ele_loan":
                return ELE_LOAN;
            case "ele_verification":
                return ELE_VERIFICATION;
            case "dat_basetower":
                return DAT_BASETOWER;
            case "dat_baseantenna":
                return DAT_BASEANTENNA;
            case "rpt_prv_expense_budget_apply":
                return RPT_PRV_EXPENSE_BUDGET_APPLY;
            case "ele_recover_payment":
                return ELE_RECOVER_PAYMENT;
            case "rent_anomalous_warning":
                return RENT_ANOMALOUS_WARNIGNG;
			/*case "twr_rentinformationtower_room":
				return TWR_RENTINFORMATIONTOWER_ROOM;
			case "twr_rentinformation_room":
				return TWR_RENTINFORMATION_ROOM;
			case "twr_rentinformationtower_trans":
				return TWR_RENTINFORMATIONTOWER_TRANS;
			case "twr_rentinformation_trans":
				return TWR_RENTINFORMATION_TRANS;
			case "twr_rentinformationtower_tiny":
				return TWR_RENTINFORMATIONTOWER_TINY;
			case "twr_rentinformation_tiny":
				return TWR_RENTINFORMATION_TINY;
			case "twr_rentinformationtower_nonstand":
				return TWR_RENTINFORMATIONTOWER_NONSTAND;
			case "twr_rentinformation_nonstand":
				return TWR_RENTINFORMATION_NONSTAND;
			case "twr_towerbill_charge_back":
				return TWR_TOWERBILL_CHARGE_BACK;
			case "twr_roombill_charge_back":
				return TWR_ROOMBILL_CHARGE_BACK;
			case "twr_transbill_charge_back":
				return TWR_TRANSBILL_CHARGE_BACK;
			case "twr_tinybill_charge_back":
				return TWR_TINYBILL_CHARGE_BACK;
			case "twr_nonstandbill_charge_back":
				return TWR_NONSTANDBILL_CHARGE_BACK;*/
            default:
                return OTHER_TABLE;
        }
    }

    public String getValue() {
        return value;
    }

    public static OrderCodeEnum setValue(String desc) {
        return null;
    }

}
