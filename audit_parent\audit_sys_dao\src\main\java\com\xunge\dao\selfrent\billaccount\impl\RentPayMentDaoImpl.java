package com.xunge.dao.selfrent.billaccount.impl;

import com.xunge.dao.AbstractBaseDao;
import com.xunge.dao.selfrent.billaccount.IRentPaymentDao;
import com.xunge.model.selfrent.billAccount.RentImageCheckMeterVO;
import com.xunge.model.selfrent.billAccount.RentPaymentVO;

import java.util.List;
import java.util.Map;

public class RentPayMentDaoImpl extends AbstractBaseDao implements IRentPaymentDao {

    final String Namespace = "com.xunge.dao.RentPaymentVOMapper.";

    @Override
    public List<RentPaymentVO> queryRentPaymentByBillAccountId(String billAccountId) {
        // TODO Auto-generated method stub
        return this.getSqlSession().selectList(Namespace + "queryRentPaymentByBillAccountId", billAccountId);
    }

    @Override
    public List<RentPaymentVO> queryRentPaymentByPaymentId(String paymentId) {
        // TODO Auto-generated method stub
        return this.getSqlSession().selectList(Namespace + "queryRentPaymentByPaymentId", paymentId);
    }

    @Override
    public int insertRentPayment(RentPaymentVO rentPaymentVO) {
        // TODO Auto-generated method stub
        return this.getSqlSession().insert(Namespace + "insertRentPayment", rentPaymentVO);
    }

    @Override
    public int insertOperateTime(RentPaymentVO rentPaymentVO) {
        // TODO Auto-generated method stub
        return this.getSqlSession().insert(Namespace + "insertOperateTime", rentPaymentVO);
    }

    @Override
    public int updateRentPayment(RentPaymentVO rentPaymentVO) {
        // TODO Auto-generated method stub
        return this.getSqlSession().update(Namespace + "updateRentPayment", rentPaymentVO);
    }

    @Override
    public int updatePaymentBillaccountType(Map<String,Object> map) {
        // TODO Auto-generated method stub
        return this.getSqlSession().update(Namespace + "updatePaymentBillaccountType", map);
    }

    @Override
    public int updateStateToDelete(RentPaymentVO rentPaymentVO) {
        return this.getSqlSession().update(Namespace + "updateStateToDelete", rentPaymentVO);
    }

    @Override
    public List<Map<String, Object>> queryRentResourcePaymentByBillaccountId(String billaccountId) {
        return this.getSqlSession().selectList(Namespace + "queryRentResourcePaymentByBillaccountId", billaccountId);
    }

    @Override
    public List<Map<String, Object>> queryRentTowerPaymentByBillaccountId(String billaccountId) {
        return this.getSqlSession().selectList(Namespace + "queryRentTowerPaymentByBillaccountId", billaccountId);
    }

    @Override
    public List<Map<String, Object>> queryRentResourcePaymentByBillaccountIds(String billaccountId) {
        return this.getSqlSession().selectList(Namespace + "queryRentResourcePaymentByBillaccountIds", billaccountId);
    }

    @Override
    public List<Map<String, Object>> queryRentContractPaymentByBillaccountIds(String billaccountId) {
        return this.getSqlSession().selectList(Namespace + "queryRentContractPaymentByBillaccountIds", billaccountId);
    }

    @Override
    public List<Map<String, Object>> queryRentResourcePaymentByBillaccountIds2(Map<String, Object> paramMap) {
        return this.getSqlSession().selectList(Namespace + "queryRentResourcePaymentByBillaccountIds2", paramMap);
    }

    @Override
    public List<RentImageCheckMeterVO> queryImageCheckList(RentImageCheckMeterVO imageCheckMeterVO) {
        return this.getSqlSession().selectList(Namespace + "queryImageCheckList", imageCheckMeterVO);
    }
}
