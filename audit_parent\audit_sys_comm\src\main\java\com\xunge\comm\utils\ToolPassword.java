package com.xunge.comm.utils;

import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @date 2019年06月04日
 * @description 1、口令至少由8位及以上大小写字母、数字及特殊符号等混合、随机组成。
 * 2、口令中不能出现3位以上（含三位）连续字母、数字、特殊字符，如ABC、Abc、123、！@#等。
 * 3、口令中不能出现3位以上（含三位）重复字母、数字、特殊字符，如AAA、Aaa、111、###等。
 * 4、口令中不能出现3位以上（含三位）键盘上连续的字母、数字、特殊字符，如qwe（键盘第一行前三个字母）、asd（键盘第二行前三个字母）、qaz（键盘第一列三个字母）、
 * 1qaz（ 键盘第一列数字加前三个字母）、！QAZ（键盘第一列特殊字符加前三个字母）等。
 * 5、密码中不得出现3位以上（含三位）连续、重复或键盘上连续的字母、数字、特殊字符，如ABC、AAA、qwe、asd等。
 * 6、密码不得为账号的反序，比如root/toor
 */
public class ToolPassword {

    public static Map<String, Object> validateAccountPwd(String account, String password) {
        Map<String, Object> maps = new HashMap<>();
        //1.账号或密码不能为空
        if (StringUtils.isBlank(account) || StringUtils.isBlank(password)) {
            maps.put("flag", false);
            maps.put("msg", "账号或密码不能为空");
            return maps;
        }
        //2.密码不符合规则：密码为账号的反序！
        String reverseAccount = StringUtils.reverse(account);
        if (reverseAccount.equals(password)) {
            maps.put("flag", false);
            maps.put("msg", "密码不符合规则：新密码为账号的反序！");
            return maps;
        }
        // 密码不符合规则：密码必须是数字字母特殊字符且不能小于8位
        if (!validatePwdSZX(password)) {
            maps.put("flag", false);
            maps.put("msg", "密码不符合规则：新密码必须是含有小写字母、大写字母、数字、特殊符号且不能小于8位");
            return maps;
        }
        // 密码不符合规则：密码包含连续三个相同字符！
        if (isSameThreeCharacter(password)) {
            maps.put("flag", false);
            maps.put("msg", "密码不符合规则：新密码包含连续三个相同字符！");
            return maps;
        }
        // 密码不符合规则：密码中出现3位以上（含三位）连续字母或数字，如ABC、Abc等！
        if (isOutContineThreeSequence(password)) {
            maps.put("flag", false);
            maps.put("msg", "密码不符合规则：新密码中出现3位以上（含三位）连续字母或数字，如ABC、Abc等！");
            return maps;
        }
        // 密码不符合规则：密码包含键盘上任意连续的3个字符或shift转换字符！
        if (validateContinueThreeKey(password)) {
            maps.put("flag", false);
            maps.put("msg", "密码不符合规则：新密码包含键盘上任意连续的3个字符或shift转换字符！");
            return maps;
        }

        maps.put("flag", true);
        maps.put("msg", "密码符合要求");
        return maps;
    }

    private static boolean validatePwdSZX(String password) {
        // 要验证的字符串
        String str = password;
        if (password.trim().length() < 8) {
            return false;
        }
        // 密码是含有小写字母、大写字母、数字、特殊符号的两种及以上
        String regEx = "^(?![A-Z]+$)(?![a-z]+$)(?!\\d+$)(?![\\W_]+$)\\S{6,16}$";
        // 字符串是否与正则表达式相匹配
        boolean rs = password.matches(regEx);
        return rs;
    }

    //密码中不能出现3位以上（含三位）连续字母，如ABC、Abc等。
    public static boolean isOutContineThreeSequence(String instr) {
        String str = instr.toLowerCase();
        int increasingResult = 1;
        int currentIncreasingResult = 1;
        char[] array = str.toCharArray();
        for (int i = 1; i < array.length; i++) {
            //最长连续递增
            if (array[i] == array[i - 1] + 1) {
                currentIncreasingResult++;
            } else {
                currentIncreasingResult = 1;
            }
            increasingResult = Integer.max(increasingResult, currentIncreasingResult);
        }
        if (increasingResult >= 3) {
            return true;
        }
        return false;
    }

    //密码不得包含连续三个相同字符
    private static boolean isSameThreeCharacter(String str) {
        String s = str.toLowerCase();
        Pattern pattern = Pattern.compile("(\\w|[!@#$%^&*])\\1{2,}");
        Matcher matcher = pattern.matcher(s);
        boolean result=false;
        while (matcher.find()) {
            result=true;
        }
        return result;
    }

    //密码不得包含键盘上任意连续的3个字符或shift转换字符
    private static boolean validateContinueThreeKey(String str) {

        //定义横向穷举
        String[][] keyCode = {{"`~·", "1=", "2@@", "3#", "4$￥", "5%", "6^……", "7&", "8*", "9(（", "0）)", "-_", "=+"},
                {" ", "qQ", "wW", "eE", "rR", "tT", "yY", "uU", "iI", "oO", "pP", "[{【", "]}】", "\\|、"},
                {" ", "aA", "sS", "dD", "fF", "gG", "hH", "jJ", "kK", "lL", ";:", "\'\"’“"},
                {" ", "zZ", "xX", "cC", "vV", "bB", "nN", "mM", ",《<", ".>》", "/?？"}};

        //找出给出的字符串，每个字符，在坐标系中的位置。
        char[] c = str.toCharArray();
        List<Integer> x = new ArrayList<Integer>();
        List<Integer> y = new ArrayList<Integer>();
        for (int i = 0; i < c.length; i++) {
            char temp = c[i];
            toHere:
            for (int j = 0; j < keyCode.length; j++) {
                for (int k = 0; k < keyCode[j].length; k++) {
                    String jk = keyCode[j][k];
                    if (jk.contains(String.valueOf(temp))) {
                        x.add(j);
                        y.add(k);
                        break toHere;
                    }
                }
            }
        }
        boolean flag = false;
        for (int i = 0; i < x.size() - 2; i++) {
            // 如果X一致，那么就是在一排
            if (x.get(i).equals( x.get(i + 1)) && x.get(i + 1).equals( x.get(i + 2))) {//四者在同一行上
                if (y.get(i) > y.get(i + 2)) {
                    if (y.get(i) - 1 == y.get(i + 1) && y.get(i) - 2 == y.get(i + 2)) {
                        flag = true;
                        break;
                    }
                } else {
                    if (y.get(i) + 1 == y.get(i + 1) && y.get(i) + 2 == y.get(i + 2)) {
                        flag = true;
                        break;
                    }
                }

            } else if (!x.get(i).equals(x.get(i + 1)) && !x.get(i + 1).equals( x.get(i + 2) )&& !x.get(i).equals(x.get(i + 2))

            ) {//四者均不在同一行上,但是如果y相同，说明是一列
                if (y.get(i).equals(y.get(i + 1)) && y.get(i + 1).equals(y.get(i + 2))) {
                    flag = true;
                    break;
                }
            }

        }
        return flag;
    }
}
