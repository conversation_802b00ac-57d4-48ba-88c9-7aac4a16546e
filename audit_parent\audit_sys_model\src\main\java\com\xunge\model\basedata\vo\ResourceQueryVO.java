package com.xunge.model.basedata.vo;

import com.xunge.core.model.UserLoginInfo;

import java.math.BigDecimal;

/**
 * 机房查询 VO
 * <p>
 * Title: RoomQueryVO
 *
 * <AUTHOR>
 */
public class ResourceQueryVO extends BaseDataVO {

    private String prvId;
    private String city;
    private String region;
    private String reg;
    private String resName;
    private Integer resType;
    private String property;
    private Integer auditStatus;
    private String paymentId;
    private String[] paymentIds;
    private Integer status;
    private Integer queryType;
    private String depId;
    private String startDate;
    private String endDate;
    private Integer positionType;
    // 登录用户信息
    private UserLoginInfo loginUser;
    //位置点名称
    private String regName;
    private Integer dataFrom;
    private String dataFroms;

    private Integer freeFlag;

    private String relationFlag;

    private BigDecimal changeFloor;

    private BigDecimal changeCeiling;

    private String towerSiteCode;
    // 是否巡检 0巡检 1未巡检
    private String isInspect;

    // 普服资源
    private Integer ifTeleCmnServ;
    // 5G标识
    private Integer fivegFlag;


    public void setFivegFlag(Integer fivegFlag) {
        this.fivegFlag = fivegFlag;
    }

    public Integer getFivegFlag() {
        return fivegFlag;
    }

    public Integer getIfTeleCmnServ() {
        return ifTeleCmnServ;
    }

    public void setIfTeleCmnServ(Integer ifTeleCmnServ) {
        this.ifTeleCmnServ = ifTeleCmnServ;
    }

    public String getRelationFlag() {
        return relationFlag;
    }

    public void setRelationFlag(String relationFlag) {
        this.relationFlag = relationFlag;
    }

    public Integer getFreeFlag() {
        return freeFlag;
    }

    public void setFreeFlag(Integer freeFlag) {
        this.freeFlag = freeFlag;
    }

    public String getIsInspect() {
        return isInspect;
    }

    public void setIsInspect(String isInspect) {
        this.isInspect = isInspect;
    }

    public String getDataFroms() {
        return dataFroms;
    }

    public void setDataFroms(String dataFroms) {
        this.dataFroms = dataFroms;
    }

    public Integer getDataFrom() {
        return dataFrom;
    }

    public void setDataFrom(Integer dataFrom) {
        this.dataFrom = dataFrom;
    }

    public String getResName() {
        return resName;
    }

    public void setResName(String resName) {
        this.resName = resName;
    }

    public String getRegName() {
        return regName;
    }

    public void setRegName(String regName) {
        this.regName = regName;
    }

    public Integer getPositionType() {
        return positionType;
    }

    public void setPositionType(Integer positionType) {
        this.positionType = positionType;
    }

    public String getPrvId() {
        return prvId;
    }

    public void setPrvId(String prvId) {
        this.prvId = prvId;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getRegion() {
        return region;
    }

    public void setRegion(String region) {
        this.region = region;
    }

    public String getReg() {
        return reg;
    }

    public void setReg(String reg) {
        this.reg = reg == null ? reg : reg.trim();
    }

    public Integer getResType() {
        return resType;
    }

    public void setResType(Integer resType) {
        this.resType = resType;
    }

    public String getProperty() {
        return property;
    }

    public void setProperty(String property) {
        this.property = property;
    }

    public Integer getAuditStatus() {
        return auditStatus;
    }

    public void setAuditStatus(Integer auditStatus) {
        this.auditStatus = auditStatus;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getQueryType() {
        return queryType;
    }

    public void setQueryType(Integer queryType) {
        this.queryType = queryType;
    }

    public UserLoginInfo getLoginUser() {
        return loginUser;
    }

    public void setLoginUser(UserLoginInfo loginUser) {
        this.loginUser = loginUser;
    }

    public String getDepId() {
        return depId;
    }

    public void setDepId(String depId) {
        this.depId = depId;
    }

    public String getStartDate() {
        return startDate;
    }

    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }

    public String getEndDate() {
        return endDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    public String getPaymentId() {
        return paymentId;
    }

    public void setPaymentId(String paymentId) {
        this.paymentId = paymentId;
    }

    public String[] getPaymentIds() {
        return paymentIds;
    }

    public void setPaymentIds(String[] paymentIds) {
        this.paymentIds = paymentIds;
    }

    public BigDecimal getChangeFloor() {
        return changeFloor;
    }

    public void setChangeFloor(BigDecimal changeFloor) {
        this.changeFloor = changeFloor;
    }

    public BigDecimal getChangeCeiling() {
        return changeCeiling;
    }

    public void setChangeCeiling(BigDecimal changeCeiling) {
        this.changeCeiling = changeCeiling;
    }

    public String getTowerSiteCode() {
        return towerSiteCode;
    }

    public void setTowerSiteCode(String towerSiteCode) {
        this.towerSiteCode = towerSiteCode;
    }
}
