package com.xunge.core.util;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName: ResponseUtils
 * @Description: HttpResponse工具类
 * <AUTHOR>
 * @Date: 2023/4/27 15:25
 * @Version V1.0.0
 * @Since 1.8
 */
@Slf4j
public final class ResponseUtils {
    private ResponseUtils(){

    }

    /**
     * 响应头部内容设置
     * @param fileName
     * @param response
     * @param request
     */
    public static void responseHeader(String fileName, HttpServletResponse response, HttpServletRequest request) {
        try {
            request.setCharacterEncoding("UTF-8");
            response.setCharacterEncoding("UTF-8");
            response.setContentType("application/x-download");
            String userAgent = request.getHeader("USER-AGENT");
            // IE浏览器 window10 Edge
            if (org.apache.commons.lang3.StringUtils.contains(userAgent, "MSIE")
                    || org.apache.commons.lang3.StringUtils.contains(userAgent, "Trident")
                    || org.apache.commons.lang3.StringUtils.contains(userAgent, "Edge")) {
                fileName = URLEncoder.encode(fileName, "UTF-8");
                // google,火狐浏览器
            } else if (org.apache.commons.lang3.StringUtils.contains(userAgent, "Mozilla")) {
                fileName = new String(fileName.getBytes(), "ISO8859-1");
            } else {
                // 其他浏览器
                fileName = URLEncoder.encode(fileName, "UTF-8");
            }
            response.addHeader("Content-Disposition", "attachment;filename=" + fileName);
        }catch (Exception e){
            log.error("responseHeader 出错", e);
        }
    }

    /**
     * 将错误信息放在Response里面。例如在导出失败时可以在页面提示错误
     * @param response
     * @param message
     * @throws IOException
     */
    public static void setErrorMsgInResponse(HttpServletResponse response, String message) {
        try {
            if (!response.isCommitted()) {
                response.reset();
                response.setContentType("application/json");
                response.setCharacterEncoding("utf-8");
                Map<String, String> map = new HashMap<>();
                map.put("status", "failure");
                map.put("message", message);
                response.getWriter().println(JSON.toJSONString(map));
            }
        } catch (IOException e) {
            log.error("response.getWriter() 出错", e);
        }
    }
}
