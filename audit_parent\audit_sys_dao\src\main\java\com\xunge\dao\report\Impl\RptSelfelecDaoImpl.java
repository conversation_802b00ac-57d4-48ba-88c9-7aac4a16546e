package com.xunge.dao.report.Impl;

import com.xunge.dao.AbstractBaseDao;
import com.xunge.dao.report.IRptSelfelecDao;
import com.xunge.model.report.RptEleBillaccountVO;
import com.xunge.model.report.RptEleBillamountVO;
import com.xunge.model.report.RptElePaymentVO;
import com.xunge.model.selfelec.RptPrvElePaymentRedundancyMon;

import java.util.List;
import java.util.Map;

public class RptSelfelecDaoImpl extends AbstractBaseDao implements IRptSelfelecDao {

    final String RptEleBillaccountNamespace = "com.xunge.mapping.report.RptEleBillaccountMapper.";
    final String RptEleBillamountNamespace = "com.xunge.mapping.report.RptEleBillamountMapper.";
    final String RptElePaymentNamespace = "com.xunge.mapping.report.RptElePaymentMapper.";

    @Override
    public List<RptEleBillaccountVO> queryRptEleBillaccount(Map<String, Object> paraMap) {
        return this.getSqlSession().selectList(RptEleBillaccountNamespace + "queryRptEleBillaccount", paraMap);
    }

    @Override
    public List<RptEleBillaccountVO> queryRptEleBillaccountByPrvId(Map<String, Object> paraMap) {
        return this.getSqlSession().selectList(RptEleBillaccountNamespace + "queryRptEleBillaccountByPrvId", paraMap);
    }

    @Override
    public List<RptEleBillaccountVO> queryRptEleBillaccountByPregId(Map<String, Object> paraMap) {
        return this.getSqlSession().selectList(RptEleBillaccountNamespace + "queryRptEleBillaccountByPregId", paraMap);
    }

    @Override
    public List<RptEleBillamountVO> queryRptEleBillamount(Map<String, Object> paraMap) {
        return this.getSqlSession().selectList(RptEleBillamountNamespace + "queryRptEleBillamount", paraMap);
    }

    @Override
    public List<RptEleBillamountVO> queryRptEleBillamountByPrvId(Map<String, Object> paraMap) {
        return this.getSqlSession().selectList(RptEleBillamountNamespace + "queryRptEleBillamountByPrvId", paraMap);
    }

    @Override
    public List<RptEleBillamountVO> queryRptEleBillamountByPregId(Map<String, Object> paraMap) {
        return this.getSqlSession().selectList(RptEleBillamountNamespace + "queryRptEleBillamountByPregId", paraMap);
    }

    @Override
    public List<RptElePaymentVO> queryRptElePayment(Map<String, Object> paraMap) {
        return this.getSqlSession().selectList(RptElePaymentNamespace + "queryRptElePayment", paraMap);
    }

    @Override
    public List<RptElePaymentVO> queryRptElePaymentByPrvId(Map<String, Object> paraMap) {
        return this.getSqlSession().selectList(RptElePaymentNamespace + "queryRptElePaymentByPrvId", paraMap);
    }

    @Override
    public List<RptElePaymentVO> queryRptElePaymentByPregId(Map<String, Object> paraMap) {
        return this.getSqlSession().selectList(RptElePaymentNamespace + "queryRptElePaymentByPregId", paraMap);
    }

    @Override
    public List<RptPrvElePaymentRedundancyMon> queryRedundancyById(Map<String, Object> paraMap) {
        return this.getSqlSession().selectList(RptElePaymentNamespace + "queryRedundancyById", paraMap);
    }

}