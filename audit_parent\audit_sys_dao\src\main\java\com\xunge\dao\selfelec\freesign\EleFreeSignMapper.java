package com.xunge.dao.selfelec.freesign;


import com.xunge.model.selfelec.freesign.EleFreeSign;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;


@Mapper
public interface EleFreeSignMapper {

    EleFreeSign queryFreeSignInfo(@Param("billamountId") String billamountId, @Param("type") Integer type);

    void invalidFreeSignInfo(@Param("billamountId") String summaryId, @Param("type") Integer type);

    void addFreeSignInfo(@Param("eleFreeSign") EleFreeSign eleFreeSign, @Param("type") Integer type);

    EleFreeSign queryLastSignType(@Param("userId") String userId, @Param("type") Integer type);
}
