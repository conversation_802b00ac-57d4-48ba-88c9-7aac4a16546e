package com.xunge.dao.towerrent.trans;

import com.xunge.model.towerrent.trans.TowerTrans;

import java.util.List;
import java.util.Map;

/**
 * 描述：
 * Created on 2019/8/20.
 * <p>Title:</p>
 * <p>Copyright:Copyright (c) 2017</p>
 * <p>Company:安徽科大国创</p>
 * <p>Department:西南二区BU</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @update
 */
public interface ITowerTransDao {

    /**
     * 条件查询微站起租数据
     *
     * @param paraMap
     * @return
     */
    List<TowerTrans> getTwrRentTransByParam(Map<String, Object> paraMap);

    /**
     * 根据业务主键获取起租单和账单信息
     *
     * @param queryList
     * @return
     */
    List<Map> findRentInfoAndBillInfo(List<Map> queryList);
}
