package com.xunge.model.costcenter;

import java.io.Serializable;
import java.util.Date;

public class CostCenterVO implements Serializable {

    private static final long serialVersionUID = -7684588362330520516L;

/*	//段类型
	private String segmentType;
	//值(不能重复)
	private String flexValueDisp;
	//转换的值(等于 FLEX_VALUE_DISP 的值)
	private String flexValueMeaning;
	//COA段值描述
	private String description;
	//是否启用 Y:是 N:否
	private String enabledFlag;
	//自 yyyy-mm-dd
	private Date startDateActive;
	//至 yyyy-mm-dd
	private Date endDateActive;
	//父  Y:是 N:否
	private String summaryFlag;
	//组 (LEV1：一级科目 LEV2：二级科目 LEV3：三级科目 LEV4：四级科目)
	private String structuredHierarchyName;
	//层
	private String hierarchyLevel;
	//允许预算 Y:是 N:否
	private String allowBudget;
	//允许过账 Y:是 N:否
	private String allowPosting;
	//账户类型(A:资产类账户 L:负债类账户 O:所有者权益类账户 R:收入类账户 E:费用类账户)
	private String accountType;
	//第三方控制账户(SUPPLIER:供应商 N:否 CUSTOMER:客户 Y:是 R:限制人工日记账)
	private String thirdPartyControlAccount;
	//调节 Y:是 N:否
	private String adjust;
	//上下文
	private String context;
	//定义子范围自
	private String childFlexValueLowDisp;
	//定义子范围至
	private String childFlexValueHighDisp;
	//包括(C:仅限于子值 P:仅限于父值)
	private String rangeAttribute;
	//来源(M：手工 PA：PA 导入)
	private String source;
	//增值税科目类型
	private String vatAcType;
	//业务类型
	private String businessType;
	//税率
	private String taxTare;
	//费项值(收入类科目对应费项值)
	private String attribute1;
	//属性 2(属性字段（备用）
	private String attribute2;
	//属性 3(属性字段（备用）
	private String attribute3;
	//属性 4(属性字段（备用）
	private String attribute4;
	//最后更新时间
	private Date lastUpdateDate;
	//查询结果扩展
	private String outputExt;*/

    private String segmenttype;
    private String flexvaluedisp;
    private String flexvaluemeaning;
    private String flexvaluename;
    private String description;
    private String enabledflag;
    private String summaryflag;
    private String structuredhierarchyname;
    private String hierarchylevel;
    private String allowbudget;
    private String allowposting;
    private String accounttype;
    private String thirdpartycontrolaccount;
    private String adjust;
    private String context;
    private String childflexvaluelowdisp;
    private String childflexvaluehighdisp;
    private String rangeattribute;
    private String source;
    private String vatactype;
    private String businesstype;
    private String taxtare;
    private String attribute1;
    private String attribute2;
    private String attribute3;
    private String attribute4;
    private String attribute5;
    private String outputext;
    private Date startDate;
    private Date endDate;
    private Date lastDate;
    private Date createTime;
    private Date updateTime;

    public String getFlexvaluename() {
        return flexvaluename;
    }

    public void setFlexvaluename(String flexvaluename) {
        this.flexvaluename = flexvaluename;
    }

    public String getSegmenttype() {
        return segmenttype;
    }

    public void setSegmenttype(String segmenttype) {
        this.segmenttype = segmenttype;
    }

    public String getFlexvaluedisp() {
        return flexvaluedisp;
    }

    public void setFlexvaluedisp(String flexvaluedisp) {
        this.flexvaluedisp = flexvaluedisp;
    }

    public String getFlexvaluemeaning() {
        return flexvaluemeaning;
    }

    public void setFlexvaluemeaning(String flexvaluemeaning) {
        this.flexvaluemeaning = flexvaluemeaning;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getEnabledflag() {
        return enabledflag;
    }

    public void setEnabledflag(String enabledflag) {
        this.enabledflag = enabledflag;
    }

    public String getSummaryflag() {
        return summaryflag;
    }

    public void setSummaryflag(String summaryflag) {
        this.summaryflag = summaryflag;
    }

    public String getStructuredhierarchyname() {
        return structuredhierarchyname;
    }

    public void setStructuredhierarchyname(String structuredhierarchyname) {
        this.structuredhierarchyname = structuredhierarchyname;
    }

    public String getHierarchylevel() {
        return hierarchylevel;
    }

    public void setHierarchylevel(String hierarchylevel) {
        this.hierarchylevel = hierarchylevel;
    }

    public String getAllowbudget() {
        return allowbudget;
    }

    public void setAllowbudget(String allowbudget) {
        this.allowbudget = allowbudget;
    }

    public String getAllowposting() {
        return allowposting;
    }

    public void setAllowposting(String allowposting) {
        this.allowposting = allowposting;
    }

    public String getAccounttype() {
        return accounttype;
    }

    public void setAccounttype(String accounttype) {
        this.accounttype = accounttype;
    }

    public String getThirdpartycontrolaccount() {
        return thirdpartycontrolaccount;
    }

    public void setThirdpartycontrolaccount(String thirdpartycontrolaccount) {
        this.thirdpartycontrolaccount = thirdpartycontrolaccount;
    }

    public String getAdjust() {
        return adjust;
    }

    public void setAdjust(String adjust) {
        this.adjust = adjust;
    }

    public String getContext() {
        return context;
    }

    public void setContext(String context) {
        this.context = context;
    }

    public String getChildflexvaluelowdisp() {
        return childflexvaluelowdisp;
    }

    public void setChildflexvaluelowdisp(String childflexvaluelowdisp) {
        this.childflexvaluelowdisp = childflexvaluelowdisp;
    }

    public String getChildflexvaluehighdisp() {
        return childflexvaluehighdisp;
    }

    public void setChildflexvaluehighdisp(String childflexvaluehighdisp) {
        this.childflexvaluehighdisp = childflexvaluehighdisp;
    }

    public String getRangeattribute() {
        return rangeattribute;
    }

    public void setRangeattribute(String rangeattribute) {
        this.rangeattribute = rangeattribute;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getVatactype() {
        return vatactype;
    }

    public void setVatactype(String vatactype) {
        this.vatactype = vatactype;
    }

    public String getBusinesstype() {
        return businesstype;
    }

    public void setBusinesstype(String businesstype) {
        this.businesstype = businesstype;
    }

    public String getTaxtare() {
        return taxtare;
    }

    public void setTaxtare(String taxtare) {
        this.taxtare = taxtare;
    }

    public String getAttribute1() {
        return attribute1;
    }

    public void setAttribute1(String attribute1) {
        this.attribute1 = attribute1;
    }

    public String getAttribute2() {
        return attribute2;
    }

    public void setAttribute2(String attribute2) {
        this.attribute2 = attribute2;
    }

    public String getAttribute3() {
        return attribute3;
    }

    public void setAttribute3(String attribute3) {
        this.attribute3 = attribute3;
    }

    public String getAttribute4() {
        return attribute4;
    }

    public void setAttribute4(String attribute4) {
        this.attribute4 = attribute4;
    }

    public String getAttribute5() {
        return attribute5;
    }

    public void setAttribute5(String attribute5) {
        this.attribute5 = attribute5;
    }

    public String getOutputext() {
        return outputext;
    }

    public void setOutputext(String outputext) {
        this.outputext = outputext;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public Date getLastDate() {
        return lastDate;
    }

    public void setLastDate(Date lastDate) {
        this.lastDate = lastDate;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}