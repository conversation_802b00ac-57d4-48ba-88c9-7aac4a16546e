package com.xunge.dao.system.log;

import com.xunge.model.system.log.SysLogVO;

import java.util.List;
import java.util.Map;

public interface ISysLogDao {
    /**
     * 日志记录模糊查询
     *
     * @param paraMap
     * @param pageNumber
     * @param pageSize
     * @return
     * <AUTHOR>
     */
    public List<SysLogVO> queryLogMsg(Map<String, Object> paraMap);

    /**
     * 删除日志记录
     *
     * @param paraMap
     * @return
     * <AUTHOR>
     */
    public int deleteLogMsg(Map<String, Object> paraMap);

    /**
     * 日志记录入库
     *
     * @param sysLog
     * @return
     */
    public int insertLog(SysLogVO sysLog);

    /**
     * @description 新增 历史信息
     * <AUTHOR>
     * @date 创建时间：2017年11月22日
     */
    public int insertHistoryInfo(Map<String, Object> paraMap);
}