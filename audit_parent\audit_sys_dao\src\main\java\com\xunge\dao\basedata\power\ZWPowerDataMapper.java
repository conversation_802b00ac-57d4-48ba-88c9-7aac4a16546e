package com.xunge.dao.basedata.power;

import com.xunge.model.basedata.power.PowerSiteDegree;
import com.xunge.model.basedata.power.ZWPowerData;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 卓望动环数据
 */
public interface ZWPowerDataMapper {
    List<ZWPowerData> queryZWPowerData(Map<String, Object> map);

    List<ZWPowerData> exportZWPowerData(Map<String, Object> map);

    BigDecimal queryZWPowerDataCount(Map<String, Object> map);

    List<ZWPowerData> queryPowerDataForBillaccount(Map<String, Object> map);

    List<String> queryCityNameOfZWPowerData();

}
