package com.xunge.model.contract;

import java.math.BigDecimal;
import java.util.Date;

public class RentContract {
    private String rentcontractId;

    private String contractId;

    private String supplierId;

    private String paymentperiodId;

    private String prvId;

    private String contractsysId;

    private BigDecimal totalAmountnotax;

    private Integer includeTax;

    private BigDecimal billamountTaxratio;

    private BigDecimal taxAmount;

    private BigDecimal totalAmount;

    private BigDecimal yearAmount;

    private Integer addressType;

    private Integer houseType;

    private Integer chargeType;

    private Integer propertyType;

    private BigDecimal propertyArea;

    private String propertyAddress;

    private String propertyName;

    private String rentcontractNote;

    private Date auditingDate;

    private String auditingUserId;

    private Integer auditState;

    private String auditingRoleCode;

    private String tag;

    private Date createTime;

    private Date updateTime;

    private String contractCode;
    private Integer updType;//根据标识更新，0：contractCode, 1:contractsysId

    public String getRentcontractId() {
        return rentcontractId;
    }

    public void setRentcontractId(String rentcontractId) {
        this.rentcontractId = rentcontractId;
    }

    public String getContractId() {
        return contractId;
    }

    public void setContractId(String contractId) {
        this.contractId = contractId;
    }

    public String getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(String supplierId) {
        this.supplierId = supplierId;
    }

    public String getPaymentperiodId() {
        return paymentperiodId;
    }

    public void setPaymentperiodId(String paymentperiodId) {
        this.paymentperiodId = paymentperiodId;
    }

    public String getPrvId() {
        return prvId;
    }

    public void setPrvId(String prvId) {
        this.prvId = prvId;
    }

    public String getContractsysId() {
        return contractsysId;
    }

    public void setContractsysId(String contractsysId) {
        this.contractsysId = contractsysId;
    }

    public BigDecimal getTotalAmountnotax() {
        return totalAmountnotax;
    }

    public void setTotalAmountnotax(BigDecimal totalAmountnotax) {
        this.totalAmountnotax = totalAmountnotax;
    }

    public Integer getIncludeTax() {
        return includeTax;
    }

    public void setIncludeTax(Integer includeTax) {
        this.includeTax = includeTax;
    }

    public BigDecimal getBillamountTaxratio() {
        return billamountTaxratio;
    }

    public void setBillamountTaxratio(BigDecimal billamountTaxratio) {
        this.billamountTaxratio = billamountTaxratio;
    }

    public BigDecimal getTaxAmount() {
        return taxAmount;
    }

    public void setTaxAmount(BigDecimal taxAmount) {
        this.taxAmount = taxAmount;
    }

    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    public BigDecimal getYearAmount() {
        return yearAmount;
    }

    public void setYearAmount(BigDecimal yearAmount) {
        this.yearAmount = yearAmount;
    }

    public Integer getAddressType() {
        return addressType;
    }

    public void setAddressType(Integer addressType) {
        this.addressType = addressType;
    }

    public Integer getHouseType() {
        return houseType;
    }

    public void setHouseType(Integer houseType) {
        this.houseType = houseType;
    }

    public Integer getChargeType() {
        return chargeType;
    }

    public void setChargeType(Integer chargeType) {
        this.chargeType = chargeType;
    }

    public Integer getPropertyType() {
        return propertyType;
    }

    public void setPropertyType(Integer propertyType) {
        this.propertyType = propertyType;
    }

    public BigDecimal getPropertyArea() {
        return propertyArea;
    }

    public void setPropertyArea(BigDecimal propertyArea) {
        this.propertyArea = propertyArea;
    }

    public String getPropertyAddress() {
        return propertyAddress;
    }

    public void setPropertyAddress(String propertyAddress) {
        this.propertyAddress = propertyAddress;
    }

    public String getPropertyName() {
        return propertyName;
    }

    public void setPropertyName(String propertyName) {
        this.propertyName = propertyName;
    }

    public String getRentcontractNote() {
        return rentcontractNote;
    }

    public void setRentcontractNote(String rentcontractNote) {
        this.rentcontractNote = rentcontractNote;
    }

    public Date getAuditingDate() {
        return auditingDate;
    }

    public void setAuditingDate(Date auditingDate) {
        this.auditingDate = auditingDate;
    }

    public String getAuditingUserId() {
        return auditingUserId;
    }

    public void setAuditingUserId(String auditingUserId) {
        this.auditingUserId = auditingUserId;
    }

    public Integer getAuditState() {
        return auditState;
    }

    public void setAuditState(Integer auditState) {
        this.auditState = auditState;
    }

    public String getAuditingRoleCode() {
        return auditingRoleCode;
    }

    public void setAuditingRoleCode(String auditingRoleCode) {
        this.auditingRoleCode = auditingRoleCode;
    }

    public String getTag() {
        return tag;
    }

    public void setTag(String tag) {
        this.tag = tag;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getContractCode() {
        return contractCode;
    }

    public void setContractCode(String contractCode) {
        this.contractCode = contractCode;
    }

    public Integer getUpdType() {
        return updType;
    }

    public void setUpdType(Integer updType) {
        this.updType = updType;
    }

}