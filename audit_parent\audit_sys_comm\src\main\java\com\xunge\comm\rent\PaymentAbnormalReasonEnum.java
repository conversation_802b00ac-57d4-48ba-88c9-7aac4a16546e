package com.xunge.comm.rent;

/**
 * @ClassName PaymentAbnormalReasonEnum
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/3/6 11:46
 */
public enum  PaymentAbnormalReasonEnum {
    paceFeeRepeat(1,"以下站点疑似与铁塔服务费场地费重复报账"),
    paymentNullDate(2,"存在缴费空档期"),
    onlyReleaseTower(3,"缴费时报账点未关联机房或位置点"),
    reourceOutLine(4,"缴费时报账点所关联的资源存在“退网”或“工程”状态"),
    startTimeLessEnd(5,"缴费期始小于已缴费期终（未经三费报账）"),
    reourcePaymented(6,"缴费的资源关联标示“资源”在该缴费周期已有缴费记录"),
    amountOverContract(7,"累计缴费金额已超过合同或固化信息的总金额"),
    dateOverContract(8,"缴费申请日期超过合同或固化信息期终"),
    zeroPowerRate(9,"缴费时报账点所关联的资源存在额定功率为0"),
    zeroFlow(10,"存在近3个月流量数据为零的情况"),
    reversePayment(11,"存在倒序缴费");


    public  Integer code;
    public  String reason;

    PaymentAbnormalReasonEnum(int code, String reason) {
        this.code = code;
        this.reason = reason;
    }
}
