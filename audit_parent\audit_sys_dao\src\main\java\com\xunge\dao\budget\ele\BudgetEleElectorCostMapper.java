package com.xunge.dao.budget.ele;

import com.xunge.model.budget.ele.BudgetEleElectorCostVO;
import com.xunge.model.budget.ele.BudgetEleParamVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @author: LiangCheng
 * Date: 2022/7/4 15:15
 * Description: 用电成本
 */
public interface BudgetEleElectorCostMapper {

    /**
     * 保存用电成本
     */
    void saveEleElectorCost(BudgetEleParamVO budgetEleParamVO);

    List<BudgetEleElectorCostVO> getEleElectorCost(BudgetEleParamVO budgetEleParamVO);

    /**
     *用于集团草稿修改，更新集团建议值
     */
    void editElectorCostGroupDraft(BudgetEleElectorCostVO budgetEleElectorCostVO);

    /**
     *用于省侧填报修改，更新实际值
     */
    void editElectorCost(BudgetEleElectorCostVO budgetEleElectorCostVO);

    void delBudgetEleData(BudgetEleParamVO budgetEleParamVO);

    Integer queryFinalDataIsExists(BudgetEleParamVO budgetEleParamVO);

}
