package com.xunge.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 导出freemarker数据类型
 *
 * <AUTHOR>
 * @date 2020/7/31
 */
@AllArgsConstructor
@Getter
public enum ExportDataTypeEnum {
    /**
     * 报账点电费审核,特殊缴费流程电费审核
     */
    ELE_PAYMENT("ele_payment", "elePaymentAuditExport.ftl"),
    ELE_SPECIAL_PAYMENT("ele_special_payment", "elePaymentAuditExport.ftl"),
    /**
     * 报账点预付费审核
     */
    ELE_LOAN("ele_loan", "eleLoanAuditExport.ftl"),
    /**
     * 报账点核销审核
     */
    ELE_VERIFICATION("ele_verification", "eleVerificationAuditExport.ftl"),
    /**
     * 报账点租费审核
     */
    RENT_PAYMENT("rent_payment", "rentPaymentAuditExport.ftl");

    private String dataType;
    private String templateName;


}
