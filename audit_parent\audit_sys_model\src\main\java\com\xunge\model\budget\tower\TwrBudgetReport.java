package com.xunge.model.budget.tower;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * twr_budget_report
 * <AUTHOR>
@Data
public class TwrBudgetReport implements Serializable  {


    /**
     * 主键ID
     */
    private String id;

    /**
     * 省份ID
     */
    private String prvId;

    /**
     * 省份名称
     */
    private String prvName;

    /**
     * 年份
     */
    private Integer onYear;

    /**
     * 月份
     */
    private Integer onMonth;

    /**
     * 业务类型({塔类,1}, {室分,2}, {微站,3}, {传输,4}, {非标,5}, {合计,6})
     */
    private Integer productType;

    /**
     * 预算年份
     */
    private String budgetTime;

    /**
     * 最新账期累计摊销值
     */
    @BudgetFiledValidate(true)
    private BigDecimal stockOrderAmoritzeFee;

    /**
     * 最新账期计提金额
     */
    @BudgetFiledValidate(true)
    private BigDecimal stockOrderAccrualFee;

    /**
     * 剩余月份应付费用
     */
    @BudgetFiledValidate(true)
    private BigDecimal stockOrderCopeFee;

    /**
     * 本年账期年份1月-账期月份存量订单在新年度的费用-预算金额
     */
    private BigDecimal stockOrderBudgetFee;

    /**
     * 本年账期年份1月-账期月份存量订单在新年度的费用-核减金额
     */
    @BudgetFiledValidate(false)
    private BigDecimal stockOrderSubtractFee;

    /**
     * 调整金额
     */
    @BudgetFiledValidate(false)
    private BigDecimal stockOrderAdjustFee;

    /**
     * 备注
     */
    @BudgetFiledValidate(false)
    private String stockOrderRemark;

    /**
     * 本年起租订单在新年补足费用
     */
    @BudgetFiledValidate(true)
    private BigDecimal complementNewRentFee;

    /**
     * 本年起租订单在新年补足费用-预算金额
     */
    private BigDecimal complementNewBudgetFee;

    /**
     * 本年起租订单在新年补足费用-核减金额
     */
    @BudgetFiledValidate(false)
    private BigDecimal complementNewSubtractFee;

    /**
     * 调整金额
     */
    @BudgetFiledValidate(false)
    private BigDecimal complementNewAdjustFee;

    /**
     * 备注
     */
    @BudgetFiledValidate(false)
    private String complementNewRemark;

    /**
     * 单订单服务费（本年新址新建订单年均值）
     */
    @BudgetFiledValidate(true)
    private BigDecimal rentOrderNewSiteFee;

    /**
     * 单订单服务费（本年共址订单年均值）
     */
    @BudgetFiledValidate(true)
    private BigDecimal rentOrderShareSiteFee;

    /**
     * 本年共址改造订单平均产品单元数
     */
    @BudgetFiledValidate(true)
    private BigDecimal rentOrderCurrentUnitNumber;

    /**
     * 年底起租的新址新建订单数
     */
    @BudgetFiledValidate(true)
    private Integer rentOrderSiteEndNumber;

    /**
     * 年底起租的共址改造订单数
     */
    @BudgetFiledValidate(true)
    private Integer rentOrderShareEndNumber;

    /**
     * 新年共址改造站址平均产品单元数
     */
    @BudgetFiledValidate(true)
    private BigDecimal rentOrderNewUnitNumber;

    /**
     * 本年账期月份-12月新起租订单在新年度的费用-预算金额
     */
    private BigDecimal rentOrderBudgetFee;

    /**
     * 本年账期月份-12月新起租订单在新年度的费用-核减金额
     */
    @BudgetFiledValidate(false)
    private BigDecimal rentOrderSubtractFee;

    /**
     * 调整金额
     */
    @BudgetFiledValidate(false)
    private BigDecimal rentOrderAdjustFee;

    /**
     * 备注
     */
    @BudgetFiledValidate(false)
    private String rentOrderRemark;

    /**
     * 单站服务费（本年新建站年均值）
     */
    @BudgetFiledValidate(true)
    private BigDecimal newSiteCurrentYearFee;

    /**
     * 单订单服务费（共址订单年均值）
     */
    @BudgetFiledValidate(true)
    private BigDecimal newSiteShareYearFee;

    /**
     * 本年共址改造订单平均产品单元数
     */
    @BudgetFiledValidate(true)
    private BigDecimal newSiteCurrentUnitNumber;

    /**
     * 新年新建站址数
     */
    @BudgetFiledValidate(true)
    private Integer newSiteNumber;

    /**
     * 新年共址改造订单数
     */
    @BudgetFiledValidate(true)
    private Integer newSiteShareOrderNumber;

    /**
     * 月份数（新址新建）
     */
    @BudgetFiledValidate(true)
    private BigDecimal newSiteCurrentMonthNumber;

    /**
     * 月份数（共址改造）
     */
    @BudgetFiledValidate(true)
    private BigDecimal newSiteShareMonthNumber;

    /**
     * 新年共址改造站址平均产品单元数
     */
    @BudgetFiledValidate(true)
    private BigDecimal newSiteShareUnitNumber;

    /**
     * 新年新建站在新年的费用-预算金额
     */
    private BigDecimal newSiteBudgetFee;

    /**
     * 新年新建站在新年的费用-核减金额
     */
    @BudgetFiledValidate(false)
    private BigDecimal newSiteSubtractFee;

    /**
     * 调整金额
     */
    @BudgetFiledValidate(false)
    private BigDecimal newSiteAdjustFee;

    /**
     * 备注
     */
    @BudgetFiledValidate(false)
    private String newSiteRemark;

    /**
     * 新年度总站址数
     */
    @BudgetFiledValidate(true)
    private Integer promoteSharedSiteNumber;

    /**
     * 单站服务费（当年独享站年化均值）
     */
    @BudgetFiledValidate(true)
    private BigDecimal promoteSharedSiteFee;

    /**
     * 共享率提升百分比
     */
    @BudgetFiledValidate(true)
    private BigDecimal promoteSharedSiteRadio;

    /**
     * 月份数
     */
    @BudgetFiledValidate(true)
    private BigDecimal promoteSharedMonthNumber;

    /**
     * 提升共享范围-预算金额
     */
    private BigDecimal promoteSharedBudgetFee;

    /**
     * 提升共享范围-核减金额
     */
    @BudgetFiledValidate(false)
    private BigDecimal promoteSharedSubtractFee;

    /**
     * 调整金额
     */
    @BudgetFiledValidate(false)
    private BigDecimal promoteSharedAdjustFee;

    /**
     * 备注
     */
    @BudgetFiledValidate(false)
    private String promoteSharedRemark;

    /**
     * 单站服务费（年化均值）
     */
    @BudgetFiledValidate(true)
    private BigDecimal hireLogoutSiteFee;

    /**
     * 单订单服务费（年化均值）
     */
    @BudgetFiledValidate(true)
    private BigDecimal hireLogoutOrderFee;

    /**
     * 退租站址数
     */
    @BudgetFiledValidate(true)
    private Integer hireLogoutBackSiteNumber;

    /**
     * 退租订单数
     */
    @BudgetFiledValidate(true)
    private Integer hireLogoutBackOrderNumber;

    /**
     * 月份数（退租站址）
     */
    @BudgetFiledValidate(true)
    private BigDecimal hireLogoutSiteMonthNumber;

    /**
     * 月份数（退租订单）
     */
    @BudgetFiledValidate(true)
    private BigDecimal hireLogoutOrderMonthNumber;

    /**
     * 站址退租退网-预算金额
     */
    private BigDecimal hireLogoutBudgetFee;

    /**
     * 站址退租退网-核减金额
     */
    @BudgetFiledValidate(false)
    private BigDecimal hireLogoutSubtractFee;

    /**
     * 调整金额
     */
    @BudgetFiledValidate(false)
    private BigDecimal hireLogoutAdjustFee;

    /**
     * 备注
     */
    @BudgetFiledValidate(false)
    private String hireLogoutRemark;

    /**
     * 单站塔租（年化均值）
     */
    @BudgetFiledValidate(true)
    private BigDecimal universalServiceSiteFee;

    /**
     * 新增压降站点
     */
    @BudgetFiledValidate(true)
    private Integer universalServiceDropSite;

    /**
     * 塔租折扣
     */
    @BudgetFiledValidate(true)
    private BigDecimal universalServiceRentRadio;

    /**
     * 月份数
     */
    @BudgetFiledValidate(true)
    private BigDecimal universalServiceMonthNumber;

    /**
     * 免除普遍服务站址-预算金额
     */
    private BigDecimal universalServiceBudgetFee;

    /**
     * 免除普遍服务站址-核减金额
     */
    @BudgetFiledValidate(false)
    private BigDecimal universalServiceSubtractFee;

    /**
     * 调整金额
     */
    @BudgetFiledValidate(false)
    private BigDecimal universalServiceAdjustFee;

    /**
     * 备注
     */
    @BudgetFiledValidate(false)
    private String universalServiceRemark;

    /**
     * 预算调整补足-预算金额
     */
    @BudgetFiledValidate(false)
    private BigDecimal budgetSupplementBudgetFee;

    /**
     * 预算调整补足-核减金额
     */
    @BudgetFiledValidate(false)
    private BigDecimal budgetSupplementSubtractFee;

    /**
     * 调整金额
     */
    @BudgetFiledValidate(false)
    private BigDecimal budgetSupplementAdjustFee;

    /**
     * 备注
     */
    @BudgetFiledValidate(false)
    private String budgetSupplementRemark;

    /**
     * 油机发电（非包干）-预算金额
     */
    @BudgetFiledValidate(true)
    private BigDecimal oilBudgetFee;

    /**
     * 油机发电（非包干）-核减金额
     */
    @BudgetFiledValidate(false)
    private BigDecimal oilSubtractFee;

    /**
     * 调整金额
     */
    @BudgetFiledValidate(false)
    private BigDecimal oilAdjustFee;

    /**
     * 备注
     */
    @BudgetFiledValidate(false)
    private String oilRemark;

    private Integer flowType;

    /**
     * 单订单服务费（本年新址新建订单年均值）订单数
     */
    private BigDecimal rentOrderNewOrderAmount;

    /**
     * 单订单服务费（本年共址订单年均值）订单数
     */
    private BigDecimal rentOrderShareOrderAmount;

    /**
     * 单站服务费（本年新建站年均值）站址数
     */
    private BigDecimal newSiteCurrentSiteAmount;

    /**
     * 单订单服务费（共址订单年均值）订单数
     */
    private BigDecimal newSiteShareOrderAmount;

    /**
     * 单站服务费（当年独享站年化均值）站址刷量
     */
    private BigDecimal promoteSharedSiteAmount;


    /**
     * 单站服务费（年化均值）站址数量
     */
    private BigDecimal hireLogoutSiteAmount;

    /**
     * 单订单服务费（年化均值）订单数
     */
    private BigDecimal hireLogoutOrderAmount;

    /**
     * 单站塔租（年化均值）站址数量
     */
    private BigDecimal universalServiceSiteAmount;


    /**
     * 10-12月起租的新址新建订单在当年使用月数
     */
    @BudgetFiledValidate(false)
    private Integer rentOrderSiteMonth;

    /**
     * 10-12月起租的共址改造订单在当年的使用月数
     */
    @BudgetFiledValidate(false)
    private Integer rentOrderShareMonth;

    /**
     * 10-12月起租订单在当年执行金额
     */
    private BigDecimal rentOrderExecutionFee;

    // 预算调整补足-执行金额
    @BudgetFiledValidate(false)
    private BigDecimal budgetSupplementExecutionFee;

    // 预算调整补足-执行金额备注
    @BudgetFiledValidate(false)
    private String budgetSupplementExecutionRemark;

    // 油机发电（非包干）-执行金额
    @BudgetFiledValidate(false)
    private BigDecimal oilExecutionFee;

    // 油机发电（非包干）_执行金额备注
    @BudgetFiledValidate(false)
    private String oilExecutionRemark;

    // 前一年执行金额
    @BudgetFiledValidate(true)
    private BigDecimal historyOneExecutionFee;

    // 前一年租赁站址数
    @BudgetFiledValidate(false)
    private Integer historyOneSiteNumber;

    // 前一年单站服务费
    private BigDecimal historyOneSiteFee;

    // 前两年执行金额
    @BudgetFiledValidate(true)
    private BigDecimal historyTwoExecutionFee;

    // 前两年租赁站址数
    @BudgetFiledValidate(false)
    private Integer historyTwoSiteNumber;

    // 前两年单站服务费
    private BigDecimal historyTwoSiteFee;

    // 前三年执行金额
    @BudgetFiledValidate(true)
    private BigDecimal historyThreeExecutionFee;

    // 前三年租赁站址数
    @BudgetFiledValidate(false)
    private Integer historyThreeSiteNumber;

    // 前三年单站服务费
    private BigDecimal historyThreeSiteFee;

    // 当年起租站址到达数
    private Integer historyCurrentSiteNumber;

    // 当年单站服务费
    private BigDecimal historyCurrentSiteFee;

    // 下一年起租站址到达数
    private Integer historyNextSiteNumber;

    // 下一年单站服务费
    private BigDecimal historyNextSiteFee;

    @BudgetFiledValidate(false)
    private String historyRemark;


    private Integer historySiteNumber;

    private Integer historyMonthSiteNumber;

    private Integer promoteSharedCurrentNumber;
    private static final long serialVersionUID = 1L;
}