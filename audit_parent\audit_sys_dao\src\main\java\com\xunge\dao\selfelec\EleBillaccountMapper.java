package com.xunge.dao.selfelec;

import com.xunge.model.basedata.DatBaseresource;
import com.xunge.model.selfelec.*;
import com.xunge.model.selfelec.billaccount.BillaccountQueryDto;
import com.xunge.model.selfelec.loan.EleLoanBalance;
import com.xunge.model.selfelec.vo.EleBillaccountChangeLog;
import org.activiti.engine.impl.persistence.entity.HistoricActivityInstanceEntity;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

public interface EleBillaccountMapper {

    int countByExample(EleBillaccountExample example);


    int deleteByExample(EleBillaccountExample example);


    int deleteByPrimaryKey(String billaccountId);

    /**
     * @description 删除报账点与合同关联关系
     * <AUTHOR>
     * @date 创建时间：2017年8月30日
     */
    public int deleteBillaccountContract(Map<String, Object> paraMap);

    /**
     * @description 报账点删除审核通过，报账点与合同关联关系调整：删除审核通过=4
     * <AUTHOR>
     * @date 创建时间：2017年8月30日
     */
    public int billDeleteAuditBillaccountContract(Map<String, Object> paraMap);

    /**
     * @description 删除报账点与资源关联关系
     * <AUTHOR>
     * @date 创建时间：2017年8月30日
     */
    public int deleteBillaccountResource(Map<String, Object> paraMap);

    /**
     * @description 报账点删除审核通过，报账点与资源关联关系调整：删除审核通过=4
     * <AUTHOR>
     * @date 创建时间：2017年8月30日
     */
    public int billDeleteAuditBillaccountResource(Map<String, Object> paraMap);

    public int billDeleteAuditBillaccountPowerData(Map<String, Object> paraMap);

    int insert(EleBillaccount record);


    int insertSelective(EleBillaccount record);

    int insertOperateTime(EleBillaccount record);


    List<EleBillaccount> selectByExample(EleBillaccountExample example);


    EleBillaccount selectByPrimaryKey(String billaccountId);

    EleBillaccount selectAuditingStateByBillaccountId(String billaccountId);

    EleBillaccount selectLastPayment(String billaccountId);

    List<EleBillaccount> selectByPrimaryKeyList(List<String> idList);

    EleBillaccount selectByPrimaryKeyForTW(String billaccountId);


    int updateByExampleSelective(@Param("record") EleBillaccount record, @Param("example") EleBillaccountExample example);


    int updateByExample(@Param("record") EleBillaccount record, @Param("example") EleBillaccountExample example);


    int updateByPrimaryKeySelective(EleBillaccount record);


    int updateByPrimaryKey(EleBillaccount record);

    int updateRemarkByPrimaryKey(EleBillaccount record);

    /**
     * 修改流程状态
     *
     * @param hashMaps
     * @return
     * <AUTHOR>
     */
    int updateActivityCommit(Map<String, Object> hashMaps);

    /**
     * 修改流程状态并增加删除原因
     *
     * @param hashMaps
     * @return
     * <AUTHOR>
     */
    int updateActivityCommitAndWriteDeleteReason(Map<String, Object> hashMaps);

    /**
     * 查询报账点审核页面
     *
     * @param hashMaps
     * @return
     * <AUTHOR>
     */
    List<EleBillaccount> queryEleBillaccount(Map<String, Object> hashMaps);

    List<EleBillaccount> queryEleBillAccountAudit(BillaccountQueryDto billaccountQueryDto);

    /**
     * 根据报账点id查询报账点信息
     *
     * @param hashMaps
     * @return
     */
    public List<EleBillaccount> queryBillaccountById(Map<String, Object> hashMaps);

    /**
     * 根据报账点id查询报账点信息
     *
     * @param hashMaps
     * @return
     */
    public EleBillaccount queryOneBillaccountById(Map<String, String> hashMaps);


    /**
     * 根据报账点id查询报账点时间信息
     *
     * @param
     * @return
     */
    List<EleBillaccount> queryBillaccountTimeById(Map<String, Object> hashMaps);

    /**
     * 根据报账点ids集合查询报账点信息
     *
     * @param hashMaps
     * @return
     */
    public List<EleBillaccount> queryEleBillaccountByIds(List<String> billaccountIds);

    /**
     * 报账标杆数据查询
     *
     * @param paramMap
     * @return
     * <AUTHOR>
     */
    public List<EleBenchmarkBillaccountLoanBean> queryEleBillBenchmark(Map<String, Object> paramMap);

    int updateStateByPrimaryKey(String billaccountId);

    /**
     * 根据报账点id查询计划缴费日期
     *
     * @param billaccountId
     * @return
     */
    public List<EleBillaccount> queryEleBillaccountById(Map<String, Object> paramMap);

    public int updatePlanDateById(Map<String, Object> paramMap);

    void updateEleBillaccountPlanDate(Map<String, String> param);

    /**
     * 通过报账点编码查询报账点
     *
     * @param billaccountCode
     * @return
     */
    public EleBillaccount queryBillaccountByCode(Map<String, String> para);

    /**
     * 通过报账点编码查询报账点(由于历史原因，报账点编码存在重复)
     *
     * @param billaccountCode
     * @return
     */
    public List<EleBillaccount> queryBillaccountListByCode(Map<String, String> para);

    /**
     * 删除报账点
     *
     * @param paraMap
     * @return
     * <AUTHOR>
     */
    public int deleteSpecialBillaccountById(Map<String, Object> paraMap);

    /**
     * 查询特殊报账点审核页面
     *
     * @param paraMap
     * @return
     */
    List<EleBillaccount> querySpecialEleBillaccount(Map<String, Object> paraMap);

    List<EleBillaccount> querySpecialEleBillAccountForAudit(BillaccountQueryDto billaccountQueryDto);

    /**
     * 计算报账点额定功率标杆
     *
     * @param map
     * @return
     * @date 2018年05月18日
     * <AUTHOR>
     */
    Double getPowerratingDegree(Map<String, String> map);

    /**
     * 查询标杆数据
     *
     * @param map
     * @return
     * @date 2018年05月18日
     * <AUTHOR>
     */
    List<EleBenchmarkBillaccountBean> getBenchmarkList(Map<String, Object> map);

    /**
     * 补录日标杆
     *
     * @param list
     * @return
     * @date 2018年05月24日
     * <AUTHOR>
     */
    int insertBenchmark(List<EleBenchmarkBillaccountBean> list);

    /**
     * 更新标杆数据
     *
     * @param
     * @return
     * @date 2018年11月20日
     * <AUTHOR>
     */
    int updateDayBenchmark(EleBenchmarkBillaccountBean eleBenchmarkBillaccountBean);

    /**
     * @Title: queryBillaccountListByIds @Description: TODO(这里用一句话描述这个方法的作用) @param @param
     * maps @param @return 设定文件 @return List<EleBillaccount> 返回类型 @throws
     */

    List<EleBillaccount> queryBillaccountListByIds(Map<String, Object> maps);


    List<EleBillaccount> queryCostCenterName(@Param("costCenterList") List<String> costCenterList);


    /**
     * @Title: queryBillaccountByIds @Description: TODO(这里用一句话描述这个方法的作用) @param @param
     * maps @param @return 设定文件 @return List<EleBillaccount> 返回类型 @throws
     */

    List<EleBillaccount> queryBillaccountByIds(Map<String, Object> maps);

    int queryEleBillaccountMeterNum(String billaccountId);

    /**
     * 批量查询报账点关联电表数量
     */
    List<Map<String, Object>> batchQueryEleBillaccountMeterNum(@Param("billaccountIds") List<String> billaccountIds);


    List<EleBillaccount> queryBillaccountByPaymentIds(Map<String, Object> maps);

    EleBillaccount queryBillaccountId(Map<String, String> accountParam);


    /**
     * 查询报账点下挂资源的状态
     *
     * @param
     * @return
     * @date 2019年01月18日
     * <AUTHOR>
     */
    List<Integer> queryBaseresourceState(String billaccountId);

    /**
     * 根据ID和报账点编码模糊查询
     *
     * @param paraMap
     * @return
     */
    EleBillaccount queryBeanByIdAndKey(Map<String, Object> paraMap);

    List<DatBaseresource> queryBaseresource(Map<String, Object> map);

    /**
     * 跟据保报账点id和电表id查（铁塔站址编码）
     *
     * @param map
     * @return
     */
    List<DatBaseresource> queryeBaseresourceelectricmeter(Map<String, Object> map);

    /**
     * 报账点删除审核通过 资源关联关系=4
     *
     * @param map
     * @return
     */
    List<DatBaseresource> queryBaseresourceDeleteAudit(Map<String, Object> map);

    List<DatBaseresource> queryBatchResourceByBillaccountId(Map<String, Object> map);

    int queryPaymentByBillid(String billaccountId);

    List<VEleBillaccount> queryBillaccountByWarning(Map<String, Object> paraMap);

    List<DatBaseresource> queryBatchBaseresource(Map<String, Object> map);

    List<DatBaseresource> queryRentBatchBaseresource(Map<String, Object> map);

    int updateBillaccountState(Map map);

    void deleteBillaccountInfo(String billaccountId);

    void deleteContractbillaccountRelation(String billaccountId);

    @Select("SELECT cancellation_balance cancellationBalance FROM ele_loan_balance WHERE billaccount_id=#{billaccountId} and type=#{type} limit 1")
    EleLoanBalance getEleLoanBalanceByBillAccountId(@Param("billaccountId") String billaccountId, @Param("type") String type);

    Map<String, Object> queryUserInfo(@Param("billacountId") String billacountId);

    int isertBillaccountChangeLoG(List<EleBillaccountChangeLog> list);

    List<EleBillaccountChangeLog> queryLastInfo(@Param("billacountId") String billaccountId);

    int countBillacoountChangeLog(@Param("billacountId") String billacountId);

    List<DatBaseresource> queryBatchBaseresourceMore(@Param("list") List<String> list);

    /**
     * 报账标杆数据查询
     *
     * @param paramMap
     * @return
     */
    public List<EleBenchmarkBillaccountLoanBean> queryEleBillBenchmarkNew(Map<String, Object> paramMap);

    EleBillaccountRelation queryResourceRelation(Map<String,Object> paramMap);

    EleBillaccountRelation queryElectricmeterRelation(Map<String, Object> map);

    /**
     * 电费报账点普服比例
     * @param regId 传地市
     * @param baseresourceIds 资源ID
     * @param ifTeleCmnServ 是否普服
     * @return 能耗
     */
    BigDecimal queryEleBillaccountPowerConsumption(@Param("regId")String regId,@Param("baseresourceIds") List<String> baseresourceIds,@Param("ifTeleCmnServ") Integer ifTeleCmnServ);

    List<DatResourceInNetworkBean> queryResourceInNetworkTime(List<String> list);

    List<BillaccountInfoDto> selectPaymentByBillamountCode(@Param("billamountCode") String billamountCode);

    List<BillaccountInfoDto> selectVerifcationByBillamountCode(@Param("billamountCode") String billamountCode);

    void updateBillaccountInfo(@Param("billaccountInfoDtoList") List<BillaccountInfoDto> billaccountInfoDtoList);

    int addUpdateRecord(EleBillaccountUpdateRecordDto eleBillaccountUpdateRecordDto);

    Integer queryProvinceFlag(String taskId);

    Integer selectBillaccountTypeById(@Param("billaccountId") String billaccountId);

    int updateBillAudit(@Param("paymentCode") String paymentCode, @Param("newUserLoginname") String newUserLoginname);

    /**
     * 根据报账点查询当前关联的电表（审核通过状态）
     * @param billaccountId 报账点id
     * @return 关联电表列表
     */
    List<VDatElectricmeter> queryMeterByBillaccountId(String billaccountId);

    boolean insertBillaccountAuditinfo(EleBillaccountAuditinfo eleBillaccountAuditinfo);

    List<EleBillaccountAuditinfo> selectByBillaccountId(String billaccountId);
}
