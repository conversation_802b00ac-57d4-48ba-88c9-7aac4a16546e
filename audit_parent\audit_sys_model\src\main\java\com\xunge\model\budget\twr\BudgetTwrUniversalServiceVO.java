package com.xunge.model.budget.twr;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2022/7/28
 * @description 免除普遍服务站址
 */
@Data
public class BudgetTwrUniversalServiceVO {

    /**
     * 省份ID
     */
    private String prvId;

    /**
     * 省份名称
     */
    private String prvName;
    /**
     * 业务类型({塔类,1}, {室分,2}, {微站,3}, {传输,4}, {非标,5}, {合计,6})
     */
    private Integer productType;

    /**
     * 单站塔租（年化均值）
     */
    private BigDecimal universalServiceSiteFee;
    /**
     * 新增压降站点
     */
    private Integer universalServiceDropSite;
    /**
     * 塔租折扣
     */
    private BigDecimal universalServiceRentRadio;
    /**
     * 月份数
     */
    private BigDecimal universalServiceMonthNumber;
    /**
     * 免除普遍服务站址-预算金额
     */
    private BigDecimal universalServiceBudgetFee;
    /**
     * 免除普遍服务站址-核减金额
     */
    private BigDecimal universalServiceSubtractFee;

    /**
     * 调整金额
     */
    private BigDecimal universalServiceAdjustFee;
    private BigDecimal universalServiceAdjustFeeAfter;

    private String universalServiceRemark;

}
