package com.xunge.dao.system.monitor.impl;

import com.xunge.comm.system.RESULT;
import com.xunge.dao.AbstractBaseDao;
import com.xunge.dao.system.monitor.MonitorServiceDao;
import org.springframework.stereotype.Service;

/**
 * @descript: 实现类
 * @author: binbin.wang
 * @date: 2018-08-21
 **/
@Service("monitorDao")
public class MonitorServiceDaoImpl extends AbstractBaseDao implements MonitorServiceDao {
    final String Namespace = "com.xunge.mapping.MonitorVOMapper.";

    @Override
    public String insertMySql(String prvId) {
        int result = this.getSqlSession().insert(Namespace + "insertMySql", prvId);
        return (result == 0) ? RESULT.FAIL_0 : RESULT.SUCCESS_1;
    }

    @Override
    public String deleteMySql(String prvId) {
        int result = this.getSqlSession().delete(Namespace + "deleteMySql", prvId);
        return (result == 0) ? RESULT.FAIL_0 : RESULT.SUCCESS_1;
    }
}
