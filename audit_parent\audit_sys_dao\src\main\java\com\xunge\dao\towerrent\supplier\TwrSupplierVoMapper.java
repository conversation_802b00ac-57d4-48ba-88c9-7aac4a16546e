package com.xunge.dao.towerrent.supplier;

import com.xunge.model.towerrent.supplier.TwrSupplierVo;

import java.util.List;
import java.util.Map;

/**
 * TODO: 铁塔供应商mapper
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2018/11/9 10:28
 */
public interface TwrSupplierVoMapper {

    /**
     * 根据条件查询铁塔供应商并分页
     *
     * @param paramMap
     * @return
     */
    List<TwrSupplierVo> queryTowerSupplierVO(Map<String, Object> paramMap);

    /**
     * 检测铁塔供应商是否重复
     *
     * @param paramMap
     * @return
     */
    List<TwrSupplierVo> cherkTowerSupplier(Map<String, Object> paramMap);

    /**
     * 新增铁塔供应商
     *
     * @param supplierVo
     * @return
     */
    int addTwrSupplier(TwrSupplierVo supplierVo);

    int updateTwrSupplier(TwrSupplierVo record);

    TwrSupplierVo selectByPrimaryKey(Map<String, Object> map);


    int deleteTwrSupplier(Map<String, Object> paramMap);

}
