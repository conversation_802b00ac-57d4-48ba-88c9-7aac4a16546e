package com.xunge.dao.towerrent.tiny.impl;

import com.xunge.core.page.Page;
import com.xunge.dao.AbstractBaseDao;
import com.xunge.dao.towerrent.tiny.ITinyFeeDao;
import com.xunge.filter.PageInterceptor;
import com.xunge.model.towerrent.settlement.OtherFeeTinyVO;
import com.xunge.model.towerrent.settlement.TowerAndMobileBillTinyConfirmVO;
import com.xunge.model.towerrent.settlement.TowerBillBalanceTinyVO;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service("iTinyFeeDao")
public class TinyFeeDaoImpl extends AbstractBaseDao implements ITinyFeeDao {

    final String TinyFee = "com.xunge.mapping.TowerSideBillTinyVOMapper.";
    final String OtherFeeTiny = "com.xunge.mapping.OtherFeeTinyVOMapper.";

    @Override
    public List<TowerBillBalanceTinyVO> queryTinyBill(Map<String, Object> paramMap) {
        return this.getSqlSession().selectList(TinyFee + "queryTinyBill", paramMap);
    }

    @Override
    public int updateTinyFeeSumcodeToNull(Map<String, Object> paramMap) {
        return this.getSqlSession().update(TinyFee + "updateTinyFeeSumcodeToNull", paramMap);
    }

    @Override
    public int updateTinyBillSetSumcode(TowerBillBalanceTinyVO tinyVO) {
        return this.getSqlSession().update(TinyFee + "updateTinyBillSetSumcode", tinyVO);
    }

    @Override
    public List<TowerAndMobileBillTinyConfirmVO> queryAccountedTinyBill(Map<String, Object> paramMap) {
        return this.getSqlSession().selectList(TinyFee + "queryAccountedTinyBill", paramMap);
    }

    @Override
    public Page<TowerAndMobileBillTinyConfirmVO> queryAccountedTinyBillByPage(Map<String, Object> paraMap, int pageNumber, int pageSize) {
        PageInterceptor.startPage(pageNumber, pageSize);
        this.getSqlSession().selectList(TinyFee + "queryAccountedTinyBill", paraMap);
        return PageInterceptor.endPage();
    }

    @Override
    public List<OtherFeeTinyVO> queryAccountedOtherFeeTiny(Map<String, Object> paramMap) {
        return this.getSqlSession().selectList(OtherFeeTiny + "queryAccountedOtherFeeTiny", paramMap);
    }

    @Override
    public Page<List<OtherFeeTinyVO>> queryAccountedOtherFeeTinyByPage(Map<String, Object> paraMap, int pageNumber, int pageSize) {
        PageInterceptor.startPage(pageNumber, pageSize);
        this.getSqlSession().selectList(OtherFeeTiny + "queryAccountedOtherFeeTiny", paraMap);
        return PageInterceptor.endPage();
    }

    @Override
    public TowerAndMobileBillTinyConfirmVO queryTinyRental(Map<String, Object> map) {
        return this.getSqlSession().selectOne(TinyFee + "queryTinyRental", map);
    }
}
