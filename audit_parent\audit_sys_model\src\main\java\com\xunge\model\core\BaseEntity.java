package com.xunge.model.core;

import java.io.Serializable;

/**
 * Entity支持类
 *
 * <AUTHOR>
 * @version 2014-05-16
 */
public abstract class BaseEntity<T> implements Serializable {
    private static final long serialVersionUID = 1L;


    private String id;

    private  String content;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }
}
