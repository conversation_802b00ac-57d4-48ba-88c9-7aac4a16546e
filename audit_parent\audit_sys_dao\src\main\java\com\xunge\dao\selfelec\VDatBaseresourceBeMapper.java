package com.xunge.dao.selfelec;

import com.xunge.model.selfelec.VDatBaseresourceBe;
import com.xunge.model.selfelec.VDatBaseresourceBeExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface VDatBaseresourceBeMapper {
    
    int countByExample(VDatBaseresourceBeExample example);

    
    int deleteByExample(VDatBaseresourceBeExample example);

    
    int insert(VDatBaseresourceBe record);

    
    int insertSelective(VDatBaseresourceBe record);

    
    List<VDatBaseresourceBe> selectByExample(VDatBaseresourceBeExample example);

    
    int updateByExampleSelective(@Param("record") VDatBaseresourceBe record, @Param("example") VDatBaseresourceBeExample example);

    
    int updateByExample(@Param("record") VDatBaseresourceBe record, @Param("example") VDatBaseresourceBeExample example);
}