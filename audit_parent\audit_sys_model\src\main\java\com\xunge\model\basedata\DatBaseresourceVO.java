package com.xunge.model.basedata;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelCollection;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.xunge.model.BaseActVO;
import com.xunge.model.activity.Act;
import com.xunge.model.selfelec.EleBenchmarkParaVO;
import com.xunge.model.selfelec.electricmeter.DatElectricMeterVO;
import com.xunge.model.selfrent.billAccount.BillAccountVO;
import com.xunge.model.selfrent.contract.RentContractVO;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

public class DatBaseresourceVO extends BaseActVO {
    private String baseresourceId;

    private String basesiteId;
    private String basesiteCuid;
    //站点名称
    private String basesiteName;

    private String prvId;

    private String prvCode;

    private String prvSname;

    private String pregId;

    private String pregName;

    private String regId;

    private String regName;

    private Integer baseresourceType;

    // 动环在站点类型编码
    private String siteTypeCode;

    private String baseresourceCategory;

    private String baseresourceCuid;
    @Excel(name = "关联资源编码", orderNum = "9")
    private String baseresourceCode;
    @Excel(name = "关联资源名称", orderNum = "10")
    private String baseresourceName;

    private String baseresourceAddress;

    private BigDecimal baseresourceArea;

    private Integer baseresourceMaintenance;

    private String towerSiteCode;

    private Date baseresourceOpendate;

    private Date baseresourceStopdate;
    @Excel(name = "产权单位", isImportField = "true", replace = {"中国铁塔_1", "中国移动_2", "中国联通_3",
            "中国电信_4", "中国铁通_5", "中国广电_6", "业主_7", "其他_8"})
    private String roomOwner;
    @Excel(name = "产权性质", isImportField = "true", replace = {"自有（自建)_1", "自有（合建）_2",
            "自有（购买）_3", "租用_4", "用户所有_5", "其他_6"})
    private Integer roomProperty;

    private String roomShare;

    private BigDecimal baseresourceLongitude;

    private BigDecimal baseresourceLatitude;

    private BigDecimal airconditionerPower;

    private Integer baseresourceState;

    private String baseresourceNote;

    private Integer auditingState;

    private String auditingUserId;

    private Date auditingDate;

    private Integer dataFrom;

    private Integer baseresourceLevel;

    private BigDecimal equipmentPower;
    //铁塔空调额定功率
    private BigDecimal towerEquipmentPower;

    private Integer serviceSiteType;


    private Date startTime;


    private String operateUserId;


    //0 旧机房 新机房
    private Integer type;

    //报账点实体类
    private List<BillAccountVO> billAccountVO;
    //电费报账点codes
    private String eleBillaccountCodes;
    //租费报账点codes
    private String rentBillaccountCodes;

    //合同实体类
    private RentContractVO rentContractVO;

    // 经度字符串
    private String baseresourceLongitudeStr;
    // 纬度字符串
    private String baseresourceLatitudeStr;
    //关联资源ID
    private String relationBaseresourceId;
    //关联资源名称
    private String relationBaseresourceCuid;

    //免费标识(0：不免费 1：免电费 2：免租费 3：电租皆免费)
    private Integer freeFlag;

    //最近功率变动（30天内）
    private BigDecimal changeRange;
    private Integer ctcRoomCategory;
    private List<DatBasestationVO> datBasestationVO;
    private List<DatBasesiteVO> datBaseSiteVO;
    private Act act;
    private EleBenchmarkParaVO eleBenchmark;
    private String create_user;
    private String create_ip;
    private Date create_time;
    private String update_user;
    private String update_ip;
    private Date update_time;
    private BigDecimal runningTime;
    private String belongDept;
    @ExcelCollection(name = "关联电表信息", orderNum = "25")
    private List<DatElectricMeterVO> eleMeterVO;
    // 5G标识
    private Integer fivegFlag;
    // 是否属于电信普遍服务
    private Integer ifTeleCmnServ;
    // 电信普遍服务项目编码
    private String teleCmnServProCode;
    // 电信普遍服务项目名称
    private String teleCmnServProName;
    
    private Integer serviceSiteTypeReport;

    public void setFivegFlag(Integer fivegFlag) {
        this.fivegFlag = fivegFlag;
    }

    public void setIfTeleCmnServ(Integer ifTeleCmnServ) {
        this.ifTeleCmnServ = ifTeleCmnServ;
    }

    public void setTeleCmnServProCode(String teleCmnServProCode) {
        this.teleCmnServProCode = teleCmnServProCode;
    }

    public void setTeleCmnServProName(String teleCmnServProName) {
        this.teleCmnServProName = teleCmnServProName;
    }

    public Integer getFivegFlag() {
        return fivegFlag;
    }

    public Integer getIfTeleCmnServ() {
        return ifTeleCmnServ;
    }

    public String getTeleCmnServProCode() {
        return teleCmnServProCode;
    }

    public String getTeleCmnServProName() {
        return teleCmnServProName;
    }

    public String getEleBillaccountCodes() {
        return eleBillaccountCodes;
    }

    public void setEleBillaccountCodes(String eleBillaccountCodes) {
        this.eleBillaccountCodes = eleBillaccountCodes;
    }

    public String getRentBillaccountCodes() {
        return rentBillaccountCodes;
    }

    public void setRentBillaccountCodes(String rentBillaccountCodes) {
        this.rentBillaccountCodes = rentBillaccountCodes;
    }

    public String getRelationBaseresourceId() {
        return relationBaseresourceId;
    }

    public void setRelationBaseresourceId(String relationBaseresourceId) {
        this.relationBaseresourceId = relationBaseresourceId;
    }

    public String getRelationBaseresourceCuid() {
        return relationBaseresourceCuid;
    }

    public void setRelationBaseresourceCuid(String relationBaseresourceCuid) {
        this.relationBaseresourceCuid = relationBaseresourceCuid;
    }

    public Integer getBaseresourceLevel() {
        return baseresourceLevel;
    }

    public void setBaseresourceLevel(Integer baseresourceLevel) {
        this.baseresourceLevel = baseresourceLevel;
    }

    public BigDecimal getEquipmentPower() {
        return equipmentPower;
    }

    public void setEquipmentPower(BigDecimal equipmentPower) {
        this.equipmentPower = equipmentPower;
    }

    public Integer getServiceSiteType() {
        return serviceSiteType;
    }

    public void setServiceSiteType(Integer serviceSiteType) {
        this.serviceSiteType = serviceSiteType;
    }

    public Integer getCtcRoomCategory() {
        return ctcRoomCategory;
    }

    public void setCtcRoomCategory(Integer ctcRoomCategory) {
        this.ctcRoomCategory = ctcRoomCategory;
    }

    public List<DatBasesiteVO> getDatBaseSiteVO() {
        return datBaseSiteVO;
    }

    public void setDatBaseSiteVO(List<DatBasesiteVO> datBaseSiteVO) {
        this.datBaseSiteVO = datBaseSiteVO;
    }

    public Act getAct() {
        return act;
    }

    public void setAct(Act act) {
        this.act = act;
    }

    public String getBasesiteName() {
        return basesiteName;
    }

    public void setBasesiteName(String basesiteName) {
        this.basesiteName = basesiteName;
    }

    public String getBaseresourceId() {
        return baseresourceId;
    }

    public void setBaseresourceId(String baseresourceId) {
        this.baseresourceId = baseresourceId == null ? null : baseresourceId.trim();
    }

    public String getBasesiteId() {
        return basesiteId;
    }

    public void setBasesiteId(String basesiteId) {
        this.basesiteId = basesiteId == null ? null : basesiteId.trim();
    }

    public String getPrvId() {
        return prvId;
    }

    public void setPrvId(String prvId) {
        this.prvId = prvId == null ? null : prvId.trim();
    }

    public String getPrvSname() {
        return prvSname;
    }

    public void setPrvSname(String prvSname) {
        this.prvSname = prvSname == null ? null : prvSname.trim();
    }

    public String getPregId() {
        return pregId;
    }

    public void setPregId(String pregId) {
        this.pregId = pregId == null ? null : pregId.trim();
    }

    public String getPregName() {
        return pregName;
    }

    public void setPregName(String pregName) {
        this.pregName = pregName == null ? null : pregName.trim();
    }

    public String getRegId() {
        return regId;
    }

    public void setRegId(String regId) {
        this.regId = regId == null ? null : regId.trim();
    }

    public String getRegName() {
        return regName;
    }

    public void setRegName(String regName) {
        this.regName = regName == null ? null : regName.trim();
    }

    public Integer getBaseresourceType() {
        return baseresourceType;
    }

    public void setBaseresourceType(Integer baseresourceType) {
        this.baseresourceType = baseresourceType;
    }

    public String getBaseresourceCategory() {
        return baseresourceCategory;
    }

    public void setBaseresourceCategory(String baseresourceCategory) {
        this.baseresourceCategory = baseresourceCategory;
    }

    public String getBaseresourceCuid() {
        return baseresourceCuid;
    }

    public void setBaseresourceCuid(String baseresourceCuid) {
        this.baseresourceCuid = baseresourceCuid == null ? null : baseresourceCuid.trim();
    }

    public String getBaseresourceCode() {
        return baseresourceCode;
    }

    public void setBaseresourceCode(String baseresourceCode) {
        this.baseresourceCode = baseresourceCode == null ? null : baseresourceCode.trim();
    }

    public String getBaseresourceName() {
        return baseresourceName;
    }

    public void setBaseresourceName(String baseresourceName) {
        this.baseresourceName = baseresourceName == null ? null : baseresourceName.trim();
    }

    public String getBaseresourceAddress() {
        return baseresourceAddress;
    }

    public void setBaseresourceAddress(String baseresourceAddress) {
        this.baseresourceAddress = baseresourceAddress == null ? null : baseresourceAddress.trim();
    }

    public BigDecimal getBaseresourceArea() {
        return baseresourceArea;
    }

    public void setBaseresourceArea(BigDecimal baseresourceArea) {
        this.baseresourceArea = baseresourceArea;
    }

    public Integer getBaseresourceMaintenance() {
        return baseresourceMaintenance;
    }

    public void setBaseresourceMaintenance(Integer baseresourceMaintenance) {
        this.baseresourceMaintenance = baseresourceMaintenance;
    }

    public String getTowerSiteCode() {
        return towerSiteCode;
    }

    public void setTowerSiteCode(String towerSiteCode) {
        this.towerSiteCode = towerSiteCode == null ? null : towerSiteCode.trim();
    }

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    public Date getBaseresourceOpendate() {
        return baseresourceOpendate;
    }

    public void setBaseresourceOpendate(Date baseresourceOpendate) {
        this.baseresourceOpendate = baseresourceOpendate;
    }

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    public Date getBaseresourceStopdate() {
        return baseresourceStopdate;
    }

    public void setBaseresourceStopdate(Date baseresourceStopdate) {
        this.baseresourceStopdate = baseresourceStopdate;
    }

    public String getRoomOwner() {
        return roomOwner;
    }

    public void setRoomOwner(String roomOwner) {
        this.roomOwner = roomOwner;
    }

    public Integer getRoomProperty() {
        return roomProperty;
    }

    public void setRoomProperty(Integer roomProperty) {
        this.roomProperty = roomProperty;
    }

    public String getRoomShare() {
        return roomShare;
    }

    public void setRoomShare(String roomShare) {
        this.roomShare = roomShare;
    }

    public BigDecimal getBaseresourceLongitude() {
        return baseresourceLongitude;
    }

    public void setBaseresourceLongitude(BigDecimal baseresourceLongitude) {
        this.baseresourceLongitude = baseresourceLongitude;
    }

    public BigDecimal getBaseresourceLatitude() {
        return baseresourceLatitude;
    }

    public void setBaseresourceLatitude(BigDecimal baseresourceLatitude) {
        this.baseresourceLatitude = baseresourceLatitude;
    }

    public BigDecimal getAirconditionerPower() {
        return airconditionerPower;
    }

    public void setAirconditionerPower(BigDecimal airconditionerPower) {
        this.airconditionerPower = airconditionerPower;
    }

    public Integer getBaseresourceState() {
        return baseresourceState;
    }

    public void setBaseresourceState(Integer baseresourceState) {
        this.baseresourceState = baseresourceState;
    }

    public String getBaseresourceNote() {
        return baseresourceNote;
    }

    public void setBaseresourceNote(String baseresourceNote) {
        this.baseresourceNote = baseresourceNote == null ? null : baseresourceNote.trim();
    }

    public Integer getAuditingState() {
        return auditingState;
    }

    public void setAuditingState(Integer auditingState) {
        this.auditingState = auditingState;
    }

    public String getAuditingUserId() {
        return auditingUserId;
    }

    public void setAuditingUserId(String auditingUserId) {
        this.auditingUserId = auditingUserId == null ? null : auditingUserId.trim();
    }

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    public Date getAuditingDate() {
        return auditingDate;
    }

    public void setAuditingDate(Date auditingDate) {
        this.auditingDate = auditingDate;
    }

    public Integer getDataFrom() {
        return dataFrom;
    }

    public void setDataFrom(Integer dataFrom) {
        this.dataFrom = dataFrom;
    }

    public List<DatBasestationVO> getDatBasestationVO() {
        return datBasestationVO;
    }

    public void setDatBasestationVO(List<DatBasestationVO> datBasestationVO) {
        this.datBasestationVO = datBasestationVO;
    }

    public String getCreate_user() {
        return create_user;
    }

    public void setCreate_user(String create_user) {
        this.create_user = create_user;
    }

    public String getCreate_ip() {
        return create_ip;
    }

    public void setCreate_ip(String create_ip) {
        this.create_ip = create_ip;
    }

    public Date getCreate_time() {
        return create_time;
    }

    public void setCreate_time(Date create_time) {
        this.create_time = create_time;
    }

    public String getUpdate_user() {
        return update_user;
    }

    public void setUpdate_user(String update_user) {
        this.update_user = update_user;
    }

    public String getUpdate_ip() {
        return update_ip;
    }

    public void setUpdate_ip(String update_ip) {
        this.update_ip = update_ip;
    }

    public Date getUpdate_time() {
        return update_time;
    }

    public void setUpdate_time(Date update_time) {
        this.update_time = update_time;
    }

    public BigDecimal getRunningTime() {
        return runningTime;
    }

    public void setRunningTime(BigDecimal runningTime) {
        this.runningTime = runningTime;
    }

    public EleBenchmarkParaVO getEleBenchmark() {
        return eleBenchmark;
    }

    public void setEleBenchmark(EleBenchmarkParaVO eleBenchmark) {
        this.eleBenchmark = eleBenchmark;
    }

    public List<DatElectricMeterVO> getEleMeterVO() {
        return eleMeterVO;
    }

    public void setEleMeterVO(List<DatElectricMeterVO> eleMeterVO) {
        this.eleMeterVO = eleMeterVO;
    }

    public String getBelongDept() {
        return belongDept;
    }

    public void setBelongDept(String belongDept) {
        this.belongDept = belongDept;
    }

    public List<BillAccountVO> getBillAccountVO() {
        return billAccountVO;
    }

    public void setBillAccountVO(List<BillAccountVO> billAccountVO) {
        this.billAccountVO = billAccountVO;
    }

    public RentContractVO getRentContractVO() {
        return rentContractVO;
    }

    public void setRentContractVO(RentContractVO rentContractVO) {
        this.rentContractVO = rentContractVO;
    }

    public String getBaseresourceLongitudeStr() {
        return baseresourceLongitudeStr;
    }

    public void setBaseresourceLongitudeStr(String baseresourceLongitudeStr) {
        this.baseresourceLongitudeStr = baseresourceLongitudeStr;
    }

    public String getBaseresourceLatitudeStr() {
        return baseresourceLatitudeStr;
    }

    public void setBaseresourceLatitudeStr(String baseresourceLatitudeStr) {
        this.baseresourceLatitudeStr = baseresourceLatitudeStr;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getPrvCode() {
        return prvCode;
    }

    public void setPrvCode(String prvCode) {
        this.prvCode = prvCode;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public String getOperateUserId() {
        return operateUserId;
    }

    public void setOperateUserId(String operateUserId) {
        this.operateUserId = operateUserId;
    }

    public String getBasesiteCuid() {
        return basesiteCuid;
    }

    public void setBasesiteCuid(String basesiteCuid) {
        this.basesiteCuid = basesiteCuid;
    }

    public Integer getFreeFlag() {
        return freeFlag;
    }

    public void setFreeFlag(Integer freeFlag) {
        this.freeFlag = freeFlag;
    }

    public BigDecimal getTowerEquipmentPower() {
        return towerEquipmentPower;
    }

    public void setTowerEquipmentPower(BigDecimal towerEquipmentPower) {
        this.towerEquipmentPower = towerEquipmentPower;
    }

    public BigDecimal getChangeRange() {
        return changeRange;
    }

    public void setChangeRange(BigDecimal changeRange) {
        this.changeRange = changeRange;
    }
    public String getSiteTypeCode() {
        return siteTypeCode;
    }

    public void setSiteTypeCode(String siteTypeCode) {
        this.siteTypeCode = siteTypeCode;
    }

	public Integer getServiceSiteTypeReport() {
		return serviceSiteTypeReport;
	}

	public void setServiceSiteTypeReport(Integer serviceSiteTypeReport) {
		this.serviceSiteTypeReport = serviceSiteTypeReport;
	}
}