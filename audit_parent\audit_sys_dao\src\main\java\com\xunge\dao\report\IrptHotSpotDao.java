package com.xunge.dao.report;

import java.util.List;
import java.util.Map;

public interface IrptHotSpotDao {
    /**
     * 查询热点报表信息通过省份
     *
     * @return
     * <AUTHOR>
     */
    public List<Map<String, Object>> queryHotSpotByPrv(Map<String, Object> map);

    /**
     * 查询热点报表信息通过地市
     *
     * @return
     * <AUTHOR>
     */
    public List<Map<String, Object>> queryHotSpotByPreg(Map<String, Object> map);

    public List<Map<String, Object>> queryHotSpot(Map<String, Object> map);
}