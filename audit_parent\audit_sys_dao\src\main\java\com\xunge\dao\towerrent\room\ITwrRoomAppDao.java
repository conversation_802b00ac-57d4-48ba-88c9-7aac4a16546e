package com.xunge.dao.towerrent.room;

import com.xunge.model.towerrent.room.TowerRoom;

import java.util.List;
import java.util.Map;

/**
 * Created by l<PERSON><PERSON><PERSON> on 2019/7/24.
 */
public interface ITwrRoomAppDao {
    /*TowerRentRoomVO getRwrRentRoomInfoById(String rentInformationTowerRoomId);

    TowerRentRoomVO queryBeanById(Map<String, Object> map);

    int updateRentRoomChangeCheckState(Map<String, String> map);

    int updateRentRoomCheckState(Map<String, String> map);

    MobileRentRoomVO getMobileRentRoomById(Map<String, String> paramMap);

    int deleteChangeByBussId(Map<String, String> map);

    int deleteByBussId(Map<String, String> map);

    TowerLinkNew getResLink(String towerStationCode);

    int insertRoomAsTower(Map<String, String> map);

    int insertRoomChangeAsTower(Map<String, String> map);

    List<TowerRentRoomChange> getTwrRoomUpdateHistory(Map<String, Object> paraMap);

    int updateMobileRentRoomByParam(Map<String, Object> paraMap);

    int insertBatchByTwrRentChange(Map<String, Object> paraMap);

    MobileRentRoomVO getMobileRentRoomInfoById(Map<String, Object> param) ;

    MobileRentRoomVO queryMobileBeanById(Map<String, Object> param);

    List<MobileRentRoomChangeVO> getMobileChangeHistory(Map<String, String> auditMap);

    int mobileChangeCheckStateUpdate(Map<String, String> auditMap);

    int mobileRoomCheckStateUpdate(Map<String, String> auditMap);

    Page<MobileRentRoomChangeVO> getMobileChangeHistory(Map<String, Object> map, int pageNum, int pageSize);

    Page<TowerRentRoomChange> getTwrRoomUpdateHistory(Map<String, Object> paraMap, int pageNum, int pageSize);*/

    /**
     * 条件查询微站起租数据
     *
     * @param param
     * @return
     */
    List<TowerRoom> getTwrRentRoomsByParam(Map<String, Object> param);

    /**
     * 根据业务主键获取起租单和账单信息
     *
     * @param queryList
     * @return
     */
    List<Map> findRentInfoAndBillInfo(List<Map> queryList);
}
