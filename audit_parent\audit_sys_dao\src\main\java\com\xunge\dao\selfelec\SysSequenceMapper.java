package com.xunge.dao.selfelec;

import com.xunge.model.util.SysSequence;

import java.util.Map;

/**
 * <AUTHOR>
 * @descript 编码记录表
 * @date 2017-08-30 17:12:23
 */
public interface SysSequenceMapper {

    SysSequence selectByPrimaryKey(Map<String, Object> paraMap);

    int updateByPrimaryKey(SysSequence record);

    int insertSequence(SysSequence record);

    SysSequence selectBySeqCode(String seqCode);

    int insertSelective(SysSequence record);

    int updateBySeqCode(SysSequence record);
}