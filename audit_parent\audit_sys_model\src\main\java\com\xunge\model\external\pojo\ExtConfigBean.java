package com.xunge.model.external.pojo;

import java.io.Serializable;

/**
 * @Description external配置类
 * <AUTHOR>
 * @Date 2021/1/25 11:13
 * @modifier ZXX
 * @date 2021/1/25 11:13
 * @Version 1.0
 **/
public class ExtConfigBean implements Serializable {

    private static final long serialVersionUID = 1356840763958472899L;
    /*id*/
    private Integer configId;
    /*配置key*/
    private String configKey;
    /*配置value*/
    private String configValue;
    /*配置描述*/
    private String configDescribe;

    public Integer getConfigId() {
        return configId;
    }

    public void setConfigId(Integer configId) {
        this.configId = configId;
    }

    public String getConfigKey() {
        return configKey;
    }

    public void setConfigKey(String configKey) {
        this.configKey = configKey;
    }

    public String getConfigValue() {
        return configValue;
    }

    public void setConfigValue(String configValue) {
        this.configValue = configValue;
    }

    public String getConfigDescribe() {
        return configDescribe;
    }

    public void setConfigDescribe(String configDescribe) {
        this.configDescribe = configDescribe;
    }

    @Override
    public String toString() {
        return "ExtConfigBean{" +
                "configId=" + configId +
                ", configKey='" + configKey + '\'' +
                ", configValue='" + configValue + '\'' +
                ", configDescribe='" + configDescribe + '\'' +
                '}';
    }
}
