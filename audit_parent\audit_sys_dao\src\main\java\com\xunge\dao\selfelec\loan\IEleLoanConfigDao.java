package com.xunge.dao.selfelec.loan;

import com.xunge.dao.core.CrudDao;
import com.xunge.model.selfelec.loan.EleLoanConfig;

import java.util.List;

/**
 * 省份借款天数配置DAO接口
 *
 * <AUTHOR>
 * @version 2018-03-07
 */
public interface IEleLoanConfigDao extends CrudDao<EleLoanConfig> {

    public EleLoanConfig get(String id);

    public List<EleLoanConfig> findList(EleLoanConfig eleLoanConfig);

    public void save(EleLoanConfig eleLoanConfig);

    public int delete(EleLoanConfig eleLoanConfig);

    public int insert(EleLoanConfig eleLoanConfig);

    public int update(EleLoanConfig eleLoanConfig);
}