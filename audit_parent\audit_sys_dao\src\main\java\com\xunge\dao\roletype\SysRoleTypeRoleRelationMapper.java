package com.xunge.dao.roletype;

import com.xunge.model.roletype.SysRoleTypeRoleRelation;
import com.xunge.model.roletype.SysRoleTypeRoleRelationVo;
import com.xunge.model.roletype.SysUserRoleExclusionVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface SysRoleTypeRoleRelationMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table sys_roletype_role_relation
     *
     * @mbggenerated Thu May 05 08:39:14 CST 2022
     */
    int deleteByPrimaryKey(Long roletypeRoleRealtionId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table sys_roletype_role_relation
     *
     * @mbggenerated Thu May 05 08:39:14 CST 2022
     */
    int insert(SysRoleTypeRoleRelation record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table sys_roletype_role_relation
     *
     * @mbggenerated Thu May 05 08:39:14 CST 2022
     */
    int insertSelective(SysRoleTypeRoleRelation record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table sys_roletype_role_relation
     *
     * @mbggenerated Thu May 05 08:39:14 CST 2022
     */
    SysRoleTypeRoleRelation selectByPrimaryKey(Long roletypeRoleRealtionId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table sys_roletype_role_relation
     *
     * @mbggenerated Thu May 05 08:39:14 CST 2022
     */
    int updateByPrimaryKeySelective(SysRoleTypeRoleRelation record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table sys_roletype_role_relation
     *
     * @mbggenerated Thu May 05 08:39:14 CST 2022
     */
    int updateByPrimaryKey(SysRoleTypeRoleRelation record);


    List<SysRoleTypeRoleRelationVo> queryRoleTypeRoleRelation(Map<String,Object> paramMap);

    /**
     *
     * @param paramMap
     * @return
     */
    List<SysRoleTypeRoleRelationVo> queryUserByRoleIds(Map<String,Object> paramMap);


    /**
     * 根据当前角色  及当前角色大类ID 查询 互斥的角色大类信息
     * @param paramMap roleIds  当前角色ID 集合  roleTypeId  当前角色大类ID
     * @return
     */
    List<SysRoleTypeRoleRelationVo> queryExclusionRoleTypeByRoleIds(Map<String,Object> paramMap);

    /**
     * 根据当前角色大类ID 查询互斥的角色信息
     * @param paramMap
     * @return
     */
    List<SysRoleTypeRoleRelationVo> queryExclusionRoleTypeRelationRoleDetailByRoleTypeId(Map<String,Object> paramMap);


    void deleteRelationRoleTypeId(@Param("roleTypeId") Integer roleTypeId, @Param("roleIds")List<String> exclusionIds);

    void deleteRelationRoleTypeByRoleId(@Param("roleTypeIds") List<Integer> roleTypeIds, @Param("roleId") String roleId);


    int batchInsertRelation( List<SysRoleTypeRoleRelation> list);


    List<SysRoleTypeRoleRelationVo> queryRelationRoleTypeByRoleIds(Map<String,Object> paramMap);

    List<String> queryUserRoleByUserId(String userId);

    List<SysUserRoleExclusionVo> queryExclusionDetailByRoleIds(Map<String,Object> map);


}