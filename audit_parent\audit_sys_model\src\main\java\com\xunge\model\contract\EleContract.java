package com.xunge.model.contract;

import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.Date;

@Slf4j
public class EleContract implements Cloneable {

    private String prvId;

    private Integer auditingState;

    //标识哪些可以在页面更新
    private String tag;
    private Date create_time;
    private Date update_time;
    private String contractsysId;
    
    private String elecontractId;
    
    private String contractId;
    
    private String supplierId;
    
    private String paymentperiodId;
    
    private Integer priceType;
    
    private String elecontractPrice;
    
    private Integer includePriceTax;
    
    private BigDecimal flatPrice;
    
    private BigDecimal peakPrice;
    
    private BigDecimal valleyPrice;
    
    private BigDecimal topPrice;
    
    private Integer supplyMethod;
    
    private Integer buyMethod;
    
    private Integer paymentMethod;
    
    private Integer includeTax;
    
    private BigDecimal taxRate;
    
    private Integer isIncludeAll;
    
    private Integer paySign;
    
    private BigDecimal paySignAccount;
    
    private BigDecimal contractMoney;
    
    private BigDecimal contractTax;
    
    private BigDecimal contractTotalAmount;
    
    private BigDecimal contractYearAmount;
    
    private Integer independentMeter;
    
    private BigDecimal cmccRatio;
    
    private BigDecimal unicomRatio;
    
    private BigDecimal telcomRatio;
    
    private Integer includeLoss;
    
    private Integer lossType;
    
    private String paymentUser;
    
    private String paymentTelphone;
    
    private String elecontractNote;
    private String contractCode;
    private Integer updType;//根据标识更新，0：contractCode, 1:contractsysId

    public String getTag() {
        return tag;
    }

    public void setTag(String tag) {
        this.tag = tag;
    }

    public Integer getAuditingState() {
        return auditingState;
    }

    public void setAuditingState(Integer auditingState) {
        this.auditingState = auditingState;
    }

    public String getPrvId() {
        return prvId;
    }

    public void setPrvId(String prvId) {
        this.prvId = prvId;
    }

    public String getContractsysId() {
        return contractsysId;
    }

    public void setContractsysId(String contractsysId) {
        this.contractsysId = contractsysId;
    }

    
    public String getElecontractId() {
        return elecontractId;
    }

    
    public void setElecontractId(String elecontractId) {
        this.elecontractId = elecontractId == null ? null : elecontractId.trim();
    }

    
    public String getContractId() {
        return contractId;
    }

    
    public void setContractId(String contractId) {
        this.contractId = contractId == null ? null : contractId.trim();
    }

    
    public String getSupplierId() {
        return supplierId;
    }

    
    public void setSupplierId(String supplierId) {
        this.supplierId = supplierId == null ? null : supplierId.trim();
    }

    
    public String getPaymentperiodId() {
        return paymentperiodId;
    }

    
    public void setPaymentperiodId(String paymentperiodId) {
        this.paymentperiodId = paymentperiodId == null ? null : paymentperiodId.trim();
    }

    
    public Integer getPriceType() {
        return priceType;
    }

    
    public void setPriceType(Integer priceType) {
        this.priceType = priceType;
    }

    
    public String getElecontractPrice() {
        return elecontractPrice;
    }

    
    public void setElecontractPrice(String elecontractPrice) {
        this.elecontractPrice = elecontractPrice == null ? null : elecontractPrice.trim();
    }

    
    public Integer getIncludePriceTax() {
        return includePriceTax;
    }

    
    public void setIncludePriceTax(Integer includePriceTax) {
        this.includePriceTax = includePriceTax;
    }

    
    public BigDecimal getFlatPrice() {
        return flatPrice;
    }

    
    public void setFlatPrice(BigDecimal flatPrice) {
        this.flatPrice = flatPrice;
    }

    
    public BigDecimal getPeakPrice() {
        return peakPrice;
    }

    
    public void setPeakPrice(BigDecimal peakPrice) {
        this.peakPrice = peakPrice;
    }

    
    public BigDecimal getValleyPrice() {
        return valleyPrice;
    }

    
    public void setValleyPrice(BigDecimal valleyPrice) {
        this.valleyPrice = valleyPrice;
    }

    
    public BigDecimal getTopPrice() {
        return topPrice;
    }

    
    public void setTopPrice(BigDecimal topPrice) {
        this.topPrice = topPrice;
    }

    
    public Integer getSupplyMethod() {
        return supplyMethod;
    }

    
    public void setSupplyMethod(Integer supplyMethod) {
        this.supplyMethod = supplyMethod;
    }

    
    public Integer getBuyMethod() {
        return buyMethod;
    }

    
    public void setBuyMethod(Integer buyMethod) {
        this.buyMethod = buyMethod;
    }

    
    public Integer getPaymentMethod() {
        return paymentMethod;
    }

    
    public void setPaymentMethod(Integer paymentMethod) {
        this.paymentMethod = paymentMethod;
    }

    
    public Integer getIncludeTax() {
        return includeTax;
    }

    
    public void setIncludeTax(Integer includeTax) {
        this.includeTax = includeTax;
    }

    
    public BigDecimal getTaxRate() {
        return taxRate;
    }

    
    public void setTaxRate(BigDecimal taxRate) {
        this.taxRate = taxRate;
    }

    
    public Integer getIsIncludeAll() {
        return isIncludeAll;
    }

    
    public void setIsIncludeAll(Integer isIncludeAll) {
        this.isIncludeAll = isIncludeAll;
    }

    
    public Integer getPaySign() {
        return paySign;
    }

    
    public void setPaySign(Integer paySign) {
        this.paySign = paySign;
    }

    
    public BigDecimal getPaySignAccount() {
        return paySignAccount;
    }

    
    public void setPaySignAccount(BigDecimal paySignAccount) {
        this.paySignAccount = paySignAccount;
    }

    
    public BigDecimal getContractMoney() {
        return contractMoney;
    }

    
    public void setContractMoney(BigDecimal contractMoney) {
        this.contractMoney = contractMoney;
    }

    
    public BigDecimal getContractTax() {
        return contractTax;
    }

    
    public void setContractTax(BigDecimal contractTax) {
        this.contractTax = contractTax;
    }

    
    public BigDecimal getContractTotalAmount() {
        return contractTotalAmount;
    }

    
    public void setContractTotalAmount(BigDecimal contractTotalAmount) {
        this.contractTotalAmount = contractTotalAmount;
    }

    
    public BigDecimal getContractYearAmount() {
        return contractYearAmount;
    }

    
    public void setContractYearAmount(BigDecimal contractYearAmount) {
        this.contractYearAmount = contractYearAmount;
    }

    
    public Integer getIndependentMeter() {
        return independentMeter;
    }

    
    public void setIndependentMeter(Integer independentMeter) {
        this.independentMeter = independentMeter;
    }

    
    public BigDecimal getCmccRatio() {
        return cmccRatio;
    }

    
    public void setCmccRatio(BigDecimal cmccRatio) {
        this.cmccRatio = cmccRatio;
    }

    
    public BigDecimal getUnicomRatio() {
        return unicomRatio;
    }

    
    public void setUnicomRatio(BigDecimal unicomRatio) {
        this.unicomRatio = unicomRatio;
    }

    
    public BigDecimal getTelcomRatio() {
        return telcomRatio;
    }

    
    public void setTelcomRatio(BigDecimal telcomRatio) {
        this.telcomRatio = telcomRatio;
    }

    
    public Integer getIncludeLoss() {
        return includeLoss;
    }

    
    public void setIncludeLoss(Integer includeLoss) {
        this.includeLoss = includeLoss;
    }

    
    public Integer getLossType() {
        return lossType;
    }

    
    public void setLossType(Integer lossType) {
        this.lossType = lossType;
    }

    
    public String getPaymentUser() {
        return paymentUser;
    }

    
    public void setPaymentUser(String paymentUser) {
        this.paymentUser = paymentUser == null ? null : paymentUser.trim();
    }

    
    public String getPaymentTelphone() {
        return paymentTelphone;
    }

    
    public void setPaymentTelphone(String paymentTelphone) {
        this.paymentTelphone = paymentTelphone == null ? null : paymentTelphone.trim();
    }

    
    public String getElecontractNote() {
        return elecontractNote;
    }

    
    public void setElecontractNote(String elecontractNote) {
        this.elecontractNote = elecontractNote == null ? null : elecontractNote.trim();
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;
        EleContract other = (EleContract) obj;
        if (cmccRatio == null) {
            if (other.cmccRatio != null) return false;
        } else if (!cmccRatio.equals(other.cmccRatio)) return false;
        if (contractMoney == null) {
            if (other.contractMoney != null) return false;
        } else if (!contractMoney.equals(other.contractMoney)) return false;
        if (contractTax == null) {
            if (other.contractTax != null) return false;
        } else if (!contractTax.equals(other.contractTax)) return false;
        if (contractTotalAmount == null) {
            if (other.contractTotalAmount != null) return false;
        } else if (!contractTotalAmount.equals(other.contractTotalAmount)) return false;
        if (contractYearAmount == null) {
            if (other.contractYearAmount != null) return false;
        } else if (!contractYearAmount.equals(other.contractYearAmount)) return false;
        if (contractsysId == null) {
            if (other.contractsysId != null) return false;
        } else if (!contractsysId.equals(other.contractsysId)) return false;
        if (elecontractNote == null) {
            if (other.elecontractNote != null) return false;
        } else if (!elecontractNote.equals(other.elecontractNote)) return false;
        if (elecontractPrice == null) {
            if (other.elecontractPrice != null) return false;
        } else if (!elecontractPrice.equals(other.elecontractPrice)) return false;
        if (flatPrice == null) {
            if (other.flatPrice != null) return false;
        } else if (!flatPrice.equals(other.flatPrice)) return false;
        if (includePriceTax == null) {
            if (other.includePriceTax != null) return false;
        } else if (!includePriceTax.equals(other.includePriceTax)) return false;
        if (includeTax == null) {
            if (other.includeTax != null) return false;
        } else if (!includeTax.equals(other.includeTax)) return false;
        if (independentMeter == null) {
            if (other.independentMeter != null) return false;
        } else if (!independentMeter.equals(other.independentMeter)) return false;
        if (isIncludeAll == null) {
            if (other.isIncludeAll != null) return false;
        } else if (!isIncludeAll.equals(other.isIncludeAll)) return false;
        if (paymentMethod == null) {
            if (other.paymentMethod != null) return false;
        } else if (!paymentMethod.equals(other.paymentMethod)) return false;
        if (paymentperiodId == null) {
            if (other.paymentperiodId != null) return false;
        } else if (!paymentperiodId.equals(other.paymentperiodId)) return false;
        if (peakPrice == null) {
            if (other.peakPrice != null) return false;
        } else if (!peakPrice.equals(other.peakPrice)) return false;
        if (priceType == null) {
            if (other.priceType != null) return false;
        } else if (!priceType.equals(other.priceType)) return false;
        if (supplierId == null) {
            if (other.supplierId != null) return false;
        } else if (!supplierId.equals(other.supplierId)) return false;
        if (supplyMethod == null) {
            if (other.supplyMethod != null) return false;
        } else if (!supplyMethod.equals(other.supplyMethod)) return false;
        if (taxRate == null) {
            if (other.taxRate != null) return false;
        } else if (!taxRate.equals(other.taxRate)) return false;
        if (telcomRatio == null) {
            if (other.telcomRatio != null) return false;
        } else if (!telcomRatio.equals(other.telcomRatio)) return false;
        if (topPrice == null) {
            if (other.topPrice != null) return false;
        } else if (!topPrice.equals(other.topPrice)) return false;
        if (unicomRatio == null) {
            if (other.unicomRatio != null) return false;
        } else if (!unicomRatio.equals(other.unicomRatio)) return false;
        if (valleyPrice == null) {
            if (other.valleyPrice != null) return false;
        } else if (!valleyPrice.equals(other.valleyPrice)) return false;
        return true;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((cmccRatio == null) ? 0 : cmccRatio.hashCode());
        result = prime * result + ((contractMoney == null) ? 0 : contractMoney.hashCode());
        result = prime * result + ((contractTax == null) ? 0 : contractTax.hashCode());
        result = prime * result + ((contractTotalAmount == null) ? 0 : contractTotalAmount.hashCode());
        result = prime * result + ((contractYearAmount == null) ? 0 : contractYearAmount.hashCode());
        result = prime * result + ((contractsysId == null) ? 0 : contractsysId.hashCode());
        result = prime * result + ((elecontractNote == null) ? 0 : elecontractNote.hashCode());
        result = prime * result + ((elecontractPrice == null) ? 0 : elecontractPrice.hashCode());
        result = prime * result + ((flatPrice == null) ? 0 : flatPrice.hashCode());
        result = prime * result + ((includePriceTax == null) ? 0 : includePriceTax.hashCode());
        result = prime * result + ((includeTax == null) ? 0 : includeTax.hashCode());
        result = prime * result + ((independentMeter == null) ? 0 : independentMeter.hashCode());
        result = prime * result + ((isIncludeAll == null) ? 0 : isIncludeAll.hashCode());
        result = prime * result + ((paymentMethod == null) ? 0 : paymentMethod.hashCode());
        result = prime * result + ((paymentperiodId == null) ? 0 : paymentperiodId.hashCode());
        result = prime * result + ((peakPrice == null) ? 0 : peakPrice.hashCode());
        result = prime * result + ((priceType == null) ? 0 : priceType.hashCode());
        result = prime * result + ((supplierId == null) ? 0 : supplierId.hashCode());
        result = prime * result + ((supplyMethod == null) ? 0 : supplyMethod.hashCode());
        result = prime * result + ((taxRate == null) ? 0 : taxRate.hashCode());
        result = prime * result + ((telcomRatio == null) ? 0 : telcomRatio.hashCode());
        result = prime * result + ((topPrice == null) ? 0 : topPrice.hashCode());
        result = prime * result + ((unicomRatio == null) ? 0 : unicomRatio.hashCode());
        result = prime * result + ((valleyPrice == null) ? 0 : valleyPrice.hashCode());
        return result;
    }

    @Override
    public Object clone() {
        EleContract addr = null;
        try {
            addr = (EleContract) super.clone();
        } catch (CloneNotSupportedException e) {
            log.error("EleContract出错", e);
        }
        return addr;
    }

    public Date getCreate_time() {
        return create_time;
    }

    public void setCreate_time(Date create_time) {
        this.create_time = create_time;
    }

    public Date getUpdate_time() {
        return update_time;
    }

    public void setUpdate_time(Date update_time) {
        this.update_time = update_time;
    }

    public String getContractCode() {
        return contractCode;
    }

    public void setContractCode(String contractCode) {
        this.contractCode = contractCode;
    }

    public Integer getUpdType() {
        return updType;
    }

    public void setUpdType(Integer updType) {
        this.updType = updType;
    }

}