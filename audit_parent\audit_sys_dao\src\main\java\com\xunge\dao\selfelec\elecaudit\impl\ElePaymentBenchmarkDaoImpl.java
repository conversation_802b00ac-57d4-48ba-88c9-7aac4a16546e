package com.xunge.dao.selfelec.elecaudit.impl;

import com.xunge.dao.AbstractBaseDao;
import com.xunge.dao.selfelec.elecaudit.IElePaymentBenchmarkDao;
import com.xunge.model.selfelec.ElePaymentBenchmark;
import com.xunge.model.selfelec.ElecPaymentBenchmarkInfo;
import com.xunge.model.selfelec.benchmark.EleBenchmarkActualBean;

import java.util.List;
import java.util.Map;

public class ElePaymentBenchmarkDaoImpl extends AbstractBaseDao implements IElePaymentBenchmarkDao {

    final String namespace = "com.xunge.dao.selfelec.ElePaymentBenchmarkMapper.";

    @Override
    public List<ElePaymentBenchmark> queryAllByForginKey(Map<String, Object> list) {
        List<ElePaymentBenchmark> elePayBenchMarkList = this.getSqlSession().selectList(namespace + "queryAllByForginKey", list);
        return elePayBenchMarkList;
    }

    @Override
    public int insertBenchmarkInfo(List<ElePaymentBenchmark> paramMap) {
        return this.getSqlSession().insert(namespace + "insertBenchmarkInfo", paramMap);
    }

    @Override
    public int delPaymentBenchmark(String billaccountpaymentdetailId) {
        return this.getSqlSession().delete(namespace + "delPaymentBenchmark", billaccountpaymentdetailId);

    }

    @Override
    public List<ElecPaymentBenchmarkInfo> getRecorBenchmarkData() {
        List<ElecPaymentBenchmarkInfo> elecPaymentBenchmarkInfoList = this.getSqlSession().selectList(namespace + "getRecorBenchmarkData");
        return elecPaymentBenchmarkInfoList;
    }

    @Override
    public int insertBenchmarkActual(List<EleBenchmarkActualBean> list) {
        return this.getSqlSession().insert(namespace + "insertBenchmarkActual", list);
    }

    @Override
    public List<EleBenchmarkActualBean> selectBenchmarkActualByPid(String billaccountpaymentdetailId) {
        return this.getSqlSession().selectList(namespace + "selectBenchmarkActualByPid", billaccountpaymentdetailId);
    }

    @Override
    public int deleteBenchmarkActualByIds(List<Integer> list) {
        return this.getSqlSession().delete(namespace + "deleteBenchmarkActualByIds", list);
    }
}
