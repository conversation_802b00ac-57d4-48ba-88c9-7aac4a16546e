package com.xunge.dao.roletype;

import com.xunge.model.roletype.SysRoleType;
import com.xunge.model.roletype.SysRoleTypeVo;

import java.util.List;
import java.util.Map;

public interface SysRoleTypeMapper {

    int deleteByPrimaryKey(Integer roletypeId);


    int insert(SysRoleType record);


    int insertSelective(SysRoleType record);


    SysRoleType selectByPrimaryKey(Integer roletypeId);


    int updateByPrimaryKeySelective(SysRoleType record);


    int updateByPrimaryKey(SysRoleType record);


    List<SysRoleTypeVo> querySysRoleType(Map<String,Object> paramMap );

    SysRoleTypeVo querySysRoleTypeDetail(Integer roletypeId);
}