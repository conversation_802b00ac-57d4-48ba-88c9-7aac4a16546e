package com.xunge.model.external.utils;

import com.xunge.core.util.DateUtils;
import com.xunge.core.util.MD5Util;
import com.xunge.model.external.entity.BaseSignRo;
import com.xunge.model.system.oauth.SysOauthClientVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;

/**
 * <p>
 * 签名验证工具类
 * </p>
 *
 * <AUTHOR>
 */
@Slf4j
public class SignUtils {

    public static String prodSign(BaseSignRo signRo, String clientKey) {
        // String accessDateStr = DateUtils.format(signRo.getAccessDate(), DateUtils.FULL_ST_FORMAT);
        try {
            return MD5Util.encode(String.format("%s%s%s%s", clientKey,
                    signRo.getClientId(), signRo.getPrvCode(), signRo.getAccessDate()));
        } catch (Exception e) {
            log.error("UnsupportedEncodingException", e.getMessage());
            return null;
        }
    }

    public static boolean checkSign(BaseSignRo signRo, SysOauthClientVO sysClientVO) {
        // 验证账号是否存在
        boolean checkAccessKey = checkAccessKey(sysClientVO);
        if (!checkAccessKey) return false;
        // 验证账号是否有效
        boolean checkStatus = checkStatus(sysClientVO);
        if (!checkStatus) return false;
        // 验证是否有接口访问权限
//        checkMethod(sysClientVO);
        // 验证访问时间是否有效
        boolean checkAccessDate = checkAccessDate(signRo);
        if (!checkAccessDate) return false;
        // 验证签名是否有效
        boolean checkSign = checkSign(signRo, sysClientVO.getClientSecret());
        if (!checkSign) return false;
        return true;
    }

    private static boolean checkSign(BaseSignRo signRo, String accessKeySecret) {
        String sign = prodSign(signRo, accessKeySecret);
        if (!StringUtils.equals(sign, signRo.getSign())) {
            log.error("签名不正确", sign);
            return false;
        }
        return true;
    }

    private static boolean checkAccessKey(SysOauthClientVO sysClientVO) {
        if (null == sysClientVO) {
            log.error("用户密钥不存在");
            return false;
        }
        return true;
    }

    private static boolean checkStatus(SysOauthClientVO sysClientVO) {
        if (sysClientVO.getStatus() != 1) {
            log.error("用户密钥停用");
            return false;
        }
        return true;
    }

    @SuppressWarnings("deprecation")
    /*private static void checkMethod(SysApiCo apiCo) {
        String methodStr = apiCo.getMethod();
        if (StringUtils.isNotBlank(methodStr)) {
            HttpServletRequest request = HttpRequestUtils.getHttpServletRequest();
            String reqtMethod = StringUtils.replaceAll(StringUtils.substring(request.getRequestURI(), 1), "/", ".");

            methodStr = StringUtils.replaceAll(methodStr, "，", ",");
            String[] methods = StringUtils.split(methodStr, ",");
            boolean authz = false;
            for (String method : methods) {
                if (StringUtils.equals(StringUtils.trim(method), reqtMethod)) {
                    authz = true;
                    break;
                }
            }
            if (!authz) {
                BssExpUtils.error("没有访问该方法权限", log);
            }
        }
    }*/

    private static boolean checkAccessDate(BaseSignRo signRo) {
        Date accessDate = DateUtils.parseDate(signRo.getAccessDate());
        Date ftDateBeg = DateUtils.subtractTime(accessDate, -1000 * 60 * 10); //减去60s
        Date ftDateEnd = DateUtils.subtractTime(accessDate, 1000 * 60 * 10);  //增加60s
        if (ftDateBeg.after(new Date())) {
            log.error("请求时间过于延后");
            return false;
        }
        if (ftDateEnd.before(new Date())) {
            log.error("请求时间过于提前");
            return false;
        }
        return true;
    }
}