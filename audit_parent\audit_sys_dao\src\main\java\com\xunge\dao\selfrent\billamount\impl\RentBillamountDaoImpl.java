package com.xunge.dao.selfrent.billamount.impl;

import com.xunge.core.page.Page;
import com.xunge.dao.AbstractBaseDao;
import com.xunge.dao.selfrent.billamount.RentBillamountDao;
import com.xunge.filter.PageInterceptor;
import com.xunge.model.selfrent.billamount.RentBillamountPaymentVO;
import com.xunge.model.selfrent.billamount.RentBillamountVO;
import com.xunge.model.selfrent.billamount.RentBillamountVerificationDetail;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2017年6月27日 上午10:14:11
 */
public class RentBillamountDaoImpl extends AbstractBaseDao implements RentBillamountDao<RentBillamountVO> {

    final String Namespace = "com.xunge.dao.RentBillamountVOMapper.";

    public Page<List<RentBillamountVO>> queryRentBillamountPage(Map<String, Object> map) {
        // TODO Auto-generated method stub
        PageInterceptor.startPage(Integer.parseInt(map.get("pageNumber").toString()), Integer.parseInt(map.get("pageSize").toString()));
        this.getSqlSession().selectList(Namespace + "queryRentBillamountPage", map);
        return PageInterceptor.endPage();
    }

    public Page<List<RentBillamountVO>> queryRentBillamountPage1(Map<String, Object> map) {
        // TODO Auto-generated method stub
        PageInterceptor.startPage(Integer.parseInt(map.get("pageNumber").toString()), Integer.parseInt(map.get("pageSize").toString()));
        this.getSqlSession().selectList(Namespace + "queryRentBillamountPage1", map);
        return PageInterceptor.endPage();
    }

    public List<RentBillamountPaymentVO> queryRentBillamountPage2(Map<String, Object> map) {
        return this.getSqlSession().selectList(Namespace + "queryRentBillamountPage2", map);
    }


    /**
     * 插入租费报账汇总
     *
     * @param rentBillamountVO
     * @return
     */
    public int insertRentBillamountVO(RentBillamountVO rentBillamountVO) {

        return this.getSqlSession().insert(Namespace + "insert", rentBillamountVO);
    }

    /**
     * 更新汇总单状态
     *
     * @param rentBillamountVO
     * @return
     */
    public int updateRentBillamountState(RentBillamountVO rentBillamountVO) {

        return this.getSqlSession().update(Namespace + "updateStateByPrimaryKey", rentBillamountVO);
    }

    public int updateByPrimaryKeySelective(RentBillamountVO rentBillamountVO) {

        return this.getSqlSession().update(Namespace + "updateByPrimaryKeySelective", rentBillamountVO);
    }

    public RentBillamountVO queryRentBillamountById(Map<String, Object> map) {

        return this.getSqlSession().selectOne(Namespace + "queryRentBillamountById", map);
    }

    public RentBillamountVO selectRentBillamountById(Map<String, Object> map) {

        return this.getSqlSession().selectOne(Namespace + "selectRentBillamountById", map);
    }

    public List<RentBillamountVO> queryRentBillamountByIds(Map<String, Object> map) {

        return this.getSqlSession().selectList(Namespace + "queryRentBillamountByIds", map);
    }

    @Override
    public int deleteBybillamountId(String billamountId) {
        // TODO Auto-generated method stub
        return this.getSqlSession().delete(Namespace + "deleteBybillamountId", billamountId);
    }

    /**
     * 更新租费报账汇总信息状态 -3 已删除
     */
    @Override
    public int updateByPrimaryKey(RentBillamountVO rentBillamount) {

        return this.getSqlSession().update(Namespace + "updateByPrimaryKey", rentBillamount);
    }

    /**
     * 根据汇总单code查询billamount
     *
     * @param billamountCode
     * @return
     */
    public RentBillamountVO selectBillamountByCode(String billamountCode) {
        return this.getSqlSession().selectOne(Namespace + "selectBillamountByCode", billamountCode);
    }

    /**
     * 定时器查询payment补充信息对应缴费单编码
     *
     * @return
     */
    public List<String> queryInquiryClaimPmtBillamount(Map<String, Object> map) {
        return this.getSqlSession().selectList(Namespace + "queryInquiryClaimPmtBillamount", map);
    }


    @Override
    public int updateSupplierInfo(Map<String, Object> paraMap) {
        return this.getSqlSession().update(Namespace + "updateSupplierInfo", paraMap);

    }


    @Override
    public int updateComtractInfo(Map<String, Object> paraMap) {
        return this.getSqlSession().update(Namespace + "updateComtractInfo", paraMap);
    }

    @Override
    public int updateBillamountAdjustById(RentBillamountVO rentBillamountVO) {
        return this.getSqlSession().update(Namespace + "updateBillamountAdjustById", rentBillamountVO);
    }

    @Override
    public List<RentBillamountVerificationDetail> exportVerificationDetails(Map paramMap) {
        return this.getSqlSession().selectList(Namespace + "exportVerificationDetails", paramMap);
    }

    @Override
    public RentBillamountVO queryRentBillamountByBillamountId(Map<String, Object> map) {
        return this.getSqlSession().selectOne(Namespace + "queryRentBillamountByBillamountId", map);
    }

    @Override
    public void editPayment(RentBillamountVO rentBillamountVO) {
        this.getSqlSession().update(Namespace + "editPayment", rentBillamountVO);
    }


}
