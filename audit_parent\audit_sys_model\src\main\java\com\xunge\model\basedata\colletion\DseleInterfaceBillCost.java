package com.xunge.model.basedata.colletion;

import cn.afterturn.easypoi.excel.annotation.Excel;

import java.math.BigInteger;

/**
 * @创建时间 2019/4/26
 * @描述： 直供电接口采集账单运行报表Vo
 */
public class DseleInterfaceBillCost {

    /**
     * 主键ID
     */
    private BigInteger instanceId;

    /**
     * 单据状态（已建立报账单草稿，入库失败）
     */
    @Excel( name = "单据状态" )
    private String instanceState;

    /**
     * 接口错误提示
     */
    @Excel( name = "接口错误提示" )
    private String verifyError;

    /**
     * 校验入库时间
     */
    @Excel( name = "校验入库时间" )
    private String insertTime;

    /**
     * 省份
     */
    @Excel( name = "省份" )
    private String prvName;

    /**
     * 地市
     */
    @Excel( name = "地市" )
    private String pregName;

    /**
     * 区县
     */
    @Excel( name = "区县" )
    private String regName;

    /**
     * 托收号
     */
    @Excel( name = "托收号" )
    private String collectNumber;

    /**
     * 户号
     */
    @Excel( name = "户号" )
    private String accountNumber;

    /**
     * 供电单位
     */
    @Excel( name = "供电单位" )
    private String eleCompany;

    /**
     * 地址
     */
    @Excel( name = "地址" )
    private String address;

    /**
     * 表资产号
     */
    @Excel( name = "表资产号" )
    private String meterCode;

    /**
     * 本次抄表日期
     */
    @Excel( name = "本次抄表日期" )
    private String billamountEnddate;

    /**
     * 上次抄表日期
     */
    @Excel( name = "上次抄表日期" )
    private String billamountStartdate;

    /**
     * 分时类型
     */
    @Excel( name = "分时类型" )
    private String meterType;

    /**
     * 示数类型
     */
    @Excel( name = "示数类型" )
    private String numberType;
   
    /**
     * 上期指数
     */
    @Excel( name = "上期指数" )
    private String lastReadnum;

    /**
     * 本期指数
     */
    @Excel( name = "本期指数" )
    private String nowReadnum;

    /**
     * 单价
     */
    @Excel( name = "单价(元)" )
    private String degreePrice;

    /**
     * 线损
     */
    @Excel( name = "线损" )
    private String lineLoss;

    /**
     * 变损
     */
    @Excel( name = "变损" )
    private String tranformerLoos;

    /**
     * 抄见电量
     */
    @Excel( name = "抄见电量" )
    private String consumeDegree;

    /**
     * 扣减电量
     */
    @Excel( name = "扣减电量" )
    private String reduceDegree;

    /**
     * 倍率
     */
    @Excel( name = "倍率" )
    private String meterRate;

    /**
     * 计费电量
     */
    @Excel( name = "计费电量" )
    private String chargeDegree;

    /**
     * 退补电量
     */
    @Excel( name = "退补电量" )
    private String backDegree;

    /**
     * 基本电费
     */
    @Excel( name = "基本电费(元)" )
    private String baseFee;

    /**
     * 力调电费
     */
    @Excel( name = "力调电费" )
    private String adustFee;

    /**
     * 电度电费
     */
    @Excel( name = "电度电费" )
    private String eleFee;

    /**
     * 目录电费
     */
    @Excel( name = "目录电费" )
    private String dirFee;

    /**
     * 代征电费
     */
    @Excel( name = "代征电费" )
    private String agentFee;

    /**
     * 总电费
     */
    @Excel( name = "总电费（元）" )
    private String totalFee;

    /**
     * 电表编号
     */
    @Excel( name = "电表编号" )
    private String meterNumber;

	public BigInteger getInstanceId() {
		return instanceId;
	}

	public void setInstanceId(BigInteger instanceId) {
		this.instanceId = instanceId;
	}

	public String getInstanceState() {
		return instanceState;
	}

	public void setInstanceState(String instanceState) {
		this.instanceState = instanceState;
	}

	public String getVerifyError() {
		return verifyError;
	}

	public void setVerifyError(String verifyError) {
		this.verifyError = verifyError;
	}

	public String getInsertTime() {
		return insertTime;
	}

	public void setInsertTime(String insertTime) {
		this.insertTime = insertTime;
	}

	public String getPrvName() {
		return prvName;
	}

	public void setPrvName(String prvName) {
		this.prvName = prvName;
	}

	public String getPregName() {
		return pregName;
	}

	public void setPregName(String pregName) {
		this.pregName = pregName;
	}

	public String getCollectNumber() {
		return collectNumber;
	}

	public void setCollectNumber(String collectNumber) {
		this.collectNumber = collectNumber;
	}

	public String getAccountNumber() {
		return accountNumber;
	}

	public void setAccountNumber(String accountNumber) {
		this.accountNumber = accountNumber;
	}

	public String getEleCompany() {
		return eleCompany;
	}

	public void setEleCompany(String eleCompany) {
		this.eleCompany = eleCompany;
	}

	public String getAddress() {
		return address;
	}

	public void setAddress(String address) {
		this.address = address;
	}

	public String getMeterCode() {
		return meterCode;
	}

	public void setMeterCode(String meterCode) {
		this.meterCode = meterCode;
	}

	public String getBillamountEnddate() {
		return billamountEnddate;
	}

	public void setBillamountEnddate(String billamountEnddate) {
		this.billamountEnddate = billamountEnddate;
	}

	public String getBillamountStartdate() {
		return billamountStartdate;
	}

	public void setBillamountStartdate(String billamountStartdate) {
		this.billamountStartdate = billamountStartdate;
	}

	public String getMeterType() {
		return meterType;
	}

	public void setMeterType(String meterType) {
		this.meterType = meterType;
	}

	public String getNumberType() {
		return numberType;
	}

	public void setNumberType(String numberType) {
		this.numberType = numberType;
	}

	public String getDegreePrice() {
		return degreePrice;
	}

	public void setDegreePrice(String degreePrice) {
		this.degreePrice = degreePrice;
	}

	public String getLineLoss() {
		return lineLoss;
	}

	public void setLineLoss(String lineLoss) {
		this.lineLoss = lineLoss;
	}

	public String getTranformerLoos() {
		return tranformerLoos;
	}

	public void setTranformerLoos(String tranformerLoos) {
		this.tranformerLoos = tranformerLoos;
	}

	public String getConsumeDegree() {
		return consumeDegree;
	}

	public void setConsumeDegree(String consumeDegree) {
		this.consumeDegree = consumeDegree;
	}

	public String getReduceDegree() {
		return reduceDegree;
	}

	public void setReduceDegree(String reduceDegree) {
		this.reduceDegree = reduceDegree;
	}

	public String getLastReadnum() {
		return lastReadnum;
	}

	public void setLastReadnum(String lastReadnum) {
		this.lastReadnum = lastReadnum;
	}

	public String getNowReadnum() {
		return nowReadnum;
	}

	public void setNowReadnum(String nowReadnum) {
		this.nowReadnum = nowReadnum;
	}

	public String getMeterRate() {
		return meterRate;
	}

	public void setMeterRate(String meterRate) {
		this.meterRate = meterRate;
	}

	public String getChargeDegree() {
		return chargeDegree;
	}

	public void setChargeDegree(String chargeDegree) {
		this.chargeDegree = chargeDegree;
	}

	public String getBackDegree() {
		return backDegree;
	}

	public void setBackDegree(String backDegree) {
		this.backDegree = backDegree;
	}

	public String getBaseFee() {
		return baseFee;
	}

	public void setBaseFee(String baseFee) {
		this.baseFee = baseFee;
	}

	public String getAdustFee() {
		return adustFee;
	}

	public void setAdustFee(String adustFee) {
		this.adustFee = adustFee;
	}

	public String getEleFee() {
		return eleFee;
	}

	public void setEleFee(String eleFee) {
		this.eleFee = eleFee;
	}

	public String getDirFee() {
		return dirFee;
	}

	public void setDirFee(String dirFee) {
		this.dirFee = dirFee;
	}

	public String getAgentFee() {
		return agentFee;
	}

	public void setAgentFee(String agentFee) {
		this.agentFee = agentFee;
	}

	public String getTotalFee() {
		return totalFee;
	}

	public void setTotalFee(String totalFee) {
		this.totalFee = totalFee;
	}

	public String getRegName() {
		return regName;
	}

	public void setRegName(String regName) {
		this.regName = regName;
	}

	public String getMeterNumber() {
		return meterNumber;
	}

	public void setMeterNumber(String meterNumber) {
		this.meterNumber = meterNumber;
	}
	
}
