package com.xunge.dao.maintenance.impl;

import com.xunge.dao.AbstractBaseDao;
import com.xunge.dao.maintenance.IMaintenanceDao;
import com.xunge.model.selfrent.billamount.BillamountLogVO;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * @description:
 * @author: dxd
 * @create: 2019-12-04 14:56
 **/
@Service("maintenanceDao")
public class MaintenanceDaoImpl extends AbstractBaseDao implements IMaintenanceDao {

    final String Namespace = "com.xunge.mapping.MaintenanceMapper.";

    @Override
    public Map<String, Object> queryRentNum(String auditOrderCode) {
        return this.getSqlSession().selectOne(Namespace + "queryRentNum", auditOrderCode);
    }

    @Override
    public Map<String, Object> queryTowerNum(String auditOrderCode) {
        return this.getSqlSession().selectOne(Namespace + "queryTowerNum", auditOrderCode);
    }

    @Override
    public Map<String, Object> queryETowerNum(String auditOrderCode) {
        return this.getSqlSession().selectOne(Namespace + "queryETowerNum", auditOrderCode);
    }

    @Override
    public Map<String, Object> queryEleAfterNum(String auditOrderCode) {
        return this.getSqlSession().selectOne(Namespace + "queryEleAfterNum", auditOrderCode);
    }

    @Override
    public Map<String, Object> queryElePrepayNum(String auditOrderCode) {
        return this.getSqlSession().selectOne(Namespace + "queryElePrepayNum", auditOrderCode);
    }

    @Override
    public Map<String, Object> queryEleSheetNum(String auditOrderCode) {
        return this.getSqlSession().selectOne(Namespace + "queryEleSheetNum", auditOrderCode);
    }

    @Override
    public Map<String, Object> queryElePowerNum(String auditOrderCode) {
        return this.getSqlSession().selectOne(Namespace + "queryElePowerNum", auditOrderCode);
    }

    @Override
    public Map<String, Object> queryEleTowerNum(String auditOrderCode) {
        return this.getSqlSession().selectOne(Namespace + "queryEleTowerNum", auditOrderCode);
    }

    @Override
    public int insertSysLog(BillamountLogVO billamountLog) {
        return this.getSqlSession().update(Namespace + "insertSysLog", billamountLog);
    }

    @Override
    public int updateMaintenanceCheckState(Map<String, Object> updateMap) {
        return this.getSqlSession().update(Namespace + "updateMaintenanceCheckState", updateMap);
    }

    @Override
    public int updateDetailByIds(Map<String, Object> updateMap) {
        return this.getSqlSession().update(Namespace + "updateDetailByIds", updateMap);
    }

    @Override
    public int updatePaymentByIds(Map<String, Object> updateMap) {
        return this.getSqlSession().update(Namespace + "updatePaymentByIds", updateMap);
    }

    @Override
    public Map<String, Object> queryCodeByBillNum(Map<String, Object> updateMap) {
        return this.getSqlSession().selectOne(Namespace + "queryCodeByBillNum", updateMap);
    }

    @Override
    public int updateBillState(Map<String, Object> updateMap) {
        return this.getSqlSession().update(Namespace + "updateBillState", updateMap);
    }

    @Override
    public String getBillClaimNumByBillAmountId(Map<String, Object> map) {
        return this.getSqlSession().selectOne(Namespace + "getBillClaimNumByBillAmountId", map);
    }

    @Override
    public Map<String, Object> queryOilTowerNum(String auditOrderCode) {
        return this.getSqlSession().selectOne(Namespace + "queryOilTowerNum", auditOrderCode);
    }

    @Override
    public List<BigDecimal> queryEleAfterDetail(String auditOrderCode) {
        return this.getSqlSession().selectList(Namespace + "queryEleAfterDetail", auditOrderCode);
    }

    @Override
    public List<BigDecimal> queryEleTowerDetail(String auditOrderCode) {
        return this.getSqlSession().selectList(Namespace + "queryEleTowerDetail", auditOrderCode);
    }

    @Override
    public List<BigDecimal> queryRentNumDetail(String auditOrderCode) {
        return this.getSqlSession().selectList(Namespace + "queryRentNumDetail", auditOrderCode);
    }

    @Override
    public List<BigDecimal> queryTowerNumDetail(String auditOrderCode) {
        return this.getSqlSession().selectList(Namespace + "queryTowerNumDetail", auditOrderCode);
    }

    @Override
    public List<BigDecimal> queryOilTowerDetail(String auditOrderCode) {
        return this.getSqlSession().selectList(Namespace + "queryOilTowerDetail", auditOrderCode);
    }

    @Override
    public List<BigDecimal> queryElePrepayDetail(String auditOrderCode) {
        return this.getSqlSession().selectList(Namespace + "queryElePrepayDetail", auditOrderCode);
    }

    @Override
    public List<BigDecimal> queryEleSheetDetail(String auditOrderCode) {
        return this.getSqlSession().selectList(Namespace + "queryEleSheetDetail", auditOrderCode);
    }

    @Override
    public List<BigDecimal> queryElePowerDetail(String auditOrderCode) {
        return this.getSqlSession().selectList(Namespace + "queryElePowerDetail", auditOrderCode);
    }

    @Override
    public Map<String, Object> queryEleAccTowerNum(String auditOrderCode) {
        return this.getSqlSession().selectOne(Namespace + "queryEleAccTowerNum", auditOrderCode);
    }

    @Override
    public List<BigDecimal> queryEleAccTowerDetail(String auditOrderCode) {
        return this.getSqlSession().selectList(Namespace + "queryEleAccTowerDetail", auditOrderCode);
    }

    @Override
    public Map<String, Object> queryEleAccCodeByBillNum(Map<String, Object> updateMap) {
        return this.getSqlSession().selectOne(Namespace + "queryEleAccCodeByBillNum", updateMap);
    }

    @Override
    public int updateBillAmount(Map<String, Object> updateMap) {
        return this.getSqlSession().update(Namespace + "updateBillAmount", updateMap);
    }

    @Override
    public int updateJTTableByIds(Map<String, Object> updateMap) {
        return this.getSqlSession().update(Namespace + "updateJTTableByIds", updateMap);
    }

    @Override
    public int updateJTSnapTableByIds(Map<String, Object> updateMap) {
        return this.getSqlSession().update(Namespace + "updateJTSnapTableByIds", updateMap);
    }

    @Override
    public String getJTBillClaimNumByBillAmountId(Map<String, Object> map) {
        return this.getSqlSession().selectOne(Namespace + "getJTBillClaimNumByBillAmountId", map);
    }

    @Override
    public Map<String, Object> queryRentAccrualNum(String auditOrderCode) {
        return this.getSqlSession().selectOne(Namespace + "queryRentAccrualNum", auditOrderCode);
    }

    @Override
    public List<BigDecimal> queryRentAccrualDetail(String auditOrderCode) {
        return this.getSqlSession().selectList(Namespace + "queryRentAccrualDetail", auditOrderCode);
    }

    @Override
    public Map<String, Object> queryTowerAccrualNum(String auditOrderCode) {
        return this.getSqlSession().selectOne(Namespace + "queryTowerAccrualNum", auditOrderCode);
    }
    @Override
    public Map<String, Object> queryTowerAccrualManualNum(String auditOrderCode){
        return this.getSqlSession().selectOne(Namespace + "queryTowerAccrualManualNum", auditOrderCode);
    }
    @Override
    public Map<String, Object> queryEleOffsNum(String auditOrderCode){
        return this.getSqlSession().selectOne(Namespace + "queryEleOffsNum", auditOrderCode);
    }
    @Override
    public Map<String, Object> queryRentOffsNum(String auditOrderCode){
        return this.getSqlSession().selectOne(Namespace + "queryRentOffsNum", auditOrderCode);
    }
    @Override
    public Map<String, Object> queryTowerOffsNum(String auditOrderCode){
        return this.getSqlSession().selectOne(Namespace + "queryTowerOffsNum", auditOrderCode);
    }

    @Override
    public Map<String, Object> queryCodeByBillNumForRentAccrual(String billNum) {
        return this.getSqlSession().selectOne(Namespace + "queryCodeByBillNumForRentAccrual", billNum);
    }

    @Override
    public Map<String, Object> queryCodeByBillNumForTowerAccrual(String billNum) {
        return this.getSqlSession().selectOne(Namespace + "queryCodeByBillNumForTowerAccrual", billNum);
    }

    @Override
    public Map<String, Object> queryCodeByBillNumForTowerAccrualManual(String billNum) {
        return this.getSqlSession().selectOne(Namespace + "queryCodeByBillNumForTowerAccrualManual", billNum);
    }
    @Override
    public Map<String, Object> queryCodeByBillNumForEleOffs(String billNum){
        return this.getSqlSession().selectOne(Namespace + "queryCodeByBillNumForEleOffs", billNum);
    }
    @Override
    public Map<String, Object> queryCodeByBillNumForRentOffs(String billNum){
        return this.getSqlSession().selectOne(Namespace + "queryCodeByBillNumForRentOffs", billNum);
    }
    @Override
    public Map<String, Object> queryCodeByBillNumForTowerOffs(String billNum){
        return this.getSqlSession().selectOne(Namespace + "queryCodeByBillNumForTowerOffs", billNum);
    }

    @Override
    public int updateErpReturnCodeForRentAccrual(Map<String, Object> updateMap) {
        return this.getSqlSession().update(Namespace + "updateErpReturnCodeForRentAccrual", updateMap);
    }
    @Override
    public int updateErpReturnCodeForEleOffs(Map<String, Object> updateMap){
        return this.getSqlSession().update(Namespace + "updateErpReturnCodeForEleOffs", updateMap);
    }
    @Override
    public int updateErpReturnCodeForRentOffs(Map<String, Object> updateMap){
        return this.getSqlSession().update(Namespace + "updateErpReturnCodeForRentOffs", updateMap);
    }
    @Override
    public int updateErpReturnCodeForTowerOffs(Map<String, Object> updateMap){
        return this.getSqlSession().update(Namespace + "updateErpReturnCodeForTowerOffs", updateMap);
    }

    @Override
    public int updatePushStateForRentAccrual(Map<String, Object> updateMap) {
        return this.getSqlSession().update(Namespace + "updatePushStateForRentAccrual", updateMap);
    }

    @Override
    public int updateErpReturnCodeForTowerAccrual(Map<String, Object> updateMap) {
        return this.getSqlSession().update(Namespace + "updateErpReturnCodeForTowerAccrual", updateMap);
    }

    @Override
    public int updateErpReturnCodeForTowerAccrualManual(Map<String, Object> updateMap) {
        return this.getSqlSession().update(Namespace + "updateErpReturnCodeForTowerAccrualManual", updateMap);
    }

    @Override
    public int updatePushStateForTowerAccrual(Map<String, Object> updateMap) {
        return this.getSqlSession().update(Namespace + "updatePushStateForTowerAccrual", updateMap);
    }

    @Override
    public int updatePushStateForTowerAccrualManual(Map<String, Object> updateMap) {
        return this.getSqlSession().update(Namespace + "updatePushStateForTowerAccrualManual", updateMap);
    }

    @Override
    public int updateErpReturnCodeForETowerAccountsummary(Map<String, Object> updateMap) {
        return this.getSqlSession().update(Namespace + "updateErpReturnCodeForETowerAccountsummary", updateMap);
    }
    @Override
    public int updatePushStateForETowerAccountsummary(Map<String, Object> updateMap) {
        return this.getSqlSession().update(Namespace + "updatePushStateForETowerAccountsummary", updateMap);
    }

    @Override
    public int updatePushStateForEleOffs(Map<String, Object> updateMap) {
        return this.getSqlSession().update(Namespace + "updatePushStateForEleOffs", updateMap);
    }

    @Override
    public int updatePushStateFoRentOffs(Map<String, Object> updateMap) {
        return this.getSqlSession().update(Namespace + "updatePushStateFoRentOffs", updateMap);
    }

    @Override
    public int updatePushStateForTowerOffs(Map<String, Object> updateMap) {
        return this.getSqlSession().update(Namespace + "updatePushStateForTowerOffs", updateMap);
    }
}
