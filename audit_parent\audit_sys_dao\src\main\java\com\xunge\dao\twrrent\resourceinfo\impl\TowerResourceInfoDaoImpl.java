package com.xunge.dao.twrrent.resourceinfo.impl;

import com.xunge.core.page.Page;
import com.xunge.dao.AbstractBaseDao;
import com.xunge.dao.twrrent.resourceinfo.ITowerResourceInfoDao;
import com.xunge.filter.PageInterceptor;
import com.xunge.model.towerrent.mobile.TowerRruDisCompare;
import com.xunge.model.towerrent.mobile.TowerRruDisConfig;
import com.xunge.model.towerrent.rentmanager.StartRentCompareDetailVo;
import com.xunge.model.towerrent.rentmanager.StartRentCompareStatisticsVo;
import com.xunge.model.towerrent.rentmanager.TowerResourceInfoVO;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

//import sun.rmi.rmic.Names;

/**
 * 铁塔资源信息dao实现类
 *
 * <AUTHOR>
 */
public class TowerResourceInfoDaoImpl extends AbstractBaseDao implements ITowerResourceInfoDao {

    final String Namespace = "com.xunge.dao.towerrent.rentmanager.TowerResourceInfoMapper.";
    final String Namespace1 = "com.xunge.dao.TwrRentInformationVOMapper.";
    final String NamespaceHistory = "com.xunge.dao.TwrRentinformationBackupMapper.";

    @Override
    public String selectInformation(Map<String, Object> Map) {
        return this.getSqlSession().selectOne(Namespace1 + "selectInformation", Map);
    }

    @Override
    public int insetMoble(Map<String, Object> rentinformationtowerMap) {
        return this.getSqlSession().insert(Namespace + "insetMoble", rentinformationtowerMap);
    }

    @Override
    public int updateRentinfirmation(Map<String, Object> map) {
        return this.getSqlSession().update(Namespace1 + "updateRentinfirmation", map);
    }

    @Override
    public int updateTowerCheckState(Map<String, Object> paraMap) {
        return this.getSqlSession().update(Namespace + "updateTowerCheckState", paraMap);
    }


    @Override
    public int updateTowerConfirmState(Map<String, Object> paraMap) {
        return this.getSqlSession().update(Namespace + "updateTowerConfirmState", paraMap);
    }


    @Override
    public int deleteinformation(Map<String, Object> paraMap) {
        return this.getSqlSession().update(Namespace1 + "deleteinformation", paraMap);
    }

    @Override
    public TowerResourceInfoVO queryByRentinformationtowerId(String id) {
        return this.getSqlSession().selectOne(Namespace + "queryTowerResourceInfoVOById", id);
    }

    /**
     * 分页查询铁塔资源信息
     *
     * <AUTHOR>
     */
    @Override
    public Page<TowerResourceInfoVO> queryTowerResourceInfo(Map<String, Object> paraMap, int pageNumber, int pageSize) {
        PageInterceptor.startPage(pageNumber, pageSize);
        this.getSqlSession().selectList(Namespace + "queryTowerResourceInfo", paraMap);
        return PageInterceptor.endPage();
    }

    /**
     * 根据id查询铁塔资源信息
     */
    @Override
    public TowerResourceInfoVO queryTowerResourceInfoVOById(Map<String, Object> paraMap) {
        return this.getSqlSession().selectOne(Namespace + "queryTowerResourceInfoVOById", paraMap);
    }

    @Override
    public TowerResourceInfoVO queryByRentinformationId(String id) {
        return this.getSqlSession().selectOne(Namespace + "selectByPrimaryKey", id);
    }

    /**
     * 审核完成后修改状态
     */
    @Override
    public int updateCommit(Map<String, Object> paraMap) {
        return this.getSqlSession().update(Namespace + "updateCommit", paraMap);
    }

    @Override
    public int updateCommitMobile(Map<String, Object> paraMap) {
        return this.getSqlSession().update(Namespace + "updateCommitMobile", paraMap);
    }

    /**
     * 查询铁塔资源信息
     *
     * <AUTHOR>
     */
    @Override
    public List<TowerResourceInfoVO> queryTowerResourceInfo(Map<String, Object> paraMap) {
        return this.getSqlSession().selectList(Namespace + "queryTowerResourceInfo", paraMap);
    }

    @Override
    public List<TowerResourceInfoVO> queryTowerResourceAllInfoByPrvId(String prvId) {
        return this.getSqlSession().selectList(Namespace + "queryTowerResourceAllInfoByPrvId", prvId);
    }

    @Override
    public List<String> queryTowerResourceInfoByPrvId(String prvId) {
        return this.getSqlSession().selectList(Namespace + "getTowerRentInformationsByPrvId", prvId);
    }

    @Override
    public List<String> queryMobileResourceInfoByPrvId(String prvId) {
        return this.getSqlSession().selectList(Namespace + "getMobileRentInformationsByPrvId", prvId);
    }

    /**
     * 新增铁塔资源信息
     *
     * <AUTHOR>
     */
    @Override
    public int insertTowerResourceInfo(TowerResourceInfoVO towerResourceInfoVO) {
        return this.getSqlSession().insert(Namespace + "insertTowerResourceInfo", towerResourceInfoVO);
    }

    /**
     * 批量新增铁塔资源信息
     *
     * <AUTHOR>
     */
    @Override
    public int insertTowerResourceInfoList(List<TowerResourceInfoVO> towerResourceInfoList) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("towerResourceInfoList", towerResourceInfoList);
        return this.getSqlSession().insert(Namespace + "insertTowerResourceInfoList", map);
    }

    /**
     * 修改铁塔资源信息
     *
     * <AUTHOR>
     */
    @Override
    public int updateTowerResourceInfo(TowerResourceInfoVO towerResourceInfoVO) {
        return this.getSqlSession().update(Namespace + "updateTowerResourceInfo", towerResourceInfoVO);
    }

    @Override
    public int comfirmStateUpdate(Map<String, Object> param) {
        return this.getSqlSession().update(Namespace + "comfirmStateUpdate", param);
    }

    @Override
    public int comfirmStateUpdateMobile(Map<String, Object> param) {
        return this.getSqlSession().update(Namespace + "comfirmStateUpdateMobile", param);
    }


    @Override
    public int checkStatusInitializeByPrimary(List<String> rentInformationTowerIds) {
        return this.getSqlSession().update(Namespace + "checkStatusInitializeByPrimary", rentInformationTowerIds);
    }

    /**
     * 批量修改铁塔资源信息
     *
     * <AUTHOR>
     */
    @Override
    public int updateTowerResourceInfoList(List<TowerResourceInfoVO> towerResourceInfoList) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("towerResourceInfoList", towerResourceInfoList);
        return this.getSqlSession().update(Namespace + "updateTowerResourceInfoBatch", map);
    }

    @Override
    public int updateMobileResourceInfoBatch(List<TowerResourceInfoVO> updateTowerResourceInfoList) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("towerResourceInfoList", updateTowerResourceInfoList);
        return this.getSqlSession().update(Namespace + "updateMobileResourceInfoBatch", map);
    }

    @Override
    public List<TowerResourceInfoVO> queryStandardBuild(Map<String, Object> param) {
        return this.getSqlSession().selectList(Namespace + "queryStandardBuild", param);
    }

    @Override
    public List<TowerResourceInfoVO> queryHisStandardBuild(Map<String, Object> param) {
        return this.getSqlSession().selectList(Namespace + "queryHisStandardBuild", param);
    }

    @Override
    public List<TowerResourceInfoVO> queryTowerResourceInfoByIds(Map<String, Set<String>> commonData) throws Exception {
        return this.getSqlSession().selectList(Namespace + "queryTowerResourceInfoByIds", commonData);
    }

    @Override
    public int deleteTowerRentInformationByRentIds(List<String> rentinformationIds) {
        return this.getSqlSession().update(Namespace + "deleteTowerRentInformationByRentIds", rentinformationIds);
    }

    @Override
    public Page<TowerResourceInfoVO> queryMobileChangeInfo(Map<String, Object> paraMap, int pageNumber, int pageSize) {
        PageInterceptor.startPage(pageNumber, pageSize);
        this.getSqlSession().selectList(Namespace + "queryMobileChangeInfo", paraMap);
        return PageInterceptor.endPage();
    }

    @Override
    public int completeMobileChange(Map<String, Object> param) {
        return this.getSqlSession().update(Namespace + "completeMobileChange", param);
    }

    @Override
    public int mobileInfoHistoryBackup(Map<String, Object> param) {
        return this.getSqlSession().insert(NamespaceHistory + "mobileInfoHistoryBackup", param);
    }

    @Override
    public int towerResourceInfoCountById(String rentinformationtowerId) {
        return this.getSqlSession().selectOne(Namespace + "towerResourceInfoCountById", rentinformationtowerId);
    }

    @Override
    public Map<String, Object> queryBeanById(Map<String, Object> map) {
        return this.getSqlSession().selectOne(Namespace + "queryBeanById", map);
    }

    @Override
    public int mobileRoomBackup(Map<String, Object> param) {
        return this.getSqlSession().insert(NamespaceHistory + "mobileRoomRentDataCopy", param);
    }

    @Override
    public int mobileTinyBackup(Map<String, Object> param) {
        return this.getSqlSession().insert(NamespaceHistory + "mobileTinyRentDataCopy", param);
    }

    @Override
    public int mobileTransBackup(Map<String, Object> param) {
        return this.getSqlSession().insert(NamespaceHistory + "mobileTransRentDataCopy", param);
    }

    @Override
    public int mobileNonstandBackup(Map<String, Object> param) {
        return this.getSqlSession().insert(NamespaceHistory + "mobileNonstandRentDataCopy", param);
    }

    @Override
    public int mobileChangeCreateByBackup(Map<String, Object> param) {
        return this.getSqlSession().insert(NamespaceHistory + "mobileChangeCreate", param);
    }

    @Override
    public int updatePtShareDis(Map<String, Object> param) {
        return this.getSqlSession().update(NamespaceHistory + "updatePtShareDis", param);
    }

    @Override
    public int updateRoomShareDis(Map<String, Object> param) {
        return this.getSqlSession().update(NamespaceHistory + "updateRoomShareDis", param);
    }

    @Override
    public int updatePtShareDis3(Map<String, Object> param) {
        return this.getSqlSession().update(NamespaceHistory + "updatePtShareDis3", param);
    }

    @Override
    public int updateRoomShareDis3(Map<String, Object> param) {
        return this.getSqlSession().update(NamespaceHistory + "updateRoomShareDis3", param);
    }

    @Override
    public List<TowerRruDisConfig> getTwrRruDisInfo(Map<String, Object> paraMap) {
        return this.getSqlSession().selectList(Namespace + "getTwrRruDisInfo", paraMap);
    }

    @Override
    public List<TowerRruDisConfig> getTwrRruDisInfoWithoutName(Map<String, Object> paramMap) {
        return this.getSqlSession().selectList(Namespace + "getTwrRruDisInfoWithoutName", paramMap);
    }

    @Override
    public List<TowerRruDisCompare> getTwrRruDisCompareInfo(Map<String, Object> paraMap) {
        return this.getSqlSession().selectList(Namespace + "getTwrRruDisCompareInfo", paraMap);
    }

    @Override
    public int refreshResource(Map<String, Object> paramMap) {
        return this.getSqlSession().update(Namespace + "refreshResource", paramMap);
    }

    @Override
    public Page<StartRentCompareDetailVo> queryStartRentCompareDetail(Map<String, Object> paraMap, int pageNumber, int pageSize) {
        PageInterceptor.startPage(pageNumber, pageSize);
        this.getSqlSession().selectList(Namespace + "queryStartRentCompareDetail", paraMap);
        return PageInterceptor.endPage();
    }

    @Override
    public Page<StartRentCompareStatisticsVo> queryStartRentCompareStatistics(Map<String, Object> paraMap, int pageNumber, int pageSize) {
        PageInterceptor.startPage(pageNumber, pageSize);
        this.getSqlSession().selectList(Namespace + "queryStartRentCompareStatistics", paraMap);
        return PageInterceptor.endPage();
    }

    @Override
    public List<StartRentCompareStatisticsVo> queryStartRentCompareStatisticsExport(Map<String, Object> paraMap) {
        return this.getSqlSession().selectList(Namespace + "queryStartRentCompareStatistics", paraMap);
    }

    @Override
    public List<StartRentCompareStatisticsVo> queryStartRentCompareStatisticsRentNumber(Map<String, Object> paraMap) {
        List<StartRentCompareStatisticsVo> list =this.getSqlSession().selectList(Namespace + "queryStartRentCompareStatisticsRnetNumber", paraMap);
        return list;
    }

    @Override
    public List<StartRentCompareDetailVo> queryStartRentCompareDetail(Map<String, Object> paraMap) {
        return this.getSqlSession().selectList(Namespace + "queryStartRentCompareDetail", paraMap);
    }

    @Override
    public String getMaxUpdateTime() {
        return this.getSqlSession().selectOne(Namespace + "getMaxUpdateTime", null);
    }

    @Override
    public List<String> getAccountPeriod() {
        return this.getSqlSession().selectList(Namespace + "getAccountPeriod", null);
    }
}
