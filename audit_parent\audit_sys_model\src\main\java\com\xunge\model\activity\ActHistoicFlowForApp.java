package com.xunge.model.activity;

import java.io.Serializable;

/**
 * 工作流EntityForApp
 */
public class ActHistoicFlowForApp implements Serializable {

    private static final long serialVersionUID = 781304658798409233L;
    private String title; // 任务标题
    private String comment; // 任务意见
    private String assignee; // 任务执行人名称
    private String beginDate; // 开始查询日期
    private String endDate;// 结束查询日期
    private String durationTime;//历时
    private String regName;

    public ActHistoicFlowForApp() {
        super();
        // TODO Auto-generated constructor stub
    }

    public String getBeginDate() {
        return beginDate;
    }

    public void setBeginDate(String beginDate) {
        this.beginDate = beginDate;
    }

    public String getEndDate() {
        return endDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    public String getRegName() {
        return regName;
    }

    public void setRegName(String regName) {
        this.regName = regName;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment == null ? "无" : comment;
    }

    public String getAssignee() {
        return assignee;
    }

    public void setAssignee(String assignee) {
        this.assignee = assignee;
    }

    public String getDurationTime() {
        return durationTime;
    }

    public void setDurationTime(String durationTime) {
        this.durationTime = durationTime;
    }

}
