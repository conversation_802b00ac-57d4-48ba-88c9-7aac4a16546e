package com.xunge.dao.selfelec.loan;

import com.xunge.model.selfelec.loan.EleLoan;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * @创建人 LiXs
 * @创建时间 2019/11/12
 * @描述：
 */
public interface EleLoanMapper {

    /**
     * 根据缴费单编码查询缴费记录
     *
     * @param loanCode
     * @return
     */
    List<EleLoan> queryElecLoanVOByPaymentCode(String loanCode);

    /**
     * 查询数据库中最大数值的code
     *
     * @param param
     * @return
     */
    Map<String, Object> queryMaxCode(Map<String, Object> param);


    Map<String,String> selectBillamountIdFromEleLoanForUpdate(@Param("loanId") String loanId);

    List< Map<String,Object>> selectAuditingStateFromEleLoanForUpdate(@Param("idList")List<String> idList);

    Integer queryTurnNum(String loanCode);

    int updateBillAudit(@Param("loanCode") String loanCode, @Param("newUserLoginname") String newUserLoginname);
}
