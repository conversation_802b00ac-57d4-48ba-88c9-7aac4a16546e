package com.xunge.dao.selfelec.benchmarktotidb;

import com.xunge.model.selfelec.BenchmarkBean;

import java.util.List;
import java.util.Map;

public interface EleBenchmarkMonthMapper {

    List<BenchmarkBean> selectByCondition(Map<String, Object> map);

    BenchmarkBean selectOne(Map<String, Object> map);

    BenchmarkBean queryMaxBenchmarkMonByBillAccountId(Map<String, Object> map);

    List<Map<String, Object>> queryBillaccountBenchmarkInfo(String billaccountId);

    Map<String, Object> queryTByMonthAndRegId(Map<String, Object> map);

    Map<String, Object> queryCountBillaccountId(Map<String, Object> map);

    int updateBillaccountIdHistory(Map<String, Object> map);

    int insertBillaccountIdHistory(Map<String, Object> map);
}