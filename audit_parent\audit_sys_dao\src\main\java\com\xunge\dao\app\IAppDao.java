package com.xunge.dao.app;


import com.xunge.core.page.Page;
import com.xunge.model.app.AppPermission;

import java.util.Map;

public interface IAppDao {
    /**
     * 添加APP使用权限
     *
     * @param params
     * @return
     */
    int addAppPermission(Map<String, Object> params);

    /**
     * 停用或启用
     *
     * @param params
     * @return
     */
    int stopServe(Map<String, Object> params);

    /**
     * 查询所有APP使用权限
     *
     * @param params
     * @return
     */
    Page<AppPermission> queryAllAppPermission(Map<String, Object> params);
}
