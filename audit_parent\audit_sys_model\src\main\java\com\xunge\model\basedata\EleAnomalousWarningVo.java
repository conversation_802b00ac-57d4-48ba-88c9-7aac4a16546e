package com.xunge.model.basedata;

import com.xunge.model.activity.Act;

/**
 * eleAnomalousWarningVo
 *
 * <AUTHOR>
 * @version V1.0
 * @description <文件描述: >
 * @date 2019/10/10
 * @email <EMAIL>
 */
public class EleAnomalousWarningVo extends Act {
    private String recoverIds;
    private String businessId;
    private String businessCode;
    private String title;
    private String name;
    // private String beginDate;
    private String status;
    private String category;
    private String majorId;
    private String isIncludeAll;
    private String billaccountId;

    private String warningType;
    private String paymentFlag;

    private Integer eleType;

    public String getBillaccountId() {
        return billaccountId;
    }

    public void setBillaccountId(String billaccountId) {
        this.billaccountId = billaccountId;
    }


    public String getIsIncludeAll() {
        return isIncludeAll;
    }

    public void setIsIncludeAll(String isIncludeAll) {
        this.isIncludeAll = isIncludeAll;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    // public String getBeginDate() {
    //  return beginDate;
    // }

    // public void setBeginDate(String beginDate) {
    //    this.beginDate = beginDate;
    // }

    public String getBusinessId() {
        return businessId;
    }

    public void setBusinessId(String businessId) {
        this.businessId = businessId;
    }

    public String getBusinessCode() {
        return businessCode;
    }

    public void setBusinessCode(String businessCode) {
        this.businessCode = businessCode;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getRecoverIds() {
        return recoverIds;
    }

    public void setRecoverIds(String recoverIds) {
        this.recoverIds = recoverIds;
    }

    public String getMajorId() {
        return majorId;
    }

    public void setMajorId(String majorId) {
        this.majorId = majorId;
    }

    public String getWarningType() {
        return warningType;
    }

    public void setWarningType(String warningType) {
        this.warningType = warningType;
    }

    public String getPaymentFlag() {
        return paymentFlag;
    }

    public void setPaymentFlag(String paymentFlag) {
        this.paymentFlag = paymentFlag;
    }

    public Integer getEleType() {
        return eleType;
    }

    public void setEleType(Integer eleType) {
        this.eleType = eleType;
    }
}
