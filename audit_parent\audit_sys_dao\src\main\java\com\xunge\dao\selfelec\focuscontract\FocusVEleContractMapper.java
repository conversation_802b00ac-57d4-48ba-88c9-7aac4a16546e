package com.xunge.dao.selfelec.focuscontract;

import com.xunge.model.selfelec.VEleContract;
import com.xunge.model.selfelec.VEleContractCuring;
import com.xunge.model.selfelec.VEleContractExample;
import com.xunge.model.selfelec.focuscontract.ExportVEleContract;
import com.xunge.model.selfelec.focuscontract.FocusVEleContract;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

public interface FocusVEleContractMapper {
    
    int countByExample(VEleContractExample example);

    
    int deleteByExample(VEleContractExample example);

    
    int insert(VEleContract record);

    
    int insertSelective(VEleContract record);

    
    List<VEleContract> selectByExample(VEleContractExample example);

    List<VEleContract> selectByExample(Map<String, Object> map);

    
    int updateByExampleSelective(@Param("record") VEleContract record, @Param("example") VEleContractExample example);

    
    int updateByExample(@Param("record") VEleContract record, @Param("example") VEleContractExample example);

    List<FocusVEleContract> queryAllElecContract(Map<String, Object> paramMap);

    /**
     * 查询合同
     *
     * @param focusVEleContract
     * @return
     */
    FocusVEleContract queryBeanById(FocusVEleContract focusVEleContract);

    List<ExportVEleContract> queryCheckElecContract(Map<String, Object> paramMap);

    List<ExportVEleContract> queryExportElecContract(Map<String, Object> paramMap);

    /**
     * @param paramMap 电费合同查询
     * @return
     */
    List<FocusVEleContract> queryAllEleccontractList(Map<String, Object> paramMap);

    /**
     * 电费合同查询
     * v1
     */
    List<FocusVEleContract> queryEleContractList(Map<String, Object> paramMap);

    List<FocusVEleContract> querySupplementContractList(Map<String, Object> paramMap);

    List<VEleContractCuring> queryAllElecContractCuring(Map<String, Object> paramMap);

    VEleContract queryVEleContractVOById(Map<String, Object> paramMap);

    FocusVEleContract queryForAgreementById(Map<String, Object> paramMap);

    int updateContractVO(Map<String, Object> paramMap);

    FocusVEleContract queryOneElecContractById(Map<String, Object> paraMap);

    /**
     * @description 根据电费合同id查询此条合同最终缴费日期
     * <AUTHOR>
     * @date 创建时间：2017年9月7日
     */
    public String getElePaymentEnddate(Map<String, Object> paraMap);

    /**
     * @description 查询报账点和合同关联表信息
     * <AUTHOR>
     * @date 创建时间：2017年9月8日
     */
    List<String> queryBillaccountContract(Map<String, Object> map);

    /**
     * @description 批量修改合同状态
     * <AUTHOR>
     * @date 创建时间：2017年10月19日
     */
    public boolean updateContractState(Map<String, Object> paramMap);

    /**
     * @description 查询所有需要修改系统统一编码的固化信息
     * <AUTHOR>
     * @date 创建时间：2018年1月30日
     */
    public List<VEleContract> queryContractBySysCode(Map<String, Object> paraMap);

    /**
     * @description 批量新增电费固化信息
     * <AUTHOR>
     * @date 创建时间：2018年2月6日
     */
    public int insertElecContractInfoList(Map<String, Object> paraMap);

    /**
     * @description 批量修改电费固化信息
     * <AUTHOR>
     * @date 创建时间：2018年2月6日
     */
    public int updateElecContractInfoList(Map<String, Object> paraMap);

    /**
     * @description 根据省份id查询所有合同
     * <AUTHOR>
     * @date 创建时间：2018年1月30日
     */
    List<VEleContractCuring> queryAllElecContractCuringByPrvId(Map<String, Object> paraMap);

    List<VEleContract> queryEleContractByPrvId(Map<String, Object> paraMap);

    /**
     * @Title: queryOneElecContractFlow @Description: TODO(这里用一句话描述这个方法的作用) @param @param
     * paramMap @param @return 设定文件 @return FocusVEleContract 返回类型 @throws
     */

    FocusVEleContract queryOneElecContractFlow(Map<String, Object> paramMap);

    void deleteByContractIds(Map<String, Object> paraMap);


    /**
     * @param @param  map
     * @param @return 设定文件
     * @return List<FocusVEleContract>    返回类型
     * @throws
     * @Title: queryFocusVEleContractList
     * @Description: TODO(这里用一句话描述这个方法的作用)
     */

    List<FocusVEleContract> queryFocusVEleContractList(Map<String, Object> map);

    /**
     * 查询合同总金额，已报账金额
     * @param elecontractId 电费合同id
     * @return 合同信息
     */
    FocusVEleContract findContractAmount(String elecontractId);

    /**
     * 固化关联合同，查询合同金额
     * @param contractId 合同id
     * @return 合同信息
     */
    FocusVEleContract findContractAmountById(String contractId);

    /**
     * 根据合同id查询未推送的所有缴费金额
     * @param contractIds
     * @return
     */
    BigDecimal findSumBillAmount(@Param("contractIds") List<String> contractIds);

    /**
     * 根据合同查询关联合同的固化id
     * @param contractIdRel 合同id
     * @return 固化id
     */
    List<String> findContractIds(String contractIdRel);


    int updateContractVOAuto(Map<String, Object> paramMap);

}