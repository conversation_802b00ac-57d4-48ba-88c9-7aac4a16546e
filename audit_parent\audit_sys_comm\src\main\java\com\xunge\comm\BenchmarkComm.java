package com.xunge.comm;

import java.math.BigDecimal;

/**
 * @description 标杆配置标识
 * <AUTHOR>
 * @date 创建时间：2023年11月16日
 */
public class BenchmarkComm {
	/**
	 * 历史日均电量标杆免稽核配置 同比
	 */
	public static final String hisLastDayDegreeBenchmarkLimit = "hisLastDayDegreeBenchmarkLimit";

	/**
	 * 历史日均电量标杆免稽核配置 环比
	 */
	public static final String hisNowDayDegreeBenchmarkLimit = "hisNowDayDegreeBenchmarkLimit";


	/**
	 * 历史日均电量标杆免稽核配置 超标率特定值666.09
	 */
	public static final BigDecimal hisDayDegreeBenchmarkLimitRate = new BigDecimal("666.09");

}
