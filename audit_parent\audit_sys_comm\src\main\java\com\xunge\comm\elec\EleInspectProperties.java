package com.xunge.comm.elec;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.util.List;

/**
 * 稽核配置文件
 */
@Component
@Data
public class EleInspectProperties implements Serializable {

    /**
     * 稽核报账基础数据信息传报至省公司（多个省份对应同一url。省份之间用,分割，省份与url之间用@分割，多个配置用;分割）
     */
    @Value("#{'${prvCodeInfoApplyUrl}'.split(';')}")
    private List<String> prvCodeInfoApplyUrl;
    /**
     * 网络费用系统稽核结果信息传报至省公司（多个省份对应同一url。省份之间用,分割，省份与url之间用@分割，多个配置用;分割）
     */
    @Value("#{'${prvCodeResortApplyUrl}'.split(';')}")
    private List<String> prvCodeResortApplyUrl;
}
