package com.xunge.model.budget;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class RentBudgetBaseData {

    private String prvId;

    private String prvName;

    /**
     * 业务类型({传输位置点,-1}, {综合位置点,0}, {核心机楼,1}, {汇聚传输站点,2}, {基站,3}, {室分及WLAN,4}, {家客集客,5}, {IDC机房,6}, {基地,7}, {其他,8}, {合计行,9})
     */
    private Integer serviceSiteType;

    /**
     * 单站费用（系统数据）
     */
    private BigDecimal avgSiteAmountSys;

    /**
     * 站点数量合计（系统数据）
     */
    private Integer siteNum;

    /**
     * 预算类型(1租费2三方塔)
     */
    private Integer budgetType;

    /**
     * 报账点数量-合同期内
     */
    private Integer billaccountNumWithin;

    /**
     * 站点数量-合同期内
     */
    private Integer siteNumWithin;

    /**
     * 预算金额-合同期内
     */
    private BigDecimal budgetAmountWithin;

    /**
     * 报账点数量-合同期外
     */
    private Integer billaccountNumOutside;

    /**
     * 站点数量-合同期外
     */
    private Integer siteNumOutside;

    /**
     * 预算金额-合同期外
     */
    private BigDecimal budgetAmountOutside;

    /**
     * 站点数量-合同期内+合同期外去重
     */
    private Integer siteNumTotal;

    /**
     * 上月-年底平均新增规模
     */
    private Integer siteNumEndYearIncreaseAvg;

    /**
     * 整年平均新增规模
     */
    private Integer siteNumYearIncreaseAvg;
}
