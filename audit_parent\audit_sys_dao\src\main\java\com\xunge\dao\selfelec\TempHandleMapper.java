package com.xunge.dao.selfelec;

import com.xunge.model.basedata.DatAttachment;
import com.xunge.model.selfelec.EleBenchmarkBillaccount;
import com.xunge.model.selfelec.EleBillaccountPaymentdetail;
import com.xunge.model.selfelec.VEleBillaccount;
import com.xunge.model.selfrent.billAccount.BillAccountVO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019年03月06日
 */
public interface TempHandleMapper {

    List<EleBillaccountPaymentdetail> getHistoryData();

    List<VEleBillaccount> queryRepeatEleBillCode(String prvId);

    /**
     * 处理重复报账点编码，更新对应表的报账点编码
     *
     * @param map
     * @return
     * @date 2019年02月20日
     * <AUTHOR>
     */
    void handleTableBillCode(Map<String, String> map);

    /**
     * 处理重复报账点编码，更新电表台账报账点编码(缴费)
     *
     * @param map
     * @return
     * @date 2019年02月20日
     * <AUTHOR>
     */
    void handleMeterLedgerBillCode(Map<String, String> map);

    /**
     * 处理重复报账点编码，更新电表台账报账点编码(核销)
     *
     * @param map
     * @return
     * @date 2019年02月20日
     * <AUTHOR>
     */
    void handleVerificateMeterLedgerBillCode(Map<String, String> map);

    /**
     * 处理重复报账点编码，更新电表台账报账点编码(IC核销)
     *
     * @param map
     * @return
     * @date 2019年02月20日
     * <AUTHOR>
     */
    void handleIcVerificateMeterLedgerBillCode(Map<String, String> map);

    List<BillAccountVO> queryRepeatRentBillCode(String prvId);

    List<DatAttachment> getAttachmentList(Map<String, String> map);

}
