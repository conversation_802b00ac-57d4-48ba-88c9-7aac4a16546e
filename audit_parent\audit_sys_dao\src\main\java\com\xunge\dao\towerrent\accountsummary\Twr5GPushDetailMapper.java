package com.xunge.dao.towerrent.accountsummary;

import com.xunge.model.towerrent.accountsummary.Twr5gPushDetail;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Description:  铁塔服务费5g随e签明细
 * @Author: tmy
 */
@Mapper
public interface Twr5GPushDetailMapper {
    int deleteByPrimaryKey(Long id);

    int insert(Twr5gPushDetail record);

    int insertSelective(Twr5gPushDetail record);

    Twr5gPushDetail selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(Twr5gPushDetail record);

    int updateByPrimaryKey(Twr5gPushDetail record);

    void delete5gDetailBySummaryId(@Param("accountsummaryId") String accountsummaryId);

    void batchInsert5gPushDetail(@Param("list") List<Twr5gPushDetail> twr5gPushDetails);

    /**
     * 新增5G随e签信息
     */
    void saveFreeSignInfo(@Param("summaryId") String summaryId, @Param("summaryCode") String summaryCode,
                          @Param("signType") String signType, @Param("remark") String remark,
                          @Param("pushSwitch")String pushSwitch,
                          @Param("fileIds") String fileIds
    );

    /**
     * 将5G随e签信息置为失效状态
     * @param summaryId
     */
    void invalidFreeSignInfo(@Param("summaryId") String summaryId);

    String getCostCenterNameByCode(@Param("costCenterCode") String costCenterCode);
}
