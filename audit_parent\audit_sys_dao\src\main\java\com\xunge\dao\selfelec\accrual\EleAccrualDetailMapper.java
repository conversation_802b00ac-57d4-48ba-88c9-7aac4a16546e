package com.xunge.dao.selfelec.accrual;

import java.util.List;
import java.util.Map;

import com.xunge.model.selfelec.accrual.*;
import org.apache.ibatis.annotations.Param;

public interface EleAccrualDetailMapper {

	List<EleAccrualDetail> getExportInfo(Map<String, Object> map);

	List<EleAccrualDetail> getDetailByAccrualId(String accrualId);

	void insertEleAccrualDetail(List<EleAccrualDetail> details);

	void updateEleAccrualDetail(EleAccrualDetail detail);

	void deleteEleAccrualDetail(List<EleAccrualDetail> details);

	void deleteAccrualDetailByAccrualId(String accrualId);

	List<EleAccrualDetail> getDetailByAccrualIds(List<EleAccrual> list);

	EleAccrualDetail getDetailById(String accrualdetailId);

	void insertBillaccountNoAccrual(@Param("nolist")List<EleBillaccountNoAccrual> nolist, @Param("fromTable")String fromTable);

	void insertEleAccrualOther(List<EleAccrualOtherAmount> add);

	void updateEleAccrualOther(EleAccrualOtherAmount ed);

	void deleteEleAccrualOther(List<EleAccrualOtherAmount> del);

	List<EleAccrualOtherAmount> getOtherByAccrualId(String accrualId);

	EleAccrualOtherAmount getPreOtherInfo(EleAccrualOtherAmount param);

	void deleteAccrualOtherByAccrualId(String accrualId);

	List<EleAccrualOtherAmountExt> selectAccrualOtherAmountsByAccrualIds(List<String> accrualIdList);

	int updateAccrualOtherFeeDetail(List<EleAccrualOtherAmount> otherAmountList);

	EleAccrualOtherAmount getDiffPreOtherInfo( Map<String,Object> param);

	EleAccrualDetail queryAccrualDetailByBillaccountId(@Param("billaccountId") String billaccountId, @Param("startDate") String startDate, @Param("endDate") String endDate);

	List<String> getOtherAmountByAccrualsIsNull(List<String> lists);

}
