package com.xunge.dao.report;

import com.xunge.model.report.RptEleSaveCostVO;

import java.util.List;
import java.util.Map;

public interface IRptEleSaveCostDao {

    /**
     * 查询各省电费稽核节约数据
     *
     * @param paraMap
     * @return
     * <AUTHOR>
     */
    public List<RptEleSaveCostVO> queryAllEleSaveCost(Map<String, Object> paraMap);

    /**
     * 根据省份查询各地市电费稽核节约数据
     *
     * @param paraMap
     * @return
     * <AUTHOR>
     */
    public List<RptEleSaveCostVO> queryEleSaveCostByPrvId(Map<String, Object> paraMap);

    /**
     * 根据地市查询各区县电费稽核节约数据
     *
     * @param paraMap
     * @return
     * <AUTHOR>
     */
    public List<RptEleSaveCostVO> queryEleSaveCostByPregId(Map<String, Object> paraMap);

}
