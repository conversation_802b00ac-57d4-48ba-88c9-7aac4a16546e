/**
 *
 */
package com.xunge.core.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateFormatUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;

/**
 * 日期工具类, 继承org.apache.commons.lang.time.DateUtils类
 * <AUTHOR>
 * @version 2014-4-15
 */
@Slf4j
public class DateUtils extends org.apache.commons.lang3.time.DateUtils {

    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    private static String[] parsePatterns = {"yyyy-MM-dd", "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd HH:mm", "yyyy-MM", "yyyy/MM/dd", "yyyy/MM/dd HH:mm:ss",
            "yyyy/MM/dd HH:mm", "yyyy/MM", "yyyy.MM.dd", "yyyy.MM.dd HH:mm:ss", "yyyy.MM.dd HH:mm", "yyyy.MM", "yyyyMM"};

    /**
     * 得到当前日期字符串 格式（yyyy-MM-dd）
     */
    public static String getDate() {
        return getDate("yyyy-MM-dd");
    }

    /**
     * 得到当前日期字符串 格式（yyyy-MM-dd） pattern可以为："yyyy-MM-dd" "HH:mm:ss" "E"
     */
    public static String getDate(String pattern) {
        return DateFormatUtils.format(new Date(), pattern);
    }

    /**
     * 得到日期字符串 默认格式（yyyy-MM-dd） pattern可以为："yyyy-MM-dd" "HH:mm:ss" "E"
     */
    public static String formatDate(Date date, Object... pattern) {
        String formatDate = null;
        if (pattern != null && pattern.length > 0) {
            formatDate = DateFormatUtils.format(date, pattern[0].toString());
        } else {
            formatDate = DateFormatUtils.format(date, "yyyy-MM-dd");
        }
        return formatDate;
    }

    /**
     * 得到日期时间字符串，转换格式（yyyy-MM-dd HH:mm:ss）
     */
    public static String formatDateTime(Date date) {
        return formatDate(date, "yyyy-MM-dd HH:mm:ss");
    }

    /**
     * 得到当前时间字符串 格式（HH:mm:ss）
     */
    public static String getTime() {
        return formatDate(new Date(), "HH:mm:ss");
    }

    /**
     * 得到当前日期和时间字符串 格式（yyyy-MM-dd HH:mm:ss）
     */
    public static String getDateTime() {
        return formatDate(new Date(), "yyyy-MM-dd HH:mm:ss");
    }

    /**
     * 得到当前年份字符串 格式（yyyy）
     */
    public static String getYear() {
        return formatDate(new Date(), "yyyy");
    }

    /**
     * 得到当前月份字符串 格式（MM）
     */
    public static String getMonth() {
        return formatDate(new Date(), "MM");
    }

    /**
     * 得到当天字符串 格式（dd）
     */
    public static String getDay() {
        return formatDate(new Date(), "dd");
    }

    /**
     * 得到当前星期字符串 格式（E）星期几
     */
    public static String getWeek() {
        return formatDate(new Date(), "E");
    }

    /**
     * 日期型字符串转化为日期 格式 { "yyyy-MM-dd", "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd HH:mm", "yyyy/MM/dd",
     * "yyyy/MM/dd HH:mm:ss", "yyyy/MM/dd HH:mm", "yyyy.MM.dd", "yyyy.MM.dd HH:mm:ss", "yyyy.MM.dd
     * HH:mm" }
     */
    public static Date parseDate(Object str) {
        if (str == null) {
            return null;
        }
        try {
            return parseDate(str.toString(), parsePatterns);
        } catch (ParseException e) {
            return null;
        }
    }

    /**
     * 获取过去的天数
     * @param date
     * @return
     */
    public static long pastDays(Date date) {
        long t = new Date().getTime() - date.getTime();
        return t / (24 * 60 * 60 * 1000);
    }

    /**
     * 获取过去的小时
     * @param date
     * @return
     */
    public static long pastHour(Date date) {
        long t = new Date().getTime() - date.getTime();
        return t / (60 * 60 * 1000);
    }

    /**
     * 获取过去的分钟
     * @param date
     * @return
     */
    public static long pastMinutes(Date date) {
        long t = new Date().getTime() - date.getTime();
        return t / (60 * 1000);
    }

    /**
     * 转换为时间（天,时:分:秒.毫秒）
     * @param timeMillis
     * @return
     */
    public static String formatDateTime(long timeMillis) {
        long day = timeMillis / (24 * 60 * 60 * 1000);
        long hour = (timeMillis / (60 * 60 * 1000) - day * 24);
        long min = ((timeMillis / (60 * 1000)) - day * 24 * 60 - hour * 60);
        long s = (timeMillis / 1000 - day * 24 * 60 * 60 - hour * 60 * 60 - min * 60);
        long sss = (timeMillis - day * 24 * 60 * 60 * 1000 - hour * 60 * 60 * 1000 - min * 60 * 1000 - s * 1000);
        StringBuilder sb = new StringBuilder();
        if (day > 0) {
            sb.append(day).append("天");
        }
        if (hour > 0) {
            sb.append(hour).append("时");
        }
        if (min > 0) {
            sb.append(min).append("分");
        }
        if (s > 0) {
            sb.append(s).append("秒");
        }
        return sb.toString();
    }

    /**
     * 获取两个日期之间的天数
     *
     * @param before
     * @param after
     * @return
     */
    public static double getDistanceOfTwoDate(Date before, Date after) {
        long beforeTime = before.getTime();
        long afterTime = after.getTime();
        return (afterTime - beforeTime) / (1000L * 60 * 60 * 24);
    }

    /**
     * 获取两个日期之间的天数(String)
     *
     * @param start
     * @param end
     * @return
     */
    public static double getDistanceOfTwoDate(String start, String end) {
        Date before = parseDate(start);
        Date after = parseDate(end);
        long beforeTime = before != null ? before.getTime() : 0L;
        long afterTime = after != null ? after.getTime() : 0L;
        return (afterTime - beforeTime) / (1000L * 60 * 60 * 24);
    }

    /**
     * 获取两个日期之间的月数
     *
     * @param before
     * @param after
     * @return
     */
    public static int getMonthDiff(Date before, Date after) {
        Calendar beforeCal = Calendar.getInstance();
        Calendar afterCal = Calendar.getInstance();
        beforeCal.setTime(before);
        afterCal.setTime(after);
        if (afterCal.getTimeInMillis() < beforeCal.getTimeInMillis()) {
            return 0;
        }
        int year1 = beforeCal.get(Calendar.YEAR);
        int year2 = afterCal.get(Calendar.YEAR);
        int month1 = beforeCal.get(Calendar.MONTH);
        int month2 = afterCal.get(Calendar.MONTH);
        int day1 = beforeCal.get(Calendar.DAY_OF_MONTH);
        int day2 = afterCal.get(Calendar.DAY_OF_MONTH);

        int yearInterval = year2 - year1;

        if (month2 < month1 || month1 == month2 && day2 < day1) {
            yearInterval--;
        }
        // 获取月数差值
        int monthInterval = (month2 + 12) - month1;
        if (day2 > day1) {
            monthInterval++;
        }
        monthInterval %= 12;
        return yearInterval * 12 + monthInterval;
    }


    /**
     * 校验两个字符串时间，先后 且任意一天不等于当天
     *
     * @param billamountStartdate
     * @param billamountEnddate
     * @return
     */
    public static boolean verifyDate(String billamountStartdate, String billamountEnddate) {
        Date start = DateUtils.parseDate(billamountStartdate);
        Date end = DateUtils.parseDate(billamountEnddate);
        Date now = DateUtils.parseDate(DateUtils.formatDate(new Date()));
        if (start != null && start.after(end)) {
            //期始>期终
            return false;
        }
        if (now != null && now.equals(start)) {
            //期始等于当天
            return false;
        }
        //期终等于当天
        return now != null && !now.equals(end);
    }

    /**
     *  加减对应时间后的日期
     * @param date  需要加减时间的日期
     * @param amount    加减的时间(毫秒)
     * @return 加减对应时间后的日期
     */
    public static Date subtractTime(Date date, int amount) {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String strTime = sdf.format(date.getTime() + amount);
            Date time = sdf.parse(strTime);
            return time;
        } catch (Exception e) {
            log.error("DateUtils 出错", e);
        }
        return null;
    }

    /**
     * 获取指定日期上月
     * 2021-01-21 -> 2020-12
     *
     * @param date
     * @param format
     * @return
     */
    public static String getLastMonth(Date date, String format) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        calendar.add(Calendar.MONTH, -1);
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        return sdf.format(calendar.getTime());
    }

    /**
     * 获取指定日期当月
     * 2021-01-21 -> 2021-01
     *
     * @param date
     * @param format
     * @return
     */
    public static String getPresentMonth(Date date, String format) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        calendar.add(Calendar.MONTH, 0);
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        return sdf.format(calendar.getTime());
    }

    /**
     * 2022-02 --》2022-03 下个月
     *
     * @param dateStr
     * @param format
     * @return
     * @throws ParseException
     */
    public static Date getnextMonth(String dateStr, String format) throws ParseException {
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        Date parse = sdf.parse(dateStr);
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(parse);
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        calendar.add(Calendar.MONTH, 1);
        String date = sdf.format(calendar.getTime());
        return sdf.parse(date);
    }

    /**
     * 判断当前时间是否在[startTime, endTime]区间，注意时间格式要一致
     *
     * @param nowTime   当前时间
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return boolean
     */
    public static boolean isEffectiveDate(Date nowTime, Date startTime, Date endTime) {
        if (nowTime.getTime() == startTime.getTime()
                || nowTime.getTime() == endTime.getTime()) {
            return true;
        }

        Calendar date = Calendar.getInstance();
        date.setTime(nowTime);

        Calendar begin = Calendar.getInstance();
        begin.setTime(startTime);

        Calendar end = Calendar.getInstance();
        end.setTime(endTime);

        if (date.after(begin) && date.before(end)) {
            return true;
        } else {
            return false;
        }
    }

    public static int getDaysOfMonth(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar.getActualMaximum(Calendar.DAY_OF_MONTH);
    }
    
    public static List<String> getHasYearMonth(Date start, Date end){
    	List<String> yearMonths = new ArrayList<>();
    	SimpleDateFormat sf = new SimpleDateFormat("yyyyMM");
    	Calendar c = Calendar.getInstance();
    	c.setTime(start);
    	while(!start.after(end)) {
    		yearMonths.add(sf.format(start));
    		c.add(Calendar.MONTH, 1);
    		start = c.getTime();
    	}
    	return yearMonths;
    }

    public static Date dateAdd(Date date, int dayPos) {
        Calendar calendar = new GregorianCalendar();
        calendar.setTime(date);
        // 把日期往后增加一天,整数  往后推,负数往前移动
        calendar.add(Calendar.DATE, dayPos);
        // 这个时间就是日期往后推一天的结果
        return calendar.getTime();
    }

    /**
     * 计算两个日期字符串之间的完整月份差，考虑天数
     * @param startDateStr
     * @param endDateStr
     * @return
     */
    public static long calculateCompleteMonths(String startDateStr, String endDateStr) {
        LocalDate startDate = LocalDate.parse(startDateStr, DATE_FORMATTER);
        LocalDate endDate = LocalDate.parse(endDateStr, DATE_FORMATTER);
        return ChronoUnit.MONTHS.between(startDate, endDate);
    }

    /**
     * 测试
     * @param args
     * @throws ParseException
     */
    public static void main(String[] args) throws ParseException {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        String[] splitList = "2024-06-04 15:40:27/0#2024-06-19 19:10:56/1#(2025-05-12 14:48:02/1)#".split("#");
        String dataStr = splitList[splitList.length - 3].substring(0, 10);
        long monthDiff = DateUtils.calculateCompleteMonths(dataStr, DateUtils.formatDate(new Date(),"yyyy-MM-dd"));
        System.out.println(monthDiff);
    }
}
