package com.xunge.dao.basedata;

import com.xunge.model.basedata.DatPaymentperiod;
import com.xunge.model.basedata.DatPaymentperiodExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface DatPaymentperiodMapper {
    
    int countByExample(DatPaymentperiodExample example);

    
    int deleteByExample(DatPaymentperiodExample example);

    
    int deleteByPrimaryKey(String paymentperiodId);

    
    int insert(DatPaymentperiod record);

    
    int insertSelective(DatPaymentperiod record);

    
    List<DatPaymentperiod> selectByExample(DatPaymentperiodExample example);

    
    DatPaymentperiod selectByPrimaryKey(String paymentperiodId);

    
    int updateByExampleSelective(@Param("record") DatPaymentperiod record, @Param("example") DatPaymentperiodExample example);

    
    int updateByExample(@Param("record") DatPaymentperiod record, @Param("example") DatPaymentperiodExample example);

    
    int updateByPrimaryKeySelective(DatPaymentperiod record);

    
    int updateByPrimaryKey(DatPaymentperiod record);

    String selectIdByValue(String value);
}