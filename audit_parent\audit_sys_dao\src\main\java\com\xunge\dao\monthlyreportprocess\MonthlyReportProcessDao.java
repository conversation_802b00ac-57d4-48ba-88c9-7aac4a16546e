package com.xunge.dao.monthlyreportprocess;

import java.util.List;
import java.util.Map;

public interface MonthlyReportProcessDao {

    Integer updateKeyIndexCope(Map<String, Object> paraMap);

    Integer updateKeyIndexAm(Map<String, Object> paraMap);

    Integer deleteRentKeyIndex(Map<String, Object> paraMap);

    Integer insertKeyIndexCope(List<Map<String, Object>> listMap);

    Integer updateKeyIndexCopeNew(Map<String, Object> paraMap);

    Integer updateKeyIndexAmNew(Map<String, Object> paraMap);
}
