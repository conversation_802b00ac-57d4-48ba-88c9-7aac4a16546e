package com.xunge.dao.system.twrRegion.impl;

import com.xunge.dao.AbstractBaseDao;
import com.xunge.dao.system.twrRegion.ITwrRegionDao;
import com.xunge.model.system.region.RegionVO;

import java.util.List;
import java.util.Map;

/**
 *
 */
public class TwrRegionDaoImpl extends AbstractBaseDao implements ITwrRegionDao {
    final String nameSpace = "com.xunge.dao.system.SysTwrRegionVOMapper.";

    @Override
    public List<RegionVO> queryManaRegions(Map<String, Object> map) {
        return this.getSqlSession().selectList(nameSpace + "queryManaRegions", map);
    }

    @Override
    public List<RegionVO> queryManaProvs(Map<String, Object> map) {
        return this.getSqlSession().selectList(nameSpace + "queryManaProvs", map);
    }
}
