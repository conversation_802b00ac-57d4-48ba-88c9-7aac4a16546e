package com.xunge.model.costcenter;

import java.io.Serializable;
import java.util.Date;

/**
 * Created by l<PERSON><PERSON><PERSON> on 2019/8/7.
 */
public class SmapUserOrgVO implements Serializable {
    private static final long serialVersionUID = 1L;

    private int organizationid;
    private String organizationcode;
    private String hrorganizationcode;
    private String organizationname;
    private String companycode;
    private String companyname;
    private String city;
    private String orgshortname;
    private String erporgshortname;
    private String orgdescription;
    private String orgstylecode;
    private String orgstyle;
    private String orglevelcode;
    private String orglevel;
    private String supervisorleader;
    private String mainprincipal;
    private String manager;
    private String managerorgid;
    private String postaladdress;
    private String postalcode;
    private String telephonenumber;
    private String faxnumber;
    private String displayorder;
    private String orgstatuscode;
    private String orgstatus;
    private String parentorgcode;
    private String parentorgname;
    private String classifycode;
    private String classifyname;
    private String belongcompany;
    private String businessdutycode;
    private String businessduty;
    private int mapid;
    private String companyccode;
    private String companydesp;
    private String cstcode;
    private String cdtdesp;
    private String operationtypecode;
    private String operationtype;
    private Date lastupdatedate;
    private String attribute1;
    private String attribute2;
    private String attribute3;
    private String attribute4;
    private String attribute5;
    private String outputext;
    private Date createtime;
    private Date updatetime;

    public int getOrganizationid() {
        return organizationid;
    }

    public void setOrganizationid(int organizationid) {
        this.organizationid = organizationid;
    }

    public String getOrganizationcode() {
        return organizationcode;
    }

    public void setOrganizationcode(String organizationcode) {
        this.organizationcode = organizationcode;
    }

    public String getHrorganizationcode() {
        return hrorganizationcode;
    }

    public void setHrorganizationcode(String hrorganizationcode) {
        this.hrorganizationcode = hrorganizationcode;
    }

    public String getOrganizationname() {
        return organizationname;
    }

    public void setOrganizationname(String organizationname) {
        this.organizationname = organizationname;
    }

    public String getCompanycode() {
        return companycode;
    }

    public void setCompanycode(String companycode) {
        this.companycode = companycode;
    }

    public String getCompanyname() {
        return companyname;
    }

    public void setCompanyname(String companyname) {
        this.companyname = companyname;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getOrgshortname() {
        return orgshortname;
    }

    public void setOrgshortname(String orgshortname) {
        this.orgshortname = orgshortname;
    }

    public String getErporgshortname() {
        return erporgshortname;
    }

    public void setErporgshortname(String erporgshortname) {
        this.erporgshortname = erporgshortname;
    }

    public String getOrgdescription() {
        return orgdescription;
    }

    public void setOrgdescription(String orgdescription) {
        this.orgdescription = orgdescription;
    }

    public String getOrgstylecode() {
        return orgstylecode;
    }

    public void setOrgstylecode(String orgstylecode) {
        this.orgstylecode = orgstylecode;
    }

    public String getOrgstyle() {
        return orgstyle;
    }

    public void setOrgstyle(String orgstyle) {
        this.orgstyle = orgstyle;
    }

    public String getOrglevelcode() {
        return orglevelcode;
    }

    public void setOrglevelcode(String orglevelcode) {
        this.orglevelcode = orglevelcode;
    }

    public String getOrglevel() {
        return orglevel;
    }

    public void setOrglevel(String orglevel) {
        this.orglevel = orglevel;
    }

    public String getSupervisorleader() {
        return supervisorleader;
    }

    public void setSupervisorleader(String supervisorleader) {
        this.supervisorleader = supervisorleader;
    }

    public String getMainprincipal() {
        return mainprincipal;
    }

    public void setMainprincipal(String mainprincipal) {
        this.mainprincipal = mainprincipal;
    }

    public String getManager() {
        return manager;
    }

    public void setManager(String manager) {
        this.manager = manager;
    }

    public String getManagerorgid() {
        return managerorgid;
    }

    public void setManagerorgid(String managerorgid) {
        this.managerorgid = managerorgid;
    }

    public String getPostaladdress() {
        return postaladdress;
    }

    public void setPostaladdress(String postaladdress) {
        this.postaladdress = postaladdress;
    }

    public String getPostalcode() {
        return postalcode;
    }

    public void setPostalcode(String postalcode) {
        this.postalcode = postalcode;
    }

    public String getTelephonenumber() {
        return telephonenumber;
    }

    public void setTelephonenumber(String telephonenumber) {
        this.telephonenumber = telephonenumber;
    }

    public String getFaxnumber() {
        return faxnumber;
    }

    public void setFaxnumber(String faxnumber) {
        this.faxnumber = faxnumber;
    }

    public String getDisplayorder() {
        return displayorder;
    }

    public void setDisplayorder(String displayorder) {
        this.displayorder = displayorder;
    }

    public String getOrgstatuscode() {
        return orgstatuscode;
    }

    public void setOrgstatuscode(String orgstatuscode) {
        this.orgstatuscode = orgstatuscode;
    }

    public String getOrgstatus() {
        return orgstatus;
    }

    public void setOrgstatus(String orgstatus) {
        this.orgstatus = orgstatus;
    }

    public String getParentorgcode() {
        return parentorgcode;
    }

    public void setParentorgcode(String parentorgcode) {
        this.parentorgcode = parentorgcode;
    }

    public String getParentorgname() {
        return parentorgname;
    }

    public void setParentorgname(String parentorgname) {
        this.parentorgname = parentorgname;
    }

    public String getClassifycode() {
        return classifycode;
    }

    public void setClassifycode(String classifycode) {
        this.classifycode = classifycode;
    }

    public String getClassifyname() {
        return classifyname;
    }

    public void setClassifyname(String classifyname) {
        this.classifyname = classifyname;
    }

    public String getBelongcompany() {
        return belongcompany;
    }

    public void setBelongcompany(String belongcompany) {
        this.belongcompany = belongcompany;
    }

    public String getBusinessdutycode() {
        return businessdutycode;
    }

    public void setBusinessdutycode(String businessdutycode) {
        this.businessdutycode = businessdutycode;
    }

    public String getBusinessduty() {
        return businessduty;
    }

    public void setBusinessduty(String businessduty) {
        this.businessduty = businessduty;
    }

    public int getMapid() {
        return mapid;
    }

    public void setMapid(int mapid) {
        this.mapid = mapid;
    }

    public String getCompanyccode() {
        return companyccode;
    }

    public void setCompanyccode(String companyccode) {
        this.companyccode = companyccode;
    }

    public String getCompanydesp() {
        return companydesp;
    }

    public void setCompanydesp(String companydesp) {
        this.companydesp = companydesp;
    }

    public String getCstcode() {
        return cstcode;
    }

    public void setCstcode(String cstcode) {
        this.cstcode = cstcode;
    }

    public String getCdtdesp() {
        return cdtdesp;
    }

    public void setCdtdesp(String cdtdesp) {
        this.cdtdesp = cdtdesp;
    }

    public String getOperationtypecode() {
        return operationtypecode;
    }

    public void setOperationtypecode(String operationtypecode) {
        this.operationtypecode = operationtypecode;
    }

    public String getOperationtype() {
        return operationtype;
    }

    public void setOperationtype(String operationtype) {
        this.operationtype = operationtype;
    }

    public Date getLastupdatedate() {
        return lastupdatedate;
    }

    public void setLastupdatedate(Date lastupdatedate) {
        this.lastupdatedate = lastupdatedate;
    }

    public String getAttribute1() {
        return attribute1;
    }

    public void setAttribute1(String attribute1) {
        this.attribute1 = attribute1;
    }

    public String getAttribute2() {
        return attribute2;
    }

    public void setAttribute2(String attribute2) {
        this.attribute2 = attribute2;
    }

    public String getAttribute3() {
        return attribute3;
    }

    public void setAttribute3(String attribute3) {
        this.attribute3 = attribute3;
    }

    public String getAttribute4() {
        return attribute4;
    }

    public void setAttribute4(String attribute4) {
        this.attribute4 = attribute4;
    }

    public String getAttribute5() {
        return attribute5;
    }

    public void setAttribute5(String attribute5) {
        this.attribute5 = attribute5;
    }

    public String getOutputext() {
        return outputext;
    }

    public void setOutputext(String outputext) {
        this.outputext = outputext;
    }

    public Date getCreatetime() {
        return createtime;
    }

    public void setCreatetime(Date createtime) {
        this.createtime = createtime;
    }

    public Date getUpdatetime() {
        return updatetime;
    }

    public void setUpdatetime(Date updatetime) {
        this.updatetime = updatetime;
    }
}
