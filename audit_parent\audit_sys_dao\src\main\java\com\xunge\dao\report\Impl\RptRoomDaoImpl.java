package com.xunge.dao.report.Impl;

import com.xunge.dao.AbstractBaseDao;
import com.xunge.dao.report.IrptRoomDao;

import java.util.List;
import java.util.Map;

public class RptRoomDaoImpl extends AbstractBaseDao implements IrptRoomDao {

    final String Namespace = "com.xunge.mapping.RptRoomVOMapper.";

    @Override
    public List<Map<String, Object>> queryRoomByPrv(Map<String, Object> map) {
        return this.getSqlSession().selectList(Namespace + "queryRoomByPrv", map);
    }

    @Override
    public List<Map<String, Object>> queryRoomByPreg(Map<String, Object> map) {
        return this.getSqlSession().selectList(Namespace + "queryRoomByPreg", map);
    }

    @Override
    public List<Map<String, Object>> queryRoom(Map<String, Object> map) {
        return this.getSqlSession().selectList(Namespace + "queryRoom", map);
    }
}