package com.xunge.dao.system.sms;


import com.xunge.model.system.sms.SysSmssendRecord;
import org.apache.ibatis.annotations.Param;

import java.util.Date;

public interface SysSmssendRecordDao {
    int deleteByPrimaryKey(Integer id);

    int insert(SysSmssendRecord record);

    int insertSelective(SysSmssendRecord record);

    SysSmssendRecord selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(SysSmssendRecord record);

    int updateByPrimaryKey(SysSmssendRecord record);

    int queryDayCount(@Param("phone") String phone,@Param("date") Date date );

    int queryMinuteCount(@Param("phone")String phone,@Param("date") Date date );
}