package com.xunge.dao.yearly;

import com.xunge.model.report.RptEleIndexSingleStationOfYearVO;
import com.xunge.model.report.RptPrvEleBasesiteVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

@Mapper
public interface RptSiteCostOfYearMapper {

    public List<RptPrvEleBasesiteVo> querySiteCostAll(Map<String, Object> map);

    public List<RptPrvEleBasesiteVo> querySiteCostByPrvId(Map<String, Object> map);

    public List<RptPrvEleBasesiteVo> querySiteCostByPregId(Map<String, Object> map);

    public List<RptPrvEleBasesiteVo> querySiteCostAllTotal(Map<String, Object> map);

    public List<RptPrvEleBasesiteVo> querySiteCostByPrvIdTotal(Map<String, Object> map);

    public List<RptPrvEleBasesiteVo> querySiteCostByPregIdTotal(Map<String, Object> map);

    List<RptEleIndexSingleStationOfYearVO> queryRptEleIndexSingleStationOfYearVOList(@Param("year") int year,
                                                                                     @Param("prvId") String prvId, @Param("pregId") String pregId, @Param("regIds") List<String> regIds);
}
