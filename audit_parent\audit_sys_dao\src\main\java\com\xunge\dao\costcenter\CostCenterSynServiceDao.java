package com.xunge.dao.costcenter;

import com.xunge.model.basedata.colletion.TaskHistoryInfoVO;
import com.xunge.model.contract.SynConfig;
import com.xunge.model.costcenter.CostCenterVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

public interface CostCenterSynServiceDao {

    void updateLastSynTime(@Param("updateTime") String updateTime, @Param("signType") String signType);

    SynConfig getSnyConfig(@Param("signType") String signType);

    List<String> getDataCode(@Param("codes") Set<String> codes, @Param("signType") String signType);

    void insertTaskInfo(TaskHistoryInfoVO taskHistory);

    void insertCostCenter(List<CostCenterVO> tmp);

    void updateCostCenter(CostCenterVO costCenterVO);

	CostCenterVO queryCostCenterByCode(CostCenterVO costCenterVO);

}