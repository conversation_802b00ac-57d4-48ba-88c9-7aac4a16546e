package com.xunge.dao.selfrent.accrual;

import com.github.pagehelper.PageInfo;
import com.xunge.core.page.Page;
import com.xunge.model.selfrent.accrual.RentAccrualSummaryVO;
import com.xunge.model.selfrent.accrual.RentAccrualVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * @创建人 LiangCheng
 * @创建时间 2021/8/25 0025
 * @描述：
 */
public interface IRentAccrualSummaryDao {

    List<String> queryRentAccrualSupplierIdAll(Map<String, Object> maps);

    void saveRentAccrualSummary(RentAccrualSummaryVO rentAccrualSummary);

    PageInfo<RentAccrualSummaryVO> queryRentAccrualSummaryVO(Map<String, Object> map, int pageNumber, int pageSize);

    RentAccrualSummaryVO queryRentAccrualSummaryById(Map<String, Object> map);

    void editRentAccrualSummary(RentAccrualSummaryVO rentAccrualSummary);

    void delRentAccrualSummary(RentAccrualSummaryVO rentAccrualSummary);

    List<RentAccrualSummaryVO> queryRentAccrualSummaryList(Map<String, Object> map);

    List<RentAccrualSummaryVO> queryRentAccrualSummaryListByIds(List<String> ids);

    List<RentAccrualSummaryVO> queryRentAccrualUpper(Map<String, Object> map);

}
