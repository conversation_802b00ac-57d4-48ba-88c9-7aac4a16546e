package com.xunge.dao.selfelec.elecaudit;

import com.xunge.core.page.Page;
import com.xunge.model.selfelec.dto.NoteConfigBean;
import com.xunge.model.selfelec.vo.EleBenchmarkParaConfigVO;
import com.xunge.model.selfelec.vo.ModelParamSetVO;
import com.xunge.model.selfelec.vo.ModelParamSetVONew;

import java.util.List;
import java.util.Map;

public interface IModelParamSetDao {
    Map<String, Object> selectELeConfig();

    public Page<ModelParamSetVO> queryAll(Map<String, Object> paramMap);

    public List<ModelParamSetVO> queryAllNoPage(Map<String, Object> paramMap);

    public String updateById(Map<String, Object> paramMap);

    public void export();

    public String insertModel(Map<String, Object> paramMap);

    /**
     * 查询每省配置的标杆配置信息
     *
     * @param paramMap
     * @return
     */
    public List<EleBenchmarkParaConfigVO> queryBenchmarkParaConfig(Map<String, Object> paramMap);

    /**
     * 新增每省配置的标杆信息
     *
     * @param ids
     * @param prvId
     * @return
     */
    public int insertBenchmarkParaConfig(EleBenchmarkParaConfigVO eleBenchmarkParaConfigVO);

    /**
     * 修改每省配置的标杆信息
     *
     * @param ids
     * @param prvId
     * @return
     */
    public int updateBenchmarkParaConfig(EleBenchmarkParaConfigVO eleBenchmarkParaConfigVO);

    int deleteAll();

    int insert(Map<String, Object> map);

    List<ModelParamSetVONew> queryAllNoPageNew(Map<String, Object> paramMap);

    int insertModelNew(ModelParamSetVONew m);

    int deleteAllByregIdAndMonth(Map<String, Object> param);

    List<ModelParamSetVONew> queryAllNoPageNewexport(Map<String, Object> map);

    int insertLog(Map<String, Object> map);

    List<Map<String, Object>> selectLog();

    /**
     * 根据条件查缴费标杆配置变更日志记录
     *
     * @param map {"time":"2020-02-02"} 时间
     * @return 返回一条记录
     */
    Map<String, Object> selectBenchmarkChangeLog(Map<String, Object> map);

    List<NoteConfigBean> selectNoteConfig(Map<String, Object> param);

    int saveNoteConfig(NoteConfigBean noteConfigBean);

    int deleteNoteConfig(String[] idArray);

    int updateNoteConfigState(Map<String, Object> paramMap);

    int updateNoteConfig(NoteConfigBean noteConfigBean);
}
