package com.xunge.dao.selfelec;

import com.xunge.model.selfelec.EleContractbillaccount;
import com.xunge.model.selfelec.VEleContract;
import com.xunge.model.selfelec.VEleContractCuring;
import com.xunge.model.selfelec.VEleContractExample;
import com.xunge.model.selfelec.focuscontract.ExportVEleContract;
import com.xunge.model.selfrent.billAccount.RentBillAccountContractVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface VEleContractMapper {
    
    int countByExample(VEleContractExample example);

    
    int deleteByExample(VEleContractExample example);

    
    int insert(VEleContract record);

    
    int insertSelective(VEleContract record);

    
    List<VEleContract> selectByExample(VEleContractExample example);

    List<VEleContract> selectByExample(Map<String, Object> map);

    
    int updateByExampleSelective(@Param("record") VEleContract record, @Param("example") VEleContractExample example);

    
    int updateByExample(@Param("record") VEleContract record, @Param("example") VEleContractExample example);

    List<VEleContract> queryAllElecContract(Map<String, Object> paramMap);

    List<VEleContract> queryEleccontractWarningList(Map<String, Object> paramMap);
    List<VEleContract> exportEleccontractWarningList(Map<String, Object> paramMap);

    List<ExportVEleContract> exportQueryEleccontractWarningList(Map<String, Object> paramMap);


    List<VEleContract> queryAllElecContractExportPage(Map<String, Object> map);

    /**
     * @param paramMap 电费合同查询
     * @return
     */
    List<VEleContract> queryAllEleccontractList(Map<String, Object> paramMap);

    /**
     * 电费固话合同查询
     */
    List<VEleContract> queryEleCuringContractList(Map<String, Object> paramMap);

    List<Map<String, Object>> queryBillaccountCodeForElecontract(@Param("elecontractIds") List<String> elecontractIds);

    List<VEleContractCuring> queryAllElecContractCuring(Map<String, Object> paramMap);

    VEleContract queryVEleContractVOById(Map<String, Object> paramMap);

    VEleContract queryVEleContractVOByEleconstractId(Map<String, Object> paramMap);

    int updateContractVO(Map<String, Object> paramMap);

    VEleContract queryOneElecContractById(Map<String, Object> paraMap);
    VEleContract queryRelContract(Map<String, Object> paraMap);

    VEleContract queryFinanceOneElecContractById(Map<String, Object> paraMap);

    /**
     * @description 根据电费合同id查询此条合同最终缴费日期
     * <AUTHOR>
     * @date 创建时间：2017年9月7日
     */
    public String getElePaymentEnddate(Map<String, Object> paraMap);

    /**
     * @description 查询报账点和合同关联表信息
     * <AUTHOR>
     * @date 创建时间：2017年9月8日
     */
    List<String> queryBillaccountContract(Map<String, Object> map);

    /**
     * @description 批量修改合同状态
     * <AUTHOR>
     * @date 创建时间：2017年10月19日
     */
    public boolean updateContractState(Map<String, Object> paramMap);

    /**
     * @description 查询所有需要修改系统统一编码的固化信息
     * <AUTHOR>
     * @date 创建时间：2018年1月30日
     */
    public List<VEleContract> queryContractBySysCode(Map<String, Object> paraMap);

    /**
     * @description 批量新增电费固化信息
     * <AUTHOR>
     * @date 创建时间：2018年2月6日
     */
    public int insertElecContractInfoList(Map<String, Object> paraMap);

    /**
     * @description 批量修改电费固化信息
     * <AUTHOR>
     * @date 创建时间：2018年2月6日
     */
    public int updateElecContractInfoList(Map<String, Object> paraMap);

    /**
     * @description 修改电费固化信息
     * <AUTHOR>
     * @date 创建时间：2018年2月6日
     */
    public int updateElecContractInfo(Map<String, Object> paraMap);

    /**
     * @description 批量修改电费固化编码
     * <AUTHOR>
     * @date 创建时间：2018年10月24日
     */
    public int updateContractCodeList(Map<String, Object> paraMap);

    /**
     * @description 根据省份id查询所有合同
     * <AUTHOR>
     * @date 创建时间：2018年1月30日
     */
    List<VEleContractCuring> queryAllElecContractCuringByPrvId(Map<String, Object> paraMap);

    List<VEleContract> queryEleContractByPrvId(Map<String, Object> paraMap);

    public VEleContract queryByDatContractId(String contactId);

    /**
     * 检查数据是否存在
     *
     * @param paraMap
     * @return
     */
    int countById(Map<String, Object> paraMap);

    /**
     * 通过合同ID查询合同信息
     *
     * @param paraMap
     * @return
     */
    VEleContract queryBeanById(Map<String, Object> paraMap);

    List<VEleContractCuring> queryDContractIdList();

    List<VEleContractCuring> queryZContractIdList();

    int saveEleccontractBackups(VEleContract eleccontractBackups);

    List<EleContractbillaccount> queryStateByContractIds(Map<String, Object> paraMap);

    List<RentBillAccountContractVO> queryStateByRentContractIds(Map<String, Object> paraMap);

    int saveEleccontractHistory(VEleContract eleccontractHistory);

    int deleteBackups(@Param("billaccountpaymentdetailId") String billaccountpaymentdetailId);

    VEleContract queryElecContractIdByPayment(Map<String, Object> params);

    VEleContract queryElecContractIdByVerification(Map<String, Object> params);

    int deleteHistory(@Param("verificationId") String verificationId);

    int queryGHModel(Map<String, Object> map);

    String queryModel(Map<String, Object> map);

    /**
     * 查询合同
     *
     * @param cond
     * @return
     */
    List<VEleContract> queryDatContract(Map<String, Object> cond);

    VEleContract queryBillInfoByBillAccountId(String billAccountId);

    VEleContract queryEleContractBackupsByBillaccountDetailId(String billaccountpaymentdetailId);

    void refreshUpdateEleContractBackup(VEleContract eleccontractBackups);

    /**
     * 校验合同在缴费录入后是否更换过单价取值模式-后付费
     * @param map
     * @return
     */
    Map<String, Object> selectEPriceModel(Map<String, String> map);
    /**
     * 校验合同在缴费录入后是否更换过单价取值模式-核销
     * @param map
     * @return
     */
    Map<String, Object> selectVPriceModel(Map<String, String> map);

    String selectAuditNodeName(String taskId);

    /***
     * 根据合同id查询供电方式(审核通过)
     * @param elecontractId 合同id
     * @return 供电方式
     */
    String querySupplyMethodById(@Param("elecontractId") String elecontractId);
    VEleContract queryContractInfo(Map<String, Object> paraMap);
    VEleContract queryContractInfoBackup(Map<String, Object> paraMap);
    VEleContract queryContractInfoHistory(Map<String, Object> paramMap);

    List<VEleContract> queryContractInfoBackups(@Param("ids") List<String> billaccountpaymentdetailIds);
    List<VEleContract> queryContractInfoHistorys(@Param("ids") List<String> billaccountpaymentdetailIds);
    List<VEleContract> queryContractInfoVls(@Param("ids") List<String> loanIds);
}