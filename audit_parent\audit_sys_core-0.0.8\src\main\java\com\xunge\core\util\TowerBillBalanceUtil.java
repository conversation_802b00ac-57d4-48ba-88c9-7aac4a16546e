package com.xunge.core.util;

import com.xunge.core.exception.ParameterException;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @descript生成账单计算公式
 * @date 2017-08-11 18:12:14
 */
public class TowerBillBalanceUtil {

    // 折损率
    public static final double DEPRECIATION_RATE = 0.02;
    // 毛利率加成
    public static final double MARGIN_RATE = 0.15;
    // 增值税税率
    public static final double VAT_RATE = 0.0;
    // 电力毛利率加成
    public static final double ELECTRIC_MARGIN_RATE = 0.05;
    // 电力引入资产折旧年限
    public static final int ELECTRIC_DEPRECIATION_TIME = 10;

    public static final double RATE = 10000;
    /**
     * 数字 -12
     */
    public static int NUMBER_12 = 12;

    /**
     * 系统计算铁塔基准价格（元/年）（不含税）
     *
     * @param towerPrice        铁塔标准建造成本
     * @param depreciationTime  铁塔折旧年限
     * @param discount          折扣比例
     * @param depreciationRate  折损率
     * @param marginRate        毛利率加成
     * @param towerShareDis     铁塔共享折扣
     * @param unitProductNumber 产品单元数
     * @param vatRate           增值税税率
     * @return
     */
    public static BigDecimal calcComputeTowerPrice(BigDecimal towerPrice, BigDecimal depreciationTime, BigDecimal discount, double depreciationRate,
                                                   double marginRate, BigDecimal towerShareDis, BigDecimal unitProductNumber, double vatRate) {
        return towerPrice.multiply(BigDecimal.valueOf(RATE)).divide(depreciationTime, 6, BigDecimal.ROUND_HALF_UP).multiply(discount)
                .multiply(BigDecimal.valueOf((1 + depreciationRate))).multiply(BigDecimal.valueOf((1 + marginRate))).multiply(towerShareDis)
                .multiply(unitProductNumber).multiply(BigDecimal.valueOf((1 + vatRate)));
    }

    /**
     * 系统计算机房及配套基准价格（元/年）（不含税）
     *
     * @param roomPrice               机房标准建造价格
     * @param roomDepreciationTime    机房折旧年限
     * @param supportingPrice         配套标准建造成本
     * @param supportDepreciationTime 配套折旧年限
     * @param discount                折扣比例
     * @param depreciationRate        折损率
     * @param marginRate              毛利率加成
     * @param roomSupportingShareDis  机房及配套共享折扣
     * @param unitProductNumber       产品单元数
     * @param vatRate                 增值税税率
     * @return
     */
    public static BigDecimal calcRoomAndSupportingPrice(BigDecimal roomPrice, BigDecimal roomDepreciationTime, BigDecimal supportingPrice,
                                                        BigDecimal supportDepreciationTime, BigDecimal discount, double depreciationRate, double marginRate, BigDecimal roomSupportingShareDis,
                                                        BigDecimal unitProductNumber, double vatRate) {
        BigDecimal total = roomPrice.multiply(BigDecimal.valueOf(RATE)).divide(roomDepreciationTime, 4, BigDecimal.ROUND_HALF_UP)
                .add(supportingPrice.multiply(BigDecimal.valueOf(RATE)).divide(supportDepreciationTime, 4, BigDecimal.ROUND_HALF_UP));
        return total.multiply(discount).multiply(BigDecimal.valueOf((1 + depreciationRate))).multiply(BigDecimal.valueOf((1 + marginRate)))
                .multiply(roomSupportingShareDis).multiply(unitProductNumber).multiply(BigDecimal.valueOf((1 + vatRate)));
    }

    /**
     * 系统计算维护费（元/年）（不含税）
     *
     * @param originalMaintenanceFee 维护费原始录入值
     * @param marginRate             毛利率加成
     * @param maintenanceFeeDis      维护费折扣
     * @param unitProductNumber      产品单元数
     * @param vatRate                增值税税率
     * @return
     */
    public static BigDecimal calcMaintenanceFee(BigDecimal originalMaintenanceFee, double marginRate, BigDecimal maintenanceFeeDis,
                                                BigDecimal unitProductNumber, double vatRate) {
        return originalMaintenanceFee.multiply(BigDecimal.valueOf((1 + marginRate))).multiply(maintenanceFeeDis).multiply(unitProductNumber)
                .multiply(BigDecimal.valueOf((1 + vatRate)));
    }

    /**
     * 系统计算场地费（元/年）（不含税）
     *
     * @param originalStageFee 场地费原始录入值
     * @param stageFeeDis      场地费折扣
     * @param vatRate          增值税税率
     * @rturn
     */
    public static BigDecimal calcStageFee(BigDecimal originalStageFee, BigDecimal stageFeeDis, double vatRate) {
        return originalStageFee.multiply(stageFeeDis).multiply(BigDecimal.valueOf((1 + vatRate)));
    }

    /**
     * 系统计算电力引入费（元/年）（不含税）
     *
     * @param originalElectricImportFee 电力引入费原始录入值
     * @param electricDepreciationTime  电力引入资产折旧年限
     * @param electricMarginRate        电力毛利率加成
     * @param electricImportFeeDis      电力引入费折扣
     * @param vatRate                   增值税税率
     * @return
     */
    public static BigDecimal calcElectricImportFee(BigDecimal originalElectricImportFee, BigDecimal electricDepreciationTime,
                                                   double electricMarginRate, BigDecimal electricImportFeeDis, double vatRate) {
        return originalElectricImportFee.divide(electricDepreciationTime, 4, BigDecimal.ROUND_HALF_UP)
                .multiply(BigDecimal.valueOf((1 + electricMarginRate))).multiply(electricImportFeeDis).multiply(BigDecimal.valueOf((1 + vatRate)));
    }

    /**
     * 系统计算产品服务费合计（元/年）不含税
     *
     * @param computeTowerPrice        系统计算铁塔基准价格（元/年）（不含税）
     * @param roomAndSupportingPrice   系统计算机房及配套基准价格（元/年）（不含税）
     * @param computeTotalActualAmount 系统计算维护费（元/年）（不含税）
     * @param stageFee                 系统计算场地费（元/年）（不含税）
     * @param electricImportFee        系统计算电力引入费（元/年）（不含税）
     * @param otherFee                 系统计算其他费用（元/年）（不含税）
     * @param bbuFee                   BBU安装在铁塔机房的服务费（元/年）（不含税）
     * @param oilGeneratorElectricFee  系统计算油机发电服务费（元/年）（不含税）
     * @param electricProtectionFee    包干电费（元/年）（不含税）
     * @param hightLevelFee            超过10%高等级服务站址额外维护服务费（元/年）（不含税）
     * @param times                    月系数
     * @return
     */
    public static BigDecimal calcTotalAmountMonthOut(BigDecimal computeTowerPrice, BigDecimal roomAndSupportingPrice,
                                                     BigDecimal computeTotalActualAmount, BigDecimal stageFee, BigDecimal electricImportFee, BigDecimal otherFee, BigDecimal bbuFee,
                                                     BigDecimal oilGeneratorElectricFee, BigDecimal electricProtectionFee, BigDecimal hightLevelFee, BigDecimal times) {
        return getCalc(computeTowerPrice, times).add(getCalc(roomAndSupportingPrice, times)).add(getCalc(computeTotalActualAmount, times))
                .add(getCalc(stageFee, times)).add(getCalc(electricImportFee, times)).add(getCalc(otherFee, times)).add(getCalc(bbuFee, times))
                .add(getCalc(oilGeneratorElectricFee, times)).add(getCalc(electricProtectionFee, times)).add(getCalc(hightLevelFee, times));
    }

    /**
     * 系统计算产品服务费合计（元/年）含税
     *
     * @param totalAmount 含税服务费合计
     * @return
     */
    public static BigDecimal calcTotalAmount(BigDecimal totalAmount) {
        return totalAmount.multiply(BigDecimal.valueOf(1.06));
    }

    /**
     * 机房
     *
     * @param roomPrice
     * @param roomDepreciationTime
     * @param discount
     * @param depreciationRate
     * @param marginRate
     * @param roomSupportingShareDis
     * @param unitProductNumber
     * @param vatRate
     * @return
     */
    public static BigDecimal calcRoomPrice(BigDecimal roomPrice, BigDecimal roomDepreciationTime, BigDecimal discount, double depreciationRate,
                                           double marginRate, BigDecimal roomSupportingShareDis, BigDecimal unitProductNumber, double vatRate) {
        return roomPrice.multiply(BigDecimal.valueOf(RATE)).divide(roomDepreciationTime, 6, BigDecimal.ROUND_HALF_UP).multiply(discount)
                .multiply(BigDecimal.valueOf((1 + depreciationRate))).multiply(BigDecimal.valueOf((1 + marginRate))).multiply(roomSupportingShareDis)
                .multiply(unitProductNumber).multiply(BigDecimal.valueOf((1 + vatRate)));
    }

    /**
     * 配套
     *
     * @param supportingPrice
     * @param supportDepreciationTime
     * @param discount
     * @param depreciationRate
     * @param marginRate
     * @param roomSupportingShareDis
     * @param unitProductNumber
     * @param vatRate
     * @return
     */
    public static BigDecimal calcSupportingPrice(BigDecimal supportingPrice, BigDecimal supportDepreciationTime, BigDecimal discount,
                                                 double depreciationRate, double marginRate, BigDecimal roomSupportingShareDis, BigDecimal unitProductNumber, double vatRate) {
        return supportingPrice.multiply(BigDecimal.valueOf(RATE)).divide(supportDepreciationTime, 6, BigDecimal.ROUND_HALF_UP).multiply(discount)
                .multiply(BigDecimal.valueOf((1 + depreciationRate))).multiply(BigDecimal.valueOf((1 + marginRate))).multiply(roomSupportingShareDis)
                .multiply(unitProductNumber).multiply(BigDecimal.valueOf((1 + vatRate)));
    }

    /**
     * @param v1    被除数
     * @param v2    除数
     * @param scale 精确位数
     * @return
     */
    private static BigDecimal div(BigDecimal v1, int v2, int scale) {
        if (scale < 0) {
            throw new ParameterException("The scale must be a positive integer or zero");
        }
        BigDecimal b2 = new BigDecimal(Double.toString(v2));
        return v1.divide(b2, scale, BigDecimal.ROUND_HALF_UP);
    }

    /**
     * 计算不足一个自然月的费用
     *
     * @param obj
     * @param times
     * @return
     */
    private static BigDecimal getCalc(Object obj, BigDecimal times) {
        if (null == obj) {
            return BigDecimal.valueOf(0.0);
        } else {
            BigDecimal number = new BigDecimal(obj.toString());
            return div(number, NUMBER_12, 4).multiply(times);
        }
    }

    /**
     * 月费用 = 年费用 / 12  保留四位小数
     *
     * @param v1
     * @return
     */
    public static BigDecimal monthOfCost(BigDecimal v1) {

        return rounded(v1.divide(getBigDecimalValue(NUMBER_12), 4, BigDecimal.ROUND_DOWN));
    }

    private static BigDecimal getBigDecimalValue(Object value) {
        BigDecimal result = new BigDecimal("0.0000");
        if (null != value && value.toString().trim().length() > 0) {
            result = new BigDecimal(value.toString());
        }
        return result;
    }

    /**
     * 对小数点第三位后的一位进行四舍五入，再舍掉第三位
     *
     * @param number
     * @return
     */
    public static BigDecimal rounded(BigDecimal number) {
        if (number.compareTo(new BigDecimal(0)) == 0) {
            return number;
        } else {
            BigDecimal bigDecimal = number.setScale(3, BigDecimal.ROUND_HALF_UP);
            number = bigDecimal.setScale(2, BigDecimal.ROUND_DOWN);
            return number;
        }
    }
}
