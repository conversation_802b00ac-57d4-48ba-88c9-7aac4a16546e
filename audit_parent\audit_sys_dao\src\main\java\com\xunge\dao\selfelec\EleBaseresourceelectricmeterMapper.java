package com.xunge.dao.selfelec;

import com.xunge.model.selfelec.EleBaseresourceelectricmeter;
import com.xunge.model.selfelec.EleBaseresourceelectricmeterExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface EleBaseresourceelectricmeterMapper {
    
    int updateRelationBillaacountId(@Param("billaacountId") String billaacountId);


    int countByExample(EleBaseresourceelectricmeterExample example);

    int countByExampleForTW(EleBaseresourceelectricmeter example);


    
    int deleteByExample(EleBaseresourceelectricmeterExample example);

    
    int deleteByPrimaryKey(String baseresourceelectricmeterId);

    
    int insert(EleBaseresourceelectricmeter record);

    
    int insertSelective(EleBaseresourceelectricmeter record);

    
    List<EleBaseresourceelectricmeter> selectByExample(EleBaseresourceelectricmeterExample example);

    
    EleBaseresourceelectricmeter selectByPrimaryKey(String baseresourceelectricmeterId);

    List<EleBaseresourceelectricmeter> selectEleBaseresourceelectricmeter(EleBaseresourceelectricmeter eleBaseresourceelectricmeter);

    
    int updateByExampleSelective(@Param("record") EleBaseresourceelectricmeter record,
                                 @Param("example") EleBaseresourceelectricmeterExample example);

    
    int updateByExample(@Param("record") EleBaseresourceelectricmeter record, @Param("example") EleBaseresourceelectricmeterExample example);

    
    int updateByPrimaryKeySelective(EleBaseresourceelectricmeter record);

    int unRelationOldEleMeter(@Param("oldBaseresourceelectricmeterIds") List<String> oldBaseresourceelectricmeterIds , @Param("auditingState") Integer auditingState);
    int updateNewMeterRelationStartdate(@Param("baseresourceelectricmeterId") String baseresourceelectricmeterId);
    
    int updateByPrimaryKey(EleBaseresourceelectricmeter record);

    /**
     * 删除资源点与电表关联关系
     *
     * @param paraMap
     * @return
     */
    int deleteRescoureMeter(Map<String, Object> paraMap);

    /**
     * 报账点删除审核通过，资源点与电表关联关系调整：删除审核通过=4
     *
     * @param paraMap
     * @return
     */
    int billDeleteAuditRescoureMeter(Map<String, Object> paraMap);

    /**
     * 根据报账点编码获取资源点编码
     *
     * @param billaccountId
     * @return
     */
    List<String> queryBaseresourceIdByBillaccountId(String billaccountId);

    /**
     * 根据报账点编码获取资源点编码
     *
     * @param billaccountId
     * @return
     */
    List<String> queryDeleteAuditBaseresourceIdByBillaccountId(String billaccountId);

    /**
     * 根据电表ID获取资源信息
     *
     * @param meterId
     * @return
     */
    List<String> queryBaseresourceIdBymeterId(Map<String, String> map);

    /**
     * 根据电表ID获取资源信息
     *
     * @param meterId
     * @return
     */
    List<String> queryBaseresourceIdBymeterIds(Map<String, Object> map);

    /**
     * 修改电表
     *
     * @param record
     * @return
     * <AUTHOR>
     */
    int updateReplaceMeter(List<EleBaseresourceelectricmeter> records);

    /**
     * 修改数据状态
     *
     * @param param
     * @return
     * <AUTHOR>
     */
    int updateStatus(Map<String, Object> param);

    /**
     * 根据电表ID修改电表资源关联关系表中 电表分摊比例
     *
     * @param record
     * @return
     */
    int updateElectricmeterRatio(EleBaseresourceelectricmeter record);

    /**
     * 根据报账点ID查询合同中 移动分摊比例
     *
     * @param meterId
     * @return
     */
    Map<String, Object> queryCmccRatioFromEleContract(String billaccountId);

    /**
     * 根据电表ID查询资源电表中间关系表中 移动分摊比例
     *
     * @param meterId
     * @return
     */
    Map<String, Object> queryCmccRatioFromBaseresourceMeter(String meterId);

    /**
     * 删除共享电表多余的关联关系
     *
     * @param
     * @return
     * @date 2018年08月15日
     * <AUTHOR>
     */
    void deleteShareRelation(String meterId);

    /**
     * 当审核通过时更新当前报账点电表关联关系
     */
    void updateWhenAuditPass(@Param("auditPassDate") String auditPassDate, @Param("meterType") Integer meterType, @Param("isShare") Integer isShare, @Param("baseresourceelectricmeterId") String baseresourceelectricmeterId);

    /**
     * 根据报账点ID查询未显示的电表关联
     * @param billaccountId
     * @return
     */
    List<EleBaseresourceelectricmeter> selectNoShowByBillaccountId(@Param("billaccountId") String billaccountId);

    /**
     * 根据电表ID查询关联需要的电表信息
     * @param meterId
     * @return
     */
    EleBaseresourceelectricmeter queryMeterForRelation(@Param("meterId") String meterId);

    /**
     * 当审核通过时若之前有审核后删除的记录，记录解除关联时间
     *
     * @param paraMap
     */
    void updateLastRelationEnddate(Map<String, Object> paraMap);

    /**
     * 获取报账点关联的电表id
     * @param billaccountId
     * @return
     */
    List<String> queryMeterIdsByBillAccountId(@Param("billaccountId") String billaccountId);

}