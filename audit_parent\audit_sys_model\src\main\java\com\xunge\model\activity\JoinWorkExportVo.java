package com.xunge.model.activity;

import cn.afterturn.easypoi.excel.annotation.Excel;

import java.io.Serializable;

/**
 * @ClassName JoinWorkExportVo
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/3/10 14:17
 */
public class JoinWorkExportVo extends MyWorkExportVo implements Serializable {
    private static final long serialVersionUID = 7231730481864028599L;
    @Excel(name = "参与类别", orderNum = "1", replace = {"-_null"})
    private String operateType;

    public String getOperateType() {
        return operateType;
    }

    public void setOperateType(String operateType) {
        this.operateType = operateType;
    }
}
