package com.xunge.dao.selfelec;

import com.xunge.model.selfelec.EleContractbillaccount;
import com.xunge.model.selfelec.EleContractbillaccountExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface EleContractbillaccountMapper {
    
    int countByExample(EleContractbillaccountExample example);

    
    int deleteByExample(EleContractbillaccountExample example);

    
    int deleteByPrimaryKey(String elebillaccountcontractId);

    
    int insert(EleContractbillaccount record);

    
    int insertSelective(EleContractbillaccount record);

    
    List<EleContractbillaccount> selectByExample(EleContractbillaccountExample example);

    
    EleContractbillaccount selectByPrimaryKey(String elebillaccountcontractId);

    
    int updateByExampleSelective(@Param("record") EleContractbillaccount record, @Param("example") EleContractbillaccountExample example);

    
    int updateByExample(@Param("record") EleContractbillaccount record, @Param("example") EleContractbillaccountExample example);

    
    int updateByPrimaryKeySelective(EleContractbillaccount record);

    
    int updateByPrimaryKey(EleContractbillaccount record);

    /**
     * 批量删除报账点与合同的关联关系
     *
     * @param paraMap
     * @return
     */
    int deleteSpecialBillaccountContract(Map<String, Object> paraMap);


    /**
     * @param elecontractId
     * @param @param        elecontractIds    设定文件
     * @return void    返回类型
     * @throws
     * @Title: updateComtractInfo
     * @Description: TODO(这里用一句话描述这个方法的作用)
     */

    void updateComtractInfo(@Param("elecontractId") String elecontractId, @Param("elecontractIds") List<String> elecontractIds);

    /**
     * 当审核通过时更新当前报账点合同关联关系
     */
    void updateWhenAuditPass(@Param("auditPassDate") String auditPassDate, @Param("isDownshare") Integer isDownshare, @Param("contractState") String contractState, @Param("priceType") Integer priceType, @Param("elebillaccountcontractId") String elebillaccountcontractId);

    /**
     * 查询当前报账点合同关联关系 is_show is null
     * @param billaccountId
     * @return
     */
    EleContractbillaccount selectNoShowByBillaccountId(@Param("billaccountId") String billaccountId);

    /**
     * 查询关联合同信息
     * @param elecontractId
     * @return
     */
    EleContractbillaccount queryContractInfoForRelation(@Param("elecontractId") String elecontractId);


    /**
     * 当审核通过时若之前有审核后删除的记录，记录解除关联时间
     *
     * @param paraMap
     */
    void updateLastRelationEnddate(Map<String, Object> paraMap);
}