package com.xunge.dao.selfrent.warning;

import com.xunge.dto.selfelec.AuthorityUser;
import com.xunge.model.selfrent.warning.RentAnomalousResourceVO;
import com.xunge.model.selfrent.warning.RentAnomalousWarningDetailVo;
import com.xunge.model.selfrent.warning.RentAnomalousWarningRecoveryExport;
import com.xunge.model.selfrent.warning.RentAnomalousWarningVO;
import com.xunge.model.selfrent.warning.query.RentAnomalousWarningExportQueryDto;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.cursor.Cursor;

import java.util.Date;
import java.util.List;

/**
 * @创建人 LiangCheng
 * @创建时间 2019/10/8 0008
 * @描述：
 */
public interface RentAnomalousWarningMapper {

    public int updateAnomalousWarningStateById(RentAnomalousWarningVO vo);

    Cursor<RentAnomalousWarningDetailVo> queryRentAnomalousWarningDetailByCursor(@Param("dto") RentAnomalousWarningExportQueryDto rentAnomalousWarningQueryDto, @Param("user") AuthorityUser authorityUser);

    Cursor<RentAnomalousWarningVO> queryRentAnomalousWarningByCursor(@Param("dto") RentAnomalousWarningExportQueryDto rentAnomalousWarningQueryDto, @Param("user") AuthorityUser authorityUser);

    List<RentAnomalousResourceVO> queryRentAnomalousResourceList(@Param("resIdList") List<String> resIdList, @Param("abnormalDate") Date abnormalDate);

    String  queryRentAnomalousResourceIdListStr(@Param("id") Integer id);

    Cursor<RentAnomalousWarningRecoveryExport> queryRentAnomalousWarningRecoveryExportByCursor(@Param("dto") RentAnomalousWarningExportQueryDto rentAnomalousWarningQueryDto, @Param("user") AuthorityUser authorityUser);
}
