package com.xunge.comm.rent.accrual;

import java.util.Arrays;
import java.util.List;

public class RentAccrualMessageCommon {

    /**
     * 业务提示-计提单删除时状态不满足条件
     */
    public static final String RENT_ACCRUAL_DELETE_STAET_ERROR_MESSAGE = "计提单存在不是未提交状态的，不能删除！";

    /**
     * 业务提示-计提单提交审核时提交人卡控
     */
    public static final String RENT_ACCRUAL_SUBMIT_USER_ERROR_MESSAGE = "审批人和提交审核人不能是同一用户，请重新选择审批人!";

    /**
     * 业务提示-不同用户对同一计提单进行删除提交审核操作
     */
    public static final String RENT_ACCRUAL_DELETE_LOCK_ERROR_MESSAGE = "存在其他用户对计提单进行删除提交审核操作！";

    /**
     * 业务提示-计提单已存在流程
     */
    public static final String RENT_ACCRUAL_EXISTS_ACTIVITY_ERROR_MESSAGE = "计提单流程未完结，不能重新发起！";

    /**
     * 业务提示-计提单存在运行时异常，无法确定原因
     */
    public static final String RENT_ACCRUAL_RUNTIME_ERROR_MESSAGE = "计提单操作由于异常原因失败！";

    /**
     * 业务提示-计提单删除提交审核全部成功
     */
    public static final String RENT_ACCRUAL_DELETE_SUBMIT_ALL_SUCCESS = "计提单删除提交审核成功！";

    /**
     * 业务提示-计提单合同剩余金额校验失败
     */
    public static final String RENT_ACCRUAL_REMAIN_AMOUNT_LIMIT_ERROR_MESSAGE = "计提单金额超出合同剩余金额！";

    /**
     * 业务提示-计提单审核状态不符合条件
     */
    public static final String RENT_ACCRUAL_SUBMIT_AUDIT_STATE_ERROR_MESSAGE = "计提单审核状态不符合条件！只能对未提交的计提单提交审核！";

    /**
     * 业务提示-不同用户对同一计提单进行操作
     */
    public static final String RENT_ACCRUAL_LOCK_ERROR_MESSAGE = "存在其他用户对计提单进行操作！";

    /**
     * 业务提示-计提单提交审核全部成功
     */
    public static final String RENT_ACCRUAL_SUBMIT_ALL_SUCCESS = "计提单提交审核成功！";

    /**
     * 业务提示-本省计提模式未配置
     */
    public static final String RENT_ACCRUAL_MODE_EMPTY = "本省计提模式未配置！";

    /**
     * 业务提示-该报账点本月已存在到期计提单
     */
    public static final String RENT_BILLACCOUNT_EXPIRE_ACCRUAL_EXIST = "该报账点本月已存在到期计提单！";
}
