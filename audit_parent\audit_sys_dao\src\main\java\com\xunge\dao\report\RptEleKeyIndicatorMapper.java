package com.xunge.dao.report;

import com.xunge.model.report.RptEleKeyIndicatorVO;
import org.springframework.stereotype.Component;

@Component
public interface RptEleKeyIndicatorMapper {


    RptEleKeyIndicatorVO queryEleCosts(RptEleKeyIndicatorVO rptEleKeyIndicatorVO);

    RptEleKeyIndicatorVO queryPowerSupply(RptEleKeyIndicatorVO rptEleKeyIndicatorVO);

    RptEleKeyIndicatorVO queryBaseStationDegree(RptEleKeyIndicatorVO rptEleKeyIndicatorVO);

    RptEleKeyIndicatorVO queryBaseStationRent(RptEleKeyIndicatorVO rptEleKeyIndicatorVO);

    RptEleKeyIndicatorVO queryOtherBaseStationCharge(RptEleKeyIndicatorVO rptEleKeyIndicatorVO);
}
