package com.xunge.dao.selfelec;

import com.xunge.model.selfelec.VEleBillaccountcontract;
import com.xunge.model.selfelec.VEleBillaccountcontractExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface VEleBillaccountcontractMapper {
    
    int countByExample(VEleBillaccountcontractExample example);

    
    int deleteByExample(VEleBillaccountcontractExample example);

    
    int insert(VEleBillaccountcontract record);

    
    int insertSelective(VEleBillaccountcontract record);

    
    List<VEleBillaccountcontract> selectByExample(VEleBillaccountcontractExample example);

    List<VEleBillaccountcontract> selectByExampleFcontract(VEleBillaccountcontractExample example);

    
    int updateByExampleSelective(@Param("record") VEleBillaccountcontract record, @Param("example") VEleBillaccountcontractExample example);

    
    int updateByExample(@Param("record") VEleBillaccountcontract record, @Param("example") VEleBillaccountcontractExample example);

    /**
     * 根据报账点id查询合同状态
     *
     * @param billaccountId
     * @return
     */
    List<VEleBillaccountcontract> queryStateByBillaccountId(String billaccountId);

    /**
     * 根据报账点id查询合同缴费日期
     *
     * @param billaccountId
     * @return
     */
    public String queryPaymentperiodIdByBillaccountId(String billaccountId);

    /**
     * 首页全查缴费记录信息
     *
     * @param map
     * @return
     */
    public List<Map<String, String>> queryPaymentDetailByCondition(Map<String, Object> map);
}