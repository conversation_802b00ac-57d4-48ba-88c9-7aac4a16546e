package com.xunge.dao.selfrent.accrual;

import com.xunge.model.selfrent.accrual.AccrualOperateDateConfig;
import com.xunge.model.selfrent.accrual.RentAccrualCheckDto;
import com.xunge.model.selfrent.accrual.RentAccrualResourceCheck;
import com.xunge.model.selfrent.accrual.RentAccrualVO;
import com.xunge.model.selfrent.billamount.RentBillAmountHisPayVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * @创建人 LiangCheng
 * @创建时间 2021/08/13
 * @描述：
 */
public interface RentAccrualMapper {

    List<RentAccrualVO> queryBillaccountAccrualAll(Map<String, Object> map);

    List<RentAccrualVO> queryTowerBillaccountAccrualAll(Map<String, Object> map);

    Map<String, Object> queryMaxCode(Map<String, Object> map);

    /**
     * 查询对应账期到期计提单编码最大值
     * @param yearMonth
     * @return
     */
    int queryMaxNumForExpireAccrualCode(@Param("yearMonth") String yearMonth);

    void saveAccrual(@Param("List") List<RentAccrualVO> list);

    void saveExpireAccrual(RentAccrualVO vo);

    List<RentAccrualVO> queryBillaccountAccrualCECXAll(Map<String, Object> map);

    List<RentAccrualVO> queryTowerBillaccountAccrualCECXAll(Map<String, Object> map);

    int queryExistsRentAccraul();

    List<String> queryContractNotNeedAccrual(@Param("contractIds") List<String> contractIds);

    List<RentBillAmountHisPayVo> queryContractRemainingAmount(@Param("contractId")String contractId);

    List<RentAccrualVO> queryRentExpireAccrualBaseData(@Param("ids") List<String> ids, @Param("accrualType") Integer accrualType);

    List<RentAccrualVO> queryLastExpireAccrualInfoByAccountIds(@Param("accrualType") Integer accrualType, @Param("billAccountIds") List<String> billAccountIds, @Param("yearMonth") String yearMonth);


    AccrualOperateDateConfig getAccrualOperateDateConfigByMonth(@Param("month") String month, @Param("accrualType") Integer accrualType);

    /**
     * 查询计提单编码
     * @param idList
     * @param auditingState
     * @return
     */
    List<String> queryAccrualCodes(@Param("idList") List<String> idList, @Param("auditingState") Integer auditingState);

    /**
     * 新增租费计提删除信息
     * @param vo
     * @return
     */
    int insertAccrualDeleteInfo(RentAccrualVO vo);

    /**
     * 查询本月该报账点是否产生了到期计提
     * @param billaccountId
     * @return
     */
    int queryThisMonthExpireAccrualCountByBillaccount(@Param("billaccountId") String billaccountId,@Param("accrualType") Integer accrualType);

    /**
     * 查询报账点及合同的信息，用于生成到期计提条件校验
     * @param billaccountId
     * @return
     */
    RentAccrualCheckDto queryExpireAccrualCheckInfo(@Param("billaccountId") String billaccountId);

    /**
     * 查询报账点资源状态，用于生成到期计提条件校验
     * @param billaccountId
     * @return
     */
    List<RentAccrualResourceCheck> queryExpireAccrualCheckResource(@Param("billaccountId") String billaccountId);
}
