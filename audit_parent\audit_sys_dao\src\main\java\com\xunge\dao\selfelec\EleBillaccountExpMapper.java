package com.xunge.dao.selfelec;

import com.xunge.model.basedata.DatBaseresource;
import com.xunge.model.selfelec.*;

import java.util.List;
import java.util.Map;

public interface EleBillaccountExpMapper {
    List<VEleContract> selectByContract(VEleBillaccountcontract contract);

    /**
     * 报账点维护中合同查询
     *
     * @param contract
     * @return
     * <AUTHOR>
     */
    List<VEleContract> selectAllContractByConditions(VEleBillaccountcontract contract);

    /**
     * 固化信息关联查询合同ID
     *
     * @param contract
     * @return
     * <AUTHOR>
     */
    List<VEleContract> selectAllContractByConditionsRel(VEleBillaccountcontract contract);

    List<VEleContract> selectAllFinanceContractByConditions(VEleBillaccountcontract contract);

    List<VDatBaseresource> selectByResource(VEleBillaccountbaseresource resource);

    List<VDatElectricmeter> selectByElectricmeter(VEleBillaccountbaseresource resource);

    List<String> selectReplaceMeterId();

    List<Map<String, Object>> selectResourceRelations(Map<String, Object> map);

    List<Map<String, Object>> selectResourceTree(VEleBillaccountbaseresource resource);

    /**
     * 报账点删除审核通过 资源做展示，资源关联关系为6：报账点删除审核通过
     *
     * @param resource
     * @return
     */
    List<Map<String, Object>> selectResourceTreeDeleteAudit(VEleBillaccountbaseresource resource);

    /**
     * 修改自维缴费记录汇总id
     *
     * @param
     * @param : paymentId 缴费记录id
     * @param : billamountId 汇总id
     * @return
     */
    public int updateBillamountIdByPaymentId(Map<String, Object> hashMaps);

    /**
     * 查询特殊报账点关联的资源点信息
     *
     * @param paraMap
     * @return
     */
    List<DatBaseresource> querySpecialResource(Map<String, Object> paraMap);

    /**
     * 查询特殊报账点资源关系图
     *
     * @param map
     * @return
     */
    List<Map<String, Object>> selectSpecialResourceRelations(Map<String, Object> map);

    /**
     * 固化电费信息关联查询大集中合同ID
     *
     * @param contract
     * @return
     * <AUTHOR>
     */
    List<VEleContract> selectFocusEleContractByConditionsRel(VEleBillaccountcontract contract);

    /**
     * 固化电费信息关联查询普通合同ID
     *
     * @param contract
     * @return
     * <AUTHOR>
     */
    List<VEleContract> selectEleContractByConditionsRel(VEleBillaccountcontract contract);

    /**
     * 固化电费信息关联查询普通合同ID
     *
     * @param contract
     * @return
     * <AUTHOR>
     */
    List<VEleContract> selectCommonEleContractByConditionsRel(VEleBillaccountcontract contract);

    /**
     * 固化租费信息关联查询大集中合同ID
     *
     * @param contract
     * @return
     * <AUTHOR>
     */
    List<VEleContract> selectFocusRentContractByConditionsRel(VEleBillaccountcontract contract);

    /**
     * 固化租费信息关联查询普通合同ID
     *
     * @param contract
     * @return
     * <AUTHOR>
     */
    List<VEleContract> selectRentContractByConditionsRel(VEleBillaccountcontract contract);

    /**
     * 固化租费信息关联查询普通合同ID
     *
     * @param contract
     * @return
     * <AUTHOR>
     */
    List<VEleContract> selectCommonRentContractByConditionsRel(VEleBillaccountcontract contract);

    int updateBillamountIdById(Map<String, Object> map);

    int updateBillamountVerificationIdById(Map<String, Object> map);

    VEleBillaccountcontract queryBillaccountInfoByBaseresourceId(Map<String, Object> cond);


    List<VEleBaseresourceelectricmeter> queryBaseresourceelectricmeterByBillaccountId(String billaccountId);

    List<DatBaseresource> queryBillaccountMultipathResource(String billaccountId);

    VEleBillaccountcontract queryBillaccountInfoByBaseresource(Map<String, Object> cond);

    List<VEleBillaccountcontract> queryBillaccountInfoByBaseresourceList(Map<String, Object> cond);

    List<DatBaseresource> querySpecialPaymentResource(Map<String, Object> cond);

    List<DatBaseresource> querySpecialPaymentResourceMore(Map<String, Object> cond);

    List<DatBaseresource> querySpecialPaymentResourceNew(Map<String, Object> cond);

    List<DatBaseresource> querySpecialPaymentResourceMoreNew(Map<String, Object> cond);
}
