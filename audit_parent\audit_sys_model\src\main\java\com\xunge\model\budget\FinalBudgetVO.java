package com.xunge.model.budget;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.handler.inter.IExcelDataModel;
import cn.afterturn.easypoi.handler.inter.IExcelModel;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @author: LiangCheng
 * Date: 2023/4/19 8:45
 * Description: 最终预算 新
 */
@Data
public class FinalBudgetVO extends BudgetBaseVo implements Serializable, IExcelModel, IExcelDataModel {

    private Integer finalId;

    //预算ID
    private String workOrderId;

    //预算类型1电费2租费3三方塔4铁塔
    private Integer budgetType;

    private Integer onYear;

    private String prvId;

    @Excel( name = "省份")
    @NotNull(message = "不能为空")
    private String prvName;

    @Excel( name = "最终预算金额（万）")
    @Pattern(regexp = "\\d{1,12}(\\.\\d{0,2})?", message = "填写错误(填写正数，两位小数)！")
    @NotNull(message = "不能为空")
    private String budgetAmount;

    private String createUser;

    private Date createTime;

    private String errorMsg;

    private int rowNum;

    //旧预算ID
    private String expenseBudgetId;

    @Override
    public Integer getRowNum() {
        return rowNum;
    }

    @Override
    public void setRowNum(Integer rowNum) {
        this.rowNum = rowNum;
    }

    @Override
    public String getErrorMsg() {
        return errorMsg;
    }

    @Override
    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }
}
