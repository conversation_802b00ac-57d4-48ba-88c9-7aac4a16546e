package com.xunge.comm.system;

/**
 * <AUTHOR>
 * @description 提示信息公共变量
 * @date 创建时间：2017年10月13日
 */
public class PromptMessageComm {
    /**
     * portal数据接口
     */
    public static final String URL_PORTAL_DATA = "/portal/v1";
    public final static String TW_TYPE_TRANS = "trans"; //传输
    public final static String TW_TYPE_ROOM = "room"; //室分
    public final static String TW_TYPE_TINY = "tiny";//微站
    public final static String TW_TYPE_NONSTAND = "nonstand";//非标
    /**
     * 用户账号被锁定提示
     */
    public final static String USER_ID_LOCKED_TIPS = "您的账号已被锁定，请%d分钟后在尝试登陆";
    /**
     * 用户账号连续登陆失败5次提示
     */
    public final static String LOGIN_FAILED_FIVE_TIMES_TIPS = "您已连续登陆失败%d次，您的账号将被系统锁定";
    /**
     * 登陆失败提示
     */
    public final static String LOGIN_FAILED_TIPS = "账号或密码不正确，您还有%d次机会!";
    /**
     * 登录失效
     */
    public static String LOGIN_FAILED = "用户登录失效，请重新登录";
    /**
     * 供应商编码为空，不能生成汇总单
     */
    public static String SUPPLIERCODE_NULL = "供应商编码为空，不能生成或推送汇总单";
    /**
     * 关联供应商为空，不能推送汇总单
     */
    public static String SUPPLIER_NULL = "关联供应商为空，不能推送汇总单";
    /**
     * 流程未执行完成，不能重新发起，详情请查看流转记录！
     */
    public static String ACTIVITY_IS_NOT_NULL = "流程未执行完成，不能重新发起，详情请查看流转记录！";
    /**
     * 供应商地址为空，不能生成或推送汇总单
     */
    public static String SUPPLIERADDRESS_NULL = "供应商地址为空，不能生成或推送汇总单";
    public static String TwrSupplier_EXIST = "该地市已经存在铁塔供应商，请检查";
    /**
     * 报账人工号为空，不能生成或推送汇总单
     */
    public static String USERCODE_NULL = "报账人工号为空，不能生成或推送汇总单";
    /**
     * OA用户名为空为空，不能生成或推送汇总单
     */
    public static String USERLOGINNAME_NULL = "OA用户名为空，不能生成或推送汇总单";
    /**
     * 缴费终止日期为空，不能生成或推送汇总单
     */
    public static String PAYMENTENDDATE_NULL = "缴费终止日期为空，不能生成或推送汇总单";
    /**
     * 业务类型【bussiness_type】为null
     */
    public static String BUSINESS_TYPE_NULL = "业务类型【bussiness_type】为null";
    /**
     * 业务类型【bussiness_id】为null
     */
    public static String BUSINESS_ID_NULL = "业务类型【bussiness_id】为null";
    /**
     * 缴费起始日期为空，不能生成或推送汇总单
     */
    public static String PAYMENTSTARTDATE_NULL = "缴费起始日期为空，不能生成或推送汇总单";
    /**
     * 实际不含税金额为空，不能生成或推送汇总单
     */
    public static String DUEAMOUNTACT_NULL = "实际不含税金额为空，不能生成或推送汇总单";
    /**
     * 电表数量为空，不能生成或推送汇总单
     */
    public static String DETAILCNT_NULL = "电表数量为空，不能生成或推送汇总单";
    /**
     * 用电度数为空，不能生成或推送汇总单
     */
    public static String TOTALDEGREEACTUAL_NULL = "用电度数为空，不能生成或推送汇总单";
    /**
     * 缴费月数为空，不能生成或推送汇总单
     */
    public static String NUMBERMONTH_NULL = "缴费月数为空，不能生成或推送汇总单";
    /**
     * 实际税额为空，不能生成或推送汇总单
     */
    public static String BILLAMOUNTTAXAMOUNTACT_NULL = "实际税额为空，不能生成或推送汇总单";
    /**
     * 税率为空，不能生成或推送汇总单
     */
    public static String BILLAMOUNTTAXRATIO_NULL = "税率为空，不能生成或推送汇总单";
    /**
     * 实际报账金额为空，不能生成或推送汇总单
     */
    public static String BILLAMOUNTACTUAL_NULL = "实际报账金额为空，不能生成或推送汇总单";
    /**
     * 实际核销金额为空，不能生成或推送汇总单
     */
    public static String BILLAMOUNTACTUAL2_NULL = "实际核销金额为空，不能生成或推送汇总单";
    /**
     * 机房资源点为空，不能生成或推送汇总单
     */
    public static String BELONG_ROOM_NULL = "机房资源点为空，不能生成或推送汇总单";
    /**
     * 机房资源点为空，不能生成或推送汇总单
     */
    public static String BELONG_BILLAMOUNTWITHTAX_NULL = "报账金额不大于0，不能生成或推送汇总单";
    /**
     * 实付金额为空，不能生成或推送汇总单
     */
    public static String PAYACTAMOUNT_NULL = "实付金额为空，不能生成或推送汇总单";
    /**
     * 关联合同为空，不能生成或推送汇总单
     */
    public static String BILLAMOUNTCONTRACT_NULL = "关联合同为空，不能生成或推送汇总单";
    /**
     * 合同类型为空，不能生成或推送汇总单
     */
    public static String BILLAMOUNTCONTRACTTYEP_NULL = "合同类型为空，不能生成或推送汇总单";
    /**
     * 报账金额为空，不能生成或推送汇总单
     */
    public static String BILLAMOUNTWITHTAX_NULL = "报账金额为空，不能生成或推送汇总单";
    /**
     * 不含税金额为空，不能生成或推送汇总单
     */
    public static String BILLAMOUNTNOTAX_NULL = "不含税金额为空，不能生成或推送汇总单";
    /**
     * 含税金额为空，不能生成或推送汇总单
     */
    public static String CONTRACT_CODE_NULL = "含税金额为空，不能生成或推送汇总单";
    /**
     * 登录成功
     */
    public static String LOGIN_SUSSESS = "登录成功";
    /**
     * 旧密码输入错误，请重新输入！
     */
    public static String PASSWORD_NO_MATCHING = "旧密码输入错误，请重新输入！";
    /**
     * 密码修改成功
     */
    public static String PASSWORD_MODIF_SUCCESS = "密码修改成功！";
    /**
     * 密码修改失败
     */
    public static String PASSWORD_MODIF_FAILED = "密码修改失败！";
    /**
     * 重置密码成功
     */
    public static String PASSWORD_RESET_SUCCESS = "：重置密码成功！";
    /**
     * 重置密码失败
     */
    public static String PASSWORD_RESET_FAILED = "：重置密码失败！";
    /**
     * 账号不存在！
     */
    public static String USER_NOT_FIND_USER = "所选省不存在该账号，请联系本省管理员或客服人员进行账号注册！";
    /**
     * 账号不存在，请选择与账号对应的区域！
     */
    public static String USER_NOT_FIND = "该省账号不存在，或省份选择错误！";
    /**
     * 用户名或者密码不正确!
     */
    public static String LOGINNAME_ERROR = "账号或密码不正确!";
    /**
     *  修改的密码与最近3次修改密码相同
     */
    public static String PASSWORD_MODIFY_REPEAT = "您所修改的密码与最近3次修改密码相同，请重新修改后提交!";
    /**
     * 账户已停用！重新启用请联系管理员!
     */
    public static String USER_STOP = "账户已停用！重新启用请联系管理员!";
    /**
     * 账户已删除！重新启用请联系管理员!
     */
    public static String USER_DELETE = "账户已删除！重新启用请联系管理员!";
    /**
     * 此账户所属区县不存在或存在异常！
     */
    public static String USER_REGION_ERROR = "此账户所属区县不存在或存在异常！";
    /**
     * 操作成功
     */
    public static String OPERATION_SUSSESS = "操作成功";
    /**
     * 操作失败
     */
    public static String OPERATION_FAILED = "操作失败";
    /**
     * 新增用户
     */
    public static String ADD_USER = "新增用户";
    /**
     * 新增用户详情
     */
    public static String USER_INFO = "新增用户详情";
    /**
     * 用户所属部门明细
     */
    public static String USER_DEPT_INFO = "用户所属部门明细";
    /**
     * 用户所属角色明细
     */
    public static String USER_ROLE_INFO = "用户所属角色明细";
    /**
     * 修改用户
     */
    public static String UPDATE_USER = "修改用户";
    /**
     * 修改前用户详情
     */
    public static String BEFORE_UPDATE_USER = "修改前用户详情";
    /**
     * 修改后用户详情
     */
    public static String AFTER_UPDATE_USER = "修改后用户详情";
    /**
     * 删除用户
     */
    public static String DELETE_USER = "删除用户";
    /**
     * 启用用户
     */
    public static String OPEN_USER = "启用用户";
    /**
     * 停用用户
     */
    public static String STOP_USER = "停用用户";
    /**
     * 新增区县前明细
     */
    public static String BEFORE_ADD_REGION = "新增区县前明细";
    /**
     * 新增区县后明细
     */
    public static String AFTER_ADD_REGION = "新增区县后明细";
    /**
     * 新增区县
     */
    public static String ADD_REGION = "新增区县";
    /**
     * 分配角色
     */
    public static String ADD_ROLE = "分配角色";
    /**
     * 删除角色
     */
    public static String DELETE_ROLE = "删除角色";
    /**
     * 分配用户
     */
    public static String ALLOCATION_USER = "分配用户";
    /**
     * 停用角色
     */
    public static String STOP_ROLE = "停用角色";
    /**
     * 启用角色
     */
    public static String OPEN_ROLE = "启用角色";
    /**
     * 修改角色
     */
    public static String UPDATE_ROLE = "修改角色";
    /**
     * 角色功能权限分配
     */
    public static String AUTHEN_ROLE = "角色功能权限分配";
    /**
     * 分配前菜单明细
     */
    public static String BEFORE_AUTHEN_ROLE = "分配前菜单明细";
    /**
     * 分配后菜单明细
     */
    public static String AFTER_AUTHEN_ROLE = "分配后菜单明细";
    /**
     * 新增角色菜单
     */
    public static String ADD_ROLE_MENU = "新增角色菜单";
    /**
     * 分配用户部门权限
     */
    public static String AUTHEN_DEPT = "分配用户部门权限";
    /**
     * 用户部门权限分配前明细
     */
    public static String BEFORE_AUTHEN_DEPT = "用户部门权限分配前明细";
    /**
     * 用户部门权限分配后明细
     */
    public static String AFTER_AUTHEN_DEPT = "用户部门权限分配后明细";
    /**
     * 删除区县
     */
    public static String DELETE_REGION = "删除区县";
    /**
     * 修改区县
     */
    public static String UPDATE_REGION = "修改区县";
    /**
     * 修改系统参数
     */
    public static String UPDATE_SYSTEM_PARAMETER = "修改系统参数";
    /**
     * 启用系统参数
     */
    public static String OPEN_SYSTEM_PARAMETER = "启用系统参数";
    /**
     * 停用系统参数
     */
    public static String STOP_SYSTEM_PARAMETER = "停用系统参数";
    /**
     * 删除日志
     */
    public static String DELETE_LOG = "删除日志";
    /**
     * 新增数据字典
     */
    public static String ADD_DICTIONARY = "新增数据字典";
    /**
     * 修改数据字典
     */
    public static String UPDATE_DICTIONARY = "修改数据字典";
    /**
     * 删除数据字典
     */
    public static String DELETE_DICTIONARY = "删除数据字典";
    /**
     * 启用数据字典
     */
    public static String OPEN_DICTIONARY = "启用数据字典";
    /**
     * 停用数据字典
     */
    public static String STOP_DICTIONARY = "停用数据字典";
    /**
     * 新增部门
     */
    public static String ADD_DEPT = "新增部门";
    /**
     * 修改部门
     */
    public static String UPDATE_DEPT = "修改部门";

    //查询时间的前后缀
    /**
     * 删除部门
     */
    public static String DELETE_DEPT = "删除部门";
    /**
     * 启用部门
     */
    public static String OPEN_DEPT = "启用部门";
    /**
     * 停用部门
     */
    public static String STOP_DEPT = "停用部门";
    /**
     * 新增菜单
     */
    public static String ADD_MENU = "新增菜单";
    /**
     * 修改菜单
     */
    public static String UPDATE_MENU = "修改菜单";
    /**
     * 删除菜单
     */
    public static String DELETE_MENU = "删除菜单";
    /**
     * 启用菜单
     */
    public static String OPEN_MENU = "启用菜单";

    //执行删除操作的结果
    /**
     * 停用菜单
     */
    public static String STOP_MENU = "停用菜单";
    /**
     * 时间查询条件前缀 00:00:00
     */
    public static String PARAMETER_TIME_BEFORE = " 00:00:00";

    //执行新增操作的结果
    /**
     * 时间查询条件后缀 23:59:59
     */
    public static String PARAMETER_TIME_AFTER = " 23:59:59";
    /**
     * 业务表名为空
     */
    public static String BUSINESS_NAME_EMPTY = "业务表名为空";

    //执行修改操作的结果
    /**
     * 业务表主键为空
     */
    public static String BUSINESS_PRIMARY_EMPTY = "业务表主键为空";
    /**
     * 不存在或流程已经结束。
     */
    public static String ACTIVITY_TASK_EMPTY = "不存在或流程已经结束。";
    /**
     * 审核开始
     */
    public static String AUDIT_BEGIN = "审核开始";
    /**
     * 审核结束
     */
    public static String AUDIT_END = "审核结束";
    /**
     * 用户删除数据成功
     */
    public static String OPERATION_DELETE_SUCCESS = "用户删除数据成功";
    /**
     * 用户删除数据失败
     */
    public static String OPERATION_DELETE_FAILED = "用户删除数据失败";
    /**
     * 用户新增数据成功
     */
    public static String OPERATION_INSERT_SUCCESS = "用户新增数据成功";
    /**
     * 用户新增数据失败
     */
    public static String OPERATION_INSERT_FAILED = "用户新增数据失败";
    /**
     * 参数解析异常
     */
    public static String OPERATION_PARAM_FAILED ="参数解析异常";
    /**
     * 用户修改数据成功
     */
    public static String OPERATION_UPDATE_SUCCESS = "用户修改数据成功";
    /**
     * 用户修改数据失败
     */
    public static String OPERATION_UPDATE_FAILED = "用户修改数据失败";
    /**
     * 铁塔产品服务费
     */
    public static String TWSERViCR_CHARGE = "铁塔产品服务费";
    /**
     * 铁塔产品服务费,其他塔类费用
     */
    public static String TWSERViCR_OTHER = "铁塔产品服务费,其他站类费用";
    /**
     * 集团指标扣款
     */
    public static String GROUP_INDT_DT = "集团指标扣款";
    /**
     * 省指标扣款
     */
    public static String PROVINCE_INDT_DT = "省指标扣款";
    /**
     * 省指标扣款
     */
    public static String PRACE_FEE = "场地费重复缴费明细";
    public static String PRACE_BALANCE_FEE = "场地费重复缴费（服务费）明细";
    public static String PRACE_RENT_FEE = "场地费重复缴费（租费）明细";

    public static String WRITE_OFF_FEE = "列账并冲销明细";
    /**
     * 地方指标扣款
     */
    public static String COUNTY_INDT_DT = "地市指标扣款";
    /**
     * 生成费用汇总单成功
     */
    public static String BILL_GENERATATED_SUCCESS = "生成费用汇总账单成功 ";
    /**
     * 生成汇总单失败，汇总单费用类型已存在，请检查后重新执行生成！
     */
    public static String BILL_GENERATATED_FAILE = "生成汇总单失败，请检查后重新执行生成！ ";
    /**
     * 费用汇总单验证失败！
     */
    public static String BILL_VERIFY_FAILE = "费用汇总单验证失败！ ";
    /**
     * 请求汇总单中存在数据状态错误
     */
    public static String BILL_REQUEST_ERROR = "请求汇总单中存在数据状态错误 ";
    /**
     * 参数异常
     */
    public static String PARAMETER_ERROR = "参数异常";
    /**
     * 未查询到相关费用汇总单
     */
    public static String BILL_SELECT_FAILED = "未查询到相关费用汇总单";
    /**
     * 无
     */
    public static String ENGINE = "无 ";
    /**
     * 集团数据类型字典code
     */
    public static String DATACOLLECTTEMP_TYPE = "DATACOLLECTTEMP_TYPE";
    /**
     * ：报账点租费信息保存成功
     */
    public static String BILLACCOUNT_RENTINFO_SUCCESS = "：报账点租费信息保存成功";
    /**
     * 集团领导查看页面code
     */
    public static String JT_LOOKMENU_CODE = "M01050102";
    /**
     * 省公司领导查看页面code
     */
    public static String PRV_MENU_CODE = "M02080101";
    /**
     * 省公司操作查看页面code
     */
    public static String PRV_LOOKMENU_CODE = "M02080102";
    /**
     * 当前任务：
     */
    public static String CURR_TASK = "当前任务：";
    /**
     * 节点名称
     */
    public static String NODE_NAME = "节点名称";
    /**
     * 节点说明
     */
    public static String NODE_DESC = "节点说明";
    /**
     * 描述
     */
    public static String DESCIPTION = "描述";
    /**
     * 任务类型
     */
    public static String TASK_TYPE = "任务类型";
    /**
     * 删除费用汇总单成功
     */
    public static String BILL_DELETE_SUCCESS = "删除费用汇总单成功 ";
    /**
     * 删除费用汇总单失败
     */
    public static String BILL_DELETE_FAILE = "删除费用汇总单失败 ";
    /**
     * 校验费用汇总单状态通过
     */
    public static String BILL_VERIFY_SECCUSS = "校验费用汇总单状态通过";
    /**
     * 删除费用汇总单失败，校验费用汇总单状态未通过
     */
    public static String BILL_DELETE_VERIFY_FAILE = "删除费用汇总单失败，校验费用汇总单状态未通过 ";
    /**
     * 别名-v_sys_region
     */
    public static String ALIAS_NAME = "v_sys_region";
    /**
     * 别名-reg
     */
    public static String ALIAS_NAME_REG = "reg";
    /**
     * 任务所属角色
     */
    public static String TASK_OF_ROLE = "任务所属角色";
    /**
     * 当前处理人
     */
    public static String CURR_HANDLER = "当前处理人";
    /**
     * 创建模型失败:
     */
    public static String CREATE_MODEL_FAIL = "创建模型失败:";
    /**
     * 设置成功，模块ID=
     */
    public static String SET_SUCCESS = "设置成功，模块ID=";
    /**
     * 删除模型成功
     */
    public static String MODEL_DELETE_SUCCESS = "删除模型成功";
    /**
     * 个人信息修改成功
     */
    public static String PERSONAL_INFORMATION_CHANGES_SUCCESS = "个人信息修改成功";
    /**
     * 个人信息修改失败
     */
    public static String PERSONAL_INFORMATION_CHANGES_FAILED = "个人信息修改失败";
    /**
     * 导出数据成功
     */
    public static String EXPORT_INFO_SUCCESS = "导出数据成功";
    /**
     * 导出数据失败
     */
    public static String EXPORT_INFO_FAIL = "导出数据失败";
    /**
     * noname 无名
     */
    public static String NO_NAME = "无";
    /**
     * 查询报账汇总信息成功
     */
    public static String SELECT_BILLAMOUNT_SUCCESS = "查询报账汇总信息成功";
    /**
     * 删除报账汇总信息成功
     */
    public static String DELETE_BILLAMOUNT_SUCCESS = "删除报账汇总信息成功";
    /**
     * 查询报账汇总信息成功
     */
    public static String PUSH_BILLAMOUNT_SUCCESS = "推送报账汇总信息成功";
    /**
     * 集团驳回省级上报数据成功，但省公司业务员短信发送失败
     */
    public static String JT_REJECT_ORDER_SUCCESS_BUT_PRVBUSSMS_FAILED = "集团驳回省级上报数据成功，但省公司业务员短信发送失败";
    /**
     * 集团驳回省级上报数据失败
     */
    public static String JT_REJECT_ORDER_FAILED = "集团驳回省级上报数据失败";
    /**
     * 集团驳回省级上报数据成功
     */
    public static String JT_REJECT_ORDER_SUCCESS = "集团驳回省级上报数据成功";
    /**
     * 集团驳回省级上报数据成功，但短信发送失败
     */
    public static String JT_REJECT_ORDER_SUCCESS_BUT_SMS_FAILED = "集团驳回省级上报数据成功，但短信发送失败";
    /**
     * 集团用户完结工单成功，但省公司修改失败
     */
    public static String USER_FINISH_ORDER_SUCCESS_BUT_FINISHPRV_FAILED = "集团用户完结工单成功，但省公司修改失败";
    /**
     * 集团用户完结工单成功
     */
    public static String USER_FINISH_ORDER_SUCCESS = "集团用户完结工单成功";
    /**
     * 集团用户完结工单失败
     */
    public static String USER_FINISH_ORDER_FAILED = "集团用户完结工单失败";
    /**
     * 用户派发集团收集数据成功，但省公司业务员短信发送失败
     */
    public static String USER_SENDMSG_TO_PRV_SUCCESS_BUT_PRVBUSSMS_FAILED = "用户派发集团收集数据成功，但省公司业务员短信发送失败";
    /**
     * 用户派发集团收集数据失败
     */
    public static String USER_SENDMSG_TO_PRV_FAILED = "用户派发集团收集数据失败";
    /**
     * 用户派发集团收集数据成功
     */
    public static String USER_SENDMSG_TO_PRV_SUCCESS = "用户派发集团收集数据成功";
    /**
     * 用户派发集团收集数据成功，但短信发送失败
     */
    public static String USER_SENDMSG_TO_PRV_SUCCESS_BUT_SMS_FAILED = "用户派发集团收集数据成功，但短信发送失败";
    /**
     * 用户派发集团收集数据成功，但省公司提示短信发送失败
     */
    public static String USER_SENDMSG_TO_PRV_SUCCESS_BUT_PRVSMS_FAILED = "用户派发集团收集数据成功，但省公司提示短信发送失败";
    /**
     * 克隆模型出错
     */
    public static String COPY_MODEL_FAIL = "克隆模型出错";
    /**
     * 登录超时异常！
     */
    public static String LOGIN_TIMEOUT = "登录超时异常！";
    /**
     * 用户未关联角色，获取菜单失败！
     */
    public static String USER_DOES_NOT_ASSOCIATE_ROLE = "用户未关联角色，获取菜单失败！";
    /**
     * 该角色未关联菜单，获取菜单失败！
     */
    public static String ROLE_IS_NOT_ASSOCIATED_WITH_MENU = "该角色未关联菜单，获取菜单失败！";
    /**
     * 参数获取异常！
     */
    public static String PARAMETER_ACQUISITION_EXCEPTION = "参数获取异常！";
    /**
     * 用户角色获取失败，请重新登录！
     */
    public static String USER_ROLE_FAILED = "用户角色获取失败，请重新登录！";
    /**
     * 获取子菜单失败！
     */
    public static String GET_SUBMENU_FAILED = "获取子菜单失败！";
    /**
     * 提交审核成功
     */
    public static String COMMIT_AUDIT_SUCCESS = "提交审核成功！";

    /**
     * 提交审核成功
     */
    public static String AUTO_AUDIT_SUCCESS = "自动稽核通过！";
    /**
     * 提交审核成功
     */
    public static String COMMIT_AUDIT_SUCCESS_TMP = "提交审核成功";
    /**
     * 提交审核成功
     */
    public static String COMMIT_DELETE_AUDIT_SUCCESS = "提交删除审核成功！";
    /**
     * 提交审核失败
     */
    public static String COMMIT_AUDIT_FAILED = "提交审核失败！";
    /**
     * 提交审核失败
     */
    public static String COMMIT_AUDIT_FAILED_TMP = "提交审核失败";

    /**
     * 条
     */
    public static String COMMIT_AUDIT_UNIT = "条,";

    /**
     * 条
     */
    public static String RENT_ACCRUAL_COMMIT_FAIL_REASON = "以下单子由于计提金额超出合同剩余金额提交失败:";

    /**
     * 条
     */
    public static String RENT_ACCRUAL_COMMIT_FAIL_REASON_LOCK = "以下单子由于其他用户正在操作，提交失败:";

    /**
     * 提交审核失败
     */
    public static String COMMIT_AUDIT_AGAIN = "该缴费单已在提交审核中，请刷新列表后重试！";
    /**
     * 提交数据为空
     */
    public static String COMMIT_AUDIT_DATANULL = "提交审核数据为空，请重新选择！";
    /**
     * 提交审核失败，草稿单不允许提交审核
     */
    public static String COMMIT_AUDIT_FAILED_IFDRAFT = "该数据为缴费草稿,提交审核失败！";
    /**
     * 部署成功，流程ID=
     */
    public static String DEPLOYMENT_SUCCESS = "部署成功，流程ID=";
    /**
     * 部署失败，没有流程。
     */
    public static String DEPLOYMENT_FAIL = "部署失败，没有流程。";
    /**
     * 设计模型图不正确，检查模型正确性，模型ID=
     */
    public static String DESIGN_MODEL_ERROR = "设计模型图不正确，检查模型正确性，模型ID=";
    /**
     * 查询数据成功
     */
    public static String SELECT_INFO_SUCCESS = "查询数据成功";
    /**
     * 》成功
     */
    public static String USER_SAVE_AND_SEND_ORDER_SUCCESS = "》成功";
    /**
     * 》失败
     */
    public static String USER_SAVE_AND_SEND_ORDER_FAILED = "》失败";
    /**
     * 用户保存并派发工单《
     */
    public static String USER_SAVE_AND_SEND_ORDER = "用户保存并派发工单《";
    /**
     * 用户保存并上报工单成功
     */
    public static String USER_SAVE_AND_SEND_PRVORDER_SUCCESS = "用户保存并上报工单成功";
    /**
     * 用户省级上报信息失败
     */
    public static String USER_SEND_PRVMSG_FAILED = "用户省级上报信息失败";
    /**
     * 用户省级上报信息失败
     */
    public static String USER_SEND_PRVMSG_FAILED_NULL = "用户省级上报信息失败，上报用户信息为空";
    /**
     * 用户省级上报信息成功
     */
    public static String USER_SEND_PRVMSG_SUCCESS = "用户省级上报信息成功";
    /**
     * 省份CODE:JT
     */
    public static String PRV_CODE_JT = "JT";
    /**
     * 数据类型：其他
     */
    public static String DATA_TYPE_OTHER = "其他";
    /**
     * 用户上报信息成功
     */
    public static String USER_UPLOAD_MSG_SUCCESS = "用户上报信息成功";
    /**
     * 用户上报信息失败
     */
    public static String USER_UPLOAD_MSG_FAILED = "用户上报信息失败";
    /**
     * 集团下发模板未上传完毕，请继续上传:
     */
    public static String MODEL_HASNOT_UPLOAD_FINISH = "集团下发模板未上传完毕，请继续上传:";
    /**
     * 用户签收工单失败
     */
    public static String USER_SIGN_ORDER_FAILED = "用户签收工单失败";
    /**
     * 用户签收工单成功，但短信发送失败
     */
    public static String USER_SIGN_ORDER_SUCCESS_BUT_SMS_FAILED = "用户签收工单成功，但短信发送失败";
    /**
     * 工单转派成功，但短信发送失败
     */
    public static String TURN_TO_OTHER_SUCCESS_BUT_SMS_FAILED = "工单转派成功，但短信发送失败";
    /**
     * 用户签收工单成功
     */
    public static String USER_SIGN_ORDER_SUCCESS = "用户签收工单成功";
    /**
     * 工单转派成功
     */
    public static String TURN_TO_OTHER_SUCCESS = "工单转派成功";
    /**
     * 导出model的xml文件失败，模型ID=
     */
    public static String EXP_MODEL_XML_FAIL = "导出model的xml文件失败，模型ID=";
    /**
     * 请选择要部署的流程文件
     */
    public static String SELECT_DEPLOYMENT_FILE = "请选择要部署的流程文件";
    /**
     * 用户登录成功：
     */
    public static String LOGIN_SUCCESS = "用户登录成功：";
    /**
     * 验证码不正确!
     */
    public static String INCORRECT_VERIFICATION_CODE = "验证码不正确!";
    /**
     * 登录拉取用户信息失败
     */
    public static String PULL_USER_INFORMATION_FAILED = "登录拉取用户信息失败";
    /**
     * WEB登录系统错误
     */
    public static String WEB_LOGIN_SYS_ERROR = "WEB登录系统错误";
    /**
     * 获取报账人用户信息失败
     */
    public static String PULL_USER_FAILED = "获取报账人用户信息失败";

    /**
     * sso校验UID异常
     */
//	public static String SSO_CHECK__EXCEPTION = "sso校验UID异常！";

    /**
     * sso获取sessiondata异常
     */
//	public static String SSO_GET_SESSIONDATA_EXCEPTION = "sso获取sessiondata异常！";
    /**
     * sso登录获取uid = {}
     */
    public static String LOGIN_UID = "sso登录获取uid = {}";
    /**
     * uid为空或空字符串！
     */
    public static String UID_IS_EMPTY = "uid为空或空字符串！";
    /**
     * 用户sso登录成功
     */
    public static String USER_SSO_LOGIN_IS_SUCCESS = "用户sso登录成功";
    /**
     * SSO登录拉取用户信息失败
     */
    public static String SSO_LOGIN_FAILED_TO_PULL_USER_INFORMATION = "SSO登录拉取用户信息失败";
    /**
     * 无法获取sso客户端！
     */
    public static String UNABLE_TO_GET_SSO_CLIENT = "无法获取sso客户端！";
    /**
     * sso校验sessionData异常！
     */
    public static String SSO_CHECK_SESSIONDATA_EXCEPTION = "sso校验sessionData异常！";
    /**
     * sso校验UID异常
     */
    public static String SSO_CHECK__EXCEPTION = "sso校验UID异常！";
    /**
     * sso获取sessiondata异常
     */
    public static String SSO_GET_SESSIONDATA_EXCEPTION = "sso获取sessiondata异常！";
    /**
     * sso客户端为空！
     */
    public static String SSO_CLIENT_IS_EMPTY = "sso客户端为空！";
    /**
     * 转换模型成功，模型ID=
     */
    public static String CONVERSION_MODEL_SUCCESS = "转换模型成功，模型ID=";
    /**
     * 不支持的文件类型：
     */
    public static String UNSUPPORT_FILE_TYPE = "不支持的文件类型：";
    /**
     * 部署失败！
     */
    public static String DEPLOY_FAIL = "部署失败！";
    /**
     * true
     */
    public static String RESULT_TRUE = "true";
    /**
     * false
     */
    public static String RESULT_FALSE = "false";
    /**
     * 查询成功
     */
    public static String SEARCH_SUCCESS = "查询成功";
    /**
     * 已激活ID为[
     */
    public static String ACTIVE_ID = "已激活ID为[";
    /**
     * 已挂起ID为[
     */
    public static String SUSPEND_ID = "已挂起ID为[";
    /**
     * ]的流程定义。
     */
    public static String PROC_DEF = "]的流程定义。";
    /**
     * 无操作
     */
    public static String NO_OPERATION = "无操作";
    /**
     * 关联集团收集数据类型与集团收集表失败
     */
    public static String LINK_JTDATA_TYPE_AND_JTORDER_FAILED = "关联集团收集数据类型与集团收集表失败";
    /**
     * 配置已存在!
     */
    public static String CONFIG_EXIST = "配置已存在!";
    /**
     * 上传时新增集团收集数据类型失败
     */
    public static String UPLOAD_AND_INSERT_DATA_TYPE_FAILED = "上传时新增集团收集数据类型失败";
    /**
     * 上传时修改集团收集数据类型失败
     */
    public static String UPLOAD_AND_UPDATE_DATA_TYPE_FAILED = "上传时修改集团收集数据类型失败";
    /**
     * 合同数据采集任务
     */
    public static String CONTRACT_DATA_COLL_TASK = "合同数据采集任务";
    /**
     * 综合资源采集任务
     */
    public static String INTEGRAT_RESOURCE_COLL_TASK = "综合资源采集任务";
    /**
     * 动环性能采集任务
     */
    public static String DYNAMIC_RING_COLL_TASK = "动环性能采集任务";
    /**
     * 电租费合同采集任务
     */
    public static String SYNELETRIC_RING_COLL_TASK = "电租费合同采集任务";
    /**
     * 正常
     */
    public static String NORMAL = "正常";
    /**
     * 正常
     */
    public static String USEING = "启用";
    /**
     * 停用
     */
    public static String DISABLED = "停用";
    /**
     * 该省上报文件
     */
    public static String THIS_PRV_SEND_MSG = "该省上报文件";
    /**
     * 集团已经完结此工单
     */
    public static String JT_WAS_FINISH_THIS_ORDER = "集团已经完结此工单";
    /**
     * 集团驳回上报信息 ：“
     */
    public static String JT_REJECT_ORDER_HEAD = "集团驳回上报信息 ：“";
    /**
     * ”
     */
    public static String JT_REJECT_ORDER_END = "”";
    /**
     * 0 00 14 * * ? *
     */
    public static String TASK_CRONTAB = "0 00 14 * * ? *";
    /**
     * 14:00
     */
    public static String TASK_TIME = "14:00";
    /**
     * 集团派发工单《
     */
    public static String JT_SEND_ORDER_HEAD = "集团派发工单《";
    /**
     * 》到该省
     */
    public static String JT_SEND_ORDER_END = "》到该省";
    /**
     * * * ? *
     */
    public static String CRON1 = "* * ? *";
    /**
     * ? * MON
     */
    public static String CRON2 = "? * MON";
    /**
     * 1 * ?
     */
    public static String CRON3 = "1 * ?";
    /**
     * :
     */
    public static String SYMBOL1 = ":";
    /**
     * 导入数据条数:
     */
    public static String NUMBER_OF_IMPORTED_DATA = "导入数据条数:";
    /**
     * ，失败
     */
    public static String FAILURE = "，失败 ";
    /**
     * 个设备，导入信息如下：[
     */
    public static String IMPORT_INFORMATION = " 个设备，导入信息如下：";
    /**
     * redirect:/
     */
    public static String REDIRECT = "redirect:/";
    /**
     * 正在初始化...
     */
    public static String IS_LOADING = "正在初始化...";
    /**
     * 信息
     */
    public static String DEVICE_INFO = "信息";
    /**
     * 提交成功
     */
    public static String SUBMITTED_SUCCESS = "提交成功";
    /**
     * 设备信息导出-
     */
    public static String DEVICE_INFO_EXPORT = "设备信息导出-";
    /**
     * 租赁台账导出-
     */
    public static String rentreimburse = "租赁费台账模板-";
    /**
     * 请求进度成功
     */
    public static String ASK_PROGRESS_SUCCESS = "请求进度成功";
    /**
     * 上传文件超过
     */
    public static String UPLOAD_FILE_OVER = "上传文件超过";
    /**
     * ,无法正常上传！
     */
    public static String UPLOAD_FAIL = ",无法正常上传！";
    /**
     * ERROR
     */
    public static String ERROR = "error";
    /**
     * 导入报账点信息成功,共导入
     */
    public static String IMPORT_BILLING_POINT_INFO_SUCCESS = "导入报账点信息完成,共导入";
    /**
     * 条数据
     */
    public static String DATAS = "条数据 ";
    /**
     * ,开始保存错误数据。
     */
    public static String SAVE_ERR_DATAS = ",开始保存错误数据。";
    /**
     * 保存错误数据成功，开始保存源文件
     */
    public static String SAVE_SOURCE_DATAS = "保存错误数据成功，开始保存源文件。 ";
    /**
     * 保存源文件成功。导入结束
     */
    public static String SAVE_SOURCE_DATAS_SUCCESS = "保存源文件成功。导入结束 ";
    /**
     * 不是合法的Excel模板
     */
    public static String NOT_VALID_EXCEL_TEMPLATE = "不是合法的Excel模板";
    /**
     * ，或数据异常！
     */
    public static String DATA_EXCEPTION = "，或数据异常！";
    /**
     * 数据异常无法导入！请按照导入模板重新编辑报账表格。
     */
    public static String IMPORT_DATA_EXCEPTION = "数据异常无法导入！请按照导入模板重新编辑报账表格。";
    /**
     * has file upload
     */
    public static String HAS_FILE_UPLOAD = "----------------has file upload!";
    /**
     * SUCCESS
     */
    public static String SUCCESS = "success";
    /**
     * 文件不存在或已被移除！
     */
    public static String FILE_IS_NOT_FIND = "文件不存在或已被移除！";
    /**
     * FILE NAME IS
     */
    public static String FILE_NAME_IS = "----------------file name is!";
    /**
     * LOG
     */
    public static String LOG = "LOG";
    /**
     * CSV
     */
    public static String CSV = "CSV";
    /**
     * TXT
     */
    public static String TXT = "TXT";
    /**
     * excel XLS
     */
    public static String XLS = "XLS";
    /**
     * excel XLSX
     */
    public static String XLSX = "XLSX";
    /**
     * Billbalance
     */
    public static String Billbalance = "Billbalance";
    /**
     * TowerInfo
     */
    public static String TowerInfo = "TowerInfo";
    /**
     * UTF-8
     */
    public static String UTF_8 = "UTF-8";
    /**
     * files
     */
    public static String UPLOAD_FILES = "files";
    /**
     * err
     */
    public static String UPLOAD_ERR = "err";
    /**
     * saveErr
     */
    public static String UPLOAD_SAVE_ERR = "saveErr";
    /**
     * 添加失败,同一地市在同一月份不能重复添加！
     */
    public static String ADD_FAILED_INFO = "添加失败,同一地市在同一月份不能重复添加！";
    /**
     * 添加成功
     */
    public static String ADD_SUCCESS = "添加成功 ";
    /**
     * 修改成功
     */
    public static String MODIFYED_SUCCESS = "修改成功！";
    /**
     * 修改失败
     */
    public static String MODIFYED_FAILED = "修改失败！";
    /**
     * 机房
     */
    public static String ENGINE_HOME = "机房";
    /**
     * 资源点
     */
    public static String RESOURCE_POINT = "资源点";
    /**
     * 热点
     */
    public static String HOT_POINT = "热点";
    public static String POSITION_POINT = "位置点";
    /**
     * date-type 年月日时分秒 yyyy-MM-dd HH:mm:ss(24)
     */
    public static String DATE_TYPE_24H = "yyyy-MM-dd HH:mm:ss";
    /**
     * ：保存信息成功
     */
    public static String SAVE_INFO_SUCCESS = ":保存信息成功";
    /**
     * ：保存信息失败
     */
    public static String SAVE_INFO_FAILED = ":保存信息失败";
    /**
     * RESOURCE-
     */
    public static String BASE_RESOURCE_ID = "RESOURCE-";
    /**
     * MB
     */
    public static String SIZE_MB = "MB";
    /**
     * 0MB
     */
    public static String SIZE_0MB = "0MB";
    /**
     * 删除文件异常:
     */
    public static String DELETE_FILE_EXCEPTION = "删除文件异常:";
    /**
     * 删除文件成功
     */
    public static String DELETE_FILE_SUCCESS = "删除文件成功";
    /**
     * 删除文件失败
     */
    public static String DELETE_FILE_FAIL = "删除文件失败";
    /**
     * 上传附件：【
     */
    public static String UPLOAD_ATTACHMENT = "上传附件：【";
    /**
     * 】成功
     */
    public static String WORD_SUCCESS = "】成功";
    /**
     * 省内自设考核指标扣罚
     */
    public static String PROVINCE_SELFASSESSMENT_INDT_DT = "省内自设考核指标扣罚";
    /**
     * 电费合同固化信息
     */
    public static String ELEC_CONTRACT_HARDINFO = "电费合同固化信息";
    /**
     * 稽核报表明细
     */
    public static String AUDIT_WORK_INFO = "稽核报表明细";
    /**
     * 新增电费固化信息
     */
    public static String INSERT_ELEC_CURING_CONTRACT = "新增电费固化信息";
    /**
     * 修改电费合同
     */
    public static String UPDATE_ELEC_CURING_CONTRACT = "修改电费固化信息";
    /**
     * 新增电费合同信息
     */
    public static String INSERT_ELEC_FOCUS_CONTRACT = "新增电费合同信息";
    /**
     * 变更电费合同信息
     */
    public static String CHANGE_ELEC_FOCUS_CONTRACT = "变更电费合同信息";
    /**
     * 转让电费合同信息
     */
    public static String TRANSFER_ELEC_FOCUS_CONTRACT = "转让电费合同信息";
    /**
     * 解除电费合同信息
     */
    public static String RELEASE_ELEC_FOCUS_CONTRACT = "解除电费合同信息";
    /**
     * 查询失败!
     */
    public static String SELECT_FAIL = "查询失败!";
    /**
     * 上传文件超过
     */
    public static String UPLOAD_FILE_BIG_HEAD = "上传文件超过";
    /**
     * ,无法正常上传！
     */
    public static String UPLOAD_FILE_BIG_END = ",无法正常上传！";
    /**
     * 省内自设考核指标扣罚.xls
     */
    public static String PROVINCE_SELFASSESSMENT_INDT_DT_xls = "省内自设考核指标扣罚.xls";
    /**
     * 生成导出文件成功
     */
    public static String MAKE_OUT_FILE_SUCCESS = "生成导出文件成功";
    /**
     * 生成导出文件失败
     */
    public static String MAKE_OUT_FILE_FAILED = "生成导出文件失败";
    /**
     * 路径 /asserts/export/
     */
    public static String WAY_OF_ASSERTS_EXPORT = "/asserts/export/";
    /**
     * 路径 /asserts/
     */
    public static String WAY_OF_ASSERTS = "/asserts/";
    /**
     * 路径 WEB-INF/asserts/export
     */
    public static String WAY_OF_WEB_INF = "WEB-INF/asserts/export";
    /**
     * 路径 /asserts/power/
     */
    public static String WAY_OF_POWER = "/asserts/power/";
    /**
     * 路径 WEB-INF/asserts/power
     */
    public static String WAY_OF_WEB_POWER = "WEB-INF/asserts/power";
    /**
     * 路径/asserts/meter/
     */
    public static String WAY_OF_METER = "/asserts/meter/";
    /**
     * 路径 WEB-INF/asserts/
     */
    public static String WAY_OF_WEB_METER = "WEB-INF/asserts/meter";
    /**
     * 路径 WEB-INF/asserts/files-disk/temp
     */
    public static String WAY_OF_WEB_INF_ASSERTS_FILES_DISK_TEMP = "WEB-INF/asserts/files-disk/temp";
    /**
     * 路径 WEB-INF/asserts/files-disk/model/
     */
    public static String WAY_OF_WEB_INF_ASSERTS_FILES_DISK_MODEL = "WEB-INF/asserts/files-disk/model";
    public static String WAY_OF_WEB_INF_ASSERTS_FILES_FINANCE_DISK_MODEL = "WEB-INF/asserts/files-disk/finance_model";
    public static String FINANCE_WAY_OF_WEB_INF_ASSERTS_FILES_DISK_MODEL = "WEB-INF/asserts/files-disk/finance_model";
    /**
     * 路径 WEB-INF/asserts/export/electricmeter
     */
    public static String WAY_OF_ELECTRICMETER = "WEB-INF/asserts/export/electricmeter";
    /**
     * 路径 /asserts/export/electricmeter/
     */
    public static String WAY_OF_EXPORT_ELECTRICMETER = "/asserts/export/electricmeter/";
    /**
     * 重定向：/asserts/tpl/system/activity/act/process-editor/modeler.jsp?modelId=
     */
    public static String WAY_REDIRECT = "/asserts/tpl/system/activity/act/process-editor/modeler.jsp?modelId=";
    /**
     * Site-
     */
    public static String BASE_SITE = "Site-";
    /**
     * 导入省内扣罚成功,共导入
     */
    public static String IMPORT_INFO = "导入省内扣罚成功,共导入";
    /**
     * 站点
     */
    public static String SITE = "站点";
    /**
     * 站点信息
     */
    public static String SITE_INFO = "站点信息";
    /**
     * 站点信息导出-
     */
    public static String SITE_INFO_EXPORT = "站点信息导出-";
    /**
     * 供应商信息导出-
     */
    public static String SUPPLIER_EXPORT = "供应商信息导出-";
    /**
     * 供应商信息导入-
     */
    public static String SUPPLIER_IMPORT = "供应商信息导入-";
    /**
     * 铁塔供应商导出-
     */
    public static String TOWER_SUPPLIER_EXPORT = "铁塔供应商信息导出-";
    /**
     * 铁塔供应商导入-
     */
    public static String TOWER_SUPPLIER_IMPORT = "铁塔供应商信息导入-";
    /**
     * 供应商信息
     */
    public static String SUPPLIER_MSG = "供应商信息";
    /**
     * 供应商信息为空
     */
    public static String SUPPLIER_MSG_NULL = "供应商信息为空";
    /**
     * 条数据
     */
    public static String NUM_DATA = "条数据";
    /**
     * 节点x坐标
     */
    public static String NODE_X = "x";
    /**
     * 节点y坐标
     */
    public static String NODE_Y = "y";
    /**
     * 宽度属性width
     */
    public static String WIDTH = "width";
    /**
     * 高度属性height
     */
    public static String HEIGHT = "height";
    /**
     * 批量起租单
     */
    public static String RENT_BILLS = "批量起租单";
    /**
     * 批量起租单.xls
     */
    public static String RENT_BILLS_XLS = "批量起租单.xls";
    public static String RENT_BILLS_XLS1 = "批量起租单（已关联综资）.xls";
    public static String RENT_BILLS_XLS2 = "批量起租单（已关联但无所属资源）.xls";
    public static String RENT_BILLS_XLS0 = "批量起租单（未关联综资）.xls";
    /**
     * 移动资源信息详情
     */
    public static String MBILE_RESOURCE_DETAILS = "移动资源信息详情";
    /**
     * 移动资源信息详情.xls
     */
    public static String MBILE_RESOURCE_DETAILS_XLS = "移动资源信息详情.xls";
    public static String TWR_MBILE_RESOURCE_DETAILS_XLS = "需求管控平台源信息详情.xls";
    /**
     * 已删除
     */
    public static String BILL_DELETE_WORDS1 = "已删除";
    /**
     * 条未推送汇总单。
     */
    public static String BILL_DELETE_WORDS2 = "条未推送汇总单。";
    /**
     * 没有返回结果
     */
    public static String NO_RESULT = "没有返回结果";
    /**
     * 用户删除集团既定扣罚信息成功:
     */
    public static String USER_DELETE_DEDUCTION_INFO_SUCCESS = "用户删除集团既定扣罚信息成功:";
    /**
     * 用户删除集团既定扣罚信息失败:
     */
    public static String USER_DELETE_DEDUCTION_INFO_FAILED = "用户删除集团既定扣罚信息失败:";
    /**
     * 电表已存在!
     */
    public static String METER_EXISTS = "电表已存在!";
    /**
     * 表资产号已存在!
     */
    public static String TRICMETER_EXISTS = "表资产号已存在!";
    /**
     * 导入集团既定扣罚信息成功,共导入
     */
    public static String USER_IMPORT_DEDUCTION_INFO_SUCCESS = "导入集团既定扣罚信息成功,共导入";
    /**
     * 集团既定考核指标扣罚信息
     */
    public static String GROUP_ASSESSMENT_DEDUCTION_INFO = "集团既定考核指标扣罚信息";
    /**
     * Get重定向。。。。。。。。。。。。。。。。。。。。。。。。。。。。
     */
    public static String REDIRECT_GET = "Get重定向。。。。。。。。。。。。。。。。。。。。。。。。。。。。";
    /**
     * Post重定向。。。。。。。。。。。。。。。。。。。。。。。。。。。。
     */
    public static String REDIRECT_POST = "Post重定向。。。。。。。。。。。。。。。。。。。。。。。。。。。。";
    /**
     * 不可重复缴费！
     */
    public static String DO_NOT_REPEAT_PAYMENTS = "不可重复缴费！";
    /**
     * ----------create get----------------
     */
    public static String CREATE_GET = "----------create   get----------------";
    /**
     * 路径/asserts/tpl/system/activity/act/process-editor/modelerOpenWithBlank
     */
    public static String WAY_MODELER_OPEN_WITH_BLANK = "/asserts/tpl/system/activity/act/process-editor/modelerOpenWithBlank";
    /**
     * 集团既定考核指标扣罚信息.xls
     */
    public static String GROUP_ASSESSMENT_DEDUCTION_INFO_XLS = "集团既定考核指标扣罚信息.xls";
    /**
     * 查询集团既定考核指标扣罚信息成功
     */
    public static String SELECT_GROUP_ASSESSMENT_DEDUCTION_INFO_SUCCESS = "查询集团既定考核指标扣罚信息成功";
    /**
     * insert
     */
    public static String INSERT = "insert";
    /**
     * update
     */
    public static String UPDATE = "update";
    /**
     * clone
     */
    public static String CLONE = "clone";
    /**
     * http://b3mn.org/stencilset/bpmn2.0#
     */
    public static String NAMESPACE = "http://b3mn.org/stencilset/bpmn2.0#";
    /**
     * .bpmn20.xml
     */
    public static String PROCESS_NAME_END_WITH = ".bpmn20.xml";
    /**
     * Content-Disposition
     */
    public static String CONTENT_DISPOSITION = "Content-Disposition";
    /**
     * 附件attachment; filename=
     */
    public static String ATTACHMENT = "attachment; filename=";
    /**
     * 导入地市扣罚成功,共导入
     */
    public static String IMPORT_COUNTY_DEDUCTION_INFO_SUCCESS = "导入地市扣罚成功,共导入";
    /**
     * canvas
     */
    public static String CANVAS = "canvas";
    /**
     * ------------------流程定义列表---------------------
     */
    public static String SYSO_PROCESS_DEF = "------------------流程定义列表---------------------";
    /**
     * write to json string error:
     */
    public static String WRITE_TO_JSON_ERROR = "write to json string error:";
    /**
     * parse json string error:
     */
    public static String PARSE_JSON_ERROR = "parse json string error:";
    /**
     * 移动资源变更日志
     */
    public static String MOBILE_RESOURCE_CHANGE_LOG = "移动资源变更日志";
    /**
     * 移动资源变更日志.xls
     */
    public static String MOBILE_RESOURCE_CHANGE_LOG_XLS = "移动资源变更日志.xls";
    /**
     * update json string:
     */
    public static String UPDATE_JSON = "update json string:";
    /**
     * to object:
     */
    public static String TO_OBJECT = " to object:";
    /**
     * error.
     */
    public static String UPDATE_JSON_TO_OBJ_ERROR = " error.";
    /**
     * Error creating model JSON
     */
    public static String ERROR_CREATE_MODEL_JSON = "Error creating model JSON";
    /**
     * ----------------開始saveModel----------------
     */
    public static String SYSO_BEGIN_SAVE_MODEL = "------------------开始saveModel--------------";
    /**
     * -----------------結束saveModel---------------
     */
    public static String SYSO_END_SAVE_MODEL = "-----------------结束saveModel---------------";
    /**
     * 暂无信息
     */
    public static String NO_INFO = "暂无信息";
    /**
     * Error saving model
     */
    public static String ERROR_SAVE_MODEL = "Error saving model";
    /**
     * Error while loading stencil set
     */
    public static String ERROR_LOAD_STENCIL_SET = "Error while loading stencil set";
    /**
     * 未找到需要审核的数据！
     */
    public static String NOT_FIND_AUDIT_OBDATAS = "未找到需要审核的数据！";
    /**
     * stencilset.json
     */
    public static String STENCIL_SET_JSON = "stencilset.json";
    /**
     * json
     */
    public static String JSON = "json";
    /**
     * 导入铁塔租赁账单成功,共导入
     */
    public static String IMPORT_TOWERRENT_BILLS_SUCCESS = "导入铁塔租赁账单成功,共导入";
    /**
     * 导入铁塔变更信息成功,共导入
     */
    public static String IMPORT_TOWERRENT_CHANGE_SUCCESS = "导入铁塔变更信息成功,共导入";
    /**
     * 导入铁塔批量起租单成功。
     */
    public static String IMPORT_TOWERRENT_RESOURCE_SUCCESS = "导入铁塔批量起租单成功。";
    /**
     * 区县
     */
    public static String COUNTY = "区县";
    /**
     * -job
     */
    public static String JOB = "-job";
    /**
     * -jgroup
     */
    public static String JGROUP = "-jgroup";
    /**
     * -trigger
     */
    public static String TRIGGER = "-trigger";
    /**
     * -tgroup
     */
    public static String TGROUP = "-tgroup";
    /**
     * Cookie MaxAge(30*24*60*60) 存活期为一个月 30*24*60*60
     */
    public static int COOKIE_MAXAGE = 30 * 24 * 60 * 60;
    /**
     * 一天 60 * 60 * 1000* 24 * 30
     */
    public static int DAY = 60 * 60 * 1000 * 24 * 30;
    /**
     * 批量业务变更通知单.xls
     */
    public static String BUSINESS_CHANGES_NOTICE_INFO_XLS = "批量业务变更通知单.xls";
    /**
     * STATION-
     */
    public static String BASE_STATION_ID = "STATION-";
    /**
     * /404
     */
    public static String FORM_KEY_404 = "/404";
    /**
     * 报账点缴费信息录入失败！
     */
    public static String BILLACCOUNT_PAYMENT_ENTERING_FAILED = "报账点缴费信息录入失败！";
    /**
     * 报账点关联机房缴费信息录入失败！
     */
    public static String BILLACCOUNT_LINKROOM_PAYMENT_ENTERING_FAILED = "报账点关联机房或铁塔缴费信息录入失败！";
    /**
     * 未找到需要审核的项目！
     */
    public static String NOT_FIND_NEED_AUDIT_PROJECTS = "未找到需要审核的项目！";
    /**
     * 10006
     */
    public static String RESOURCE_TYPE_10006 = "10006";
    /**
     * 10005
     */
    public static String RESOURCE_TYPE_10005 = "10005";
    /**
     * 导入铁塔终止服务信息成功,共导入
     */
    public static String IMPORT_TOWER_TERMINATION_SERVICE_INFO_SUCCESS = "导入铁塔终止服务信息成功,共导入";
    /**
     * png
     */
    public static String PNG = "png";
    /**
     * can not get property activityId from processInstance:
     */
    public static String SYSO_CAN_NOT_GET_ACTIVITY_ID = "can not get property activityId from processInstance:";
    /**
     * 终止服务单.xls
     */
    public static String TERMINATE_SERVICE_ORDER_XLS = "终止服务单.xls";
    /**
     * 正在保存数据
     */
    public static String BEING_SAVE_DATA = "正在保存数据...";
    /**
     * 正在保存错误数据
     */
    public static String BEING_SAVE_ERRDATA = "正在保存错误数据...";
    /**
     * 保存错误数据成功
     */
    public static String BEING_SAVE_ERRDATA_SUCCESS = "保存错误数据成功...";
    /**
     * 正在保存源数据
     */
    public static String BEING_SAVE_RESOURCEDATA = "正在保存源数据...";
    /**
     * 保存源数据成功.
     */
    public static String BEING_SAVE_RESOURCEDATA_SUCCESS = "保存源数据成功...";
    /**
     * 导入成功
     */
    public static String IMPORT_SUCCESS = "导入成功。";
    /**
     * 共导入
     */
    public static String IMPORT_SUM = "共导入";
    /**
     * 条数据
     */
    public static String IMPORT_SUCCESS_NUMBER = "条数据";
    /**
     * 已经存在
     */
    public static String ALREADY_EXIST_NUMBER = "已经存在";
    /**
     * ,不符合规范
     */
    public static String NOT_INLINEWITH_NORM = "不符合规范";
    /**
     * SUPPLIER-
     */
    public static String SUPPLIER_ID = "SUPPLIER-";
    /**
     * 是
     */
    public static String YES = "是";
    /**
     * 否
     */
    public static String NO = "否";
    /**
     * 条
     */
    public static String STRIP = "条";
    /**
     * xml
     */
    public static String XML = "xml";
    /**
     * catalina.home
     */
    public static String CATALINA_HOME = "catalina.home";
    /**
     * importProcessMsg
     */
    public static String IMPORT_PROCESS_MSG = "importProcessMsg";
    /**
     * importProcess
     */
    public static String IMPORT_PROCESS = "importProcess";
    /**
     * 100
     */
    public static String STR_100 = "100";
    /**
     * 5
     */
    public static String STR_5 = "5";
    /**
     * resource
     */
    public static String RESOURCE = "resource";
    /**
     * 1440
     */
    public static String STR_1440 = "1440";
    /**
     * 其他
     */
    public static String OTHER = "其他";
    /**
     * .zip
     */
    public static String SUFFIX_ZIP = ".zip";
    /**
     * D:\\temp
     */
    public static String ZIP_PATH = "D:\\temp";
    /**
     * 生成账单成功！
     */
    public static String GENERATED_BILLS_SUCCESS = "生成账单成功！";
    /**
     * 移动侧账单生成成功！
     */
    public static String GENERATED_Mobile_BILLS_SUCCESS = "移动侧账单生成成功！";
    /**
     * 重新生成账单成功！
     */
    public static String AGAIN_GENERATED_BILLS_SUCCESS = "重新生成账单成功！";
    /**
     * #
     */
    public static String JING_SYMBOL = "#";
    /**
     * 新增了
     */
    public static String INSERT_INFO = "新增了";
    /**
     * 更新了
     */
    public static String UPDATE_INFO = "更新了";
    /**
     * ""
     */
    public static String KONG_SYMBOL = "";
    /**
     * regIds
     */
    public static String REGLDS = "regIds";
    /**
     * alias
     */
    public static String ALIAS = "alias";
    /**
     * reg
     */
    public static String REG = "reg";
    /**
     * The scale must be a positive integer or zero
     */
    public static String PROPORTION_INT_ZERO = "The scale must be a positive integer or zero";
    /**
     * 账单对账
     */
    public static String BILL_RECONCILIATION = "账单对账";
    /**
     * 账单确认
     */
    public static String BILL_CONFIRM = "账单确认";
    /**
     * 铁塔结算账单确认
     */
    public static String TW_BILL_CONFIRM = "铁塔结算账单确认";
    /**
     * :
     */
    public static String COLON_SYMBOL = ":";
    /**
     * ,
     */
    public static String COMMA_SYMBOL = ",";
    /**
     * 请选择需要操作的数据
     */
    public static String SELECT_OPERATION_DATAS = "请选择需要操作的数据";
    /**
     * /
     */
    public static String SLASH_SYMBOL = "/";
    /**
     * 未获取到部门信息
     */
    public static String NO_DEPARTMENT_INFO = "未获取到部门信息";
    /**
     * -------集团短信下发返回：----------
     */
    public static String GROUP_SEND_SMS_RETURN = "-------集团短信下发返回：----------";
    public static String VERIFY_CODE_SEND_SMS_RETURN = "-------验证码登录下发返回结果：----------";
    /**
     * "该地市罚金已经计算完成并且已生成费用汇总，不可再次计算"
     */
    public static String FINE_YES_COUNT = "该地市罚金已经计算完成并且已生成费用汇总，不可再次计算";
    /**
     * ../editor/stencilsets/bpmn2.0/bpmn2.0.json
     */
    public static String URL = "../editor/stencilsets/bpmn2.0/bpmn2.0.json";
    /**
     * No
     */
    public static String NO_EN = "No";
    /**
     * Error converting {}
     */
    public static String ERROR_CONVERT = "Error converting {}";
    /**
     * Boundary event
     */
    public static String BOUNDARY_EVENT = "Boundary event ";
    /**
     * is not attached to any activity
     */
    public static String EVENT_NOT_ATTACHED_TO_ACTIVITY = " is not attached to any activity";
    /**
     * Skipping edge {} because source ref is null
     */
    public static String SKIP_EDGE_AND_SOURCE_REF_NULL = "Skipping edge {} because source ref is null";
    /**
     * Skipping edge {} because target ref is null
     */
    public static String SKIP_EDGE_AND_TARGET_REF_NULL = "Skipping edge {} because target ref is null";
    /**
     * 该地市罚金已经计算完成但并未生成费用汇总，数据更新完成
     */
    public static String FINE_YES_COUNT_NO_SUMMARY_YES_UPDATE = "该地市罚金已经计算完成但并未生成费用汇总，数据更新完成";
    /**
     * 正在更新数据
     */
    public static String BEING_UPDATE_DATA = "正在更新数据...";
    /**
     * "该月账单中未存在"
     */
    public static String NO_BILL_DATAS = "该月账单中未存在";
    /**
     * processinstance
     */
    public static String SCOPE_PROCESS_INSTANCE_LOWER_CASE = "processinstance";
    /**
     * http://activiti.com/modeler
     */
    public static String MODELER_NAMESPACE = "http://activiti.com/modeler";
    /**
     * http://activiti.org/test
     */
    public static String TARGET_NAMESPACE = "http://activiti.org/test";
    /**
     * image
     */
    public static String IMAGE = "image";
    /**
     * zip
     */
    public static String ZIP = "zip";
    /**
     * bar
     */
    public static String BAR = "bar";
    /**
     * bpmn
     */
    public static String BPMN = "bpmn";
    /**
     * bpmn20.xml
     */
    public static String BPMN20_XML = "bpmn20.xml";
    /**
     * suspend
     */
    public static String STATE_SUSPEND = "suspend";
    /**
     * active
     */
    public static String STATE_ACTIVE = "active";
    /**
     * null
     */
    public static String NOTHING_NULL = "null";
    /**
     * 扣罚名称
     */
    public static String FINE_NAME = "扣罚名称";
    /**
     * 扣罚明细
     */
    public static String FINE_DETAIL = "扣罚明细";
    /**
     * 张三
     */
    public static String ZS = "张三";
    /**
     * 黄埔区
     */
    public static String HUANGPU_DISTRICT = "黄埔区";
    /**
     * "地市自设考核指标扣罚"
     */
    public static String CITY_SELFASSESSMENT_INDT_DT = "地市自设考核指标扣罚";
    /**
     * todo
     */
    public static String TO_DO = "todo";
    /**
     * startEvent
     */
    public static String START_EVENT = "startEvent";
    /**
     * endEvent
     */
    public static String END_EVENT = "endEvent";
    /**
     * 2017-7
     */
    public static String DATE_20177 = "2017-7";
    /**
     * 运营商地市
     */
    public static String CARRIERS_CTIY = "运营商地市";
    /**
     * 需求承接地市
     */
    public static String NEED_CITY = "需求承接地市";
    /**
     * 站址所属地市
     */
    public static String SITE_CITY = "站址所属地市";
    /**
     * exclusiveGateway
     */
    public static String EXCLUSIVE_GATEWAY = "exclusiveGateway";
    /**
     * userType
     */
    public static String USER_TYPE = "userType";
    /**
     * userTask 用户节点类型
     */
    public static String USER_TASK_TYPE = "userTask";
    /**
     * candidate
     */
    public static String CANDIDATE = "candidate";
    /**
     * .png
     */
    public static String SUFFIX_PNG = ".png";
    /**
     * 运营商区县
     */
    public static String CARRIERS_REGION = "运营商区县";
    /**
     * executionListeners
     */
    public static String EXECUTION_LISTENERS = "executionListeners";
    /**
     * properties
     */
    public static String PROPERTIES = "properties";
    /**
     * multiInstance
     */
    public static String MULTI_INSTANCE = "multiInstance";
    /**
     * sequential
     */
    public static String SEQUENTIAL = "sequential";
    /**
     * parallel
     */
    public static String PARALLEL = "parallel";
    /**
     * ,未在铁塔侧关联
     */
    public static String NO_CORRELATE_TOWER = ",未在铁塔侧关联";
    /**
     * flow
     */
    public static String FLOW = "flow";
    /**
     * not implement method.
     */
    public static String NO_IMPLEMENT_METHOD = "not implement method.";
    /**
     * Process instance could not be found
     */
    public static String PROCESS_INSTANCE_NOT_FOUND = "Process instance could not be found";
    /**
     * No process definition id provided
     */
    public static String NO_PROCESS_DEF_ID = "No process definition id provided";
    /**
     * Process definition
     */
    public static String PROCESS_DEF = "Process definition ";
    /**
     * could not be found
     */
    public static String NOT_FOUND = " could not be found";
    /**
     * 准备推送----1：
     */
    public static String READY_TO_PUSH1 = "准备推送----1：";
    /**
     * 已推送汇总单，将不重复推送：
     */
    public static String PUSHED_SUMMARY = "已推送汇总单，将不重复推送：";
    /**
     * 准备推送----2：
     */
    public static String READY_TO_PUSH2 = "准备推送----2：";
    /**
     * -缴电费
     */
    public static String PAYMENT = "-缴电费";
    /**
     * -缴租费
     */
    public static String PAYMENT_RENT = "-缴租费";
    /**
     * -预付费
     */
    public static String PAYMENT_LOAN = "-预付费";
    /**
     * -核销
     */
    public static String PAYMENT_VERIFICATION = "-核销";
    /**
     * 供应商服务类别
     */
    public static String SUPPLIER_SERVICE_CATEGORY = "供应商服务类别";
    /**
     * Wrapping response with JSONP callback '
     */
    public static String WRAPPING_RESPONSE_WITH_JSONP_CALLBACK = "Wrapping response with JSONP callback '";
    /**
     * yyyy-MM-dd HH:mm
     */
    public static String FORMAT_yyyyMMddHHmm = "yyyy-MM-dd HH:mm";
    /**
     * yyyy-MM
     */
    public static String FORMAT_yyyyMM = "yyyy-MM";
    /**
     * yyyy-MM
     */
    public static String FORMAT_yyyyMMdd = "yyyy-MM-dd";
    /**
     * %
     */
    public static String PERCENT_SIGN = "%";
    /**
     * _
     */
    public static String UNDER_LINE = "_";
    /**
     * 无标杆
     */
    public static String NO_BENCHMARK = "无标杆";
    /**
     * 无稽核
     */
    public static String NO_CHECKRESULT = "无稽核";
    /**
     * ------------开始推送报账接口：
     */
    public static String START_PUSHING_THE_BILLING_INTERFACE = "------------开始推送报账接口：";
    /**
     * ------------推送完成：
     */
    public static String PUSH_COMPLETION1 = "------------推送完成：";
    /**
     * 推送完成:
     */
    public static String PUSH_COMPLETION2 = "推送完成:";
    /**
     * 修改状态:
     */
    public static String UPDATE_STATE = "修改状态:";
    /**
     * 网络接口异常!请重试
     */
    public static String NETWORK_INTERFACE_EXCEPTION = "网络接口异常!请重试";
    /**
     * 不存在
     */
    public static String NO_BEING = "不存在";
    /**
     * 条,已执行插入
     */
    public static String INSERTED = "条,已执行插入";
    /**
     * job返回：---------------
     */
    public static String RETURN_FROM_JOB = "job返回：---------------";
    /**
     * 移动账单
     */
    public static String MOBILE_BILLS = "移动账单";
    /**
     * 标杆类型
     */
    public static String BENCHMARKING_TYPE = "标杆类型";
    /**
     * 历史电费标杆-同比
     */
    public static String HISTORICAL_BENCHMARK_YEAR_ON_YEAR = "历史电费标杆-同比";
    /**
     * 历史电费标杆-环比
     */
    public static String HISTORICAL_BENCHMARK_RING = "历史电费标杆-环比";
    /**
     * 平峰谷均价标杆
     */
    public static String PRICE_AVG_NOTAX_BENCHMARK = "平峰谷均价标杆";
    /**
     * 是否超标
     */
    public static String IF_OVER_STANDARD = "是否超标";
    /**
     * 超标率
     */
    public static String OVER_STANDARD_RATE = "超标率";
    /**
     * 0.00%
     */
    public static String ZERO_PERCENT = "0.00%";
    /**
     * Converting from milliseconds to Date fails!
     */
    public static String CONVERT_MILLISECONDS_DATE_FAILS = "Converting from milliseconds to Date fails!";
    /**
     * candidateUserIdExpressions
     */
    public static String CANDIDATE_USERID_EXPRESS = "candidateUserIdExpressions";
    /**
     * candidateGroupIdExpressions
     */
    public static String CANDIDATE_GROUPID_EXPRESS = "candidateGroupIdExpressions";
    /**
     * granting previledges for [%s, %s, %s] on [%s, %s]
     */
    public static String FORMAT = "granting previledges for [%s, %s, %s] on [%s, %s]";
    /**
     * N
     */
    public static String SUCCESS_NO_STR = "N";
    /**
     * Y
     */
    public static String SUCCESS_YES_STR = "Y";
    /**
     * 上报异常!
     */
    public static String ERR_REPORT_MSG = "上报异常，接口返回数据为null！";
    /**
     * 30000
     */
    public static String ERR_REPORT_CODE = "30000";
    /**
     * 接口服务地址错误!
     */
    public static String ERR_SERVICE_ADDRESS = "接口服务地址错误!";
    /**
     * 上报地址错误!
     */
    public static String ERR_REPORT_ADDRESS = "上报地址错误!";
    /**
     * 上报对象不能为空!
     */
    public static String ERR_REPORT_NULL_OBJECT = "上报对象不能为空!";
    /**
     * 铁塔账单
     */
    public static String TOWER_BILLS = "铁塔账单";
    /**
     * 用户任务
     */
    public static String USER_TASK = "用户任务";
    /**
     * 系统任务
     */
    public static String SERVICE_TASK = "系统任务";
    /**
     * 开始节点
     */
    public static String START_NODE = "开始节点";
    /**
     * 结束节点
     */
    public static String END_NODE = "结束节点";
    /**
     * 条件判断节点(系统自动根据条件处理)
     */
    public static String EXCLUSIVE_GATE = "条件判断节点(系统自动根据条件处理)";
    /**
     * 并行处理任务
     */
    public static String INCLUSIVE_GATEWAY = "并行处理任务";
    /**
     * 子流程
     */
    public static String CALL_ACTIVITY = "子流程";
    /**
     * 网络接口异常!
     */
    public static String ERR_NET_INTERFACE = "网络接口异常!";
    /**
     * 获取列表失败！
     */
    public static String ERR_GET_LIST = "获取列表失败！";
    /**
     * 合同差异比对成功!
     */
    public static String CONTRACT_COMPARE_SUCCESS = "合同差异比对成功!";
    /**
     * 合同差异比对失败!
     */
    public static String CONTRACT_COMPARE_FAIL = "合同差异比对失败!";
    /**
     * ?system_id=CMCC_NCMP&submit_date=
     */
    public static String BASE_URL_SUBMIT_DATE = "?system_id=CMCC_NCMP&submit_date=";
    /**
     * &page_size=
     */
    public static String BASE_URL_PAGE_SIZE = "&page_size=";
    /**
     * &current_page=
     */
    public static String BASE_URL_CURRENT_PAGE = "&current_page=";
    /**
     * ----------------columnSize:
     */
    public static String COLUMN_SIZE = "----------------columnSize:";
    /**
     * ----------------resultSize:
     */
    public static String RESULT_SIZE = "----------------resultSize:";
    /**
     * 压缩文件不合法,可能被损坏.
     */
    public static String COMPRESS_FILES_NOT_LEGAL_AND_DAMAGE = "压缩文件不合法,可能被损坏.";
    /**
     * POST
     */
    public static String POST = "POST";
    /**
     * Content-Type
     */
    public static String CONTENT_TYPE = "Content-Type";
    /**
     * text/xml;charset=UTF-8
     */
    public static String CONTENT_TYPE_TEXT_XML = "text/xml;charset=UTF-8";
    /**
     * text/javascript;charset=UTF-8
     */
    public static String CONTENT_TYPE_TEXT_JAVASCRIPT = "text/javascript;charset=UTF-8";
    /**
     * -请选择-
     */
    public static String PLEASE_CHOOSE = "-请选择-";
    /**
     * 系统异常
     */
    public static String SYSTEM_EXCEPTION = "系统异常";
    /**
     * 发送成功流水号：
     */
    public static String SEND_SERIAL_NUMBER_SUCCESS = "发送成功流水号：";
    /**
     * \\properties\\sysConfig.properties
     */
    public static String URL_SYSCONFIG = "\\properties\\sysConfig.properties";
    /**
     * 账期月份不能为空
     */
    public static String PAYMENT_MOUNTH_ISNOT_NULL = "账期月份不能为空";
    /**
     * ,请该用户重新登录，区县信息方可生效！
     */
    public static String PLEASE_RELOAD = "，请该用户重新登录，区县信息方可生效！";
    /**
     * 集团工单处理业务名称
     */
    public static String JT_REJECT_TASK_NAME = "集团派发工单";
    /**
     * 集团上报数据收集 grpDatacollect
     */
    public static String GRP_DATA_COLLECT = "grpDatacollect";
    /**
     * login
     */
    public static String URL_LOGIN = "login";
    /**
     * 回调报账接口
     */
    public static String URL_ExpendApplyStatus = "expendApplyStatus";
    /**
     * 客服接口
     */
    public static String URL_IM_CLIENT = "/im/client";
    /**
     * 合同访问租费详情页
     */
    public static String URL_RENTCONTRACTINFO = "/queryRentContractInfo";
    /**
     * 合同访问电费详情页
     */
    public static String URL_ELECONTRACTINFO = "/queryEleContractInfo";
    /**
     * 角色名集合key值 tableName
     */
    public static String TABLENAME = "tableName";
    /**
     * 角色名集合key值 id
     */
    public static String ID = "id";
    /**
     * 角色名集合key值 regId
     */
    public static String REGID = "regId";
    /**
     * 路径 act/actModelCreate
     */
    public static String WAY_ACT_ACTMODELCREATE = "act/actModelCreate";
    /**
     * index
     */
    public static String INDEX = "index";
    /**
     * No-cache
     */
    public static String NO_CACHE = "No-cache";
    /**
     * image/jpeg
     */
    public static String CONTENT_TYPE_IMAGE_JPEG = "image/jpeg";
    /**
     * 图片宽w:134
     */
    public static int IMAGE_W = 134;
    /**
     * 图片高h:40
     */
    public static int IMAGE_H = 40;
    /**
     * err/sso_error
     */
    public static String URL_ERR_SSO_ERROR = "err/sso_error";
    /**
     * redirect:/welcome
     */
    public static String URL_REDIRECT_WELCOME = "redirect:/welcome";
    /**
     * /WEB-INF/login.jsp
     */
    public static String URL_WEB_LOGIN = "/WEB-INF/login.jsp";
    /**
     * 路径 modules/act/actProcessRunningList
     */
    public static String WAY_ACT_ACTPROCESSRUNNINGLIST = "modules/act/actProcessRunningList";
    /**
     * 路径 modules/act/actProcessDeploy
     */
    public static String WAY_ACT_ACTPROCESSDEPLOY = "modules/act/actProcessDeploy";
    /**
     * 路径 redirect:/act/process
     */
    public static String REDIRECT_ACT_PROCESS = "redirect:/act/process";
    /**
     * 路径 redirect:/act/process/running/
     */
    public static String REDIRECT_ACT_PROCESS_RUNNING = "redirect:/act/process/running/";
    /**
     * 路径 \\com\\xunge\\messages\\twr_bizchange_item.properties
     */
    public static String TWR_BIZCHANGE_ITEM_PROPERTIES = "\\com\\xunge\\messages\\twr_bizchange_item.properties";
    /**
     * 数据部分导入成功
     */
    public static String IMPORTED_SUCCESS = "数据部分导入成功";
    /**
     * CID
     */
    public static String CID = "CID";
    /**
     * 编码
     */
    public static String WORDS_CODEING = "编码";
    /**
     * 换表
     */
    public static String WORDS_CHANGE_TABLE = "换表";
    /**
     * ELECONTRACT-
     */
    public static String ELECONTRACT = "ELECONTRACT-";

//	/**
//	 * Mobilerent/
//	 */
//	public static String URL_RENT = "mobileRent/";
    /**
     * dat_contract
     */
    public static String BUSINESS_TYPE = "dat_contract";
    /**
     * dat_mobileRent
     */
    public static String BUSINESS_TYPE_RENT = "dat_mobileRent";
    /**
     * 0+?$
     */
    public static String STR_ONE = "0+?$";
    /**
     * [.]$
     */
    public static String STR_TWO = "[.]$";
    /**
     * payment/
     */
    public static String URL_PAYMENT = "payment/";
    /**
     * AppRpc
     */
    public static String URL_APP = "AppRpc";
    public static String URL_NOTICE = "notice/";
    /**
     * 清零
     */
    public static String WORDS_CLEARED = "清零";
    /**
     * 第
     */
    public static String WORDS_FIRST = "第";
    /**
     * "，"
     */
    public static String SYMBOL_COMMA = "，";
    /**
     * "行数据有错误信息，无法导入，其余数据成功导入。"
     */
    public static String WORDS_THE_LINE_DATA_HAS_ERROR = "行数据有错误信息，无法导入，其余数据成功导入。";
    /**
     * 告警
     */
    public static String URL_WARNING = "warning/";
    /**
     * 追缴业务类型
     */
    public static String BUSINESS_TYPE_ELE_RECOVERPAYMENT = "ele_recover_payment";
    /**
     * ele_payment
     */
    public static String BUSINESS_TYPE_ELE_PAYMENT = "ele_payment";
    /**
     * ele_audit_payment
     */
    public static String BUSINESS_TYPE_ELE_AUDIT_PAYMENT = "ele_audit_payment";
    /**
     * ele_payment
     */
    public static String BUSINESS_TYPE_ELE_PAYMENT_BATCH = "ele_payment_batch";
    /**
     * ele_loan
     */
    public static String BUSINESS_TYPE_ELE_LOAN = "ele_loan";
    /**
     * ele_loan
     */
    public static String BUSINESS_TYPE_ELE_LOAN_AUDIT = "ele_audit_loan";
    /**
     * ele_loan
     */
    public static String BUSINESS_TYPE_ELE_LOAN_BATCH = "ele_loan_batch";
    /**
     * rent_payment
     */
    public static String BUSINESS_TYPE_RENT_PAYMENT = "rent_payment";
    /**
     * rent_audit_payment
     */
    public static String BUSINESS_TYPE_RENT_AUDIT_PAYMENT = "rent_audit_payment";
    public static String BUSINESS_TYPE_ELE_VERIFICATION = "ele_verification";

    public static String BUSINESS_TYPE_ELE_VERIFICATION_AUDIT = "ele_audit_verification";
    /**
     * powerratingDegree
     */
    public static String POWER_RATE_DEGREE = "powerratingDegree";
    /**
     * "000000"
     */
    public static String SYMBOL_STR000000 = "000000";
    /**
     * electricmeterDegree
     */
    public static String ELECTRIC_METER_DEGREE = "electricmeterDegree";
    /**
     * electricmeterDegreeIn
     */
    public static String ELECTRIC_METER_DEGREE_IN = "electricmeterDegreeIn";
    /**
     * powerloadDegree
     */
    public static String POWER_LOAD_DEGREE = "powerloadDegree";
    /**
     * day
     */
    public static String DAY_STR = "day";

    /**
     * flagDay
     */
    public static String FLAG_DAY_STR = "flagDay";
    /**
     * 2015-1-1
     */
    public static String DATE_20150101 = "2015-1-1";
    /**
     * 24*1000*60*60
     */
    public static int NUMBER_DAYS = 24 * 1000 * 60 * 60;
    /**
     * billaccountId:
     */
    public static String BILL_ACCOUNT_ID = "billaccountId: ";
    /**
     * regid:
     */
    public static String REG_ID = "regid: ";
    /**
     * ATTACHMENT-
     */
    public static String ATTACHMENT_ID = "ATTACHMENT-";
    /**
     * com/xunge/messages/regNameToRegId.properties
     */
    public static String PROPERTIES_LOADER = "com/xunge/messages/regNameToRegId.properties";
    /**
     * "无记录信息或者不在服务期内"
     */
    public static String NO_INFO_NO_SERVICE = "无记录信息或者不在服务期内";
    /**
     * "请选择合同"
     */
    public static String PLEASE_CHOSE_CONTRACT_FIRST = "请关联合同！";
    /**
     * 业务未知异常
     */
    public static String BUSINESS_UNKNOWN_EXCEPTION = "业务未知异常";
    /**
     * 参数未知异常
     */
    public static String PARAMETER_UNKNOWN_EXCEPTION = "参数未知异常";
    /**
     * 未知异常
     */
    public static String UNKNOWN_EXCEPTION = "未知异常";
    /**
     * 报账点编码重复，请重新填写！
     */
    public static String BILLACCOUNT_CODE_REPETITION = "报账点编码重复，请重新填写!";
    /**
     * Expected session attribute 'user'
     */
    public static String SESSION_ERROR_MSG = "Expected session attribute 'user'";
    /**
     * jt_datacollect_send_provincebuz_tempid
     */
    public static String SEND_PROVINCEBUZ_TEMPID = "jt_datacollect_send_provincebuz_tempid";
    /**
     * jt_datacollect_reject_provinceleader_tempid
     */
    public static String REJECT_PROVINCELEADER_TEMPID = "jt_datacollect_reject_provinceleader_tempid";
    /**
     * jt_datacollect_reject_provincebuz_tempid
     */
    public static String REJECT_PROVINCEBUZ_TEMPID = "jt_datacollect_reject_provincebuz_tempid";
    /**
     * jt_datacollect_reject_provincebuz_other_tempid
     */
    public static String REJECT_PROVINCEBUZ_OTHER_TEMPID = "jt_datacollect_reject_provincebuz_other_tempid";
    /**
     * jt_datacollect_send_leaderandcopy_tempid
     */
    public static String SEND_LEADERANDCOPY_TEMPID = "jt_datacollect_send_leaderandcopy_tempid";
    /**
     * jt_datacollect_send_provinceleader_tempid
     */
    public static String SEND_PROVINCELEADER_TEMPID = "jt_datacollect_send_provinceleader_tempid";
    /**
     * 已审核
     */
    public static String END_AUDITED = "已审核！";
    /**
     * 退回数据成功
     */
    public static String SEND_BACK_INFO_SUCCESS = "退回数据成功！";
    /**
     * 用户退回数据失败
     */
    public static String SEND_BACK_INFO_FAILED = "退回数据失败！";
    /**
     * 删除成功
     */
    public static String DELETE_SUCCESS = "删除成功！";
    /**
     * 删除失败
     */
    public static String DELETE_FAILED = "删除失败！";
    /**
     * 当前主合同状态为非正常，不能进行缴费！
     */
    public static String CONTRACT_STATE_UNNORMAL = "当前主合同状态为非正常，不能进行缴费！";
    /**
     * 当前租费合同尚未审核通过，不能进行缴费！
     */
    public static String CONTRACT_AUDIT_STATE_UNNORMAL = "当前租费合同尚未审核通过，不能进行缴费！";
    /**
     * 当前报账点关联资源点非在网，不能进行缴费！
     */
    public static String RESOURCE_STATE_UNNORMAL = "该报账点关联资源点非在网，不能进行缴费！";
    /**
     * 当前报账点关联铁塔信息非在网，不能进行缴费！
     */
    public static String TOWER_STATE_UNNORMAL = "该报账点关联铁塔信息非在网，不能进行缴费！";
    /**
     * 当前资源点尚未审核通过，不能进行缴费！
     */
    public static String RESOURCE_AUDIT_STATE_UNNORMAL = "该报账点关联资源未经过审核，不能进行缴费！";
    /**
     * 当前铁塔信息尚未审核通过，不能进行缴费！
     */
    public static String TOWER_AUDIT_STATE_UNNORMAL = "该报账点关联铁塔信息未经过审核，不能进行缴费！";
    /**
     * 当前保帐点尚未关联资源的或者铁塔信息，不能进行缴费！
     */
    public static String RESOURCE_AUDIT_STATE_UNNORMAL_TWO = "当前保帐点尚未关联资源的或者铁塔信息，不能进行缴费！";
    /**
     * 尚未关联合同，不能进行缴费！
     */
    public static String CONTRACT_UNLINKED = "尚未关联合同，不能进行缴费！";
    /**
     * 移动侧导入结果：
     */
    public static String UNLINKMOBILEDATA = "移动侧导入结果：";
    /**
     * 新增导入数据
     */
    public static String NEWIMPORTDATA = "新增导入数据";
    /**
     * 铁塔侧导入结果：导入模板数据共
     */
    public static String TOWERRESULTDATA = "铁塔侧导入结果：导入模板数据共";
    /**
     * 不符合规范数据.xls
     */
    public static String NOSTANDARD = "不符合规范数据.xls";
    /**
     * 未与移动综资资源关联数据
     */
    public static String NO_MOBILE_RESOURCE_DATA = "未与移动综资资源关联数据";
    /**
     * 没有信息
     */
    public static String NO_MSG = "-";
    /**
     * 此合同主合同信息已修改，请刷新页面重新修改！
     */
    public static String ANEW_UPDATE_DATA = "此合同主合同信息已修改，请刷新页面重新修改！";
    /**
     * 缴费时关联合同与最新合同不一致，不能修改历史缴费！
     */
    public static String CONTRACT_NOT_SAME = "缴费时关联合同与最新合同不一致，不能修改历史缴费！";
    /**
     * 报账点最新状态不合法，未关联合同，不能修改历史缴费！
     */
    public static String NO_NEW_CONTRACT = "报账点最新状态不合法，未关联合同，不能修改历史缴费！";
    /**
     * 缴费时数据不合法，未关联合同，不能修改历史缴费！
     */
    public static String NO_OLD_CONTRACT = "缴费时数据不合法，未关联合同，不能修改历史缴费！";
    /**
     * 缴费时关联供应商与最新供应商不一致，不能修改历史缴费！
     */
    public static String SUPPLIER_NOT_SAME = "缴费时关联供应商与最新供应商不一致，不能修改历史缴费！";
    /**
     * 报账点最新状态不合法，未关联供应商，不能修改历史缴费！
     */
    public static String NO_NEW_SUPPLIER = "报账点最新状态不合法，未关联供应商，不能修改历史缴费！";
    /**
     * 缴费时数据不合法，未关联供应商，不能修改历史缴费！
     */
    public static String NO_OLD_SUPPLIER = "缴费时数据不合法，未关联供应商，不能修改历史缴费！";
    /**
     * 缴费时关联机房数量与最新机房数量不一致，不能修改历史缴费！
     */
    public static String RES_NUM_NOT_SAME = "缴费时关联机房数量与最新机房数量不一致，不能修改历史缴费！";
    /**
     * 缴费时关联机房与最新机房不一致，不能修改历史缴费！
     */
    public static String RES_NOT_SAME = "缴费时关联机房与最新机房不一致，不能修改历史缴费！";
    /**
     * 租费缴费数据，
     */
    public static String RENT_PAYMENT_HIS = "租费缴费数据，";
    /**
     * 铁塔侧“审核完成，修改铁塔信息变更表审核状态”操作失败
     */
    public static String UPDATE_CHECK_STATE_FAILED = "铁塔侧-审核完成，修改铁塔信息变更表审核状态-操作失败";
    /**
     * “新增信息到无塔参数配置表”操作失败
     */
    public static String INSERT_NO_TWR_CONFIG_FAILED = "新增信息到无塔参数配置表-操作失败";
    /**
     * “根据移动起租表id修改铁塔信息变更字段”操作失败
     */
    public static String UPDATE_TWRRENT_INFO_BY_BIZCHANGE_FAILED = "'根据移动起租表id修改铁塔信息变更字段'操作失败";
    /**
     * “复制铁塔侧信息修改移动侧信息”操作失败
     */
    public static String UPDATE_TWR_RENT_INFO_FROM_COPY_TWR_TOWER_FAILED = "“复制铁塔侧信息修改移动侧信息”操作失败";
    /**
     * “复制铁塔侧信息新增到移动侧”操作失败
     */
    public static String INSERT_TWR_RENT_INFO_FROM_COPY_TWR_TOWER_FAILED = "“复制铁塔侧信息新增到移动侧”操作失败";
    /**
     * “根据业务确认单号和站址编码修改移动起租信息结束时间”操作失败
     */
    public static String UPDATE_END_DATE_BY_STOP_SERVER_FAILED = "“根据业务确认单号和站址编码修改移动起租信息结束时间”操作失败";
    /**
     * “更新报账点信息”操作失败
     */
    public static String UPDATE_BILLACCOUNT_INFO_FAILED = "“更新报账点信息”操作失败";
    /**
     * 导入资源信息操作失败
     */
    public static String IMPORT_BASE_RESOURCE_FAILED = "导入资源信息失败";
    /**
     * 铁塔服务终止表审核完成，修改审核状态操作失败
     */
    public static String UPDATE_STOP_SERVER_CHECK_STATE_FAILED = "铁塔服务终止表审核完成，修改审核状态-操作失败";
    /**
     * 审核通过修改铁塔服务终止数据状态为审核完成操作失败
     */
    public static String UPDATE_CHECK_STATE_BY_ID_FAILED = "审核通过修改铁塔服务终止数据状态为审核完成操作失败";
    /**
     * 合同信息， 租费合同信息，
     */
    public static String RENTCONTRACT_MSG = "租费合同信息，";
    /**
     * 合同信息， 大集中租费合同信息，
     */
    public static String RENTCONTRACT_CENTR_MSG = "大集中租费合同信息，";
    /**
     * 固化信息， 租费固化信息，
     */
    public static String RENTCONTRACT_CURING_MSG = "租费固化信息，";
    /**
     * 审核通过
     */
    public static String AUDITED_PASS = "审核通过！";
    /**
     * 审核失败
     */
    public static String AUDITED_NO_PASS = "审核失败！";
    /**
     * 终止合同成功
     */
    public static String STOP_CONTRACT_SUCCESS = "终止合同成功！";
    /**
     * 终止合同失败
     */
    public static String STOP_CONTRACT_FAIL = "终止合同失败！";
    /**
     * 租费报账点信息，
     */
    public static String RENT_BILLACCOUNT_MSG = "租费报账点信息，";
    /**
     * 新增集团扣罚信息汇总操作失败
     */
    public static String INSERT_TWR_GROUP_REG_PUNISH_FAILED = "新增集团扣罚信息汇总操作失败";
    /**
     * 修改集团扣罚信息汇总表失败
     */
    public static String UPDATE_TWR_GROUP_REG_PUNISH_FAILED = "修改集团扣罚信息汇总表失败";
    /**
     * 审核完成后修改铁塔起租表数据状态失败
     */
    public static String UPDATE_TOWER_CHECK_STATE_FAILED = "审核完成后修改铁塔起租表数据状态失败";
    /**
     * 新增短信下发历史记录失败
     */
    public static String INSERT_SMS_HISTORY_FAILED = "新增短信下发历史记录失败";
    /**
     * 导入供应商信息失败
     */
    public static String IMPORT_SUPPLIER_FAILED = "导入供应商信息失败";
    /**
     * 新增拆分数据（根据日期重叠的拆分纪录）操作失败
     */
    public static String INSERT_TOWER_RENT_HISTORY_FAILED = "新增拆分数据（根据日期重叠的拆分纪录）操作失败";
    /**
     * 修改最后一条拆分记录终止日期失败
     */
    public static String UPDATE_TWR_RENT_HISTORY_END_DATE_FAILED = "修改最后一条拆分记录终止日期失败";
    /**
     * 修改生效日期重叠的拆分数据操作失败
     */
    public static String UPDATE_TWR_HISTORY_CHANGE_ITEM_FAILED = "修改生效日期重叠的拆分数据操作失败";
    /**
     * 复制移动起租表到拆分表操作失败
     */
    public static String INSERT_TWR_HISTORY_FROM_MOBILE_FAILED = "复制移动起租表到拆分表操作失败";
    /**
     * 删除超出服务时间的拆分信息失败
     */
    public static String DALETE_TWR_HISTORY_OVER_DATE_FAILED = "删除超出服务时间的拆分信息失败";
    /**
     * 新增、续签租费合同操作失败
     */
    public static String INSERT_RENT_CONTRACT_FAILED = "新增、续签租费合同操作失败";
    /**
     * 删除租费合同失败
     */
    public static String DALETE_RENT_CONTRACT_FAILED = "删除租费合同失败";
    /**
     * 删除集团既定扣罚信息失败
     */
    public static String DALETE_TWR_GROUP_PUNISH_FAILED = "删除集团既定扣罚信息失败";
    /**
     * 根据扣罚信息id添加罚金失败
     */
    public static String UPDATE_PUNISH_AMOUNT_FAILED = "根据扣罚信息id添加罚金失败";
    /**
     * 导入站点信息操作失败
     */
    public static String IMPORT_SITE_FAILED = "导入站点信息操作失败";
    /**
     * 导入电表信息失败
     */
    public static String IMPORT_ELE_METER_FAILED = "导入电表信息失败";
    /**
     * 新增电费合同
     */
    public static String INSERT_ELE_CONTRACT = "新增电费合同";
    /**
     * 修改电费合同
     */
    public static String UPDATE_ELE_CONTRACT = "修改电费合同";
    /**
     * 删除电费合同
     */
    public static String DELETE_ELE_CONTRACT = "删除电费合同";
    /**
     * 删除电费固化信息
     */
    public static String DELETE_GH_CONTRACT = "删除电费固化信息";
    /**
     * 新增电表
     */
    public static String INSERT_ELETICMETER = "新增电表";
    /**
     * 修改电表信息
     */
    public static String UPDATE_ELETICMETER = "修改电表信息";
    /**
     * 删除电表
     */
    public static String DELETE_ELETICMETER = "删除电表";
    /**
     * 新增电费报账点信息
     */
    public static String INSERT_ELE_BILLACCOUNT = "新增电费报账点";
    /**
     * 修改电费报账点信息
     */
    public static String UPDATE_ELE_BILLACCOUNT = "修改电费报账点";
    /**
     * 删除电费报账点信息
     */
    public static String DELETE_ELE_BILLACCOUNT = "删除电费报账点";
    /**
     * 电费缴费录入
     */
    public static String ELE_BILLACCOUNT_PAYMENT = "电费缴费录入";
    /**
     * 修改电费缴费信息
     */
    public static String UPDATE_ELE_BILLACCOUNT_PAYMENT = "修改电费报账点缴费信息";
    /**
     * 修改电表缴费信息
     */
    public static String UPDATE_ELE_METER_PAYMENT = "修改电表缴费信息";
    /**
     * 刷新电表缴费信息
     */
    public static String REFRESH_ELE_METER_PAYMENT = "刷新电表缴费信息";
    /**
     * 删除电费缴费明细
     */
    public static String DELETE_ELE_PAYMENTDETAIL = "删除电费缴费明细";
    /**
     * 删除电费缴费明细
     */
    public static String DELETE_ELE_VERTIONDETAIL = "删除电费核销明细";
    /**
     * 删除电费缴费明细
     */
    public static String DELETE_ELE_LOANETAIL = "删除电费预付费明细";
    /**
     * 标杆参数
     */
    public static String ELE_BENCHMARK_PARA = "标杆参数";
    /**
     * 缴费标杆参数
     */
    public static String PAYMENT_BENCHMARK_PARA = "缴费标杆参数";
    /**
     * 编码为
     */
    public static String START_ELE_METER_ERR = "编码为";
    /**
     * 的电表时间格式转换失败！
     */
    public static String END_ELE_METER_ERR = "的电表时间格式转换失败！";
    /**
     * 导入租费固化信息失败！
     */
    public static String IMPORT_RENT_CONTRACT_FAILED = "导入租费固化信息失败！";
    /**
     * 导入租费固化信息成功！
     */
    public static String IMPORT_RENT_CONTRACT_SUCCESS = "导入租费固化信息成功！";
    /**
     * 导入电费固化信息失败！
     */
    public static String IMPORT_ELEC_CONTRACT_FAILED = "导入电费固化信息失败！";
    /**
     * 导入电费固化信息成功！
     */
    public static String IMPORT_ELEC_CONTRACT_SUCCESS = "导入电费固化信息成功！";
    /**
     * 报账点层面判断出缴费重复提示信息优化
     */
    public static String BILLACCOUNT_REPEAT_PAYMENTS = "不能重复缴费，该报账点已有";
    /**
     * 报账点层面判断出缴费重复提示信息优化
     */
    public static String BILLACCOUNT_REPEAT_VERIFICATE = "不能重复核销，该报账点已有";
    /**
     * 特殊报账点层面判断出缴费重复提示信息优化
     */
    public static String SPECIALBILLACCOUNT_REPEAT_PAYMENTS = "该报账点已有";
    /**
     * 重复提示信息优化
     */
    public static String TO = "至";
    /**
     * 重复提示信息优化
     */
    public static String REPEAT_PAYMENTS = "的缴费记录";
    /**
     * 重复提示信息优化
     */
    public static String REPEAT_VERIFICATE = "的核销记录";
    /**
     * 报账点下的资源判断缴费重复提示信息优化
     */
    public static String RESOURCE_REPEAT_PAYMENTS1 = "不能重复缴费，系统中【";
    /**
     * 报账点下的资源判断核销重复提示信息优化
     */
    public static String RESOURCE_REPEAT_VERIFICATE1 = "不能重复核销，系统中【";
    /**
     * 特殊报账点下的资源判断缴费重复提示信息优化
     */
    public static String SPECIALRESOURCE_REPEAT_PAYMENTS = "不能重复缴费，系统中【";
    /**
     * 报账点下的资源判断缴费重复提示信息优化
     */
    public static String RESOURCE_REPEAT_PAYMENTS2 = "】资源关联报账点：";
    public static String METER_REPEAT_PAYMENTS = "】电表关联报账点：";
    public static String CONTRACT_REPEAT_PAYMENTS = "】合同关联报账点：";
    /**
     * 报账点下的资源判断缴费重复提示信息优化
     */
    public static String RESOURCE_REPEAT_PAYMENTS3 = "时已有";
    /**
     * 铁塔信息导出-
     */
    public static String TOWER_INFO_EXPORT = "铁塔信息导出-";
    /**
     * 铁塔信息
     */
    public static String TOWER_INFO = "铁塔信息";
    /**
     * 铁塔信息
     */
    public static String BASE_TOWER = "TOWER-";
    /**
     * TENNA-
     */
    public static String TENNA_ID = "TENNA-";
    /**
     * POSITION-
     */
    public static String BASE_POSTION_ID = "POSITION-";
    /**
     * .rar
     */
    public static String SUFFIX_RAR = ".rar";
    /**
     * 铁塔资源批量起租单导入日志存入redis的key
     */
    public static String IMPORT_TOWERRENT_KEY = "plqzd_towerInfo";
    /**
     * 电费和租费汇总单编码生成重复问题提示
     */
    public static String BILLAMOUNT_CODE_DUPLICATE = "当前用户并发数已满，请稍等两分钟后再次操作";
    /**
     * 借款申请为空，不能生成或推送汇总单
     */
    public static String LOANDATE_NULL = "借款申请日期为空，不能生成或推送汇总单";
    /**
     * 借款申请为空，不能生成或推送汇总单
     */
    public static String LOANMONEY_NULL = "借款金额为空，不能生成或推送汇总单";
    /**
     * 分配权限
     */
    public static String CHOOSE_POWER = "分配权限";
    /**
     * 当前用户对应省份无权使用该接口
     */
    public static String NORIGHT_PROVINCE = "当前用户对应省份无权使用该接口";
    /**
     * 推送成功
     */
    public static String PUSH_SUCCESS = "推送成功";
    /**
     * 推送失败
     */
    public static String PUSH_FAILED = "推送失败";
    /**
     * 变更合同固化信息-不能变更供应商
     */
    public static String CANNOT_CHANGE_SUPPLIER = "不能变更供应商";
    /**
     * 转让合同固化信息-只能变更供应商
     */
    public static String TRANSFER_NEED_CHANGE_SUPPLIER = "转让协议必须更换供应商";
    /**
     * 用户解除数据成功
     */
    public static String OPERATION_RELIEVE_SUCCESS = "用户解除数据成功";
    /**
     * 用户解除数据失败
     */
    public static String OPERATION_RELIEVE_FAILED = "用户解除数据失败";
    /**
     * 用户变更数据成功
     */
    public static String OPERATION_CHANGE_SUCCESS = "用户变更数据成功";
    /**
     * 用户变更数据失败
     */
    public static String OPERATION_CHANGE_FAILED = "用户变更数据失败";
    /**
     * 用户转让数据成功
     */
    public static String OPERATION_TRANSFER_SUCCESS = "用户转让数据成功";
    /**
     * 用户转让数据失败
     */
    public static String OPERATION_TRANSFER_FAILED = "用户转让数据失败";
    /**
     * 转让协议必须更换供应商
     */
    public static String OPERATION_TRANSFER_FAILED_SUP = "转让协议必须更换供应商";
    /**
     * 缴费时关联机房或铁塔数量与最新机房或铁塔数量不一致，不能修改历史缴费！
     */
    public static String RES_TW_NUM_NOT_SAME = "缴费时关联机房或铁塔数量与最新机房或铁塔数量不一致，不能修改历史缴费！";
    /**
     * 缴费时关联机房或铁塔与最新机房或铁塔不一致，不能修改历史缴费！
     */
    public static String RES_TW_NOT_SAME = "缴费时关联机房或铁塔与最新机房或铁塔不一致，不能修改历史缴费！";
    /**
     * 铁塔产品服务费推送状态更新任务
     */
    public static String SYNTOWER_PUSH_STATE_TASK = "铁塔产品服务费汇总单推送状态同步";
    /**
     * 该地市已经添加过铁塔电费合同信息
     */
    public static String TOWER_CONTRACT_ONE = "该地市已经添加过铁塔电费合同信息";
    /**
     * 删除铁塔电费合同信息
     */
    public static String DELETE_TOWER_CONTRACT = "删除铁塔电费合同信息";
    /**
     * 修改铁塔电费合同信息
     */
    public static String UPDATE_ELEC_TOWER_CONTRACT = "修改铁塔电费合同信息";
    /**
     * 新增铁塔电费合同信息
     */
    public static String INSERT_ELEC_TOWER_CONTRACT = "新增铁塔电费合同信息";
    /**
     * 费用预算 提交成功
     */
    public static String EXPENSE_BUDGET_SUCCESS = "费用预算信息提交成功！";
    /**
     * 费用预算 提交失败
     */
    public static String EXPENSE_BUDGET_FAIL = "费用预算信息提交失败";
    /**
     * 费用预算 存在 省份
     */
    public static String EXPENSE_BUDGET_PRV_EXIT = "该省份及年份费用预算信息已存在";
    /**
     * 费用预算 存在 地市
     */
    public static String EXPENSE_BUDGET_CITY_EXIT = "该地市及年份费用预算信息已存在";
    /**
     * 费用预算 存在 区县
     */
    public static String EXPENSE_BUDGET_DISTRICT_EXIT = "该区县及年份费用预算信息已存在";
    /**
     * 费用预算 网络电费年预算已超出
     */
    public static String EXPENSE_BUDGET_ELE = "网络电费已超出";
    /**
     * 费用预算 房屋租赁费年预算已超出
     */
    public static String EXPENSE_BUDGET_RNET = "房屋租赁费年预算已超出";
    /**
     * 费用预算 铁塔公司年预算已超出
     */
    public static String EXPENSE_BUDGET_TOWER = "铁塔公司年预算已超出";
    /**
     * 费用预算 其他公司年预算已超出
     */
    public static String EXPENSE_BUDGET_OTHER = "其他公司年预算已超出";
    /**
     * 站点信息统计导出-
     */
    public static String SITE_INFO_STATISTICS_EXPORT = "站点信息统计导出-";
    /**
     * 站点信息统计
     */
    public static String SITE_INFO_STATISTICS = "站点信息统计";
    /**
     * 房屋租赁费月统计导出
     */
    public static String RENT_MONTH_STATISTICS_EXPORT = "房屋租赁费月统计导出-";
    /**
     * 房屋租赁费月统计
     */
    public static String RENT_MONTH_STATISTICS = "房屋租赁费月统计";
    /**
     * 站点电费月统计（省份）-预算及执行进度导出模板.xls
     */
    public static String SITE_MONTH_BUDGET_PRV = "站点电费月统计（省份）-预算及执行进度导出模板.xls";
    /**
     * 站点电费月统计（地市）-预算及执行进度导出模板.xls
     */
    public static String SITE_MONTH_BUDGET_PREG = "站点电费月统计（地市）-预算及执行进度导出模板.xls";
    /**
     * 站点电费月统计（区县）-预算及执行进度导出模板.xls
     */
    public static String SITE_MONTH_BUDGET_REG = "站点电费月统计（区县）-预算及执行进度导出模板.xls";
    /**
     * 站点电费月统计（省份）-预算及执行进度导出模板.xls
     */
    public static String SITE_MONTH_BUDGET_PRV_SNAPSHOOT = "站点电费月统计（省份）-预算及执行进度（快照）导出模板.xls";
    /**
     * 站点电费月统计（地市）-预算及执行进度导出模板.xls
     */
    public static String SITE_MONTH_BUDGET_PREG_SNAPSHOOT = "站点电费月统计（地市）-预算及执行进度（快照）导出模板.xls";
    /**
     * 站点电费月统计（区县）-预算及执行进度导出模板.xls
     */
    public static String SITE_MONTH_BUDGET_REG_SNAPSHOOT = "站点电费月统计（区县）-预算及执行进度（快照）导出模板.xls";
    /**
     * 移动缴电费（非铁塔公司站点）
     */
    public static String SITE_MONTH_MOBILE = "站点电费月统计-移动缴电费（非铁塔公司站点）";
    public static String ANALYSIS_RESULT = "关键指标分析结果表.xlsx";
    public static String ANALYSIS_RESULT_SNAPSHOOT = "关键指标分析结果（快照）表.xlsx";
    public static String ANALYSIS_RESULT_OF_YEAR = "关键指标分析结果表-年报.xlsx";
    /**
     * 移动公司缴电费（铁塔公司站点）
     */
    public static String SITE_MONTH_MOBILE_TOWER = "站点电费月统计-移动公司缴电费（铁塔公司站点）";
    /**
     * 铁塔公司缴电费（铁塔公司站点）
     */
    public static String SITE_MONTH_TOWER = "站点电费月统计-铁塔公司缴电费（铁塔公司站点）";
    /**
     * 铁塔公司缴电费（铁塔公司站点）
     * 应付月报
     */
    public static String SITE_MONTH_PAYABLE_TOWER = "站点电费月应付统计-铁塔公司缴电费（铁塔公司站点）";
    /**
     * 房屋租赁费预算执行情况导出
     */
    public static String RENT_MONTH_EXECUTION_EXPORT = "房屋租赁费预算执行情况-";
    /**
     * 房屋租赁费预算执行情况导出
     */
    public static String EXPENSE_BUDGET_EXECITION_EXPORT = "费用管理预算执行情况-";
    /**
     * 费用预算管理导出
     */
    public static String EXPENSE_BUDGET_EXPORT = "费用预算管理-";
    public static String OTHER_FEE_EXPORT = "其他站类费用";
    /**
     * 报账点不存在，操作失败
     */

    public static String BILLACCOUNT_DELETE = "报账点不存在，操作失败！";
    /**
     * 室分对账单
     */
    public static String ROOM_BILL_RECONCILIATION = "室分对账单";
    /**
     * 室分其他费用
     */
    public static String ROOM_BILL_OTHER = "室分其他费用";
    /**
     * 传输对账单
     */
    public static String TRANS_BILL_RECONCILIATION = "传输对账单";
    /**
     * 微站对账单
     */
    public static String TINY_BILL_RECONCILIATION = "微站对账单";
    /**
     * 非标对账单
     */
    public static String NONSTAND_BILL_RECONCILIATION = "非标对账单";
    /**
     * 非标对账单
     */
    public static String ROOM_NONSTAND_BILL_RECONCILIATION = "非标室分对账单";
    /**
     * 传输其他费用
     */
    public static String TRANS_BILL_OTHER = "传输其他费用";
    /**
     * 微站其他费用
     */
    public static String TINY_BILL_OTHER = "微站其他费用";
    /**
     * 非标其他费用
     */
    public static String NONSTAND_BILL_OTHER = "非标其他费用";
    /**
     * 移动侧账单修改详情
     */
    public static String BILL_MODIFLY = "移动侧账单修改详情";
    /**
     * 汇总单详情
     */
    public static String ACCOUNT_SUMMARY = "汇总单详情";
    /**
     * 铁塔账单退回编辑
     */
    public static String TW_BILL_CHARGE_BACK = "铁塔账单退回编辑";
    /**
     * 塔类产品服务费结算详单
     */
    public static String TW_BILL_DETAIL = "塔类产品服务费结算详单";
    public static String TW_BILL_BALANCE_MOBILE = "塔类移动账单导出";
    /**
     * 铁塔退单导出
     */
    public static String TW_BILL_CHARGE_BACK_TO_EXCEL = "铁塔退单导出";
    /**
     * 租费关键指标分析导出
     */
    public static String RENT_MONTH_KEY_INDEX_ANALYSIS_EXPORT = "租费关键指标分析导出-";
    /**
     * 租费关键指标分析
     */
    public static String RENT_MONTH_KEY_INDEX_ANALYSIS = "租费关键指标分析";
    /**
     * 报账点状态修改失败
     */
    public static String BILLACCOUNT_STATE_UPDATE_FAIL = "报账点状态修改失败";
    /**
     * 非省管理员，无权修改报账点状态
     */
    public static String BILLACCOUNT_STATE_UPDATE_NO_AUTHORITY = "您没有权限使用此操作。您可以修改报账点信息后提交审批，来更改报账点状态";
    /**
     * 电表状态修改失败
     */
    public static String ELECTRICMETER_STATE_UPDATE_FAIL = "电表状态修改失败";
    /**
     * 非省管理员，无权修改电表状态
     */
    public static String ELECTRICMETER_STATE_UPDATE_NO_AUTHORITY1 = "电表的启用\\停用状态只允许省公司管理员操作，您暂无权限。";
    /**
     * 电价下调影响统计详情导出
     */
    public static String ELEPRICE_REDUCE_DEEXPORT = "电价优惠政策影响情况统计表";
    /**
     * 电价下调影响统计列表导出
     */
    public static String ELEPRICE_REDUCE_LIEXPORT = "电价优惠政策影响情况列表导出-";
    /**
     * 电价下调影响统计附件路径
     */
    public static String URL_ELEREDUCE = "elereduce/";
    /**
     * 无APP抄表照片附件
     */
    public static String NOT_APP_METER_IMG = "无APP抄表照片附件";
    /**
     * 无APP抄表照片导出
     */
    public static String FILE_NAME_ATTACHMENT = "附件";
    /**
     * 非省管理员，无权修改抄表距离
     */
    public static String NOT_ALLOWED_UPDATE_DISTANCE = "抄表距离只允许省公司管理员操作，您暂无权限";
    /**
     * 审核结果  开始，通过，驳回，撤回,结束，删除审核通过，删除审核通过
     */
    public static String AUDIT_MSG_START = "开始";
    public static String AUDIT_MSG_PASS = "通过";
    public static String AUDIT_MSG_REJECT = "驳回";
    public static String AUDIT_MSG_CANCEL = "撤回";
    public static String AUDIT_MSG_FINISH = "结束";
    public static String AUDIT_MSG_NOT_PASS = "不通过";
    public static String AUDIT_MSG_DELETE_PASS = "删除审核通过";
    public static String AUDIT_MSG_DELETE_NOT_PASS = "删除审核不通过";

    public static String AUDIT_MSG_SENDBACK_PASS = "撤销审核通过";
    public static String AUDIT_MSG_SENDBACK_NOT_PASS = "撤销审核不通过";
    public static String AUDIT_MSG_MODIFY_AUDIT = "修改审批";
    public static String AUDIT_MSG_TURN = "转办";
    public static String FLOW_TURN = "olduserTask";

    /**
     * 电价压降统计表导出
     */
    public static String ELE_PRESSURE_DROP = "电价压降统计表";

    public static String TWR_PUNSH_DETAILS = "推送费用明细信息";

    /**
     * ext配置文件
     */
    public static String INSERT_EXT_CONFIG_SUCCESS = "新增EXT配置信息成功";
    public static String INSERT_EXT_CONFIG_FAIL = "新增EXT配置信息错误";

    public static String DELETE_EXT_CONFIG_SUCCESS = "删除EXT配置信息成功";
    public static String DELETE_EXT_CONFIG_FAIL = "删除EXT配置信息错误";

    public static String UPDATE_EXT_CONFIG_SUCCESS = "更新EXT配置信息成功";
    public static String UPDATE_EXT_CONFIG_FAIL = "更新EXT配置信息错误";

    /**
     * ext采集数据
     */
    public static String INSERT_EXT_PAYMENT_DETAIL_SUCCESS = "新增EXT采集数据成功";
    public static String INSERT_EXT_PAYMENT_DETAIL_FAIL = "新增EXT采集数据错误";

    /**
     * 站点电费月统计（省份）-预算及执行进度导出模板.xls
     */
    public static String SITE_MONTH_BUDGET_PRV_OF_YEAR = "站点电费月统计（省份）-预算及执行进度年报导出模板.xls";

    /**
     * 站点电费月统计（地市）-预算及执行进度导出模板.xls
     */
    public static String SITE_MONTH_BUDGET_PREG_OF_YEAR = "站点电费月统计（地市）-预算及执行进度年报导出模板.xls";

    /**
     * 站点电费月统计（区县）-预算及执行进度导出模板.xls
     */
    public static String SITE_MONTH_BUDGET_REG_OF_YEAR = "站点电费月统计（区县）-预算及执行进度年报导出模板.xls";

    /**
     * 参与的工单（导出）
     */
    public static String JOIN_WORK_EXPORT = "参与的工单导出";

    /**
     * 我的工单（导出）
     */
    public static String MY_WORK_EXPORT = "我的工单导出";

    public static String EXPORT_LIST_TOO_LONG = "需导出条目数量超出3000的最大限制，请先筛选列表后再尝试导出。";


    /**
     * 电费计提录入
     */
    public static String ELE_ACCRUAL_ADD = "电费计提录入";

    /**
     * 电费计提修改
     */
    public static String ELE_ACCRUAL_MODIFY = "电费计提修改";

    /**
     * 电费计提撤销
     */
    public static String ELE_ACCRUAL_CNACEL = "电费计提撤销";

    /**
     * 电费计提删除
     */
    public static String ELE_ACCRUAL_DELETE = "电费计提撤销";

    /**
     * 电费计提导入
     */
    public static String ELE_ACCRUAL_IMPORT = "电费计提导入";

    /**
     * 限制查询3个月内的数据
     */
    public static String RESTRICT_QUERIES_THREE_MONTHS = "目前仅支持查看、导出最多3个月的数据，需要导出更多时间段请分多次操作";

    public static String RESTRICT_QUERIES_SIX_MONTHS = "目前仅支持查看、导出最多6个月的数据，需要导出更多时间段请分多次操作";

	/**
	 * 租费计提修改
	 */
	public static String RENT_ACCRUAL_MODIFY = "计提修改";
	/**
	 * 租费计提修改
	 */
	public static String RENT_ACCRUAL_CANCEL = "租费计提撤销";
	/**
	 * 租费计提修改
	 */
	public static String RENT_ACCRUAL_MSG = "计提信息，";

	/**
	 * 租费计提总额配置导出
	 */
	public static String RENT_ACCRUAL_TOTAL_CONFIG_EXPORT = "租费计提总额配置";

	/**
	 * 三方塔计提总额配置导出
	 */
	public static String TOWER_ACCRUAL_TOTAL_CONFIG_EXPORT = "三方塔计提总额配置";

	/**
	 * 租赁平台合同信息导出
	 */
	public static String LEASE_PLATFORM_CONTRACT_EXPORT = "租赁平台合同信息导出";

    /**
     * 5G产品结构分析
     */
    public static String ANALYSIS_5G_REPORT = "5G产品结构分析表.xlsx";

    /**
     * 月度摊销金额导出
     */
    public static String MONTH_AMOUNT_AM_EXPORT = "月度摊销金额导出";

    /**
     * 月度摊销明细导出
     */
    public static String MONTH_AMOUNT_AM_DETAIL_EXPORT = "月度摊销明细导出";

    /**
     * 周报-租赁费概况导出
     */
    public static String RENT_WEEKLY_GENERAL_EXPORT = "周报-租赁费概况导出";

    /**
     * 周报-租赁费疑似问题单导出
     */
    public static String RENT_WEEKLY_PROBLEM_EXPORT = "周报-租赁费疑似问题单导出";

    /**
     * 周报-租赁费疑似问题单明细导出
     */
    public static String RENT_WEEKLY_PROBLEM_DETAIL_EXPORT = "周报-租赁费疑似问题单明细导出";

    /**
     * 周报-电费概况导出
     */
    public static String ELE_WEEKLY_DETAIL_EXPORT = "周报-电费概况导出";

    /**
     * 周报-电费疑似问题概况导出
     */
    public static String ELE_WEEKLY_PROBLEM_EXPORT = "周报-电费疑似问题单概况导出";

    public static String ELE_REGION_PRICE_CONFIG = "区域单价配置";

    public static String ELE_REGION_PRICE = "区域单价";

    public static String SAVE_SUCCESS = "保存成功";

    public static String SAVE_FAILE = "保存失败";

    public static String EDIT_SUCCESS = "修改成功";

    public static String EDIT_FAILE = "修改失败";
    /**
     * 周报-铁塔服务费概况导出
     */
    public static String TOWER_WEEKLY_GENERAL_EXPORT = "周报-铁塔服务费概况导出";

    /**
     * 周报-铁塔服务费疑似问题单统计导出
     */
    public static String TOWER_WEEKLY_PROBLEM_EXPORT = "周报-铁塔服务费疑似问题单统计导出";

    /**
     * 塔类共享配套信息
     */
    public static String URL_TOWERPT = "/queryTowerPtInfo";

    /**
     * 外部系统跳转登录页面
     */
    public static String URL_SSO_LOGIN_PAGE = "/ssologin/sso-login";

    /**
     * 外部系统跳转登录接口
     */
    public static String URL_SSO_LOGIN = "/ssologin/ssoLoginMain";

    /**
     * 接收外部传入的单站费用比对结果
     */
    public static String URL_RECEIVE_SITE_FEE_COMPARE_RESULT = "/selfrent/realTimeComputing/receiveSingleSiteFee";

    /**
     * 账单应付费用对比分析表
     */
    public static String TOWER_COMPARE_REPORT = "账单应付费用对比分析表.xlsx";
    /**
     * 周报-铁塔服务费疑似问题明细
     */
    public static String TOWER_WEEKLY_PROBLEM_DETAIL_EXPORT = "铁塔服务费疑似问题明细";

    /**
     * 周报-铁塔服务费疑似问题明细-当前共享折扣大于历史折扣
     */
    public static String WEEKLY_DETAIL_DISCOUNT = "当前共享折扣大于历史折扣";

    /**
     * 周报-铁塔服务费疑似问题明细-挂高逻辑异常（挂高大于塔高）
     */
    public static String WEEKLY_DETAIL_HIGHT_EXCEPTION = "挂高逻辑异常（挂高大于塔高）";

    /**
     * 周报-铁塔服务费疑似问题明细-起租挂高大于综资塔高
     */
    public static String WEEKLY_DETAIL_RENT_HIGHER= "起租挂高大于综资塔高";

    /**
     * 周报-铁塔服务费疑似问题明细-同业务确认单前后账期，铁塔产品不一致
     */
    public static String WEEKLY_DETAIL_DIFF_TOWER = "同业务确认单前后账期，铁塔产品不一致";

    /**
     * 周报-铁塔服务费疑似问题明细-同业务确认单前后账期，机房产品不一致
     */
    public static String WEEKLY_DETAIL_DIFF_ROOM = "同业务确认单前后账期，机房产品不一致";

    /**
     * 周报-铁塔服务费疑似问题明细-同业务确认单前后账期，配套产品不一致
     */
    public static String WEEKLY_DETAIL_DIFF_PT = "同业务确认单前后账期，配套产品不一致";

    /**
     * 周报-铁塔服务费疑似问题明细-同账期同站址维护费多次计费
     */
    public static String WEEKLY_DETAIL_MANY_PAY = "同账期同站址维护费多次计费";

    /**
     * 周报-铁塔服务费疑似问题明细-塔高异常(塔高为空)
     */
    public static String WEEKLY_DETAIL_NO_TOWER_HEIGHT = "塔高异常（塔高为空）";

    /**
     * 用户管理审核导出
     */
    public static String SYS_USER_AUDIT_EXPORT = "用户管理审核导出";

    /**
     *起租差异对比表导出
     */
    public static String TOWER_RENT_COMPARE_REPORT = "起租差异对比表导出";

    public static String ACCRUAL_TIPS_MSG="只允许每月24号（2月21号）至本月底进行刷新操作";


    /**
     * 普服报表-普服站点费用分析表（摊销）导出
     */
    public static String TX_RENT_NORMAL_REPORT_EXPORT = "普服站点费用分析表（摊销）导出";

    /**
     * 普服报表-普服站点费用分析表（应付）导出
     */
    public static String YF_RENT_NORMAL_REPORT_EXPORT = "普服站点费用分析表（应付）导出";


    /**
     * 普服报表-普服站点费用分析表（摊销）导出
     */
    public static String TX_TOWER_NORMAL_REPORT_EXPORT = "普服站点（综资）费用分析表（摊销）导出";

    /**
     * 普服报表-普服站点费用分析表（应付）导出
     */
    public static String YF_TOWER_NORMAL_REPORT_EXPORT = "普服站点（综资）费用分析表（应付）导出";

    /**
     * 最终预算
     */
    public static String FINAL_BUDGET = "最终预算填报模板";

    /**
     * 报账点缴费记录导出
     */
    public static String BILLACCOUNT_PAYMENT_EXPORT = "报账点缴费记录导出";

    /**
     * 报账成本中心为空 不能推送汇总单
     */
    public static final String COSTCENTERCODE_NULL = "成本中心为空,不能推送汇总单!";

    /**
     * 更换电表日期格式转换失败！
     */
    public static String METER_REPLACE_DATE_ERR = "更换电表日期格式转换失败！";

    /**
     * 小区流量明细数据
     */
    public static String AREA_FLOW_DATA = "小区流量明细数据";

    public static String SINGLE_STATION_COST_STATIS = "单站网络费用成本统计";

    public static String SINGLE_STATION_COST_ANALYSIS = "单站网络费用成本分析";

    public static String SINGLE_STATION_COST_ANALYSIS_DETAIL = "单站网络费用成本分析";

    public static String ACT_APPROVER_SUBMITTER_SAME="下级审批人和提交人不能是同一用户，请重新选择审批人！";

    public static String ACT_APPROVER_NO_PERMISSION="下级审批人无审核权限，请重新选择审批人！";
    
    /**
     * 电费计提冲销
     */
    public final static String OFFS_BILLAMOUNTS_ERR = "汇总单信息为空，请重新选择！";

}
