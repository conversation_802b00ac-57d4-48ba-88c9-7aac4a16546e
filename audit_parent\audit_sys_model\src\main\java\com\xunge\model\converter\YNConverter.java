package com.xunge.model.converter;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;

//y-是，n-否
public class YNConverter implements Converter<String> {
    @Override
    public String convertToJavaData(ReadCellData<?> cellData, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        String value = cellData.getStringValue();
        if (value.equals("是")){
            return "y";
        }else if (value.equals("否")){
            return "n";
        }
        return null;
    }

    @Override
    public WriteCellData<?> convertToExcelData(String value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        if (value == null){
            return new WriteCellData<>("");
        }else if (value.equals("y")){
            return new WriteCellData<>("是");
        }else if (value.equals("n")){
            return new WriteCellData<>("否");
        }
        return new  WriteCellData<>("");
    }
}
