package com.xunge.dao.selfelec;

import com.xunge.model.selfelec.VDatElectricmeterBr;
import com.xunge.model.selfelec.VDatElectricmeterBrExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface VDatElectricmeterBrMapper {
    
    int countByExample(VDatElectricmeterBrExample example);

    
    int deleteByExample(VDatElectricmeterBrExample example);

    
    int insert(VDatElectricmeterBr record);

    
    int insertSelective(VDatElectricmeterBr record);

    
    List<VDatElectricmeterBr> selectByExampleWithBLOBs(VDatElectricmeterBrExample example);

    
    List<VDatElectricmeterBr> selectByExample(VDatElectricmeterBrExample example);

    
    int updateByExampleSelective(@Param("record") VDatElectricmeterBr record, @Param("example") VDatElectricmeterBrExample example);

    
    int updateByExampleWithBLOBs(@Param("record") VDatElectricmeterBr record, @Param("example") VDatElectricmeterBrExample example);

    
    int updateByExample(@Param("record") VDatElectricmeterBr record, @Param("example") VDatElectricmeterBrExample example);
}