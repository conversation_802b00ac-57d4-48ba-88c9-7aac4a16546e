package com.xunge.dao.system.notice;

import com.xunge.model.system.notice.NoticeConfig;
import com.xunge.model.system.province.SysProvinceVO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface INoticeConfigMapper {

    /**
     * 查询省份信息
     */
    List<SysProvinceVO> queryAllProvince(Map<String, Object> paramMap);

    List<NoticeConfig> queryAllNotice(Map<String, Object> param);

    Integer addNoticeInfo(Map<String, Object> param);

    Integer updateNoticeInfo(Map<String, Object> map);

    NoticeConfig queryNoticeById(Map<String, Object> map);

    List<NoticeConfig> queryNoticeByIds(Map<String, Object> map);

    Integer delNoticeInfo(Map<String, Object> map);

    Integer issueOrCancelNoticeInfo(Map<String, Object> map);
}
