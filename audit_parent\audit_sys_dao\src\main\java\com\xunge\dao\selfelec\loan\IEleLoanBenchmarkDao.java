package com.xunge.dao.selfelec.loan;

import com.xunge.dao.core.CrudDao;
import com.xunge.model.selfelec.loan.EleLoanBenchmark;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 借款额度信息DAO接口
 *
 * <AUTHOR>
 * @version 2018-03-05
 */
public interface IEleLoanBenchmarkDao extends CrudDao<EleLoanBenchmark> {
    /**
     * @description 查询借款额度
     * <AUTHOR>
     * @date 创建时间：2018年03月05日
     */
    public EleLoanBenchmark queryEleLoanBenchmarkDetail(Map<String, Object> paramMap);

    /**
     * @description 删除借款额度
     * <AUTHOR>
     * @date 创建时间：2018年03月05日
     */
    public void deleteList(@Param("eleLoanBenchmarkList") List<EleLoanBenchmark> eleLoanBenchmarkList);

    //更新
    public void updateBenchmar(EleLoanBenchmark eleLoanBenchmark);

    /**
     * @description 删除借款额度
     * <AUTHOR>
     * @date 创建时间：2018年03月05日
     */
    public void deleteBenchmark(EleLoanBenchmark eleLoanBenchmark);
    /**
     * @description 删除借款额度
     * <AUTHOR>
     */
    public void deleteBenchmarkByLoanId(String loanId);

    //插入单价信息
    public int insertBorrowlimit(EleLoanBenchmark eleLoanBenchmark);

    public EleLoanBenchmark queryBorrowlimit(Map<String, Object> paramMap);


    /**
     * 删除单价信息-预付费
     * @param loanId
     */
    void deleteBorrowlimitByLoanId(String loanId);
}