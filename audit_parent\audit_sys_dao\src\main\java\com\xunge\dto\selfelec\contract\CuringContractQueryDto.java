package com.xunge.dto.selfelec.contract;

import com.xunge.dto.selfelec.PageInfo;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2021/12/21 14:26
 */
@Getter
@Setter
public class CuringContractQueryDto extends PageInfo implements Serializable {
    private static final long serialVersionUID = -6880335802847214331L;
    /**
     * 合同名称或合同编号 模糊搜索
     */
    private String contractItem;
    private String pregId;
    private String regId;
    /**
     * 录入人名称或编码
     */
    private String userCodeOrName;
    /**
     * 合同状态
     */
    private String contractState;
    /**
     * 审核状态
     */
    private String auditState;
    /**
     * 数据来源
     */
    private Integer dataFrom;

    /**
     * 供电类型
     */
    private Integer supplyMethod;

    /**
     * 单价类型
     */
    private Integer priceType;

    /**
     * 购电方式
     */
    private Integer buyMethod;

    /**
     * 是否有电损
     */
    private Integer includeLoss;
    /**
     * 当前审核人
     */
    private String stayAuditingUser;
    private int isDownShare = 1;
    private String supplierItem;

    private String dateSort;
    private String relatedBillaccount;

    private String isRelatedCon;

    private Integer contractFrom;
}
