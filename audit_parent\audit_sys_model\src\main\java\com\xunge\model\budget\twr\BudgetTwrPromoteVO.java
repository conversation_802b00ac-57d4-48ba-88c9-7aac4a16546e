package com.xunge.model.budget.twr;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2022/7/28
 * @description 提升共享范围
 */
@Data
public class BudgetTwrPromoteVO {

    /**
     * 省份ID
     */
    private String prvId;

    /**
     * 省份名称
     */
    private String prvName;
    /**
     * 业务类型({塔类,1}, {室分,2}, {微站,3}, {传输,4}, {非标,5}, {合计,6})
     */
    private Integer productType;

    /**
     * 新年度总站址数
     */
    private Integer promoteSharedSiteNumber;

    /**
     * 单站服务费（当年独享站年化均值）
     */
    private BigDecimal promoteSharedSiteFee;
    /**
     * 共享率提升百分比
     */
    private BigDecimal promoteSharedSiteRadio;
    /**
     * 月份数
     */
    private BigDecimal promoteSharedMonthNumber;
    /**
     * 提升共享范围-预算金额
     */
    private BigDecimal promoteSharedBudgetFee;
    /**
     * 提升共享范围-核减金额
     */
    private BigDecimal promoteSharedSubtractFee;


    private BigDecimal promoteSharedAdjustFee;
    private BigDecimal promoteSharedAdjustFeeAfter;

    private String promoteSharedRemark;

}
