package com.xunge.comm.elec;

/**
 * 标杆类型枚举
 * <AUTHOR>
 */
public enum BenchmarkTypeEnum {

    /**
     * 历史电费标杆-同比
     */
    ZERO(0, "历史电费标杆-同比"),

    /**
     *历史电费标杆-环比
     */
    ONE(1, "历史电费标杆-环比"),

    /**
     * 额定功率标杆
     */
    TWO(2, "额定功率标杆"),

    /**
     * 智能电表标杆（动环)
     */
    THREE(3, "智能电表标杆(动环)"),

    /**
     * 动环负载标杆
     */
    FOUR(4, "动环负载标杆"),

    /**
     * 平峰谷均价标杆
     */
    SEVEN(7, "平峰谷均价标杆"),


    /**
     * 历史日均电量标杆-同比
     */
    EIGHT(8, "历史日均电量标杆-同比"),

    /**
     * 历史日均电量标杆-环比
     */
    NINE(9, "历史日均电量标杆-环比"),

    /**
     * 智能电表标杆（集采）
     */
    TEN(10, "智能电表标杆（集采）");


    public final int code;
    public final String text;

    BenchmarkTypeEnum(int code, String text) {
        this.code = code;
        this.text = text;
    }

    /**
     * 获取枚举信息
     *
     * @param code 状态码
     * @return 结果
     */
    public static BenchmarkTypeEnum getBenchmarkRangeEnum(int code) {
        BenchmarkTypeEnum temp = null;
        for (BenchmarkTypeEnum benchmarkRangeEnum : BenchmarkTypeEnum.values()) {
            if (benchmarkRangeEnum.getCode() == code) {
                temp = benchmarkRangeEnum;
                break;
            }
        }
        return temp;
    }

    public int getCode() {
        return code;
    }

    public String getText() {
        return text;
    }
}
