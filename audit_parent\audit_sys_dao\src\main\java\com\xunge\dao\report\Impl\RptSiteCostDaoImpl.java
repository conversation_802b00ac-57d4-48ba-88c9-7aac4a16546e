package com.xunge.dao.report.Impl;

import com.xunge.dao.AbstractBaseDao;
import com.xunge.dao.report.IRptSiteCostDao;
import com.xunge.model.report.RptPrvEleBasesiteVo;

import java.util.List;
import java.util.Map;

public class RptSiteCostDaoImpl extends AbstractBaseDao implements IRptSiteCostDao {

    final String Namespace = "com.xunge.dao.selfelec.RptPrvEleBasesiteVoMapper.";

    @Override
    public List<RptPrvEleBasesiteVo> querySiteCostAll(Map<String, Object> map) {
        return this.getSqlSession().selectList(Namespace + "querySiteCostAll", map);
    }

    @Override
    public List<RptPrvEleBasesiteVo> querySiteCostByPrvId(Map<String, Object> map) {
        return this.getSqlSession().selectList(Namespace + "querySiteCostByPrvId", map);
    }

    @Override
    public List<RptPrvEleBasesiteVo> querySiteCostByPregId(Map<String, Object> map) {
        return this.getSqlSession().selectList(Namespace + "querySiteCostByPregId", map);
    }

    @Override
    public List<RptPrvEleBasesiteVo> querySiteCostAllTotal(Map<String, Object> map) {
        return this.getSqlSession().selectList(Namespace + "querySiteCostAllTotal", map);
    }

    @Override
    public List<RptPrvEleBasesiteVo> querySiteCostByPrvIdTotal(Map<String, Object> map) {
        return this.getSqlSession().selectList(Namespace + "querySiteCostByPrvIdTotal", map);
    }

    @Override
    public List<RptPrvEleBasesiteVo> querySiteCostByPregIdTotal(Map<String, Object> map) {
        return this.getSqlSession().selectList(Namespace + "querySiteCostByPregIdTotal", map);
    }

	@Override
	public RptPrvEleBasesiteVo countSiteSnapTidb(Map<String, Object> map) {
		return this.getSqlSession().selectOne(Namespace + "countSiteSnapTidb", map);
	}

    @Override
    public List<RptPrvEleBasesiteVo> querySiteCostByOnMonths(Map<String, Object> map) {
        return this.getSqlSession().selectList(Namespace + "querySiteCostByOnMonths", map);
    }

}
