package com.xunge.dao.selfelec.telecmnserv;

import com.xunge.model.selfelec.telecmnserv.TeleCmnServHistoryVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @author: LiangCheng
 * Date: 2022/9/21 9:19
 * Description: 电费普遍服务记录
 */
public interface TeleCmnServMapper {

    List<TeleCmnServHistoryVO> queryTeleCmnServHistory(@Param("billaccountId")String billaccountId,@Param("businessType")Integer businessType);

    void saveTeleCmnServHistory(TeleCmnServHistoryVO teleCmnServHistoryVO);

}
