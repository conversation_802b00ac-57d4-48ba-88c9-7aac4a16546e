package com.xunge.dao.selfelec;

import com.xunge.model.selfelec.vo.OcrEleMeterVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Author: LiangCheng
 * Date: 2022/5/30 15:32
 * Description:电表抄表图片OCR识别Mapper
 */
public interface OcrEleMeterMapper {

    /**
     * @Description ocr识别结果表
     * @param orcList
     * @return
     */
    public Integer saveOcrEleMeter(@Param("list") List<OcrEleMeterVO> orcList);


    /**
     * @Description 删除ocr识别结果表
     * @param businessType
     * @param businessId
     * @return
     */
    public Integer delOcrEleMeter(@Param("businessType") String businessType,@Param("businessId") String businessId);

    /**
     * @Description 获取已有OCR数据的电表
     * @param businessType
     * @param businessId
     * @return
     */
    public List<OcrEleMeterVO> queryOcrEleMeter(@Param("businessType") String businessType,@Param("businessId") String businessId);

    /**
     * 更新第一级审核人打开时间
     * @param businessType
     * @param businessId
     * @return
     */
    public Integer editOcrEleMeterClickTime(@Param("businessType") String businessType,@Param("businessId") String businessId);

    public List<OcrEleMeterVO> queryEleMeterImage(@Param("businessType") String businessType,@Param("businessId") String businessId);

    public String queryBusinessCode(@Param("businessType") String businessType,@Param("businessId") String businessId);


    public String queryImageUrlByAttachmentId(@Param("attachmentId") String attachmentId);
}
