package com.xunge.dao.external;

import com.xunge.model.external.pojo.ExtCrawlerRecord;

import java.util.List;

/**
 * @Description 爬取记录
 * <AUTHOR>
 * @Date 2021/1/28 15:24
 * @modifier ZXX
 * @date 2021/1/28 15:24
 * @Version 1.0
 **/
public interface ExtCrawlerRecordMapper {
    //插入记录
    int insert(ExtCrawlerRecord extCrawlerRecord);

    //插入记录
    int insertList(List<ExtCrawlerRecord> extCrawlerRecord);

    //查询记录根据用户
    List<ExtCrawlerRecord> selectByCondition(ExtCrawlerRecord extCrawlerRecord);

    //查询所有户号最近一次缴费记录
    List<ExtCrawlerRecord> selectByCondition2(ExtCrawlerRecord extCrawlerRecord);
}
