package com.xunge.dao.statistics;

import com.xunge.model.statistics.RptEleProblemAnalysis;
import com.xunge.model.statistics.RptEleWeekProblemDetail;
import com.xunge.model.statistics.YearMonthWeekVO;

import java.util.List;
import java.util.Map;

/**
 * @ClassName:    RptEleProblemAnalysisMapper
 * @Description:  ${description}
 * @Date:         2021/12/28 16:18
 * @Version:      1.0
 */
public interface RptEleProblemAnalysisMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(RptEleProblemAnalysis record);

    int insertSelective(RptEleProblemAnalysis record);

    RptEleProblemAnalysis selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(RptEleProblemAnalysis record);

    int updateByPrimaryKey(RptEleProblemAnalysis record);

    List<RptEleProblemAnalysis> queryEleProblemWeekReportList(Map<String,Object> map);

    List<RptEleProblemAnalysis> queryEleProblemWeekReportSumList(Map<String,Object> map);

    List<YearMonthWeekVO> queryProblemYearMonthWeek(Map<String,Object> map);

    public List<RptEleWeekProblemDetail> queryProblemWeekDetailExport(Map<String,Object> map);

}
