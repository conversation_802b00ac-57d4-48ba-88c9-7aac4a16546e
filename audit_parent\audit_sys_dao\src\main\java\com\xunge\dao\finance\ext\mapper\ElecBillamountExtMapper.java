package com.xunge.dao.finance.ext.mapper;

import com.xunge.model.finance.ext.accClaim.Billamount;
import com.xunge.model.selfelec.accrualoffs.AccrualOffsDetail;
import com.xunge.model.selfelec.accrualoffs.ElePaymentOffsDetail;

import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface ElecBillamountExtMapper {

    Billamount selectByPrimaryKey(@Param("billamountId") String billamountId);

    int updateByPrimaryKey(Billamount record);

    int updateClaimNumByBillamountId(Map<String, Object> map);

    int updateClaimNumByBillamountIdDetail(Map<String, Object> map);

	List<ElePaymentOffsDetail> queryPaymentOffsDetail(@Param("billamountId") String billamountId);

	void updatePaymentOffsDetail(List<ElePaymentOffsDetail> offsDetail);

	List<AccrualOffsDetail> selectPaymentOffsDetail(String billamountCode);

	void detelePaymentOffsDetail(ElePaymentOffsDetail off);

	void returnOffsAmount(ElePaymentOffsDetail off);

}
