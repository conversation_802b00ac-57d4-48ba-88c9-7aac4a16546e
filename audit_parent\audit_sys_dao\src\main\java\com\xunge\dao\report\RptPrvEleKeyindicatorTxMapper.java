package com.xunge.dao.report;

import com.xunge.model.report.RptPrvEleKeyindicatorTx;
import com.xunge.model.report.RptPrvEleKeyindicatorTxExample;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Component
public interface RptPrvEleKeyindicatorTxMapper {

    public List<RptPrvEleKeyindicatorTx> selectByExample(RptPrvEleKeyindicatorTxExample example);

    public List<RptPrvEleKeyindicatorTx> selectProvinceLevel(Map<String, Object> map);

    public List<RptPrvEleKeyindicatorTx> selectCityLevel(Map<String, Object> map);

    public List<RptPrvEleKeyindicatorTx> selectDistrictLevel(Map<String, Object> map);

}