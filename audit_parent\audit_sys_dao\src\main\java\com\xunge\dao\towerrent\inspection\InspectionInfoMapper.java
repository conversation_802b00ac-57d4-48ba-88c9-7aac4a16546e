package com.xunge.dao.towerrent.inspection;

import com.xunge.model.towerrent.inspection.*;

import java.util.List;
import java.util.Map;

/**
 * app巡检相关
 *
 * <AUTHOR>
 * @date 2019/4/28
 */
public interface InspectionInfoMapper {

    /**
     * 通过获取巡检基础信息
     *
     * @param inspectionInfo
     * @return
     */
    InspectionInfo selectByPrimaryKey(InspectionInfo inspectionInfo);

    /**
     * 通过主键更新审核状态
     *
     * @param inspectionInfo
     */
    void updateStatusByPrimaryKey(InspectionInfo inspectionInfo);


    /**
     * 通过巡检基础表ID获取变更表的记录数量
     *
     * @param inspectionInfoId
     * @return
     */
    int getInspectionChangeCount(String inspectionInfoId);

    /**
     * 通过巡检基础表ID获取变更表的记录
     *
     * @param map
     * @return
     */
    List<InspectionChange> getInspectionChangeListByKey(Map<String, String> map);


    /**
     * 根据条件查询综资侧机房信息 修改
     *
     * @param params
     * @return
     */
    List<CapitalInfoVo> getCapitalInfoVoList(Map<String, Object> params);

    /**
     * 根据条件查询铁塔侧机房信息 修改
     *
     * @param params
     * @return
     */
    List<TowerInfoVo> getTowerInfoVoList(Map<String, Object> params);

    Map<String, Object> queryProductHeightAndAntennaNum(String towerStationCode);

    /**
     * 根据cuid查询ID
     *
     * @param baseresourceCuid
     * @return
     */
    String queryAntennaNum(String inspectionInfoId);

    List<TowerInfoVo> getTowerInfoVoInfo(Map<String, Object> params);

    List<CapitalInfoVo> getCapitalInfoVoListYh(Map<String, Object> params);

    List<Map<String, Object>> queryRemarkList(String inspectionInfoId);

    SiteInfoVO getTowerInfoCompare(Map<String, Object> params);

    List<InspectionChange> queryInspectionChange(Map<String, Object> params);

    List<Map<String, Object>> queryAuditPeople(Map<String, Object> params);

    List<InspectionInfoDto> queryInspection(Map<String, Object> params);

    List<InspectionChange> queryInspectionChangeInfo(String inspectionId);

    InspectionInfoDto queryBaseresourceDetail(String inspectionId);

    List<TowerInfoVo> queryTowerRentList(Map<String, Object> params);

    String getInspectionCode(String inspectionInfoId);
}
