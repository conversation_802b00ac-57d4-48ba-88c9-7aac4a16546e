package com.xunge.model.converter.ele;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;

//税金承担方式 "移动开票认税_0", "对方开票认税_1", "移动开票对方认税_2", "_null"
public class TaxPaymenTypeConverter implements Converter<Integer> {
    @Override
    public Integer convertToJavaData(ReadCellData<?> cellData, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        String value = cellData.getStringValue();
        if (value.equals("移动开票认税")){
            return 0;
        }else if (value.equals("对方开票认税")){
            return 1;
        }else if (value.equals("移动开票对方认税")){
            return 2;
        }
        return null;
    }

    @Override
    public WriteCellData<?> convertToExcelData(Integer value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        if (value == null){
            return new WriteCellData<>("");
        }else if (value == 0){
            return new WriteCellData<>("移动开票认税");
        }else if (value == 1){
            return new WriteCellData<>("对方开票认税");
        }else if (value == 2){
            return new WriteCellData<>("移动开票对方认税");
        }
        return new  WriteCellData<>("");
    }
}
