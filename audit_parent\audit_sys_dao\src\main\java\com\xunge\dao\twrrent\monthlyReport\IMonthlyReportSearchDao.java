package com.xunge.dao.twrrent.monthlyReport;

import com.xunge.core.page.Page;
import com.xunge.model.towerrent.monthlyReport.*;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 月报查询
 */
public interface IMonthlyReportSearchDao {
    /**
     * 查询铁塔站址月报--省份
     *
     * @param param
     * @param pageSize
     * @param pageNum
     * @return
     */
    Page<RptMonthlyTwrSite> qyeryPageTwrSiteReportPrv(Map<String, Object> param, int pageSize, int pageNum);

    List<RptMonthlyTwrSite> queryListTwrSiteReportPrv(Map<String, Object> param);

    /**
     * 查询铁塔站址月报--地市
     *
     * @param param
     * @param pageSize
     * @param pageNum
     * @return
     */
    Page<RptMonthlyTwrSite> qyeryPageTwrSiteReportCity(Map<String, Object> param, int pageSize, int pageNum);

    List<RptMonthlyTwrSite> queryListTwrSiteReportCity(Map<String, Object> param);

    /**
     * 查询铁塔站址月报--区县
     *
     * @param param
     * @param pageSize
     * @param pageNum
     * @return
     */
    Page<RptMonthlyTwrSite> qyeryPageTwrSiteReportRegion(Map<String, Object> param, int pageSize, int pageNum);

    List<RptMonthlyTwrSite> queryListTwrSiteReportRegion(Map<String, Object> param);

    /**
     * 获取铁塔站址“塔类产品服务费本月未考核处罚前的金额（元，不含税）”与塔类服务费的“塔类产品服务费本月未考核处罚前的金额（元，不含税）”金额的比较信息，省份
     *
     * @param param
     * @return
     */
    List<TwrSiteMoneyCompareWithService> getCompareInfoPrv(Map<String, Object> param);

    /**
     * 获取铁塔站址“塔类产品服务费本月未考核处罚前的金额（元，不含税）”与塔类服务费的“塔类产品服务费本月未考核处罚前的金额（元，不含税）”金额的比较信息，地市
     *
     * @param param
     * @return
     */
    List<TwrSiteMoneyCompareWithService> getCompareInfoCity(Map<String, Object> param);

    /**
     * 获取铁塔站址“塔类产品服务费本月未考核处罚前的金额（元，不含税）”与塔类服务费的“塔类产品服务费本月未考核处罚前的金额（元，不含税）”金额的比较信息，区县
     *
     * @param param
     * @return
     */
    List<TwrSiteMoneyCompareWithService> getCompareInfoRegion(Map<String, Object> param);

    /**
     * 查询省份的塔类服务费月报
     *
     * @param map
     * @return
     */
    List<RptMonthlyTwrServiceCost> queryTowerServiceReportSearchPrv(Map<String, Object> map);

    /**
     * 查询地市的塔类服务费月报
     *
     * @param map
     * @return
     */
    List<RptMonthlyTwrServiceCost> queryTowerServiceReportSearchPreg(Map<String, Object> map);

    /**
     * 查询区县的塔类服务费月报
     *
     * @param map
     * @return
     */
    List<RptMonthlyTwrServiceCost> queryTowerServiceReportSearchReg(Map<String, Object> map);

    //
    List<RptMonthlyBudgetAndProgress> querybudgetAndProgressPrv(Map<String, Object> map);

    List<RptMonthlyBudgetAndProgress> querybudgetAndProgressCity(Map<String, Object> map);

    List<RptMonthlyBudgetAndProgress> querybudgetAndProgressReg(Map<String, Object> map);

    /**
     * 查询某账期的推送总金额
     *
     * @param map 账期
     * @return
     */
    BigDecimal queryTotalAmount(Map<String, Object> map);

    /**
     * 查询某账期的推送账单包含总铁塔数
     *
     * @param map 账期
     * @return
     */
    Integer queryNumAmount(Map<String, Object> map);


    List<RptMonthlyIconTowerCost> queryIronTowerReportSearchPrv(Map<String, Object> map);

    List<RptMonthlyIconTowerCost> queryIronTowerReportSearchPreg(Map<String, Object> map);

    List<RptMonthlyIconTowerCost> queryIronTowerReportSearchReg(Map<String, Object> map);

    /**
     * 查询省份的铁塔服务费月报
     *
     * @param map
     * @return
     */
    List<RptMonthlyTwrServiceCost> queryIronTowerServiceReportSearchPrv(Map<String, Object> map);

    /**
     * 查询地市的塔铁塔服务费月报
     *
     * @param map
     * @return
     */
    List<RptMonthlyTwrServiceCost> queryIronTowerServiceReportSearchPreg(Map<String, Object> map);

    /**
     * 查询区县的铁塔服务费月报
     *
     * @param map
     * @return
     */
    List<RptMonthlyTwrServiceCost> queryIronTowerServiceReportSearchReg(Map<String, Object> map);

    List<RptTowerAccount> queryTowerAccountReportPrv(Map<String, Object> param);

    List<RptTowerAccount> queryTowerAccountReportPreg(Map<String, Object> param);

    List<RptTowerAccount> queryTowerAccountReportReg(Map<String, Object> param);

    List<RptMonthlyBudgetAndProgressNew> getBudgetAndProgressAmoritizeDataWhole(Map<String, Object> paraMap);

    List<RptMonthlyBudgetAndProgressNew> getBudgetAndProgressAmoritizeDataPrv(Map<String, Object> paraMap);

    List<RptMonthlyBudgetAndProgressNew> getBudgetAndProgressAmoritizeDataPreg(Map<String, Object> paraMap);

    List<RptMonthlyIconTowerCost> queryTowerServiceFeeSearchPrv(Map<String, Object> paraMap);

    List<RptMonthlyIconTowerCost> queryTowerServiceFeeSearchPreg(Map<String, Object> paraMap);
}
