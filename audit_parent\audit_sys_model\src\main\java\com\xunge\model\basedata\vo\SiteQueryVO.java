package com.xunge.model.basedata.vo;

import com.xunge.core.model.UserLoginInfo;

import java.io.Serializable;

/**
 * 站点查询 VO
 * <p>
 * Title: SiteQueryVO
 *
 * <AUTHOR>
 */
public class SiteQueryVO extends BaseDataVO implements Serializable {

    private static final long serialVersionUID = 1128476128853139318L;
    private String siteReg;
    private String prvId;
    private String city;
    private String region;
    private String property;
    private Integer auditStatus;
    private Integer status;
    private Integer dataFrom;
    private Integer queryType;
    // 站点生命周期状态
    private Integer basesiteState;
    private String depId;
    private String dataFroms;
    private String towerSiteCode;
    // 登录用户信息
    private UserLoginInfo loginUser;
    // 是否属于电信普遍服务
    private Integer ifTeleCmnServ;

    public void setIfTeleCmnServ(Integer ifTeleCmnServ) {
        this.ifTeleCmnServ = ifTeleCmnServ;
    }

    public Integer getIfTeleCmnServ() {
        return ifTeleCmnServ;
    }

    public String getTowerSiteCode() {
        return towerSiteCode;
    }

    public void setTowerSiteCode(String towerSiteCode) {
        this.towerSiteCode = towerSiteCode;
    }

    public String getDataFroms() {
        return dataFroms;
    }

    public void setDataFroms(String dataFroms) {
        this.dataFroms = dataFroms;
    }

    public Integer getBasesiteState() {
        return basesiteState;
    }

    public void setBasesiteState(Integer basesiteState) {
        this.basesiteState = basesiteState;
    }

    public String getPrvId() {
        return prvId;
    }

    public void setPrvId(String prvId) {
        this.prvId = prvId;
    }

    public String getSiteReg() {
        return siteReg;
    }

    public void setSiteReg(String siteReg) {
        this.siteReg = siteReg == null ? siteReg : siteReg.trim();
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getRegion() {
        return region;
    }

    public void setRegion(String region) {
        this.region = region;
    }

    public String getProperty() {
        return property;
    }

    public void setProperty(String property) {
        this.property = property;
    }

    public Integer getAuditStatus() {
        return auditStatus;
    }

    public void setAuditStatus(Integer auditStatus) {
        this.auditStatus = auditStatus;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getDataFrom() {
        return dataFrom;
    }

    public void setDataFrom(Integer dataFrom) {
        this.dataFrom = dataFrom;
    }

    public Integer getQueryType() {
        return queryType;
    }

    public void setQueryType(Integer queryType) {
        this.queryType = queryType;
    }

    public UserLoginInfo getLoginUser() {
        return loginUser;
    }

    public void setLoginUser(UserLoginInfo loginUser) {
        this.loginUser = loginUser;
    }

    public String getDepId() {
        return depId;
    }

    public void setDepId(String depId) {
        this.depId = depId;
    }
}
