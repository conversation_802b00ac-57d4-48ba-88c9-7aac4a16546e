package com.xunge.dao.selfrent.accrual;

import com.xunge.dto.selfelec.AuthorityUser;
import com.xunge.model.selfrent.accrual.RentAccrualQueryDto;
import com.xunge.model.selfrent.accrual.RentAccrualVO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.cursor.Cursor;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface RentAccrualVOMapper {

    List<RentAccrualVO> queryAccrualVOPage(@Param("user") AuthorityUser loginInfo, @Param("queryDto") RentAccrualQueryDto queryDto);

    List<RentAccrualVO> queryAccrualAuditVOPage(@Param("user") AuthorityUser loginInfo, @Param("queryDto") RentAccrualQueryDto queryDto);

    Cursor<RentAccrualVO> queryAccrualVODetail(@Param("user") AuthorityUser loginInfo, @Param("queryDto") RentAccrualQueryDto queryDto);

    Cursor<RentAccrualVO> queryAccrualVOAuditDetail(@Param("user") AuthorityUser loginInfo, @Param("queryDto") RentAccrualQueryDto queryDto);


    RentAccrualVO queryRentAccrualById(String accrualId);

    List<RentAccrualVO> queryTotalAccrualAmount(@Param("list") List<RentAccrualVO> list);

    List<RentAccrualVO> queryAccrualContractAuditVOPage(@Param("user") AuthorityUser loginInfo, @Param("queryDto")  RentAccrualQueryDto dto);

    RentAccrualVO queryRentAccrualContractById(String accrualId);

    void rentAccrualContractSubmitAudit(Map<String, Object> maps);

    Cursor<RentAccrualVO> queryAccrualContractVOAuditDetail(@Param("user") AuthorityUser userRegInfo, @Param("queryDto") RentAccrualQueryDto dto);

    List<RentAccrualVO> queryContractAccrualVOPage(@Param("user") AuthorityUser loginInfo, @Param("queryDto")  RentAccrualQueryDto queryDto);

    Cursor<RentAccrualVO> queryAccrualContractVODetail(@Param("user") AuthorityUser userRegInfo, @Param("queryDto") RentAccrualQueryDto dto);
}

