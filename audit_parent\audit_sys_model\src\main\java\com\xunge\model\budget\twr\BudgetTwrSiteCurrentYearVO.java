package com.xunge.model.budget.twr;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2022/7/28
 * @description 本年10-12月新起租订单在新年度的费用
 */
@Data
public class BudgetTwrSiteCurrentYearVO {
    /**
     * 省份ID
     */
    private String prvId;

    /**
     * 省份名称
     */
    private String prvName;
    /**
     * 年份
     */
    private Integer onYear;
    /**
     * 业务类型({塔类,1}, {室分,2}, {微站,3}, {传输,4}, {非标,5}, {合计,6})
     */
    private Integer productType;

    /**
     * 单订单服务费（本年新址新建订单年均值）
     */
    private BigDecimal rentOrderNewSiteFee;
    /**
     * 单订单服务费（本年共址订单年均值）
     */
    private BigDecimal rentOrderShareSiteFee;
    /**
     * 本年共址改造订单平均产品单元数
     */
    private String rentOrderCurrentUnitNumber;
    /**
     * 年底起租的新址新建订单数
     */
    private Integer rentOrderSiteEndNumber;
    /**
     * 年底起租的共址改造订单数
     */
    private Integer rentOrderShareEndNumber;
    /**
     * 新年共址改造站址平均产品单元数
     */
    private BigDecimal rentOrderNewUnitNumber;
    /**
     * 本年账期月份-12月新起租订单在新年度的费用-预算金额
     */
    private BigDecimal rentOrderBudgetFee;
    /**
     * 本年账期月份-12月新起租订单在新年度的费用-核减金额
     */
    private BigDecimal rentOrderSubtractFee;

    /**
     * 本年账期月份-12月新起租订单在新年度的费用
     */
    private BigDecimal rentOrderNewFee;

    /**
     * 10-12月起租的新址新建订单在当年使用月数
     */
    private Integer rentOrderSiteMonth;

    /**
     * 10-12月起租的共址改造订单在当年的使用月数
     */
    private Integer rentOrderShareMonth;

    /**
     * 10-12月起租订单在当年执行金额
     */
    private BigDecimal rentOrderExecutionFee;

    /**
     * 调整金额
     */
    private BigDecimal rentOrderAdjustFee;
    private BigDecimal rentOrderAdjustFeeAfter;

    private String rentOrderRemark;
}
