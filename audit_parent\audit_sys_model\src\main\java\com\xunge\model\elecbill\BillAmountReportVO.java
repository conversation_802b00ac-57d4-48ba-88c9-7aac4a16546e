package com.xunge.model.elecbill;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

public class BillAmountReportVO implements Serializable {

    /**
     *
     */
    private static final long serialVersionUID = 6702812400588221251L;
    private String system_id = "CMCC_NCMP"; //系统唯一标识CMCC_NCMP
    private int payment_type; //一级费用类型枚举值：1、网络电费；2、站点租赁费；3、铁塔服务费；

    /**
     * 枚举值： 1、无票据预付报账（预付费） 2、有票据报账且已经发起过无票据预付报账 （核销） 3、有票据报账，无需发起无票据预付报账 （报账） 4、透支户报账 5、摊销 6、计提 7、冲销
     */
    private int reimbursement_type;
    private String collect_num;//汇总单号
    private String vendor_code;//供应商编号
    private String vendor_name;//供应商名称
    private String vendor_site;//供应商地点
    private BigDecimal apply_amount;//报账金额
    private BigDecimal document_amount;//价款
    private BigDecimal tax_amount;//税额
    private String user_number;//报账人工号
    private String user_name;//报账人用户名
    private String abstract_content;//摘要
    private String remark;//备注
    //private List<BillReportNEntityVO> entity_expend; //报账行实体
    private Integer page_size;//汇总单报账行实体每页记录的明细数量，默认值为1000。
    private Integer total_page;//汇总单的明细按照页大小拆分的总页数
    private Integer current_page;//当前报文所处总页数中的第几页
    private List<EntityExpend> entity_expend;//报账行实体

    public Integer getPage_size() {
        return page_size;
    }

    public void setPage_size(Integer page_size) {
        this.page_size = page_size;
    }

    public Integer getTotal_page() {
        return total_page;
    }

    public void setTotal_page(Integer total_page) {
        this.total_page = total_page;
    }

    public Integer getCurrent_page() {
        return current_page;
    }

    public void setCurrent_page(Integer current_page) {
        this.current_page = current_page;
    }

    public Integer getReimbursement_type() {
        return reimbursement_type;
    }

    public void setReimbursement_type(int reimbursement_type) {
        this.reimbursement_type = reimbursement_type;
    }

    public void setReimbursement_type(Integer reimbursement_type) {
        this.reimbursement_type = reimbursement_type;
    }

    public String getSystem_id() {
        return system_id;
    }

    public void setSystem_id(String system_id) {
        this.system_id = system_id;
    }

    public int getPayment_type() {
        return payment_type;
    }

    public void setPayment_type(int payment_type) {
        this.payment_type = payment_type;
    }

    public String getCollect_num() {
        return collect_num;
    }

    public void setCollect_num(String collect_num) {
        this.collect_num = collect_num;
    }

    public String getVendor_code() {
        return vendor_code;
    }

    public void setVendor_code(String vendor_code) {
        this.vendor_code = vendor_code;
    }

    public String getVendor_name() {
        return vendor_name;
    }

    public void setVendor_name(String vendor_name) {
        this.vendor_name = vendor_name;
    }

    public String getVendor_site() {
        return vendor_site;
    }

    public void setVendor_site(String vendor_site) {
        this.vendor_site = vendor_site;
    }

    public BigDecimal getApply_amount() {
        return apply_amount;
    }

    public void setApply_amount(BigDecimal apply_amount) {
        this.apply_amount = apply_amount;
    }

    public BigDecimal getDocument_amount() {
        return document_amount;
    }

    public void setDocument_amount(BigDecimal document_amount) {
        this.document_amount = document_amount;
    }

    public BigDecimal getTax_amount() {
        return tax_amount;
    }

    public void setTax_amount(BigDecimal tax_amount) {
        this.tax_amount = tax_amount;
    }

    public String getUser_number() {
        return user_number;
    }

    public void setUser_number(String user_number) {
        this.user_number = user_number;
    }

    public String getUser_name() {
        return user_name;
    }

    public void setUser_name(String user_name) {
        this.user_name = user_name;
    }

    public String getAbstract_content() {
        return abstract_content;
    }

    public void setAbstract_content(String abstract_content) {
        this.abstract_content = abstract_content;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public List<EntityExpend> getEntity_expend() {
        return entity_expend;
    }

    public void setEntity_expend(List<EntityExpend> entity_expend) {
        this.entity_expend = entity_expend;
    }
}
