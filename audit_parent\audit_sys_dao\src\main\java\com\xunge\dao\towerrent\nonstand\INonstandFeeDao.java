package com.xunge.dao.towerrent.nonstand;

import com.xunge.core.page.Page;
import com.xunge.model.towerrent.settlement.*;

import java.util.List;
import java.util.Map;

/**
 * TODO: 查询非标账单相关信息
 *
 * <AUTHOR>
 */
public interface INonstandFeeDao {

    List<TowerBillBalanceNonstandVO> queryNonstandBill(Map<String, Object> paramMap);

    int updateNonstandFeeSumcodeToNull(Map<String, Object> paramMap);

    int updateRoomNonstandFeeSumcodeToNull(Map<String, Object> paramMap);

    int updateNonstandBillSetSumcode(TowerBillBalanceNonstandVO tinyVO);

    List<TowerAndMobileBillNonstandConfirmVO> queryAccountedNonstandBill(Map<String, Object> paramMap);

    Page<List<TowerAndMobileBillNonstandConfirmVO>> queryAccountedNonstandBillByPage(
            Map<String, Object> paraMap, int pageNumber, int pageSize);

    List<TowerAndMobileBillRoomNonstandConfirmVO> queryAccountedRoomNonstandBill(Map<String, Object> paraMap);

    Page<List<TowerAndMobileBillRoomNonstandConfirmVO>> queryAccountedRoomNonstandBillByPage(
            Map<String, Object> paraMap, int pageNumber, int pageSize);

    List<OtherFeeNonstandVO> queryAccountedOtherFeeNonstand(Map<String, Object> paramMap);

    Page<List<OtherFeeNonstandVO>> queryAccountedOtherFeeNonstandByPage(Map<String, Object> paraMap, int pageNumber, int pageSize);

    int queryRoomNonstandCtlCount(String yearmonth);


    List<TowerBillBalanceRoomNonstandVO> queryRoomNonstandBill(Map<String, Object> paramMap);

    int updateRoomNonstandBillSetSumcode(TowerBillBalanceRoomNonstandVO vo);
}
