package com.xunge.dao.report.Impl;

import com.xunge.dao.AbstractBaseDao;
import com.xunge.dao.report.IrptMeterLedgerDao;
import com.xunge.model.report.RptMeterLedgerVO;

import java.util.List;

public class RptMeterLedegerDaoImpl extends AbstractBaseDao implements IrptMeterLedgerDao {

    private final String Namespace = "com.xunge.mapping.RptMeterLedgerVOMapper.";

    @Override
    public List<RptMeterLedgerVO> queryMeterLedger(RptMeterLedgerVO rptMeterLedgerVO) {
        return this.getSqlSession().selectList(Namespace + "queryMeterLedger", rptMeterLedgerVO);
    }

    @Override
    public int queryMeterLedgerCount(RptMeterLedgerVO rptMeterLedgerVO) {
        return this.getSqlSession().selectOne(Namespace + "queryMeterLedgerCount", rptMeterLedgerVO);
    }

    @Override
    public List<RptMeterLedgerVO> queryMeterLedgerNew(RptMeterLedgerVO rptMeterLedgerVO) {
        return this.getSqlSession().selectList(Namespace + "queryMeterLedgerNew", rptMeterLedgerVO);
    }

    @Override
    public int queryMeterLedgerCountNew(RptMeterLedgerVO rptMeterLedgerVO) {
        return this.getSqlSession().selectOne(Namespace + "queryMeterLedgerCountNew", rptMeterLedgerVO);
    }

}