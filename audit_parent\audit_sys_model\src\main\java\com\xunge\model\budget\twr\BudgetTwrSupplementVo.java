package com.xunge.model.budget.twr;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2022/7/28
 * @description 预算调整补足
 */
@Data
public class BudgetTwrSupplementVo {

    /**
     * 省份ID
     */
    private String prvId;

    /**
     * 省份名称
     */
    private String prvName;
    /**
     * 业务类型({塔类,1}, {室分,2}, {微站,3}, {传输,4}, {非标,5}, {合计,6})
     */
    private Integer productType;

    /**
     * 预算调整补足-预算金额
     */
    private BigDecimal budgetSupplementBudgetFee;

    // 预算调整补足-执行金额
    private BigDecimal budgetSupplementExecutionFee;
    /**
     * 预算调整补足-核减金额
     */
    private BigDecimal budgetSupplementSubtractFee;

    /**
     * 预算金额
     */
    private BigDecimal budgetSupplementAdjustFee;
    private BigDecimal budgetSupplementAdjustFeeAfter;

    private String budgetSupplementRemark;

    private String budgetSupplementExecutionRemark;

}
