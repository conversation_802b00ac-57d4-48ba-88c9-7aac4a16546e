package com.xunge.dao.selfrent.contract;

import com.xunge.model.selfrent.billAccount.DatPaymentPeriodVO;

import java.util.List;
import java.util.Map;

public interface IDatPaymentPeriodDao {
    /**
     * 查询所有缴费周期信息
     *
     * @return
     * <AUTHOR>
     */
    public List<DatPaymentPeriodVO> queryAllDatPaymentPeriod(Map<String, Object> paraMap);

    /**
     * 根据id查询缴费周期信息
     *
     * @param paraMap
     * @return
     * <AUTHOR>
     */
    public DatPaymentPeriodVO queryDatPaymentPeriodById(Map<String, Object> paraMap);
}