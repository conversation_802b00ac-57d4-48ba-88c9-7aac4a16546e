package com.xunge.dao.job;

import com.xunge.model.job.EleContractCollectionVO;
import com.xunge.model.selfelec.EleContract;

import java.util.List;

public interface EleContractCollectionVOMapper {
    int deleteByPrimaryKey(String elecontractId);

    int insert(EleContractCollectionVO record);

    int insertSelective(EleContractCollectionVO record);

    EleContractCollectionVO selectByPrimaryKey(String elecontractId);

    int updateByPrimaryKeySelective(EleContractCollectionVO record);

    int updateByPrimaryKey(EleContractCollectionVO record);

    boolean batchInsertColl(List<EleContractCollectionVO> eleContractList);

    boolean batchInsert(List<EleContract> record);

    boolean batchUpdate(List<EleContract> record);
}