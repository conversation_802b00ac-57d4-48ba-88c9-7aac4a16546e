package com.xunge.dao.selfrent.accrual.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.xunge.dao.selfrent.accrual.IRentAccrualSummaryDao;
import com.xunge.dao.selfrent.accrual.IRentAccrualSummaryMapper;
import com.xunge.model.selfrent.accrual.RentAccrualSummaryVO;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;

/**
 * @创建人 LiangCheng
 * @创建时间 2021/8/25 0025
 * @描述：
 */
public class RentAccrualSummaryDaoImpl implements IRentAccrualSummaryDao {

    @Autowired
    private IRentAccrualSummaryMapper mapper;

    @Override
    public List<String> queryRentAccrualSupplierIdAll(Map<String, Object> maps) {
        return mapper.queryRentAccrualSupplierIdAll(maps);
    }

    @Override
    public void saveRentAccrualSummary(RentAccrualSummaryVO rentAccrualSummary) {
        mapper.saveRentAccrualSummary(rentAccrualSummary);
    }

    @Override
    public PageInfo<RentAccrualSummaryVO> queryRentAccrualSummaryVO(Map<String, Object> map, int pageNumber, int pageSize) {
        PageHelper.startPage(pageNumber, pageSize);
        List<RentAccrualSummaryVO> datas = mapper.queryRentAccrualSummaryVO(map);
        PageInfo<RentAccrualSummaryVO> page = new PageInfo<>(datas);
        return page;
    }

    @Override
    public RentAccrualSummaryVO queryRentAccrualSummaryById(Map<String, Object> map) {
        return mapper.queryRentAccrualSummaryById(map);
    }

    @Override
    public void editRentAccrualSummary(RentAccrualSummaryVO rentAccrualSummary) {
        mapper.editRentAccrualSummary(rentAccrualSummary);
    }

    @Override
    public void delRentAccrualSummary(RentAccrualSummaryVO rentAccrualSummary) {
        mapper.delRentAccrualSummary(rentAccrualSummary);
    }

    @Override
    public List<RentAccrualSummaryVO> queryRentAccrualSummaryList(Map<String, Object> map) {
        return mapper.queryRentAccrualSummaryVO(map);
    }

    @Override
    public List<RentAccrualSummaryVO> queryRentAccrualSummaryListByIds(List<String> ids) {
        return mapper.queryRentAccrualSummaryVOByIds(ids);

    }

    @Override
    public List<RentAccrualSummaryVO> queryRentAccrualUpper(Map<String, Object> map) {
        return mapper.queryRentAccrualUpper(map);
    }
}
