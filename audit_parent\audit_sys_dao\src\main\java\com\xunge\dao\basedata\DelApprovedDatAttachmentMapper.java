package com.xunge.dao.basedata;

import com.xunge.model.basedata.DelApprovedDatAttachment;
import com.xunge.model.basedata.DelApprovedDatAttachmentExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * @ClassName:    DelApprovedDatAttachmentMapper
 * @Description:  ${description}
 * @Date:         2022/3/1
 * @Version:      1.0
 */
public interface DelApprovedDatAttachmentMapper {
    long countByExample(DelApprovedDatAttachmentExample example);

    int deleteByExample(DelApprovedDatAttachmentExample example);

    int deleteByPrimaryKey(String attachmentId);

    int insert(DelApprovedDatAttachment record);

    int insertSelective(DelApprovedDatAttachment record);

    List<DelApprovedDatAttachment> selectByExample(DelApprovedDatAttachmentExample example);

    DelApprovedDatAttachment selectByPrimaryKey(String attachmentId);

    int updateByExampleSelective(@Param("record") DelApprovedDatAttachment record, @Param("example") DelApprovedDatAttachmentExample example);

    int updateByExample(@Param("record") DelApprovedDatAttachment record, @Param("example") DelApprovedDatAttachmentExample example);

    int updateByPrimaryKeySelective(DelApprovedDatAttachment record);

    int updateByPrimaryKey(DelApprovedDatAttachment record);

    int updateState(DelApprovedDatAttachment record);

    Integer fileCountByMd5(String md5Num);
}
