package com.xunge.dao.basedata.collection;

import com.xunge.model.basedata.colletion.FtpFileConfigVO;

import java.util.List;

public interface FtpFileConfigVOMapper {
    boolean deleteByPrimaryKey(String fileId);

    boolean insert(FtpFileConfigVO record);

    boolean insertSelective(FtpFileConfigVO record);

    FtpFileConfigVO selectByPrimaryKey(String fileId);

    boolean updateByPrimaryKeySelective(FtpFileConfigVO record);

    boolean updateByPrimaryKey(FtpFileConfigVO record);

    List<FtpFileConfigVO> getByTaskId(String taskId);

    boolean deleteFtpFileConfigs(String[] ids);
}