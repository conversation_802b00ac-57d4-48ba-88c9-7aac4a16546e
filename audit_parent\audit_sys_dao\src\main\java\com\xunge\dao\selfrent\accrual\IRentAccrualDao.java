package com.xunge.dao.selfrent.accrual;

import com.github.pagehelper.PageInfo;
import com.xunge.model.selfrent.accrual.*;
import com.xunge.model.selfrent.contract.PlatformContractVO;

import java.util.List;
import java.util.Map;

/**
 * @创建人 LiangCheng
 * @创建时间 2021/8/17 0017
 * @描述：
 */
public interface IRentAccrualDao {

    public PageInfo<RentAccrualVO> queryRentAccrualVO(Map<String, Object> map, int pageNumber, int pageSize);

    public RentAccrualVO queryRentAccrualById(Map<String, Object> map);

    RentAccrualVO queryRentExpireAccrualById(Map<String, Object> map);

    public RentAccrualVO queryRentAccrualByCode(Map<String, Object> map);

    public RentAccrualVO queryRentExpireAccrualByCode(Map<String, Object> map);

    public Map<String, Object> queryCostCenter(Map<String, Object> map);

    public void editRentAccrual(RentAccrualVO rentAccrual);

    public void editRentExpireAccrual(RentAccrualVO rentAccrual);

    public void rentAccrualSubmitAudit(Map<String, Object> map);

    List<RentAccrualVO> queryRentAccrualList(Map<String, Object> map);

    List<RentAccrualVO> queryRentExpireAccrualList(Map<String, Object> map);

    RentAccrualVO queryRentAccrualContractByContractId(Map<String, Object> map);

    RentAccrualVO queryRentExpireAccrualContractByContractId(Map<String, Object> map);

    Map<String, Object> queryContractInfoById(Map<String, Object> map);

    void editRentAccrualSummaryInfoById(Map<String, Object> map);

    void delRentAccrualSummaryInfoById(Map<String, Object> map);

    List<RentAccrualVO> querySumRentAccrual(Map<String, Object> map);

    List<RentAccrualConfigVO> queryAccruedConfigData(Map<String, Object> map);

    int queryTotalConfigCount(Map<String, Object> map);

    void updateAccruedUpperFee(RentAccrualConfigVO vo);

    int initTotalConfig(List<RentAccrualConfigVO> list);

    List<RentAccrualConfigVO> queryCostCenterConfig(Map<String, Object> map);

    RentAccrualConfigVO queryCostCenterConfigByCode(Map<String, Object> map);

    List<PlatformContractVO> queryPlatformContract(Map<String, Object> paraMap);

    List<MonthAmountAmVO> queryAccrualMonthAmountAmList(Map<String, Object> map);

    List<MonthAmountAmDetailVO> queryAccrualMonthAmountAmDetail(Map<String, Object> map);

    List<PlatformContractVO> queryMergePlatformDetail(Map<String, Object> map);

    void deleteRentAccrualById(String accrualId);

    void deleteRentExpireAccrualById(String accrualId);

    /**
     * 根据报账点id查询是否可以计提,若可以则返回计提基础数据（该sql由原job任务迁移至此，未作改动）
     *
     * @param accrualType
     * @param billAccountIds
     * @param prvId
     * @return
     */
    List<RentAccrualVO> queryBaseAccrualByAccountIds(Integer accrualType, List<String> billAccountIds, String prvId);

    List<String> queryContractNotNeedAccrual(List<String> contractIds);

    List<RentAccrualVO> queryLastAccrualInfoByAccountIds(Integer accrualType, List<String> billAccountIds, String yearMonth);

    List<RentAccrualVO> queryLastPaymentInfoByAccountIds(List<String> billAccountIds);

    AccrualConfigVo queryAccrualConfig(Integer accrualType, String prvId);

    int addAutoAuditingRecord(AccrualAuditingVo vo);

    List<AccrualAuditingVo> queryNoNeedCheckRecord(String accrualId);

    /**
     * 查询报账点状态是否能计提
     *
     * @param billaccountId
     * @return
     */
    List<RentAccrualCheckDto> queryRentCheckDetail(String billaccountId, Integer accrualType);

    PageInfo<RentAccrualVO> queryAllRentAccrualVO(Map<String, Object> map, int pageNumber, int pageSize);

    PageInfo<RentAccrualVO> queryRentExpireAccrualVO(Map<String, Object> map, int pageNumber, int pageSize);

    void editRentExpireAccrualSummaryInfoById(Map<String, Object> map);

    List<RentAccrualVO> queryAllRentAccrualList(Map<String, Object> map);

    void delRentExpireAccrualSummaryInfoById(Map<String, Object> map);

    List<RentAccrualVO> queryRentAccrualListAll(Map<String, Object> map);
}
