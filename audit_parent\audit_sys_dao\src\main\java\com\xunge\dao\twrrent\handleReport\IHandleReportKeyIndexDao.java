package com.xunge.dao.twrrent.handleReport;

import com.xunge.model.towerrent.monthlyReport.KeyIndexTowerVO;
import com.xunge.model.towerrent.monthlyReport.TowerChargeAnnualVo;

import java.util.List;
import java.util.Map;

public interface IHandleReportKeyIndexDao {


    List<KeyIndexTowerVO> getBaseKeyIndexCopeDataPrv(Map<String, Object> paraMap);

    List<KeyIndexTowerVO> getBaseKeyIndexCopeDataPreg(Map<String, Object> paraMap);

    List<KeyIndexTowerVO> getBaseKeyIndexCopeDataReg(Map<String, Object> paraMap);

    List<KeyIndexTowerVO> getBaseKeyIndexCopeDataWhole(Map<String, Object> paraMap);

    List<TowerChargeAnnualVo> queryAnnualStatement(Map<String, Object> paraMap);
}
