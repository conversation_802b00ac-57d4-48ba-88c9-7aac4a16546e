package com.xunge.dao.towerrent.trans;

import com.xunge.core.page.Page;
import com.xunge.model.towerrent.trans.MobileRentTransChangeVO;
import com.xunge.model.towerrent.trans.MobileRentTransVO;
import com.xunge.model.towerrent.trans.TowerRentTransChange;
import com.xunge.model.towerrent.trans.TowerRentTransVO;

import java.util.List;
import java.util.Map;

/**
 * Created by liuxia<PERSON> on 2019/7/25.
 */
public interface ITwrTransDao {
    TowerRentTransVO getRwrRentTransInfoById(String rentInformationTowerTransId);

    TowerRentTransVO queryTransBeanById(Map<String, Object> map);

    MobileRentTransVO getMobileRentTransById(Map<String, String> paramMap);

    int updateRentTransChangeCheckState(Map<String, String> paramMap);

    int updateRentTransCheckState(Map<String, String> paramMap);

    int deleteChangeByBussId(Map<String, String> paramMap);

    int deleteByBussId(Map<String, String> paramMap);

    int insertTransAsTower(Map<String, String> paramMap);

    int insertTransChangeAsTower(Map<String, String> paramMap);

    List<TowerRentTransChange> getTwrTransUpdateHistory(Map<String, Object> paraMap);

    int updateMobileRentTransByParam(Map<String, Object> changeMap);

    int insertBatchByTwrRentChange(Map<String, Object> changeMap);

    MobileRentTransVO getMobileRentTransInfoById(Map<String, Object> param);

    MobileRentTransVO queryMobileTransBeanById(Map<String, Object> param);

    List<MobileRentTransChangeVO> getMobileChangeHistory(Map<String, String> paramMap);

    int mobileChangeCheckStateUpdate(Map<String, String> map);

    int mobileTransCheckStateUpdate(Map<String, String> map);

    Page<TowerRentTransChange> getTwrTransUpdateHistory(Map<String, Object> paraMap, int pageNum, int pageSize);

    Page<MobileRentTransChangeVO> getMobileChangeHistory(Map<String, Object> map, int pageNum, int pageSize);
}
