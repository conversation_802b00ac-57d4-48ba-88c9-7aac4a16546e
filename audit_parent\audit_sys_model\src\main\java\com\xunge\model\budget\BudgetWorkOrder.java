package com.xunge.model.budget;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 
 * @TableName budget_work_order
 */
@Data
public class BudgetWorkOrder extends BudgetBaseVo implements Serializable {

    /**
     * 工单名称
     */
    private String budgetName;

    /**
     * 创建人ID
     */
    private String createUser;

    /**
     * 创建人ID
     */
    private String createUserName;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 工单状态
     */
    private Integer workOrderState;

    /**
     * 是否发送短信（0：发送 1：不发送）
     */
    private Integer sendSms;

    /**
     * 备注
     */
    private String remark;

    /**
     * 更新人ID
     */
    private String updateUser;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 工单是否删除 0：已删除 1：未删除
     */
    private Integer isDelete;

    /**
     * 是否下发省 1：下发 0：不下发
     */
    private Integer ifSend;

    private String generatedUuid;

    private String prvNames;

    /**
     * 下发时间
     */
    private Date issueTime;

    /**
     * 省份工单填报审核状态，用于省份查询用
     */
    private Integer prvEscalationStatus;

    /**
     * 审核页面查询列表时需要返回此参数
     */
    private String taskId;

    /**
     * 审核页面查询参数：流程businessTable
     */
    private String businessTable;

    /**
     * 审核页面查询参数：审核人loginname
     */
    private String auditingUser;

    /**
     * 审核提交人账号
     */
    private String submitUserLoginName;

    private static final long serialVersionUID = 1L;
}