package com.xunge.dao.twrrent.towerReport;

import com.xunge.core.page.Page;
import com.xunge.model.towerrent.rentmanager.RentAndBillImportReport;
import com.xunge.model.towerrent.rentmanager.TowerReconciliationReportVO;
import com.xunge.model.towerrent.rentmanager.TowerRentInformationReport;

import java.util.List;
import java.util.Map;

/**
 * TODO：
 *
 * <AUTHOR>
 * @version 1.0 2018-08-02/14:13
 */
public interface ITowerReportDao {

    /**
     * 铁塔起租单报表
     *
     * @param paraMap
     * @param pageNumber
     * @param pageSize
     * @return
     */
    Page<TowerRentInformationReport> queryTowerRentinformationReport(Map<String, Object> paraMap, int pageNumber, int pageSize);

    List<TowerRentInformationReport> queryTowerRentinformationReport(Map<String, Object> paraMap);

    /**
     * 分页查询对账数据统计结果
     *
     * @param paraMap
     * @return
     */
    Page<TowerReconciliationReportVO> queryPageTowerReconciliationReport(Map<String, Object> paraMap);

    List<TowerReconciliationReportVO> queryTowerReconciliationReport(Map<String, Object> paraMap);

    List<RentAndBillImportReport> queryRentAndBillImportReport(Map<String, Object> paraMap);
}
