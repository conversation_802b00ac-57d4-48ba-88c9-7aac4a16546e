package com.xunge.dao.finance;

import com.xunge.model.finance.RecActivity;
import com.xunge.model.finance.RecExclude;
import com.xunge.model.finance.RecFinance;
import com.xunge.model.finance.RecFinanceExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface RecFinanceMapper {
    int countByExample(RecFinanceExample example);

    int deleteByExample(RecFinanceExample example);

    int deleteByPrimaryKey(String classId);

    int insert(RecFinance record);

    int insertSelective(RecFinance record);

    List<RecFinance> selectByExample(RecFinanceExample example);

    RecFinance selectByPrimaryKey(String classId);

    int updateByExampleSelective(@Param("record") RecFinance record, @Param("example") RecFinanceExample example);

    int updateByExample(@Param("record") RecFinance record, @Param("example") RecFinanceExample example);

    int updateByPrimaryKeySelective(RecFinance record);

    int updateByPrimaryKey(RecFinance record);

    List<RecFinance> selectList(RecFinance param);

    List<RecFinance> selectListNotLike(RecFinance param);

    List<RecFinance> selectListCanChoose(RecFinance param);

	List<RecFinance> selectPriorityAN1804(RecFinance param);
    List<RecActivity> selectActivityList(RecActivity param);

    List<RecActivity> selectActivityListTower(RecActivity param);

    List<RecFinance> selectRentClassListByPage(RecExclude param);

    List<RecExclude> selectThreeTaClassListByPage(RecExclude param);
}