package com.xunge.dao.report;

import com.xunge.model.report.*;

import java.util.List;
import java.util.Map;

public interface RptSingleStationCostAnalysisMapper {

    List<RptAreaFlowData> queryAreaFlowDataInfo(RptAreaFlowData rptAreaFlowData);

    List<RptSingleStationCostAnalysis> queryCostStatisInfo(RptSingleStationCostAnalysis singleStationCostAnalysis);

    List<RptFlowPrice> queryFlowPriceHis(RptFlowPrice rptFlowPrice);

    void addFlowPrice(RptFlowPrice rptFlowPrice);

    Integer queryFlowPriceNum();

    void deleteLastestFlowPrice();

    List<RptSingleStationCostAnalysisDetailResult> loadCostAnalysisInfo(RptSingleStationCostAnalysisDetail rptSingleStationCostAnalysisDetail);

    List<RptSingleStationCostAnalysisDetail> loadCostAnalysisDetailInfo(RptSingleStationCostAnalysisDetail rptSingleStationCostAnalysisDetail);
}
