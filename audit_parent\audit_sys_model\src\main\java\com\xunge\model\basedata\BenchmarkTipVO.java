package com.xunge.model.basedata;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2018年08月22日
 */
public class BenchmarkTipVO {

    //报账点空调总功率
    private BigDecimal airconditionerPowerTotle;
    //设备总功率
    private BigDecimal basestationPowerTotle;
    //日额定功率标杆
    private BigDecimal dayBenchmark;

    public BigDecimal getAirconditionerPowerTotle() {
        return airconditionerPowerTotle;
    }

    public void setAirconditionerPowerTotle(BigDecimal airconditionerPowerTotle) {
        this.airconditionerPowerTotle = airconditionerPowerTotle;
    }

    public BigDecimal getBasestationPowerTotle() {
        return basestationPowerTotle;
    }

    public void setBasestationPowerTotle(BigDecimal basestationPowerTotle) {
        this.basestationPowerTotle = basestationPowerTotle;
    }

    public BigDecimal getDayBenchmark() {
        return dayBenchmark;
    }

    public void setDayBenchmark(BigDecimal dayBenchmark) {
        this.dayBenchmark = dayBenchmark;
    }
}