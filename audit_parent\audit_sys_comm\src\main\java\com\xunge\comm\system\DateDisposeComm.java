package com.xunge.comm.system;

/**
 * <AUTHOR>
 * @date 2017年10月12日
 * @describe 导出导入时需要处理数据的列名
 */
public class DateDisposeComm {
	/**
	 * 区县
	 */
	public static String REGION = "区县";

	/**
	 * 共享信息
	 */
	public static String SHARE_INFO = "共享信息";

	/**
	 * 缴费明细
	 */
	public static String PAY_DETAIL = "缴费明细";
	public static String ELE_PAY_DETAIL = "电费缴费明细";
	public static String RENT_PAY_DETAIL = "租费缴费明细";
	public static String THREE_PAY_DETAIL = "三方塔缴费明细";
	public static String SPECIAL_ELE_PAY_DETAIL = "特殊电费缴费明细";
	public static String NEW_ENERGY_ELE_PAY_DETAIL = "新能源电费缴费明细";
	public static String SPECIAL_RENT_PAY_DETAIL = "特殊租费缴费明细";
	public static String SPECIAL_THREE_PAY_DETAIL = "特殊三方塔服务费缴费明细";
	public static String SPECIAL_RENT_PAY_DETAIL_AMOUNT = "汇总明细";
	public static String LOAN_PAY_DETAIL = "预付费电费缴费明细";
	public static String VERIFICATION_PAY_DETAIL = "核销电费缴费明细";
	public static String ELE_BILLAMOUNT_DETAIL = "电费汇总核查明细";
	public static String VER_BILLAMOUNT_DETAIL = "核销汇总核查明细";
	public static String RENT_BILLAMOUNT_DETAIL = "租费汇总核查明细";
	public static String BENCH_MARKE_PARA = "外部标杆详情明细";
	public static String SPECIAL_TELE_PAY_DETAIL = "特殊塔维电费缴费明细";

	public static String ELE_LOAN_INFO = "报账点电费预付费导出";

	public static String ELE_METER_INFO = "电表信息导出";

	public static String ELE_LOAN_METER_INFO = "报账点预付费先款后票明细";


	/**
	 * 缴费明细.xls
	 */
	public static String PAY_DETAIL_XLS = "缴费明细.xls";
	public static String BILL_AMOUNT_DETAIL_XLSX = "汇总单明细.xlsx";
	public static String ELE_PAY_DETAIL_XLS = "电费缴费明细.xls";
	public static String ELE_CONTRACT_PRICE_XLS = "电费合同平均单价.xlsx";
	public static String RENT_CONTRACT_PRICE_XLS = "租费合同平均单价.xlsx";
	public static String RENT_PAY_DETAIL_XLS = "租费缴费明细.xls";
	public static String THREE_PAY_DETAIL_XLS = "三方塔缴费明细.xls";
	public static String RENT_PAY_DETAIL_XLSX = "租费缴费明细.xlsx";
	public static String PAY_DETAIL_XLSX = "缴费明细.xlsx";
	public static String SPECIAL_ELE_PAY_DETAIL_XLS = "特殊电费缴费明细.xls";
	public static String ELE_BILLAMOUNT_DETAIL_XLS = "电费汇总核查明细.xls";
	public static String VER_BILLAMOUNT_DETAIL_XLS = "核销汇总核查明细.xls";
	public static String RENT_BILLAMOUNT_DETAIL_XLS = "租费汇总核查明细.xls";
	public static String BENCH_MARKE_PARA_INFO = "外部标杆详情.xls";


	/**
	 * 所属区县
	 */
	public static String BELONG_REGION = "所属区县";

	/**
	 * 所属地市
	 */
	public static String BELONG_CITY = "所属地市";

	/**
	 * 上次缴费期终
	 */
	public static String PAYMENT_ENDDATE = "上次缴费期终";

	/**
	 * 计划缴费日期
	 */
	public static String PLAY_DATE = "计划缴费日期";

	/**
	 * 合同总月数
	 */
	public static String CONTRACT_ALL_MOUNTH = "合同总月数";

	/**
	 * 剩余天数
	 */
	public static String RESIDUE_DAYS = "剩余天数";

	/**
	 * 房租合同信息.xls
	 */
	public static String RENT_CONTRACT_INFO_XLS = "房租合同信息.xls";

	/**
	 * 大集中合同房租合同信息.xls
	 */
	public static String CENTRALIZATION_RENT_CONTRACT_INFO_XLS = "大集中合同房租合同信息.xls";

	/**
	 * 租费固化信息.xls
	 */
	public static String RENT_CURING_INFO_XLS = "租费固化信息.xls";

	/**
	 * 所属部门
	 */
	public static String MANAGER_DEPARTMENT = "所属部门";

	/**
	 * 合同预警信息.xls
	 */
	public static String CONTRACT_WARNING_XLS = "合同预警信息.xls";

	/**
	 * 租费预警信息.xls
	 */
	public static String RENT_WARNING_XLS = "租费预警信息.xls";

	/**
	 * 电费缴费预警信息.xls
	 */
	public static String ELEC_WARNING_XLS = "电费缴费预警信息.xls";

	/**
	 * 电费固化信息预警信息.xls
	 */
	public static String ELEC_CACHING_WARNING_XLS = "电费固化信息预警信息.xls";

	/**
	 * 电费合同预警信息.xls
	 */
	public static String ELEC_CONTRACT_WARNING_XLS = "电费合同预警信息.xls";

	/**
	 * 我方联系人
	 */
	public static String CONTRACT_CHECKNAME_1 = "我方联系人";

	/**
	 * 对方联系人
	 */
	public static String CONTRACT_CHECKNAME_2 = "对方联系人";

	/**
	 * 报账点数据
	 */
	public static String REPORTING_POINT_DATA = "报账点数据";

	/**
	 * 报账点数据
	 */
	public static String EXPORT_UNPAID = "长期未报账的报账点导出";

	/**
	 * ".xlsx"格式
	 */
	public static String XLSX = ".xlsx";

	/**
	 * .xls
	 */
	public static String SUFF_XLS = ".xls";

	/**
	 * .xlsx
	 */
	public static String SUFF_XLSX = ".xlsx";

	/**
	 * .pdf
	 */
	public static String FORMAT_PDF = ".pdf";
	/**
	 * csv格式
	 */
	public static String SUFF_CSV = ".csv";

	/**
	 * 导出图片名称 exportImg2.bmp
	 */
	public static String IMG_BMP = "exportImg2.bmp";

	/**
	 * base64String replace words: "data:image/png;base64,"
	 */
	public static String BASE64STRING_REPLACE = "data:image/png;base64,";

	/**
	 * 导出标记-exportFlag
	 */
	public static String EXPORT_FLAG = "exportFlag";

	/**
	 * 缴费记录信息
	 */
	public static String PAYMENT_RECORD_INFO = "缴费记录信息";

	/**
	 * 缴费记录信息.xls
	 */
	public static String PAYMENT_RECORD_INFO_xls = "缴费记录信息.xls";

	/**
	 * 测试
	 */
	public static String TEST = "测试";

	/**
	 * 报账点信息管理
	 */
	public static String BILLACCOUNT_INFO_MANAGE = "报账点信息管理";

	/**
	 * 报账点信息管理
	 */
	public static String SPECIAL_BILLACCOUNT_INFO_MANAGE = "特殊报账点信息管理";

	/**
	 * 报账点信息管理.XLS
	 */
	public static String BILLACCOUNT_INFO_MANAGE_xls = "报账点信息管理.xls";

	/**
	 * 开始解析文件
	 */
	public static String BEGIN_ANALAYSIS_FILE = "开始解析文件";

	/**
	 * 解析文件完成,开始验证数据
	 */
	public static String ANALAYSIS_FILE_DONE = "解析文件完成,开始验证数据。";

	/**
	 * 验证数据完成，开始保存数据
	 */
	public static String ANALAYSIS_FILE_SAVEDONE = "验证数据完成，开始保存数据。";

	/**
	 * 额定功率标杆
	 */
	public static String RATED_POWER_BENCHMARK = "额定功率标杆";

	/**
	 * 额定功率标杆.xls
	 */
	public static String RATED_POWER_BENCHMARK_XLS = "额定功率标杆.xls";

	/**
	 * 智能电表标杆
	 */
	public static String SMARTMETER_BENCHMARK = "智能电表标杆";

	/**
	 * 智能电表标杆.xls
	 */
	public static String SMARTMETER_BENCHMARK_XLS = "智能电表标杆.xls";

	/**
	 * 开关电源负载标杆
	 */
	public static String SWITCHING_POWER_SUPPLY_LOAD_BENCHMARK = "开关电源负载标杆";

	/**
	 * 开关电源负载标杆.xls
	 */
	public static String SWITCHING_POWER_SUPPLY_LOAD_BENCHMARK_XLS = "开关电源负载标杆.xls";

	/**
	 * 动环负载标杆
	 */
	public static String MOVABLE_RING_LOAD_BENCHMARK = "动环负载标杆";

	/**
	 * 历史电表标杆
	 */
	public static String HISTORICAL_METER_BENCHMARK = "历史电表标杆";

	/**
	 * 历史电费标杆.xls
	 */
	public static String HISTORICAL_ELECTRICITY_BENCHMARK_XLS = "历史电费标杆.xls";

	/**
	 * 标杆参数设置
	 */
	public static String BENCHMARK_PARAMETER_SETTING = "标杆参数设置";

	/**
	 * 标杆参数设置.xls
	 */
	public static String BENCHMARK_PARAMETER_SETTING_XLS = "标杆参数设置.xls";

	/**
	 * 电费合同信息导出-
	 */
	public static String ELEC_CONTRACT_INFO_EXPORT = "电费合同信息导出-";

	/**
	 * 电费合同信息导出-
	 */
	public static String ELEC_CONTRACT_EXPORT = "电费固化信息导出-";


	/**
	 * 电费合同信息
	 */
	public static String ELEC_CONTRACT_INFO = "电费合同信息";

	/**
	 * 业务变更审核单.xls
	 */
	public static String BUSINESS_UPDATE_REVIEW_SINGLE = "业务变更审核单.xls";

	/**
	 * 合同分类
	 */
	public static String CONTRACT_TYPE = "合同分类";

	/**
	 * 固化信息预警信息.xls
	 */
	public static String CURING_CONTRACT_WARNING_XLS = "固化信息预警信息.xls";

	/**
	 * 报账点备注
	 */
	public static String BILLACCOUNT_NOTE = "备注";
	/**
	 * 报账点类型
	 */
	public static String BILLACCOUNT_TYPE = "报账点类型";

	/**
	 * 合同期始
	 */
	public static String CONTRACT_START = "合同期始";
	/**
	 * 合同期终
	 */
	public static String CONTRACT_END = "合同期终";

	/**
	 * 报账点状态
	 */
	public static String BILLACCOUNT_STATE = "报账点状态";
	/**
	 * 缴费备注
	 */
	public static String PAYMENT_NOTE = "缴费备注";
	/**
	 * 省份
	 */
	public static String PRVNAME = "省份";

	/**
	 * 签约日期
	 */
	public static String CONTRACT_SYS = "签约日期";
	/**
	 * 我方联系人
	 */
	public static String CONTRACT_CHECK_MY = "我方联系人";
	/**
	 * 对方联系人
	 */
	public static String CONTRACT_CHECK_OTHER = "对方联系人";
	/**
	 * 合同状态
	 */
	public static String CONTRACT_STATE = "合同状态";
	/**
	 * 是否向下共享
	 */
	public static String IS_DOWN_SHARE = "是否向下共享";
	/**
	 * 地市
	 */
	public static String PREG_NAME = "地市";
	/**
	 * 工单统计
	 */
	public static String WORK_ORDER_STATISTICS = "工单统计";

	/**
	 * 湖北
	 */
	public static String HB_prvCode = "HB";

	/**
	 * 云南
	 */
	public static String YN_prvCode = "YN";

	/**
	 * 特殊报账点
	 */
	public static String SPECIAL_BILLACCOUNT_DATA = "特殊报账点数据";

	/**
	 * 租费明细导出
	 */
	public static String RENT_DETAIL_EXPORT = "租费明细导出";
	public static String THREE_DETAIL_EXPORT = "三方塔明细导出";

	/**
	 * 租赁费年度报表
	 */
	public static String RENT_ANNUAL_XLS = "租赁费年度报表.xls";
	/**
	 * 租费计提明细
	 */
	public static String RENT_ACCRUAL_DETAIL = "租费计提明细";
	/**
	 * 租费计提修改导出
	 */
	public static String RENT_ACCRUAL_EDIT_EXPORT = "租费计提修改导出";

	/**
	 * 租费到期合同计提修改导出
	 */
	public static String RENT_EXPIRE_ACCRUAL_EDIT_EXPORT = "租费到期合同计提修改导出";
	/**
	 * 三方塔计提修改导出
	 */
	public static String TOWER_ACCRUAL_EDIT_EXPORT = "三方塔计提修改导出";

	/**
	 * 三方塔到期计提修改导出
	 */
	public static String TOWER_EXPIRE_ACCRUAL_EDIT_EXPORT = "三方塔到期计提修改导出";
	/**
	 * 三方塔计提明细
	 */
	public static String TOWER_ACCRUAL_DETAIL = "三方塔计提明细";
	/**
	 * 租费计提汇总明细
	 */
	public static String RENT_ACCRUAL_SUMMARY_DETAIL = "租费计提汇总明细";
	/**
	 * 三方塔计提汇总明细
	 */
	public static String TOWER_ACCRUAL_SUMMARY_DETAIL = "三方塔计提汇总明细";

	/**
	 * 租费零流量资源预警信息
	 */
	public static String RENT_ZERO_FLOW_RESOURCE_WARNING = "租费零流量资源预警信息";


	/**
	 * 铁塔服务费零流量站点预警
	 */
	public static String TWR_ZERO_FLOW_RESOURCE_WARNING = "铁塔服务费零流量站点预警";



	/**
	 * 三方塔服务费零流量资源预警信息
	 */
	public static String TWR_THREE_ZERO_FLOW_RESOURCE_WARNING = "三方塔服务费零流量资源预警信息";
	/**
	 * 直供电区域单价信息
	 */
	public static String ELE_REGION_PRICE = "直供电区域单价信息";

	/**
	 * 当前审核人字段
	 */
	public static String CURRENT_AUDIT_USER = "当前审核人";

	/**
	 * 租费零流量资源预警导出需要隐藏原因列的时间节点
	 */
	public static String HIDDEN_REASON_TIME = "2022-06";

	/**
	 * 预算管理，电费预算明细数据
	 */
	public static String BUDGET_ELE_SYSTEM_PAYABLE = "系统应付数据";

	/**
	 * 网络电费预算管理
	 */
	public static String BUDGET_ELE_FILE_NAME = "网络电费预算管理";

	/**
	 * 计提冲销汇总单
	 */
	public static String RENT_WRITE_OFF_ACCRUAL_SUMMARY= "计提冲销汇总单";

	/**
	 * 计提冲销汇总明细
	 */
	public static String RENT_WRITE_OFF_ACCRUAL_SUMMARY_DETAIL = "计提冲销汇总明细";
	/**
	 * 铁塔服务费费用汇总导出
	 */
	public static String TOWER_FEE_SUMMARY_EXPORT = "铁塔服务费费用汇总导出";
	/**
	 * 铁塔服务费费用审核导出
	 */
	public static String TOWER_FEE_AUDIT_EXPORT = "铁塔服务费费用审核导出";

	/**
	 * 缴费明细导出
	 */
	public static String FEE_DETAIL_EXPORT = "缴费明细导出";

	/**
	 * 特殊缴费明细导出
	 */
	public static String SPECIAL_FEE_DETAIL_EXPORT = "特殊缴费明细导出";

	/**
	 * 资源异常预警明细
	 */
	public static String RESOURCE_ANOMALOUS_WARNING_DETAIL = "资源异常预警明细";
}
