package com.xunge.dao.selfelec.warning.elePaymentWarning;

import com.xunge.model.selfelec.TowerInfo;
import com.xunge.model.selfelec.warning.PaymentWarning;
import com.xunge.model.selfelec.warning.PaymentWarningBean;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * Created by l<PERSON>xia<PERSON> on 2022/3/21.
 */
public interface PaymentWarningMapper {
    List<TowerInfo> queryPtShareCount(Map<String,Object> paramMap);

    List<TowerInfo> queryOtherTwr(Map<String,Object> paramMap);

    List<PaymentWarning> queryPaymentWarning(Map<String,Object> paramMap);

    /**
     * 批量保存预警信息
     * @param ptShareCountAndCmccWarnings
     */
    void insert(@Param("ptShareCountAndCmccWarnings") List<PaymentWarningBean> ptShareCountAndCmccWarnings);
    /**
     * 根据条件删除预警数据
     * @param paymentWarningBean
     */
    void delete(PaymentWarningBean paymentWarningBean);

    /**
     * 删除预警信息根据缴费主键id
     * @param map
     */
    void deleteByCondition(Map<String, Object> map);
}
