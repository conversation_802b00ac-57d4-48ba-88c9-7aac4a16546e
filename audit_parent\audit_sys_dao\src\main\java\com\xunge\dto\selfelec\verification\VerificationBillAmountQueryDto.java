package com.xunge.dto.selfelec.verification;

import com.xunge.dto.selfelec.PageInfo;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2021/12/13 17:15
 */
@Getter
@Setter
public class VerificationBillAmountQueryDto extends PageInfo {
    private String paymentCode;
    private String billaccountCode;
    private String loanBillamountCode;
    private String pregId;
    private String regId;
    private String className;
    private String classSmName;
    private String contractCode;
    private String supplierCode;
    private String billamountCode;
    private String billamountState;
    private String activityName;
    private String activityCode;
    private String claimTypeName;
    private String billamountDateStart;
    private String billamountDateEnd;
    private String isFinance;
    private String claimNum;
    private String minBillamountAdjust;
    private String maxBillamountAdjust;
    private String newFlag;
    private Integer useCheck;
    private String pushDateStart;
    private String pushDateEnd;
    private String flag = "0";
    private String offsFlag;
}
