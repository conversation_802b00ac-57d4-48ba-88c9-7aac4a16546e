package com.xunge.dao.twrrent.towerReport;

import com.xunge.model.towerrent.monthlyReport.TowerVillageVo;

import java.util.List;
import java.util.Map;

public interface TowerVillageReportMapper {
    /**
     * 省份查询
     */
    List<TowerVillageVo> queryTowerVillagePrv(Map<String, Object> paraMap);
    List<TowerVillageVo> queryTowerVillagePrvLj(Map<String, Object> paraMap);
    /**
     * 直辖市查询
     */
    List<TowerVillageVo> queryTowerVillagePrvZX(Map<String, Object> paraMap);
    List<TowerVillageVo> queryTowerVillagePrvZXLj(Map<String, Object> paraMap);
    /**
     * 省份合计
     */
    TowerVillageVo queryTowerVillagePrvCount(Map<String, Object> paraMap);
    TowerVillageVo queryTowerVillagePrvCountLj(Map<String, Object> paraMap);
    /**
     * 全国查询
     */
    List<TowerVillageVo> queryTowerVillageHq(Map<String, Object> paraMap);
    List<TowerVillageVo> queryTowerVillageHqLj(Map<String, Object> paraMap);
    /**
     * 全国合计
     */
    TowerVillageVo queryTowerVillageHqCount(Map<String, Object> paraMap);
    TowerVillageVo queryTowerVillageHqCountLj(Map<String, Object> paraMap);
}
