package com.xunge.comm.utils;

import lombok.extern.slf4j.Slf4j;

import javax.servlet.http.HttpServletResponse;
import java.io.*;

/**
 * 文件下载或者删除
 *
 * <AUTHOR>
 */
@Slf4j
public class ToolDown {

    public static void sendFileUrl(String fileName, String filePath, HttpServletResponse response) throws Exception {
        response.reset();
        response.setCharacterEncoding("utf-8");
        response.setContentType("application/octet-stream");
        response.setHeader("Content-disposition", "attachment; filename=" + new String(fileName.getBytes("UTF-8"), "ISO8859-1"));
        downFile(response, filePath, fileName);
		/*		File file = new File(filePath);
				if (file.exists()) {
					file.delete();
				}*/
    }

    public static void downFile(HttpServletResponse response, String filePath, String fileName) throws IOException {
        System.out.println("文件路径：" + filePath + "文件名称" + fileName);
        // 文件路径
        Long fileLength = new File(filePath).length();// 文件的长度
        System.out.println("-----------------文件路径长度：-----------------------" + fileLength);
        if (fileLength != 0) {
            BufferedInputStream bis = null;
            BufferedOutputStream bos = null;
            FileInputStream fis = null;
            try {
                response.setHeader("Content-Length", String.valueOf(fileLength));
                fis = new FileInputStream(filePath);
                bis = new BufferedInputStream(fis);
                // 输出流
                bos = new BufferedOutputStream(response.getOutputStream());
                byte[] buff = new byte[2048];
                int bytesread;
                // 写文件
                while (-1 != (bytesread = bis.read(buff, 0, buff.length))) {
                    bos.write(buff, 0, bytesread);
                }
                // 跳转的路径
                bos.flush();
            } catch (FileNotFoundException e) {
                log.error("ToolDown 出错", e);
            }finally {
                if (fis!=null) {
                    fis.close();
                }
                if (bis!=null) {
                    bis.close();
                }
                if (bos!=null) {
                    bos.close();
                }
            }
        }
    }
}
