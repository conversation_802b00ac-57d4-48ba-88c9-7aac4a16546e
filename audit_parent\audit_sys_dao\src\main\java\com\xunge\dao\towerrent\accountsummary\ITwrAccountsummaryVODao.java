package com.xunge.dao.towerrent.accountsummary;

import com.xunge.core.page.Page;
import com.xunge.model.costcenter.CostCenterVO;
import com.xunge.model.costcenter.SmapUserOrgVO;
import com.xunge.model.selfrent.billamount.BillamountLogVO;
import com.xunge.model.system.user.SysUserVO;
import com.xunge.model.towerrent.accountsummary.*;
import com.xunge.model.towerrent.contract.TwrContract;
import com.xunge.model.towerrent.settlement.BillCheckPraceFeeRentExportVO;
import com.xunge.model.towerrent.settlement.TowerAndMobileBillConfirmVO;
import com.xunge.model.towerrent.settlement.TowerPaymentBillVO;
import com.xunge.model.towerrent.settlement.TwrImageCheckMeterVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface ITwrAccountsummaryVODao {

    int deleteByPrimaryKey(String accountsummaryId);

    int insert(TwrAccountsummaryVO record);

    int insertSelective(TwrAccountsummaryVO record);

    TwrAccountsummaryVO selectByPrimaryKey(String accountsummaryId);

    int updateByPrimaryKeySelective(TwrAccountsummaryVO record);

    int updateByPrimaryKey(TwrAccountsummaryVO record);

    int insertSysLog(BillamountLogVO vo);

    int updatePushdState(Map<String, Object> map);

    /**
     * 根据参数查询费用汇总集合
     *
     * @param params Map<String,Object》
     * @return list List<Map<String, Object>>
     */
    List<Map<String, Object>> queryTwrAccountsummaryMapListByCondition(Map<String, Object> params);

    /**
     * 根据参数查询费用汇总Map分页
     *
     * @param params
     * @return
     */
    Page<Map<String, Object>> queryTwrAccountsummaryMapPage(Map<String, Object> params);

    Page<List<Map<String, Object>>> queryTowerSupplierName(Map<String, Object> params);

    /**
     * 根据参数条件删除费用汇总
     *
     * @param params
     * @return
     */
    int deleteTwrAccountsummaryByCondition(Map<String, Object> params);

    /**
     * 撤销审核
     *
     * @param params
     * @return
     */
    int updateAccountsummaryByCondition(Map<String, Object> params);

    /**
     * 根据费用汇总单Id查询验证提交过的费用汇总单数量
     *
     * @param params
     * @return
     */
    int selectSubmitedAccountsummaryCountByCondition(Map<String, Object> params);

    /**
     * 根据参数查询费用汇总集合(此方法不含针对regId == null的查询)
     *
     * @param params Map<String,Object》
     * @return list List<Map<String, Object>>
     */
    List<Map<String, Object>> queryTwrAccountsummeryMapListByCondition1(Map<String, Object> params);

    /**
     * 查询汇总费用审核列表
     *
     * @param params
     * @return
     * <AUTHOR>
     */
    Page<TwrAccountsummaryVO> queryPageAccountsummery(Map<String, Object> params);

    /**
     * 根据ID编码查询信息
     *
     * @param params
     * @return
     */
    public TwrAccountsummaryVO selectByAccountId(Map<String, Object> params);

    public List<TwrAccountsummaryPushVO> queryAccountsummaryByIds(Map<String, Object> map);

    /**
     * 铁塔产品服务费推送大集中查询
     *
     * @param map
     * @return
     * @throws Exception
     */
    public List<TwrAccountsummaryPushVO> queryAccountsummaryByIdsTwr(Map<String, Object> map) throws Exception;

    //费用汇总页面导出账单详情
    List<TowerAndMobileBillConfirmVO> selectTowerAndMobileConfirmBill(Map<String, Object> paraMap);

    List<BillCheckPraceFeeRentExportVO> queryRentPaymentByDate(Map<String, Object> map);

    /**
     * 查询推送成功的汇总单
     *
     * @param map
     * @return
     * @throws Exception
     */
    public List<TwrAccountsummaryPushVO> queryAccountsummaryByCondition(Map<String, Object> map) throws Exception;


    /**
     * 分页查询推送成功的汇总单
     *
     * @param map
     * @throws Exception
     */
    public List<TwrAccountsummaryPushVO> queryAccountsummaryPage(Map<String, Object> map) throws Exception;

    /**
     * 查询推送成功的汇总单的数量
     *
     * @param map
     * @return
     * @throws Exception
     */
    int queryAccountsummaryByConditionCount(Map<String, Object> map) throws Exception;

    /**
     * 根据ID编码查询信息
     *
     * @param params
     * @return
     */
    TwrAccountsummaryVO queryBeanById(Map<String, Object> params);

    int queryBeanCountById(Map<String, Object> params);

    String selectOrgCodeBySmapId(Map<String, Object> params);

    List<SmapUserOrgVO> selectCostCenterByOrgCode(Map<String, Object> params);

    Page<CostCenterVO> selectCostCenterPage(Map<String, Object> params);

    List<SysUserVO> querySysUserByRole(Map<String, Object> params);

    List<TowerPaymentBillVO> selectTowerPaymentBill(Map<String, Object> map);

    String qryMaxPushDateCompanyType(Map<String, Object> map);

    Page<TwrContract> findListByEntity(TwrContract twrContract, Map<String, Object> params);

    int updatePushdStateByPushCode(Map<String, Object> map);

    int insertPunshDetails(List<TwrPunshDetails> pds);

    int updatePunshDetails(Map<String, Object> map);

    int deletePunshDetails(Map<String, Object> map);

    List<TwrPunshDetails> queryPunshDetails(Map<String, Object> map);

    TwrPunshDetails queryAdjustDetail(Map<String, Object> map);

    /**
     * 查询各模块场地费重复情况
     */
    int queryTowerAbstractLogo(String accountSummaryId);

    int queryRoomAbstractLogo(String accountSummaryId);

    int queryTinyAbstractLogo(String accountSummaryId);

    int queryNonstandAbstractLogo(String accountSummaryId);

    int insertTwrRefinanceList(List<TwrAccountsummaryRefinance> twrAccountsummaryRefinances);

    int deleteTwrRefinance(Map<String, Object> params);

    List<TwrAccountsummaryRefinance> selectTwrRefinanceList(String twrAccountsummaryId);

    int checkSecondSummaryRecfinance(List<String> idList);

    int selectRecFinaceNum(List<String> idList);

    int checkActivityCodes(List<String> codeList);

    List<TwrImageCheckMeterVO> queryTowerImageCheckList(TwrImageCheckMeterVO imageCheckMeterVO);

    List<TwrOilSummaryPushDetailVO> querySummaryPushDetailBySummaryCode(String accountSummaryCode);

    int insertTwrOilSummaryPushDetail(TwrOilSummaryPushDetailVO twrOilSummaryPushDetailVO);

    List<TwrAccountsummaryPushVO> queryTwrAccountSummaryListByPushCode(String pushCode);

    TwrOilSummaryPushDetailVO querySummaryPushDetailByExecuteLink(String twrAccountSummaryCode);

    TwrOilSummaryPushDetailVO querySummaryPushDetailByReturnInfo(String twrAccountSummaryCode);

    int updatePushDetailsState(String accountsummaryCode);

    int deleteTwrAccountSummaryActRuTask(String businessKey);
}
