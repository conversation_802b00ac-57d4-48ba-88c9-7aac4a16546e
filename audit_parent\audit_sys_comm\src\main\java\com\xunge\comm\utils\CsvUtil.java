package com.xunge.comm.utils;

import cn.afterturn.easypoi.csv.CsvExportUtil;
import cn.afterturn.easypoi.csv.entity.CsvExportParams;
import com.xunge.comm.system.DateDisposeComm;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.util.List;

@Slf4j
public class CsvUtil {

    public static void generateCsvFile(String filePath, Class cla, List<?> list) throws FileNotFoundException {
        File file = new File(filePath);
        File parent = file.getParentFile();
        if (!parent.exists()) {
            parent.mkdirs();
        }
        FileOutputStream outputStream = new FileOutputStream(file);
        CsvExportParams csvExportParams = new CsvExportParams(CsvExportParams.GBK);
        CsvExportUtil.exportCsv(csvExportParams,cla,list,outputStream);
    }

    public static String getCsvFilePath(String basePath,String fileName){
        return basePath+File.separator+ fileName + DateDisposeComm.SUFF_CSV;
    }
}
