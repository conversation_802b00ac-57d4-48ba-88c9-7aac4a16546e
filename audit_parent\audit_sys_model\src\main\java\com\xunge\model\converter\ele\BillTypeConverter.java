package com.xunge.model.converter.ele;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;

//报账类型:"报账_1", "_null", "托收(透支户报账)_4", "包干预付报账_5"
public class BillTypeConverter implements Converter<Integer> {
    @Override
    public Integer convertToJavaData(ReadCellData<?> cellData, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        String value = cellData.getStringValue();
        if (value.equals("报账")){
            return 1;
        }else if (value.equals("托收(透支户报账)")){
            return 4;
        }else if (value.equals("包干预付报账")){
            return 5;
        }
        return null;
    }

    @Override
    public WriteCellData<?> convertToExcelData(Integer value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        if (value == null){
            return new WriteCellData<>("");
        }else if (value == 1){
            return new WriteCellData<>("报账");
        }else if (value == 4){
            return new WriteCellData<>("托收(透支户报账)");
        }else if (value == 5){
            return new WriteCellData<>("包干预付报账");
        }

        return new  WriteCellData<>("");
    }
}
