package com.xunge.dao.selfelec;

import com.xunge.model.selfelec.VEleBaseresourceelectricmeter;
import com.xunge.model.selfelec.VEleBaseresourceelectricmeterExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface VEleBaseresourceelectricmeterMapper {
    
    int countByExample(VEleBaseresourceelectricmeterExample example);

    
    int deleteByExample(VEleBaseresourceelectricmeterExample example);

    
    int insert(VEleBaseresourceelectricmeter record);

    
    int insertSelective(VEleBaseresourceelectricmeter record);

    
    List<VEleBaseresourceelectricmeter> selectByExample(VEleBaseresourceelectricmeterExample example);

    
    int updateByExampleSelective(@Param("record") VEleBaseresourceelectricmeter record,
                                 @Param("example") VEleBaseresourceelectricmeterExample example);

    
    int updateByExample(@Param("record") VEleBaseresourceelectricmeter record, @Param("example") VEleBaseresourceelectricmeterExample example);

    /**
     * 根据条件查询资源电表关系表
     *
     * @param map
     * @return
     */
    List<String> queryBaseresourceMeter(Map<String, Object> map);

    /**
     * 根据条件查询资源电表关系表
     *
     * @param map
     * @return
     */
    List<String> queryBaseresourceMeterByBillaccount(Map<String, Object> map);
}