package com.xunge.model.basedata;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

public class DatBaseresource implements Serializable {

    private String baseresourceId;

    private String billaccountName;

    private String basesiteId;


    private String prvId;


    private String regId;
    private String pregId;
    private String belongDept;

    private Integer baseresourceType;


    private Integer baseresourceCategory;


    private String baseresourceCuid;


    private String baseresourceCode;


    private String baseresourceName;


    private String baseresourceAddress;


    private Long baseresourceArea;


    private Date baseresourceOpendate;


    private Date baseresourceStopdate;


    private String roomOwner;


    private Integer roomProperty;


    private String roomShare;


    private Long baseresourceLongitude;


    private Long baseresourceLatitude;


    private BigDecimal airconditionerPower;
    private BigDecimal equipmentPower;
    private BigDecimal towerEquipmentPower;

    private Integer baseresourceState;

    private String baseresourceNote;
    private String towerSiteCode;

    private Integer dataFrom;
    private Integer isPay;
    private Integer processFlag;
    private Integer loanProcessFlag;
    private Integer rentProcessFlag;
    private Integer serviceSiteType;
    private String nowRelationBillaccountCode;
    private String nowRelationBillaccountName;
    private String nowRelationBillaccountId;
    /**
     * 1：后付费，2：塔维
     */
    private Integer nowRelationBillaccountFrom;

    public String getNowRelationBillaccountId() {
        return nowRelationBillaccountId;
    }

    public void setNowRelationBillaccountId(String nowRelationBillaccountId) {
        this.nowRelationBillaccountId = nowRelationBillaccountId;
    }

    public Integer getNowRelationBillaccountFrom() {
        return nowRelationBillaccountFrom;
    }

    public void setNowRelationBillaccountFrom(Integer nowRelationBillaccountFrom) {
        this.nowRelationBillaccountFrom = nowRelationBillaccountFrom;
    }
    private Integer rentProcessFlagMulti;
    private Integer billaccountType;
    private Integer freeFlag;
    private String cellName;
    private String cellTotalFlow;

    public BigDecimal getEquipmentPower() {
        return equipmentPower;
    }

    public void setEquipmentPower(BigDecimal equipmentPower) {
        this.equipmentPower = equipmentPower;
    }

    public BigDecimal getTowerEquipmentPower() {
        return towerEquipmentPower;
    }

    public void setTowerEquipmentPower(BigDecimal towerEquipmentPower) {
        this.towerEquipmentPower = towerEquipmentPower;
    }

    public String getTowerSiteCode() {
        return towerSiteCode;
    }

    public void setTowerSiteCode(String towerSiteCode) {
        this.towerSiteCode = towerSiteCode;
    }


    public String getBaseresourceId() {
        return baseresourceId;
    }


    public void setBaseresourceId(String baseresourceId) {
        this.baseresourceId = baseresourceId == null ? null : baseresourceId.trim();
    }


    public String getBasesiteId() {
        return basesiteId;
    }


    public void setBasesiteId(String basesiteId) {
        this.basesiteId = basesiteId == null ? null : basesiteId.trim();
    }


    public String getPrvId() {
        return prvId;
    }


    public void setPrvId(String prvId) {
        this.prvId = prvId == null ? null : prvId.trim();
    }


    public String getRegId() {
        return regId;
    }


    public void setRegId(String regId) {
        this.regId = regId == null ? null : regId.trim();
    }


    public Integer getBaseresourceType() {
        return baseresourceType;
    }


    public void setBaseresourceType(Integer baseresourceType) {
        this.baseresourceType = baseresourceType;
    }


    public Integer getBaseresourceCategory() {
        return baseresourceCategory;
    }


    public void setBaseresourceCategory(Integer baseresourceCategory) {
        this.baseresourceCategory = baseresourceCategory;
    }


    public String getBaseresourceCuid() {
        return baseresourceCuid;
    }


    public void setBaseresourceCuid(String baseresourceCuid) {
        this.baseresourceCuid = baseresourceCuid == null ? null : baseresourceCuid.trim();
    }


    public String getBaseresourceCode() {
        return baseresourceCode;
    }


    public void setBaseresourceCode(String baseresourceCode) {
        this.baseresourceCode = baseresourceCode == null ? null : baseresourceCode.trim();
    }


    public String getBaseresourceName() {
        return baseresourceName;
    }


    public void setBaseresourceName(String baseresourceName) {
        this.baseresourceName = baseresourceName == null ? null : baseresourceName.trim();
    }


    public String getBaseresourceAddress() {
        return baseresourceAddress;
    }


    public void setBaseresourceAddress(String baseresourceAddress) {
        this.baseresourceAddress = baseresourceAddress == null ? null : baseresourceAddress.trim();
    }


    public Long getBaseresourceArea() {
        return baseresourceArea;
    }


    public void setBaseresourceArea(Long baseresourceArea) {
        this.baseresourceArea = baseresourceArea;
    }


    public Date getBaseresourceOpendate() {
        return baseresourceOpendate;
    }


    public void setBaseresourceOpendate(Date baseresourceOpendate) {
        this.baseresourceOpendate = baseresourceOpendate;
    }


    public Date getBaseresourceStopdate() {
        return baseresourceStopdate;
    }


    public void setBaseresourceStopdate(Date baseresourceStopdate) {
        this.baseresourceStopdate = baseresourceStopdate;
    }


    public String getRoomOwner() {
        return roomOwner;
    }


    public void setRoomOwner(String roomOwner) {
        this.roomOwner = roomOwner;
    }


    public Integer getRoomProperty() {
        return roomProperty;
    }


    public void setRoomProperty(Integer roomProperty) {
        this.roomProperty = roomProperty;
    }


    public String getRoomShare() {
        return roomShare;
    }


    public void setRoomShare(String roomShare) {
        this.roomShare = roomShare;
    }


    public Long getBaseresourceLongitude() {
        return baseresourceLongitude;
    }


    public void setBaseresourceLongitude(Long baseresourceLongitude) {
        this.baseresourceLongitude = baseresourceLongitude;
    }


    public Long getBaseresourceLatitude() {
        return baseresourceLatitude;
    }


    public void setBaseresourceLatitude(Long baseresourceLatitude) {
        this.baseresourceLatitude = baseresourceLatitude;
    }


    public BigDecimal getAirconditionerPower() {
        return airconditionerPower;
    }


    public void setAirconditionerPower(BigDecimal airconditionerPower) {
        this.airconditionerPower = airconditionerPower;
    }


    public Integer getBaseresourceState() {
        return baseresourceState;
    }


    public void setBaseresourceState(Integer baseresourceState) {
        this.baseresourceState = baseresourceState;
    }


    public String getBaseresourceNote() {
        return baseresourceNote;
    }


    public void setBaseresourceNote(String baseresourceNote) {
        this.baseresourceNote = baseresourceNote == null ? null : baseresourceNote.trim();
    }


    public Integer getDataFrom() {
        return dataFrom;
    }


    public void setDataFrom(Integer dataFrom) {
        this.dataFrom = dataFrom;
    }

    public String getBelongDept() {
        return belongDept;
    }

    public void setBelongDept(String belongDept) {
        this.belongDept = belongDept;
    }

    public String getBillaccountName() {
        return billaccountName;
    }

    public void setBillaccountName(String billaccountName) {
        this.billaccountName = billaccountName;
    }

    public Integer getFreeFlag() {
        return freeFlag;
    }

    public void setFreeFlag(Integer freeFlag) {
        this.freeFlag = freeFlag;
    }

    public Integer getIsPay() {
        return isPay;
    }

    public void setIsPay(Integer isPay) {
        this.isPay = isPay;
    }

    public Integer getProcessFlag() {
        return processFlag;
    }

    public void setProcessFlag(Integer processFlag) {
        this.processFlag = processFlag;
    }

    public Integer getLoanProcessFlag() {
        return loanProcessFlag;
    }

    public void setLoanProcessFlag(Integer loanProcessFlag) {
        this.loanProcessFlag = loanProcessFlag;
    }

    public Integer getRentProcessFlag() {
        return rentProcessFlag;
    }

    public void setRentProcessFlag(Integer rentProcessFlag) {
        this.rentProcessFlag = rentProcessFlag;
    }

    public Integer getServiceSiteType() {
        return serviceSiteType;
    }

    public void setServiceSiteType(Integer serviceSiteType) {
        this.serviceSiteType = serviceSiteType;
    }

    public String getNowRelationBillaccountCode() {
        return nowRelationBillaccountCode;
    }

    public void setNowRelationBillaccountCode(String nowRelationBillaccountCode) {
        this.nowRelationBillaccountCode = nowRelationBillaccountCode;
    }

    public String getNowRelationBillaccountName() {
        return nowRelationBillaccountName;
    }

    public void setNowRelationBillaccountName(String nowRelationBillaccountName) {
        this.nowRelationBillaccountName = nowRelationBillaccountName;
    }

    public Integer getRentProcessFlagMulti() {
        return rentProcessFlagMulti;
    }

    public void setRentProcessFlagMulti(Integer rentProcessFlagMulti) {
        this.rentProcessFlagMulti = rentProcessFlagMulti;
    }

    public Integer getBillaccountType() {
        return billaccountType;
    }

    public void setBillaccountType(Integer billaccountType) {
        this.billaccountType = billaccountType;
    }

    public String getCellName() {
        return cellName;
    }

    public void setCellName(String cellName) {
        this.cellName = cellName;
    }

    public String getCellTotalFlow() {
        return cellTotalFlow;
    }

    public void setCellTotalFlow(String cellTotalFlow) {
        this.cellTotalFlow = cellTotalFlow;
    }

    public String getPregId() {
        return pregId;
    }

    public void setPregId(String pregId) {
        this.pregId = pregId;
    }
}
