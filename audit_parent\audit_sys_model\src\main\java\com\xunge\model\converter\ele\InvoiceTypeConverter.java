package com.xunge.model.converter.ele;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;

//票据类型 "增值税专票_0", "收据_1", "增值税普票_2", "收据+发票复印件+分割单_3", "_null"
public class InvoiceTypeConverter implements Converter<Integer> {
    @Override
    public Integer convertToJavaData(ReadCellData<?> cellData, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        String value = cellData.getStringValue();
        if (value.equals("增值税专票")){
            return 0;
        }else if (value.equals("收据")){
            return 1;
        }else if (value.equals("增值税普票")){
            return 2;
        }else if (value.equals("收据+发票复印件+分割单")){
            return 3;
        } else if (value.equals("政府非税收入票据")) {
            return 5;
        }
        return null;
    }

    @Override
    public WriteCellData<?> convertToExcelData(Integer value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        if (value == null){
            return new WriteCellData<>("");
        }else if (value == 0){
            return new WriteCellData<>("增值税专票");
        }else if (value == 1){
            return new WriteCellData<>("收据");
        }else if (value == 2){
            return new WriteCellData<>("增值税普票");
        }else if (value == 3){
            return new WriteCellData<>("收据+发票复印件+分割单");
        }else if (value == 5) {
            return new WriteCellData<>("政府非税收入票据");
        }

        return new  WriteCellData<>("");
    }
}
