package com.xunge.model.activity;

import java.util.List;

/**
 * 描述：
 * Created on 2020/3/18.
 * <p>Title:</p>
 * <p>Copyright:Copyright (c) 2017</p>
 * <p>Company:安徽科大国创</p>
 * <p>Department:西南二区</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @update
 */
public class SysActUserCheck {
    private String checkId;

    private String processName;

    private String procDefKey;

    private String tableName;

    private String trueTableName;

    private String tablePriKey;

    private String createUserField;
    //1-记录的ID   2记录的是用户名
    private String userType;

    private Integer isValid;
    //priKeyId
    private List<String> checkDataIds;

    private String nextUserId;

    private String nextUserName;

    private SysActUserCheck checkModel;

    public String getCheckId() {
        return checkId;
    }

    public void setCheckId(String checkId) {
        this.checkId = checkId;
    }

    public String getProcessName() {
        return processName;
    }

    public void setProcessName(String processName) {
        this.processName = processName;
    }

    public String getProcDefKey() {
        return procDefKey;
    }

    public void setProcDefKey(String procDefKey) {
        this.procDefKey = procDefKey;
    }

    public String getTableName() {
        return tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }

    public String getTablePriKey() {
        return tablePriKey;
    }

    public void setTablePriKey(String tablePriKey) {
        this.tablePriKey = tablePriKey;
    }

    public String getCreateUserField() {
        return createUserField;
    }

    public void setCreateUserField(String createUserField) {
        this.createUserField = createUserField;
    }

    public String getUserType() {
        return userType;
    }

    public void setUserType(String userType) {
        this.userType = userType;
    }

    public Integer getIsValid() {
        return isValid;
    }

    public void setIsValid(Integer isValid) {
        this.isValid = isValid;
    }

    public List<String> getCheckDataIds() {
        return checkDataIds;
    }

    public void setCheckDataIds(List<String> checkDataIds) {
        this.checkDataIds = checkDataIds;
    }

    public String getNextUserId() {
        return nextUserId;
    }

    public void setNextUserId(String nextUserId) {
        this.nextUserId = nextUserId;
    }

    public String getNextUserName() {
        return nextUserName;
    }

    public void setNextUserName(String nextUserName) {
        this.nextUserName = nextUserName;
    }

    public SysActUserCheck getCheckModel() {
        return checkModel;
    }

    public void setCheckModel(SysActUserCheck checkModel) {
        this.checkModel = checkModel;
    }

    public String getTrueTableName() {
        return trueTableName;
    }

    public void setTrueTableName(String trueTableName) {
        this.trueTableName = trueTableName;
    }
}
