package com.xunge.dao.report.Impl;

import com.xunge.dao.AbstractBaseDao;
import com.xunge.dao.report.IrptHotSpotDao;

import java.util.List;
import java.util.Map;

public class RptHotSpotDaoImpl extends AbstractBaseDao implements IrptHotSpotDao {

    final String Namespace = "com.xunge.mapping.RptHotSpotVOMapper.";

    @Override
    public List<Map<String, Object>> queryHotSpotByPrv(Map<String, Object> map) {
        return this.getSqlSession().selectList(Namespace + "queryHotSpotByPrv", map);
    }

    @Override
    public List<Map<String, Object>> queryHotSpotByPreg(Map<String, Object> map) {
        return this.getSqlSession().selectList(Namespace + "queryHotSpotByPreg", map);
    }

    @Override
    public List<Map<String, Object>> queryHotSpot(Map<String, Object> map) {
        return this.getSqlSession().selectList(Namespace + "queryHotSpot", map);
    }

}