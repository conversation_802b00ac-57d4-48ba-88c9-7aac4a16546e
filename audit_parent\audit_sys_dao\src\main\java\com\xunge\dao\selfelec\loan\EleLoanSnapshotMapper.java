package com.xunge.dao.selfelec.loan;

import com.xunge.model.selfelec.loan.EleLoanSnapshot;

/**
* <AUTHOR>
* @description 针对表【ele_loan_snapshot(预付费快照表)】的数据库操作Mapper
* @createDate 2024-10-09 11:00:52
* @Entity generator.domain.EleLoanSnapshot
*/
public interface EleLoanSnapshotMapper {

    int deleteByPrimaryKey(Long id);

    int deleteByLoanId(String loanId);

    int insert(EleLoanSnapshot record);

    int insertSelective(EleLoanSnapshot record);

    EleLoanSnapshot selectByPrimaryKey(Long id);

    EleLoanSnapshot selectByLoanId(String loanId);

    int updateByPrimaryKeySelective(EleLoanSnapshot record);

    int updateByPrimaryKey(EleLoanSnapshot record);

}
