package com.xunge.dao.twrrent.bizbasedata.impl;

import com.xunge.comm.system.RESULT;
import com.xunge.core.page.Page;
import com.xunge.core.util.SysUUID;
import com.xunge.dao.AbstractBaseDao;
import com.xunge.dao.twrrent.bizbasedata.IRoomDepreciationTimeDao;
import com.xunge.filter.PageInterceptor;
import com.xunge.model.towerrent.bizbasedata.RoomDepreciationTimeVO;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2018年06月06日
 * @description 机房折旧年限
 */
public class RoomDepreciationTimeDaoImpl extends AbstractBaseDao implements IRoomDepreciationTimeDao {

    final String RoomDepreciationTimeNamespace = "com.xunge.mapping.RoomDepreciationTimeVOMapper.";

    /**
     * 分页查询所有机房折旧年限集合
     *
     * @param pageSize
     * @param pageNumber
     * @return
     */
    @Override
    public Page<List<RoomDepreciationTimeVO>> queryRoomDepreciationTimeVO(int pageSize, int pageNumber, Map<String, Object> paramMap) {
        PageInterceptor.startPage(pageNumber, pageSize);
        this.getSqlSession().selectList(RoomDepreciationTimeNamespace + "queryRoomDepreciationTimeVO", paramMap);
        return PageInterceptor.endPage();
    }

    public List<RoomDepreciationTimeVO> queryRoomDepreciationTime(Map<String, Object> paramMap) {
        return this.getSqlSession().selectList(RoomDepreciationTimeNamespace + "queryRoomDepreciationTimeVO", paramMap);
    }

    /**
     * 根据Id删除机房折旧年限对象
     *
     * @param ids
     * @return
     */
    @Override
    public String deleteRoomDepreciationTimeById(List<String> ids) {
        Map<String, List<String>> idMap = new HashMap<String, List<String>>();
        idMap.put("idList", ids);
        int result = this.getSqlSession().delete(RoomDepreciationTimeNamespace + "deleteRoomDepreciationTimeById", idMap);
        return (result == 0) ? RESULT.FAIL_0 : RESULT.SUCCESS_1;
    }

    /**
     * 修改所选机房折旧年限对象
     *
     * @param roomDepreciationTimeVO
     * @return
     */
    @Override
    public String updateRoomDepreciationTimeById(RoomDepreciationTimeVO roomDepreciationTimeVO) {
        int result = this.getSqlSession().update(RoomDepreciationTimeNamespace + "updateRoomDepreciationTimeById", roomDepreciationTimeVO);
        return (result == 0) ? RESULT.FAIL_0 : RESULT.SUCCESS_1;
    }

    /**
     * 新增机房折旧年限对象
     *
     * @param roomDepreciationTimeVO
     * @return
     */
    @Override
    public String insertRoomDepreciationTimeById(RoomDepreciationTimeVO roomDepreciationTimeVO) {
        roomDepreciationTimeVO.setRoomdepreciationtimeId(SysUUID.generator());
        int result = this.getSqlSession().insert(RoomDepreciationTimeNamespace + "insertRoomDepreciationTimeById", roomDepreciationTimeVO);
        return (result == 0) ? RESULT.FAIL_0 : RESULT.SUCCESS_1;
    }

    /**
     * 启用或停用机房折旧年限状态
     *
     * @param ids
     * @param state
     * @return
     */
    @Override
    public String startOrStopRoomDepreciationTimeById(List<String> ids, String state) {
        Map<String, Object> idMap = new HashMap<String, Object>();
        idMap.put("idList", ids);
        idMap.put("state", state);
        int result = this.getSqlSession().update(RoomDepreciationTimeNamespace + "startOrStopRoomDepreciationTimeById", idMap);
        return (result == 0) ? RESULT.FAIL_0 : RESULT.SUCCESS_1;
    }
}
