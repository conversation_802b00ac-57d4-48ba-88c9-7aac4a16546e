package com.xunge.model.basedata;

import java.math.BigDecimal;
import java.util.Date;

public class DatContractVO {
    private String contractId;

    private String contractsysId;

    private String prvId;

    private String prvSname;

    private String pregId;

    private String pregName;

    private String regId;

    private String regName;

    private Integer isDownshare;

    private String sysDepId;

    private String userId;

    private String managerDepartment;

    private String managerUser;

    private String contractCode;
    //全网统一编码
    private String sysContractCode;

    private String contractName;

    private Integer contractType;

    private Date contractStartdate;

    private Date contractEnddate;

    private Date contractSigndate;

    private Date contractChangeenddate;

    private Date contractChangedate;

    private BigDecimal contractYearquantity;

    private String contractCheckname1;

    private String contractCheckname2;

    private String oldContractId;

    private String oldContractCode;

    private String contractFlow;

    private String contractIntroduction;

    private String contractSpaceresource;

    private Integer contractState;

    private String contractNote;

    private Integer auditingState;

    private String auditingUserId;
    private String auditingRoleCode;

    private Date auditingDate;

    private Integer dataFrom;
    private Integer pushState;

    private String create_user;
    private String create_ip;
    private Date create_time;
    private String update_user;
    private String update_ip;
    private Date update_time;

    //原合同信息  用于综合合同修改
    private String contractNameOld;
    private Date contractStartdateOld;
    private Date contractEnddateOld;
    private Date contractSigndateOld;
    private String pregIdOld;
    private String regIdOld;
    private String isDownshareOld;
    private String contractCheckname1Old;
    private String contractCheckname2Old;
    private String managerUserOld;
    private String managerDepartmentOld;
    private String contractNoteOld;

    private Integer contractCuringType;

    private Integer curingInfo;

    private Integer dataType;

    private Integer draftType;

    private String contractIdRel;

    private Integer supplyMethod;

    private String isNewData;
    private String contractStatus;

    /**
     * 审批类型
     */
    private Integer auditType;
    /**
     * 最高审批人级别
     */
    private Integer auditLevel;

    /**
     * 合同来源 接口采集2，合同批量导入省侧接口3，批量导入修改后4
     */
    private Integer contractFrom;

    private Long updateVersion;

    public Integer getAuditType() {
        return auditType;
    }

    public void setAuditType(Integer auditType) {
        this.auditType = auditType;
    }

    public Integer getAuditLevel() {
        return auditLevel;
    }

    public void setAuditLevel(Integer auditLevel) {
        this.auditLevel = auditLevel;
    }

    public Integer getPushState() {
        return pushState;
    }

    public void setPushState(Integer pushState) {
        this.pushState = pushState;
    }

    public String getContractStatus() {
        return contractStatus;
    }

    public void setContractStatus(String contractStatus) {
        this.contractStatus = contractStatus;
    }

    public Integer getCuringInfo() {
        return curingInfo;
    }

    public void setCuringInfo(Integer curingInfo) {
        this.curingInfo = curingInfo;
    }

    public Integer getDraftType() {
        return draftType;
    }

    public void setDraftType(Integer draftType) {
        this.draftType = draftType;
    }

    public Integer getDataType() {
        return dataType;
    }

    public void setDataType(Integer dataType) {
        this.dataType = dataType;
    }

    public Integer getContractCuringType() {
        return contractCuringType;
    }

    public void setContractCuringType(Integer contractCuringType) {
        this.contractCuringType = contractCuringType;
    }

    public String getSysContractCode() {
        return sysContractCode;
    }

    public void setSysContractCode(String sysContractCode) {
        this.sysContractCode = sysContractCode;
    }

    public String getContractNameOld() {
        return contractNameOld;
    }

    public void setContractNameOld(String contractNameOld) {
        this.contractNameOld = contractNameOld;
    }

    public Date getContractStartdateOld() {
        return contractStartdateOld;
    }

    public void setContractStartdateOld(Date contractStartdateOld) {
        this.contractStartdateOld = contractStartdateOld;
    }

    public Date getContractEnddateOld() {
        return contractEnddateOld;
    }

    public void setContractEnddateOld(Date contractEnddateOld) {
        this.contractEnddateOld = contractEnddateOld;
    }

    public Date getContractSigndateOld() {
        return contractSigndateOld;
    }

    public void setContractSigndateOld(Date contractSigndateOld) {
        this.contractSigndateOld = contractSigndateOld;
    }

    public String getPregIdOld() {
        return pregIdOld;
    }

    public void setPregIdOld(String pregIdOld) {
        this.pregIdOld = pregIdOld;
    }

    public String getRegIdOld() {
        return regIdOld;
    }

    public void setRegIdOld(String regIdOld) {
        this.regIdOld = regIdOld;
    }

    public String getIsDownshareOld() {
        return isDownshareOld;
    }

    public void setIsDownshareOld(String isDownshareOld) {
        this.isDownshareOld = isDownshareOld;
    }

    public String getContractCheckname1Old() {
        return contractCheckname1Old;
    }

    public void setContractCheckname1Old(String contractCheckname1Old) {
        this.contractCheckname1Old = contractCheckname1Old;
    }

    public String getContractCheckname2Old() {
        return contractCheckname2Old;
    }

    public void setContractCheckname2Old(String contractCheckname2Old) {
        this.contractCheckname2Old = contractCheckname2Old;
    }

    public String getManagerUserOld() {
        return managerUserOld;
    }

    public void setManagerUserOld(String managerUserOld) {
        this.managerUserOld = managerUserOld;
    }

    public String getManagerDepartmentOld() {
        return managerDepartmentOld;
    }

    public void setManagerDepartmentOld(String managerDepartmentOld) {
        this.managerDepartmentOld = managerDepartmentOld;
    }

    public String getContractNoteOld() {
        return contractNoteOld;
    }

    public void setContractNoteOld(String contractNoteOld) {
        this.contractNoteOld = contractNoteOld;
    }

    public String getContractId() {
        return contractId;
    }

    public void setContractId(String contractId) {
        this.contractId = contractId == null ? null : contractId.trim();
    }

    public String getContractsysId() {
        return contractsysId;
    }

    public void setContractsysId(String contractsysId) {
        this.contractsysId = contractsysId == null ? null : contractsysId.trim();
    }

    public String getPrvId() {
        return prvId;
    }

    public void setPrvId(String prvId) {
        this.prvId = prvId == null ? null : prvId.trim();
    }

    public String getPrvSname() {
        return prvSname;
    }

    public void setPrvSname(String prvSname) {
        this.prvSname = prvSname == null ? null : prvSname.trim();
    }

    public String getPregId() {
        return pregId;
    }

    public void setPregId(String pregId) {
        this.pregId = pregId == null ? null : pregId.trim();
    }

    public String getPregName() {
        return pregName;
    }

    public void setPregName(String pregName) {
        this.pregName = pregName == null ? null : pregName.trim();
    }

    public String getRegId() {
        return regId;
    }

    public void setRegId(String regId) {
        this.regId = regId == null ? null : regId.trim();
    }

    public String getRegName() {
        return regName;
    }

    public void setRegName(String regName) {
        this.regName = regName == null ? null : regName.trim();
    }

    public Integer getIsDownshare() {
        return isDownshare;
    }

    public void setIsDownshare(Integer isDownshare) {
        this.isDownshare = isDownshare;
    }

    public String getSysDepId() {
        return sysDepId;
    }

    public void setSysDepId(String sysDepId) {
        this.sysDepId = sysDepId == null ? null : sysDepId.trim();
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId == null ? null : userId.trim();
    }

    public String getManagerDepartment() {
        return managerDepartment;
    }

    public void setManagerDepartment(String managerDepartment) {
        this.managerDepartment = managerDepartment == null ? null : managerDepartment.trim();
    }

    public String getManagerUser() {
        return managerUser;
    }

    public void setManagerUser(String managerUser) {
        this.managerUser = managerUser == null ? null : managerUser.trim();
    }

    public String getContractCode() {
        return contractCode;
    }

    public void setContractCode(String contractCode) {
        this.contractCode = contractCode == null ? null : contractCode.trim();
    }

    public String getContractName() {
        return contractName;
    }

    public void setContractName(String contractName) {
        this.contractName = contractName == null ? null : contractName.trim();
    }

    public Integer getContractType() {
        return contractType;
    }

    public void setContractType(Integer contractType) {
        this.contractType = contractType;
    }

    public Date getContractStartdate() {
        return contractStartdate;
    }

    public void setContractStartdate(Date contractStartdate) {
        this.contractStartdate = contractStartdate;
    }

    public Date getContractEnddate() {
        return contractEnddate;
    }

    public void setContractEnddate(Date contractEnddate) {
        this.contractEnddate = contractEnddate;
    }

    public Date getContractSigndate() {
        return contractSigndate;
    }

    public void setContractSigndate(Date contractSigndate) {
        this.contractSigndate = contractSigndate;
    }

    public Date getContractChangeenddate() {
        return contractChangeenddate;
    }

    public void setContractChangeenddate(Date contractChangeenddate) {
        this.contractChangeenddate = contractChangeenddate;
    }

    public Date getContractChangedate() {
        return contractChangedate;
    }

    public void setContractChangedate(Date contractChangedate) {
        this.contractChangedate = contractChangedate;
    }

    public BigDecimal getContractYearquantity() {
        return contractYearquantity;
    }

    public void setContractYearquantity(BigDecimal contractYearquantity) {
        this.contractYearquantity = contractYearquantity;
    }

    public String getContractCheckname1() {
        return contractCheckname1;
    }

    public void setContractCheckname1(String contractCheckname1) {
        this.contractCheckname1 = contractCheckname1 == null ? null : contractCheckname1.trim();
    }

    public String getContractCheckname2() {
        return contractCheckname2;
    }

    public void setContractCheckname2(String contractCheckname2) {
        this.contractCheckname2 = contractCheckname2 == null ? null : contractCheckname2.trim();
    }

    public String getOldContractId() {
        return oldContractId;
    }

    public void setOldContractId(String oldContractId) {
        this.oldContractId = oldContractId == null ? null : oldContractId.trim();
    }

    public String getOldContractCode() {
        return oldContractCode;
    }

    public void setOldContractCode(String oldContractCode) {
        this.oldContractCode = oldContractCode == null ? null : oldContractCode.trim();
    }

    public String getContractFlow() {
        return contractFlow;
    }

    public void setContractFlow(String contractFlow) {
        this.contractFlow = contractFlow == null ? null : contractFlow.trim();
    }

    public String getContractIntroduction() {
        return contractIntroduction;
    }

    public void setContractIntroduction(String contractIntroduction) {
        this.contractIntroduction = contractIntroduction == null ? null : contractIntroduction.trim();
    }

    public String getContractSpaceresource() {
        return contractSpaceresource;
    }

    public void setContractSpaceresource(String contractSpaceresource) {
        this.contractSpaceresource = contractSpaceresource == null ? null : contractSpaceresource.trim();
    }

    public Integer getContractState() {
        return contractState;
    }

    public void setContractState(Integer contractState) {
        this.contractState = contractState;
    }

    public String getContractNote() {
        return contractNote;
    }

    public void setContractNote(String contractNote) {
        this.contractNote = contractNote == null ? null : contractNote.trim();
    }

    public Integer getAuditingState() {
        return auditingState;
    }

    public void setAuditingState(Integer auditingState) {
        this.auditingState = auditingState;
    }

    public String getAuditingUserId() {
        return auditingUserId;
    }

    public void setAuditingUserId(String auditingUserId) {
        this.auditingUserId = auditingUserId == null ? null : auditingUserId.trim();
    }

    public String getAuditingRoleCode() {
        return auditingRoleCode;
    }

    public void setAuditingRoleCode(String auditingRoleCode) {
        this.auditingRoleCode = auditingRoleCode;
    }

    public Date getAuditingDate() {
        return auditingDate;
    }

    public void setAuditingDate(Date auditingDate) {
        this.auditingDate = auditingDate;
    }

    public Integer getDataFrom() {
        return dataFrom;
    }

    public void setDataFrom(Integer dataFrom) {
        this.dataFrom = dataFrom;
    }

    public String getCreate_user() {
        return create_user;
    }

    public void setCreate_user(String create_user) {
        this.create_user = create_user;
    }

    public String getCreate_ip() {
        return create_ip;
    }

    public void setCreate_ip(String create_ip) {
        this.create_ip = create_ip;
    }

    public Date getCreate_time() {
        return create_time;
    }

    public void setCreate_time(Date create_time) {
        this.create_time = create_time;
    }

    public String getUpdate_user() {
        return update_user;
    }

    public void setUpdate_user(String update_user) {
        this.update_user = update_user;
    }

    public String getUpdate_ip() {
        return update_ip;
    }

    public void setUpdate_ip(String update_ip) {
        this.update_ip = update_ip;
    }

    public Date getUpdate_time() {
        return update_time;
    }

    public void setUpdate_time(Date update_time) {
        this.update_time = update_time;
    }

    public String getContractIdRel() {
        return contractIdRel;
    }

    public void setContractIdRel(String contractIdRel) {
        this.contractIdRel = contractIdRel;
    }

    public Integer getSupplyMethod() {
        return supplyMethod;
    }

    public void setSupplyMethod(Integer supplyMethod) {
        this.supplyMethod = supplyMethod;
    }

    public String getIsNewData() {
        return isNewData;
    }

    public void setIsNewData(String isNewData) {
        this.isNewData = isNewData;
    }

    public Long getUpdateVersion() {
        return updateVersion;
    }

    public void setUpdateVersion(Long updateVersion) {
        this.updateVersion = updateVersion;
    }

    public Integer getContractFrom() {
        return contractFrom;
    }

    public void setContractFrom(Integer contractFrom) {
        this.contractFrom = contractFrom;
    }
}