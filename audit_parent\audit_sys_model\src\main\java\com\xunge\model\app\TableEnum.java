package com.xunge.model.app;


/**
 * 添加新流程步骤：
 * 1.添加一项枚举值：如 ele_billaccount("4","报账点","ReimbursementPointAudit","ele_billaccount","电费报账点审核","reimbursementPointAppService",null);
 * 2.在 all("0","所有流程",null,null,null,null,"1,2,3,4")的最后一个ext参数中将上面的4添加进去，用逗号隔开。
 * 3.重写valueOff方法。
 */
public enum TableEnum {

    all("0", "所有流程", null, null, null, null, "1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36"),
    ele("-1", "电费所有流程", null, null, null, null, "1,2,3,4,5,6,7,8,32,33,34,35,36,37,38,39"),
    rent("-2", "租费所有流程", null, null, null, null, "10,11,12,13,14,15"),
    twr("-3", "铁塔所有流程", null, null, null, null, "16,17,18,19,21,22,23,24,25,26"),
    dat("-4", "公共接口所有流程", null, null, null, null, "27,28,29,30,31,40"),

    /**
     * 电费流程开始
     */
    ele_payment("1", "电费报账点缴费信息审核", "ElectricityAudit", "ele_payment", "电费报账点缴费信息审核", "elecPaymentAppService", null),
    ele_loan("2", "报账点预付费", "EleLoanAudit", "ele_loan", "电费报账点预付费审核", "eleLoanAppService", null),
    ele_verification("3", "报账点核销", "EleVerificationAudit", "ele_verification", "电费报账点核销审核", "eleVerificationAppService", null),
    ele_billaccount("4", "报账点", "ReimbursementPointAudit", "ele_billaccount", "电费报账点审核", "reimbursementPointAppService", "特殊报账点审核"),
    ele_payment_special("5", "特殊缴费流程电费", "ElectricitySpecialAudit", "ele_payment", "特殊电费报账点缴费信息审核", "electricitySpecialAppService", null),
    dat_contract("6", "电费合同信息", "contractRenewalAudit", "dat_contract", "电费合同审核", "contractRenewalAuditAppService", null),
    dat_contract_finance("7", "大集中电费合同信息", "contractRenewalFSAudit", "dat_contract", "大集中电费合同信息审核", "contractRenewalAuditAppService", null),
    dat_contract_gh("8", "电费固话信息", "contractRenewalGHAudit", "dat_contract", "电费固化信息审核", "contractRenewalAuditAppService", null),
    ele_payment_ds("32", "电费报账点直供电缴费信息审核", "ElectricityDsAudit", "ele_payment", "电费报账点直供电缴费信息审核", "elecPaymentAppService", null),
    ele_verification_ds("33", "直供电报账点核销", "EleVerificationDsAudit", "ele_verification", "电费报账点直供点核销审核", "eleVerificationAppService", null),
    ele_payment_special_ds("34", "直供电特殊缴费流程电费", "ElectricitySpecialDsAudit", "ele_payment", "特殊电费报账点直供电缴费信息审核", "electricitySpecialAppService", null),
    ele_loan_ds("35", "直供电报账点预付费", "EleLoanDsAudit", "ele_loan", "电费报账点直供电预付费审核", "eleLoanAppService", null),
    dat_contract_gh_ds("36", "直供电固话信息", "DSContractRenewalGHAudit", "dat_contract", "直供电固化信息审核", "contractRenewalAuditAppService", null),
    ele_loan_three_audit("37", "报账点预付费先款后票", "EleLoanThreeAudit", "ele_loan", "电费报账点预付费先款后票审核", "eleLoanAppService", null),
    ele_verification_three_audit("38", "电费报账点核销先款后票", "EleVerificationThreeAudit", "ele_verification", "电费报账点核销先款后票审核", "eleVerificationAppService", null),
    ele_loan_verif_three_audit("39", "电费报账点先款后票预付费核销", "EleLoanVerifThreeAudit", "ele_loan", "电费报账点先款后票预付费核销审核", "eleLoanAppService", null),

    /**
     * 电费流程结束
     */

    app_inspection("9", "巡检", "InspectionInfoAudit", "inspection_info", "巡检信息审核", "inspectionInfoAuditAppService", null),

    /**
     * 租费流程开始
     */
    rent_contract_finance("10", "大集中租费合同", "SelfRentContractFSAudit", "rent_contract", "大集中租费合同信息审核", "rentContractAuditAppService", null),
    rent_contract("11", "自维租费合同", "SelfRentContract", "rent_contract", "自维租费合同审核", "selfRentContractAppService", null),
    rent_contractGH("12", "自维租费固化", "SelfRentContractGH", "rent_contract", "自维租费固化审核", "selfRentContractAppService", null),
    rent_billaccount("13", "自维租费报账点", "SelfRentBillaccount", "rent_billaccount", "自维租费报账点审核", "rentBillaccountAppService", null),
    rent_payment("14", "自维租费报账点缴费", "SelfRentPayment", "rent_payment", "自维租费报账点缴费审核", "selfRentPaymentAppService", null),
    rent_payment_special("15", "自维租费特殊报账点缴费", "SelfRentSpecialPayment", "rent_payment", "自维租费特殊报账点缴费审核", "selfRentSpePaymentAppService", null),
    /**租费流程开始*/

    /**
     * 铁塔流程开始
     */
    twr_resource("16", "资源信息对比审核", "TwrRentbizChangeMobile", "twr_rentinformationtower", "铁塔环比移动侧变动审核流程", "towerResourceInfoAppService", null),
    twr_accountsummary("17", "报账费用汇总审核", "TwrAccountsummary", "twr_accountsummary", "报账费用汇总审核", "twrAccountsummaryAppService", null),
    twr_province_punish("18", "省内自设考核指标扣罚审核", "TwrProvincePunish", "twr_province_punish", "省扣罚审核", "twrProvincePunishAppService", null),
    twr_reg_punish("19", "地市自设考核指标扣罚审核", "TwrRegPunish", "twr_reg_punish", "地市扣审核", "twrRegPunishAppService", null),
    /*
        twr_rentinformationtower("20","铁塔资源环比变动审核","TwrRentbizChange","twr_rentinformationtower","铁塔资源环比变动审核","twrRentinformationtowerAppService",null),
    */
    twr_rentinformation("21", "移动资源信息审核", "TwrRentInformation", "twr_rentinformation", "移动资源信息审核", "twrRentInformationAppService", null),
    twr_rentinformation_backup("22", "移动资源信息审核", "TwrHisRentInformation", "twr_rentinformation_backup", "移动历史资源信息审核", "twrRentInformationHisAppService", null),
    twr_rentinformationtower_room("23", "室分资源对比审核", "TwrRentRoomAudit", "twr_rentinformationtower_room", "室分资源信息审核", "twrRentRoomAuditAppService", null),
    twr_rentinformation_room("24", "室分资源维护审核", "MobileRentRoomAudit", "twr_rentinformation_room", "室分资源维护审核", "mobileRentRoomAuditAppService", null),
    twr_rentinformationtower_trans("25", "传输资源对比审核", "TwrRentTransAudit", "twr_rentinformationtower_trans", "传输资源信息审核", "twrRentTransAuditAppService", null),
    twr_rentinformation_trans("26", "传输资源维护审核", "MobileRentTransAudit", "twr_rentinformation_trans", "传输资源维护审核", "mobileRentTransAuditAppService", null),
    /**铁塔流程结束*/

    /**
     * 公共接口流程开始
     */
    dat_baseresource("27", "位置点审核", "SelfRentAudit", "dat_baseresource", "基础资源审核", "datResourceAppService", null),
    dat_basesite("28", "站点审核", "SelfRentAudit", "dat_basesite", "站点审核", "datResourceAppService", null),
    dat_baseresource_jf("29", "机房信息审核", "SelfRentAudit", "dat_baseresource", "基础资源审核", "datResourceAppService", null),
    dat_baseantenna("30", "天线信息审核", "SelfRentAudit", "dat_baseantenna", "天线审核", "datResourceAppService", null),
    dat_basetower("31", "铁塔信息审核", "SelfRentAudit", "dat_basetower", "铁塔审核", "datResourceAppService", null),
    sys_user("40", "用户审核", "SysUserAudit", "sys_user", "用户管理审核", "sysUserAppService", null);
    /**公共接口流程结束*/

    /**
     * 流程类型枚举值
     */
    private String tableEnum;

    /**
     * 返回给APP中显示的流程名称
     */
    private String appTitle;

    /**
     * 流程定义的key，必须在act-common.js或者act-common_kd.js定义并在系统中配置
     */
    private String procDefKey;

    /**
     * 流程关联的业务表名称
     */
    private String businessTable;

    /**
     * 流程定义的中文名称，参加act-common.js或者act-common_kd.js
     */
    private String title;

    /**
     * 流程对应业务处理类的名称，在spring容器中注册的名称
     */
    private String beanName;

    /**
     * 扩展字段
     */
    private String ext;


    TableEnum(String tableEnum, String appTitle, String procDefKey, String businessTable, String title, String beanName, String ext) {
        this.tableEnum = tableEnum;
        this.appTitle = appTitle;
        this.procDefKey = procDefKey;
        this.businessTable = businessTable;
        this.title = title;
        this.beanName = beanName;
        this.ext = ext;
    }

    /**
     * 根据tableEnum获取枚举类
     *
     * @param tableEnum
     * @return
     */
    public static TableEnum valueOff(String tableEnum) {
        if (tableEnum == null) {
            return null;
        }
        int value = 0;
        try {
            value = Integer.parseInt(tableEnum.trim());
        } catch (Exception e) {
            return null;
        }
        switch (value) {
            case 1:
                return TableEnum.ele_payment;
            case 2:
                return TableEnum.ele_loan;
            case 3:
                return TableEnum.ele_verification;
            case 4:
                return TableEnum.ele_billaccount;
            case 5:
                return TableEnum.ele_payment_special;
            case 6:
                return TableEnum.dat_contract;
            case 7:
                return TableEnum.dat_contract_finance;
            case 8:
                return TableEnum.dat_contract_gh;
            case 9:
                return TableEnum.app_inspection;
            case 10:
                return TableEnum.rent_contract_finance;
            case 11:
                return TableEnum.rent_contract;
            case 12:
                return TableEnum.rent_contractGH;
            case 13:
                return TableEnum.rent_billaccount;
            case 14:
                return TableEnum.rent_payment;
            case 15:
                return TableEnum.rent_payment_special;
            case 16:
                return TableEnum.twr_resource;
            case 17:
                return TableEnum.twr_accountsummary;
            case 18:
                return TableEnum.twr_province_punish;
            case 19:
                return TableEnum.twr_reg_punish;
            /*case 20:
                return TableEnum.twr_rentinformationtower;*/
            case 21:
                return TableEnum.twr_rentinformation;
            case 22:
                return TableEnum.twr_rentinformation_backup;
            case 23:
                return TableEnum.twr_rentinformationtower_room;
            case 24:
                return TableEnum.twr_rentinformation_room;
            case 25:
                return TableEnum.twr_rentinformationtower_trans;
            case 26:
                return TableEnum.twr_rentinformation_trans;
            case 27:
                return TableEnum.dat_baseresource;
            case 28:
                return TableEnum.dat_basesite;
            case 29:
                return TableEnum.dat_baseresource_jf;
            case 30:
                return TableEnum.dat_baseantenna;
            case 31:
                return TableEnum.dat_basetower;
            case 32:
                return TableEnum.ele_payment_ds;
            case 33:
                return TableEnum.ele_verification_ds;
            case 34:
                return TableEnum.ele_payment_special_ds;
            case 35:
                return TableEnum.ele_loan_ds;
            case 36:
                return TableEnum.dat_contract_gh_ds;
            case 37:
                return TableEnum.ele_loan_three_audit;
            case 38:
                return TableEnum.ele_verification_three_audit;
            case 39:
                return TableEnum.ele_loan_verif_three_audit;
            case 40:
                return TableEnum.sys_user;
            default:
                return null;
        }
    }

    public static String transferAll(String tableEnum) {
        if (tableEnum == null) {
            return null;
        }
        if ("0".equals(tableEnum.trim())) {
            return TableEnum.all.getExt();
        } else if ("-1".equals(tableEnum.trim())) {
            return TableEnum.ele.getExt();
        } else if ("-2".equals(tableEnum.trim())) {
            return TableEnum.rent.getExt();
        } else if ("-3".equals(tableEnum.trim())) {
            return TableEnum.twr.getExt();
        } else if ("-4".equals(tableEnum.trim())) {
            return TableEnum.dat.getExt();
        } else {
            return null;
        }
    }

    public String getTableEnum() {
        return tableEnum;
    }

    public void setTableEnum(String tableEnum) {
        this.tableEnum = tableEnum;
    }

    public String getAppTitle() {
        return appTitle;
    }

    public void setAppTitle(String appTitle) {
        this.appTitle = appTitle;
    }

    public String getProcDefKey() {
        return procDefKey;
    }

    public void setProcDefKey(String procDefKey) {
        this.procDefKey = procDefKey;
    }

    public String getBusinessTable() {
        return businessTable;
    }

    public void setBusinessTable(String businessTable) {
        this.businessTable = businessTable;
    }

    public String getExt() {
        return ext;
    }

    public void setExt(String ext) {
        this.ext = ext;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getBeanName() {
        return beanName;
    }

    public void setBeanName(String beanName) {
        this.beanName = beanName;
    }
}
