package com.xunge.dao.selfelec.loan;

import com.xunge.dao.core.CrudDao;
import com.xunge.model.selfelec.EleLoanState;
import com.xunge.model.selfelec.EleLoanVo;
import com.xunge.model.selfelec.loan.EleLoan;
import com.xunge.model.selfelec.loan.EleLoanBalance;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 报账点余额表DAO接口
 *
 * <AUTHOR>
 * @version 2018-03-05
 */
public interface IEleLoanBalanceDao extends CrudDao<EleLoanBalance> {
    /**
     * 更新借款总余额
     *
     * @param eleLoanBalance
     */
    public void updateEleLoanBalance(EleLoanBalance eleLoanBalance);

    /**
     * @description 根据报账点id查询余额
     * <AUTHOR>
     * @date 创建时间：2018年03月05日
     */
    public EleLoanBalance getOneByBillaccountId(EleLoanBalance eleLoanBalance);

    /**
     * @description 根据借款总余额
     * <AUTHOR>
     * @date 创建时间：2018年03月05日
     */
    public void updateEleLoanBalanceList(@Param("loanBalanceList") List<EleLoanBalance> loanBalanceList);

    /**
     * @description 新增总余额
     * <AUTHOR>
     * @date 创建时间：2018年03月12日
     */
    public int insertEleLoanBalance(Map<String, Object> map);

    /**
     * @description 查询总余额
     * <AUTHOR>
     * @date 创建时间：2018年03月12日
     */
    public EleLoanBalance selectEleLoanBalance(Map<String, Object> map);

    /**
     * @description 根据报账点Id查询借余额
     * <AUTHOR>
     * @date 创建时间：2018年03月12日
     */
    public List<EleLoanBalance> queryEleLoanBalance(List<EleLoan> loanList);

    /**
     * <AUTHOR>
     * @date 创建时间：2018年03月12日
     */
    public void updateLoanBalance(EleLoan eleLoan);

    /**
     * @description 修改功能更新借款总余额
     * <AUTHOR>
     * @date 创建时间：2018年03月12日
     */
    public void updateBalance(Map<String, Object> map);

    /**
     * @description 删除功能更新核销金额
     * <AUTHOR>
     * @date 创建时间：2018年03月12日
     */
    public void updateCancellation(@Param("verificationId") String verificationId);

    /**
     * @description 录入功能更新核销金额
     * <AUTHOR>
     * @date 创建时间：2018年03月12日
     */
    public void updateAddCancellation(@Param("verificationId") String verificationId);

    /**
     * @description 修改功能更新核销金额
     * <AUTHOR>
     * @date 创建时间：2018年03月12日
     */
    public void updateEditCancellation(@Param("verificationId") String verificationId,
                                       @Param("oldBillAmountActual") BigDecimal oldBillAmountActual);

    public void updateEleLoanBalanceSum(Map<String, Object> map);

    /**
     * @description 财务审批后修改借款总金额
     * <AUTHOR>
     * @date 创建时间：2018年03月12日
     */
    public void updateEleLoanBalanceByCode(@Param("collectNum") String collectNum);

    //	/**
    //	 * @description 财务审批后修改借款总金额
    //	 *
    //	 * <AUTHOR>
    //	 * @date 创建时间：2018年03月12日
    //	 */
    //	public void updateLoanBalanceByCode(@Param("collectNum")String collectNum);

    /**
     * @description 财务审批再撤回后修改借款总金额核销总金额
     * <AUTHOR>
     * @date 创建时间：2018年03月12日
     */
    public void updateCancellationBalance(EleLoan eleLoan);

    /**
     * @description 财务审批审批后修改借款总金额核销总金额
     * <AUTHOR>
     * @date 创建时间：2018年03月12日
     */
    public void updateSumCancellationBalance(EleLoan eleLoan);

    /**
     * @description 借款总余额
     * @date 创建时间：2018年05月07日
     */
    public EleLoanVo getTotalAmountById(EleLoanBalance eleLoanBalance);

    /**
     * @description 借款明细
     * @date 创建时间：2018年05月08日
     */
    public List<EleLoanState> getEleloanList(EleLoan eleLoan);

    /**
     * @description 财务审批审批后修改借款总金额核销总金额
     * <AUTHOR>
     * @date 创建时间：2018年03月12日
     */
    public void updateLoanBalanceById(EleLoanBalance eleLoanBalance);

    /**
     * @Title: updateEleLoanBalanceById @Description: TODO(这里用一句话描述这个方法的作用) @param @param
     * verificationId 设定文件 @return void 返回类型 @throws
     */

    public void updateEleLoanBalanceById(@Param("verificationId") String verificationId);


    public void updateSumEleLoanBalanceById(String verificationId);
    EleLoanBalance findCancellationBalance(@Param("billaccountIds") List<String> list);

}