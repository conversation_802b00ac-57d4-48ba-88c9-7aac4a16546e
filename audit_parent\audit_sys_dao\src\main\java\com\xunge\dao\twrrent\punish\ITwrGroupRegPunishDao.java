package com.xunge.dao.twrrent.punish;

import com.xunge.model.towerrent.punish.TwrGroupRegPunishVO;

import java.util.Map;

/**
 * 集团既定考核指标扣罚汇总
 *
 * <AUTHOR>
 */
public interface ITwrGroupRegPunishDao {
    /**
     * 新增集团考核扣罚汇总信息
     *
     * @param twrGroupRegPunishVO
     * @return
     */
    public int insertSelective(TwrGroupRegPunishVO twrGroupRegPunishVO);

    /**
     * 根据id修改集团既定考核汇总信息
     *
     * @param twrGroupRegPunishVO
     * @return
     */
    public int updateByPrimaryKeySelective(TwrGroupRegPunishVO twrGroupRegPunishVO);

    /**
     * 根据年月和地市查询集团扣罚汇总信息
     *
     * @param paraMap
     * @return
     */
    public TwrGroupRegPunishVO queryGroupRegPunish(Map<String, Object> paraMap);
}