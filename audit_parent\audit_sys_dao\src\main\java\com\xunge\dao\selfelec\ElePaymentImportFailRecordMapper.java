package com.xunge.dao.selfelec;

import com.xunge.model.basedata.DatAttachment;
import com.xunge.model.selfelec.ElePaymentExport;
import com.xunge.model.selfelec.vo.EleFinancePaymentImportVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2021-08-11 8:58
 * @description:
 */
public interface ElePaymentImportFailRecordMapper {

    void deleteImportFailRecordByUser(@Param("importUserId") String importUserId);

    int insertImportFailRecord(@Param("voList") List<EleFinancePaymentImportVO> voList);

    List<EleFinancePaymentImportVO> queryVElePaymentImportFailInfo(@Param("importUserId") String importUserId);

    List<ElePaymentExport> queryVElePaymentImportSuccessInfo(Map<String, Object> map);

    List<ElePaymentExport> queryVElePaymentImportSuccessBenchmark(@Param("billaccountpaymentdetailId") String billaccountpaymentdetailId);

    List<DatAttachment> queryVElePaymentImportSuccessAttachment(@Param("billaccountpaymentdetailId") String billaccountpaymentdetailId);

    List<ElePaymentExport> queryElePaymentAuditState(Map<String, Object> map);
}
