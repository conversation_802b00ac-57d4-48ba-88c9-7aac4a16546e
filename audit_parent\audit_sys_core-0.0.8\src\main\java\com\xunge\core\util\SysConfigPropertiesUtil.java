package com.xunge.core.util;

import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.util.Properties;

/**
 * <p>
 * 获取配置项工作类<br/>
 */
@Slf4j
public class SysConfigPropertiesUtil {
    private static Properties prop = new Properties();

    static {
        try {
            prop.load(SysConfigPropertiesUtil.class.getClassLoader().getResourceAsStream("\\properties\\sysConfig.properties"));
        } catch (IOException e) {
            log.error("SysConfigPropertiesUtil 出错", e);
        }
    }

    public static String getProperty(String key) {
        try {
            if ("".equals(prop.get(key)) || prop.get(key) == null) {
                return "";
            } else {
                return (String) prop.get(key);
            }

        } catch (Exception e) {
            log.error("读取配置文件【sysConfig.properties】错误，错误信息", e);
            return "";
        }
    }
}
