package com.xunge.dao.selfelec.eleverificate;

import com.xunge.dao.core.CrudDao;
import com.xunge.model.selfelec.eleverificate.EleVerificationLoan;
import com.xunge.model.selfelec.eleverificate.VEleBillaccountVerificateInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 借款核销关联DAO接口
 *
 * <AUTHOR>
 * @version 2018-03-20
 */
public interface IEleVerificationLoanDao extends CrudDao<EleVerificationLoan> {

    /**
     * 根据核销ID获取数据列表
     *
     * @param id
     * @return
     */
    public List<EleVerificationLoan> getByVerificationId(String id);

    /**
     * 根据借款ID获取数据列表
     *
     * @param id
     * @return
     */
    public List<EleVerificationLoan> getByLoanId(String LoanId);

    public List<EleVerificationLoan> loadVerificationLoan(Map<String, Object> map);

    public List<EleVerificationLoan> loadVerificationLoans(Map<String, Object> map);

    public int updateVerificationAmount(EleVerificationLoan enti);
    public int updateVerifAmountByLoanId(EleVerificationLoan enti);

    public List<EleVerificationLoan> getAmountListList(Map<String, Object> map);

    public void deleteEleVerificationLoan(Map<String, Object> parmMap);

    /**
     * 根据借款ID删除关联表
     *
     * @param id
     * @return
     */
    public void deleteEleVerificationLoanList(@Param("eleVerificateList") List<VEleBillaccountVerificateInfo> eleVerificateList);

    /**
     * 借款详情页查询可关联借款信息
     *
     * @param id
     * @return
     */
    public List<EleVerificationLoan> queryVerificationLoan(Map<String, Object> map);

    List<EleVerificationLoan> selectVerificationLoan(String verificationId);

    int updateByVerifId(EleVerificationLoan enti);

}