package com.xunge.core.util;

import lombok.extern.slf4j.Slf4j;

import java.io.*;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * 将多个文件或文件夹压缩到一个压缩文件中，保持相同的目录结构
 */
@Slf4j
public class ZipUtil {

    private static final int BUFFER_SIZE = 2 * 1024;

    /**
     * @param srcDir           压缩文件夹路径
     * @param outDir           压缩文件输出流
     * @param KeepDirStructure 是否保留原来的目录结构,
     *                         true:保留目录结构;
     *                         false:所有文件跑到压缩包根目录下(注意：不保留目录结构可能会出现同名文件,会压缩失败)
     * @throws RuntimeException 压缩失败会抛出运行时异常
     */
    public static void toZip(String[] srcDir, String outDir,
                             boolean KeepDirStructure) throws RuntimeException, Exception {

        OutputStream out = new FileOutputStream(new File(outDir));

        long start = System.currentTimeMillis();
        ZipOutputStream zos = null;
        try {
            zos = new ZipOutputStream(out);
            List<File> sourceFileList = new ArrayList<File>();
            for (String dir : srcDir) {
                File sourceFile = new File(dir);
                sourceFileList.add(sourceFile);
            }
            compress(sourceFileList, zos, KeepDirStructure);
            long end = System.currentTimeMillis();
            log.info("压缩完成，耗时：" + (end - start) + " ms");
        } catch (Exception e) {
            throw new RuntimeException("zip error from ZipUtils", e);
        } finally {
            if (zos != null) {
                try {
                    zos.close();
                } catch (IOException e) {
                    log.error("ZipUtil 出错", e);
                }
            }
        }
    }

    /**
     * 铁塔费用汇总附件下载
     *
     * @param pathMap
     * @param outDir
     * @throws RuntimeException
     * @throws Exception
     */
    public static void toZip(Map<String, Map<String, List<String>>> pathMap, String outDir) throws RuntimeException, Exception {
        OutputStream out = new FileOutputStream(new File(outDir));
        long start = System.currentTimeMillis();
        ZipOutputStream zos = null;
        try {
            zos = new ZipOutputStream(out);
            for (String businessType : pathMap.keySet()) {
                Map<String, List<String>> businessTypeMap = pathMap.get(businessType);
                for (String yearmonth : businessTypeMap.keySet()) {
                    List<String> filePaths = businessTypeMap.get(yearmonth);
                    for (String filePath : filePaths) {
                        File sourceFile = new File(filePath);
                        compress(sourceFile, zos, businessType + "/" + yearmonth + "/" + sourceFile.getName(), true);
                    }
                }
            }
            long end = System.currentTimeMillis();
            System.out.println("压缩完成，耗时：" + (end - start) + " ms");
        } catch (Exception e) {
            throw new RuntimeException("zip error from ZipUtils", e);
        } finally {
            if (zos != null) {
                try {
                    zos.close();
                } catch (IOException e) {
                    log.error("ZipUtil 出错", e);
                }
            }
        }
    }

    /**
     * 递归压缩方法
     *
     * @param sourceFile       源文件
     * @param zos              zip输出流
     * @param name             压缩后的名称
     * @param KeepDirStructure 是否保留原来的目录结构,
     *                         true:保留目录结构;
     *                         false:所有文件跑到压缩包根目录下(注意：不保留目录结构可能会出现同名文件,会压缩失败)
     * @throws Exception
     */
    private static void compress(File sourceFile, ZipOutputStream zos,
                                 String name, boolean KeepDirStructure) throws Exception {
        FileInputStream in =null;
        try {
            byte[] buf = new byte[BUFFER_SIZE];
            if (sourceFile.isFile()) {
                zos.putNextEntry(new ZipEntry(name));
                int len;
                in = new FileInputStream(sourceFile);
                while ((len = in.read(buf)) != -1) {
                    zos.write(buf, 0, len);
                }
                // Complete the entry
                zos.closeEntry();
            } else {
                File[] listFiles = sourceFile.listFiles();
                if (listFiles == null || listFiles.length == 0) {
                    if (KeepDirStructure) {
                        zos.putNextEntry(new ZipEntry(name + "/"));
                        zos.closeEntry();
                    }

                } else {
                    for (File file : listFiles) {
                        if (KeepDirStructure) {
                            compress(file, zos, name + "/" + file.getName(),
                                    KeepDirStructure);
                        } else {
                            compress(file, zos, file.getName(), KeepDirStructure);
                        }

                    }
                }
            }
        }catch (Exception e){
            log.error(e.getMessage(),e);
        }finally {
            if (in!=null) {
                in.close();
            }
        }
    }


    private static void compress(List<File> sourceFileList,
                                 ZipOutputStream zos, boolean KeepDirStructure) throws Exception {
        FileInputStream in = null;
        try {
            byte[] buf = new byte[BUFFER_SIZE];
            for (File sourceFile : sourceFileList) {
                String name = sourceFile.getName();
                if (sourceFile.isFile()) {
                    zos.putNextEntry(new ZipEntry(name));
                    int len;
                    in = new FileInputStream(sourceFile);
                    while ((len = in.read(buf)) != -1) {
                        zos.write(buf, 0, len);
                    }
                    zos.closeEntry();
                } else {
                    File[] listFiles = sourceFile.listFiles();
                    if (listFiles == null || listFiles.length == 0) {
                        if (KeepDirStructure) {
                            zos.putNextEntry(new ZipEntry(name + "/"));
                            zos.closeEntry();
                        }

                    } else {
                        for (File file : listFiles) {
                            if (KeepDirStructure) {
                                compress(file, zos, name + "/" + file.getName(),
                                        KeepDirStructure);
                            } else {
                                compress(file, zos, file.getName(),
                                        KeepDirStructure);
                            }

                        }
                    }
                }
            }
        }catch (Exception e){
            log.error(e.getMessage(),e);
        }finally {
            if (in!=null) {
                in.close();
            }
        }
    }

    public static void main(String[] args) throws Exception {

        String[] srcDir = {"E:/科大国创工作/上线SQL/20181119",
                "E:/科大国创工作/上线SQL/20190110",
                "E:/科大国创工作/APP支持HTTPS/server.xml"};
        String outDir = "E:/ss/aaa.zip";
        ZipUtil.toZip(srcDir, outDir, true);
    }
}