package com.xunge.core.file;


import com.xunge.core.util.MD5Util;
import com.xunge.core.util.SpringContextUtil;
import org.apache.ibatis.session.SqlSession;
import org.springframework.context.ApplicationContext;

import java.io.File;
import java.io.IOException;
import java.net.URI;

/**
 * Created by liuxiao on 2022/2/17.
 */
public class NcmsFile extends File {


    public NcmsFile(String pathname) {
        super(pathname);
    }

    public NcmsFile(String parent, String child) {
        super(parent, child);
    }

    public NcmsFile(File parent, String child) {
        super(parent, child);
    }

    public NcmsFile(URI uri) {
        super(uri);
    }

    //先删附件，删完后才能删文件(一定注意)
    @Override
    public boolean delete() {
        try {
            if (this.exists() && !this.isDirectory()){
                String md5 = MD5Util.getMd5ByFile(this);
                ApplicationContext context = SpringContextUtil.getApplicationContext();
                SqlSession sqlSession = context.getBean(SqlSession.class);
                Integer count = sqlSession.selectOne("com.xunge.dao.basedata.DatAttachmentMapper.countByMd5",md5);
                Integer appFileCount = sqlSession.selectOne("com.xunge.dao.basedata.DatAttachmentMapper.appFileCountByMd5",md5);
                Integer downloadListCount = sqlSession.selectOne("com.xunge.dao.basedata.DatAttachmentMapper.downloadListCountByMd5",md5);
                if (count>0||appFileCount>0||downloadListCount>0){
                    return true;
                }
                else {
                    return super.delete();
                }
            }
            else {
                return false;
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return false;
    }
}
