package com.xunge.model.basedata.power;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @ClassName PowerSiteDegree
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/9/7 14:39
 */
@Data
public class PowerSiteDegree {
    private String id;
    @Excel(name = "年",orderNum ="1",replace = {"-_null"})
    private String year;
    @Excel(name = "月",orderNum ="2",replace = {"-_null"})
    private String month;
    private Date dataDate;
    private String provinceCode;
    @Excel(name = "省份",orderNum ="3",replace = {"-_null"})
    private String provinceName;
    private String cityCode;
    @Excel(name = "所属地市",orderNum ="4",replace = {"-_null"})
    private String cityName;
    @Excel(name = "站点编码",orderNum ="6",replace = {"-_null"})
    private String siteCode;
    @Excel(name = "站点名称",orderNum ="5",replace = {"-_null"})
    private String siteName;
    @Excel(name = "站点类型",orderNum ="7",replace = {"-_null"})
    private String siteTypeCode;
    @Excel(name = "总用电量(Kw/h)",orderNum ="8",replace = {"-_null"})
    private BigDecimal totalElectric;
    @Excel(name = "主设备用电量(Kw/h)",orderNum ="9",replace = {"-_null"})
    private BigDecimal priDeviceElectric;
    @Excel(name = "空调用电量(Kw/h)",orderNum ="10",replace = {"-_null"})
    private BigDecimal airCondiElectric;
    @Excel(name = "PUE",orderNum ="11",replace = {"-_null"})
    private BigDecimal pue;
    private Date dataTransDate;

}
