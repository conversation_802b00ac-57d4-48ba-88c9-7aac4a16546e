package com.xunge.dao.report;

import java.util.List;
import java.util.Map;

public interface IRptElecontractPriceDao {

    List<Map<String, Object>> queryRptElecontractPrice(Map<String, Object> map);

    Map<String, Object> queryRptElecontractPriceTotal(Map<String, Object> map);

    /**
     * 根据省份id查询各地市电费合同数据
     *
     * @return
     * <AUTHOR>
     */
    List<Map<String, Object>> queryRptElecontractPriceByPrvid(Map<String, Object> map);

    Map<String, Object> queryRptElecontractPriceByPrvidTotal(Map<String, Object> map);

    /**
     * 根据地市id查询区县电费合同数据
     *
     * @return
     * <AUTHOR>
     */
    List<Map<String, Object>> queryRptElecontractPriceByPregid(Map<String, Object> map);

    Map<String, Object> queryRptElecontractPriceByPregidTotal(Map<String, Object> map);
}
