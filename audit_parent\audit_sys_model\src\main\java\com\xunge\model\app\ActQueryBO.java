package com.xunge.model.app;

import com.xunge.model.system.user.SysUserVO;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description: 封装查询的实体类
 * @date 2019/3/14 14:46
 */
public class ActQueryBO implements Serializable {
    /**
     * 省份编码和ID
     */
    public static Map<String, String> proviceCodeMap = new HashMap<>();
    /**
     * 省份编码和ID
     */
    public static Map<String, String> proviceIdMap = new HashMap<>();

    static {
        proviceCodeMap.put("BJ", "110000");
        proviceCodeMap.put("TJ", "120000");
        proviceCodeMap.put("HE", "130000");
        proviceCodeMap.put("SX", "140000");
        proviceCodeMap.put("NM", "150000");
        proviceCodeMap.put("LN", "210000");
        proviceCodeMap.put("JL", "220000");
        proviceCodeMap.put("HL", "230000");
        proviceCodeMap.put("SH", "310000");
        proviceCodeMap.put("JS", "320000");
        proviceCodeMap.put("ZJ", "330000");
        proviceCodeMap.put("AH", "340000");
        proviceCodeMap.put("FJ", "350000");
        proviceCodeMap.put("JX", "360000");
        proviceCodeMap.put("SD", "370000");
        proviceCodeMap.put("HA", "410000");
        proviceCodeMap.put("HB", "420000");
        proviceCodeMap.put("HN", "430000");
        proviceCodeMap.put("GD", "440000");
        proviceCodeMap.put("GX", "450000");
        proviceCodeMap.put("HI", "460000");
        proviceCodeMap.put("CQ", "500000");
        proviceCodeMap.put("SC", "510000");
        proviceCodeMap.put("GZ", "520000");
        proviceCodeMap.put("YN", "530000");
        proviceCodeMap.put("XZ", "540000");
        proviceCodeMap.put("SN", "610000");
        proviceCodeMap.put("GS", "620000");
        proviceCodeMap.put("QH", "630000");
        proviceCodeMap.put("NX", "640000");
        proviceCodeMap.put("XJ", "650000");
        proviceCodeMap.put("TW", "710000");
        proviceCodeMap.put("HQ", "000000");
    }

    static {
        proviceIdMap.put("110000", "BJ");
        proviceIdMap.put("120000", "TJ");
        proviceIdMap.put("130000", "HE");
        proviceIdMap.put("140000", "SX");
        proviceIdMap.put("150000", "NM");
        proviceIdMap.put("210000", "LN");
        proviceIdMap.put("220000", "JL");
        proviceIdMap.put("230000", "HL");
        proviceIdMap.put("310000", "SH");
        proviceIdMap.put("320000", "JS");
        proviceIdMap.put("330000", "ZJ");
        proviceIdMap.put("340000", "AH");
        proviceIdMap.put("350000", "FJ");
        proviceIdMap.put("360000", "JX");
        proviceIdMap.put("370000", "SD");
        proviceIdMap.put("410000", "HA");
        proviceIdMap.put("420000", "HB");
        proviceIdMap.put("430000", "HN");
        proviceIdMap.put("440000", "GD");
        proviceIdMap.put("450000", "GX");
        proviceIdMap.put("460000", "HI");
        proviceIdMap.put("500000", "CQ");
        proviceIdMap.put("510000", "SC");
        proviceIdMap.put("520000", "GZ");
        proviceIdMap.put("530000", "YN");
        proviceIdMap.put("540000", "XZ");
        proviceIdMap.put("610000", "SN");
        proviceIdMap.put("620000", "GS");
        proviceIdMap.put("630000", "QH");
        proviceIdMap.put("640000", "NX");
        proviceIdMap.put("650000", "XJ");
        proviceIdMap.put("710000", "TW");
        proviceIdMap.put("000000", "HQ");
    }

    /**
     * 省份编码
     */
    private String prvCode;
    /**
     * 当前用户ID
     */
    private String userId;
    /**
     * 当前用户
     */
    private SysUserVO sysUserVO;
    /**
     * 当前任务ID
     */
    private String taskId;
    /**
     * 不同流程的枚举值，对应不同的流程
     */
    private String tableEnum;
    /**
     * 预留
     */
    private String tableEnumExt;
    /**
     * 分页参数，第几页
     */
    private Integer pageNum;
    /**
     * 分页参数，页大小
     */
    private Integer pageSize;
    /**
     * 用户名，用于搜索下级审核人的模糊查询
     */
    private String userName;
    /**
     * 用于搜索待办的关键字
     */
    private String key;
    /**
     * 业务表主键
     */
    private String businessId;
    /**
     * 审核时候的意见
     */
    private String Comment;
    /**
     * 下级审核人ID,一般为登录名
     */
    private String nextUserId;
    /**
     * 审核状态
     */
    private Integer state;
    /**
     * 角色ID
     */
    private List<String> roleIds;
    /**
     * 地市ID集合，用于权限卡控
     */
    private List<String> pregIds;
    /**
     * 区县ID集合，用于权限卡控
     */
    private List<String> regIds;

    private String regId;

    private String pregId;

    @Override
    public String toString() {
        return "ActQueryBO{" +
                "prvCode='" + prvCode + '\'' +
                ", userId='" + userId + '\'' +
                ", taskId='" + taskId + '\'' +
                ", tableEnum='" + tableEnum + '\'' +
                ", tableEnumExt='" + tableEnumExt + '\'' +
                ", pageNum=" + pageNum +
                ", pageSize=" + pageSize +
                ", userName='" + userName + '\'' +
                ", key='" + key + '\'' +
                ", businessId='" + businessId + '\'' +
                ", Comment='" + Comment + '\'' +
                ", nextUserId='" + nextUserId + '\'' +
                ", state=" + state +
                ", regId=" + regId +
                ", pregId=" + pregId +
//                ", roleIds=" + roleIds +
//                ", pregIds=" + pregIds +
//                ", regIds=" + regIds +
                '}';
    }

    public List<String> getPregIds() {
        return pregIds;
    }

    public void setPregIds(List<String> pregIds) {
        this.pregIds = pregIds;
    }

    public List<String> getRegIds() {
        return regIds;
    }

    public void setRegIds(List<String> regIds) {
        this.regIds = regIds;
    }

    public String getPrvCode() {
        return prvCode;
    }

    public void setPrvCode(String prvCode) {
        this.prvCode = prvCode;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public SysUserVO getSysUserVO() {
        return sysUserVO;
    }

    public void setSysUserVO(SysUserVO sysUserVO) {
        this.sysUserVO = sysUserVO;
    }

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public String getTableEnum() {
        return tableEnum;
    }

    public void setTableEnum(String tableEnum) {
        this.tableEnum = tableEnum;
    }

    public String getTableEnumExt() {
        return tableEnumExt;
    }

    public void setTableEnumExt(String tableEnumExt) {
        this.tableEnumExt = tableEnumExt;
    }

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getBusinessId() {
        return businessId;
    }

    public void setBusinessId(String businessId) {
        this.businessId = businessId;
    }

    public String getComment() {
        return Comment;
    }

    public void setComment(String comment) {
        Comment = comment;
    }

    public String getNextUserId() {
        return nextUserId;
    }

    public void setNextUserId(String nextUserId) {
        this.nextUserId = nextUserId;
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public List<String> getRoleIds() {
        return roleIds;
    }

    public void setRoleIds(List<String> roleIds) {
        this.roleIds = roleIds;
    }

    public String getRegId() {
        return regId;
    }

    public void setRegId(String regId) {
        this.regId = regId;
    }

    public String getPregId() {
        return pregId;
    }

    public void setPregId(String pregId) {
        this.pregId = pregId;
    }
}
