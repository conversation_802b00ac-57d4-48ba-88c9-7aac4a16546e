package com.xunge.dao.finance.rent;

import com.xunge.model.finance.rent.PushDetail;
import com.xunge.model.selfelec.accrualoffs.ElePaymentOffsDetail;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface RentFinanceMapper {

    int deleteByBillamountCode(@Param("billamountCode") String billamountCode);

    int insertFinancePushDetail(@Param("list") List<PushDetail> list);

    int updatePushDetailState(@Param("billamountCode") String billamountCode,@Param("state") Integer state,@Param("oldState") Integer oldState);

    /**
     * 查询汇总单推送需要的冲销明细
     * @param billamountId
     * @return
     */
    List<ElePaymentOffsDetail> queryBillamountWriteOffDetailToPush(@Param("billamountId") String billamountId);
}
