package com.xunge.model.finance.ext.accClaim.accwsdl;

import javax.xml.namespace.QName;
import javax.xml.ws.Service;
import javax.xml.ws.WebEndpoint;
import javax.xml.ws.WebServiceClient;
import javax.xml.ws.WebServiceFeature;
import java.net.MalformedURLException;
import java.net.URL;

/**
 * OSB Service
 * <p>
 * This class was generated by Apache CXF 3.4.0
 * 2020-10-10T11:41:15.934+08:00
 * Generated source version: 3.4.0
 */
@WebServiceClient(name = "OSB_RBS_CMF_HQ_ImportBatchAccruedClaimDocSrv",
        wsdlLocation = "http://172.16.194.159:8100/RBS/OSB_RBS_CMF_HQ_ImportBatchAccruedClaimDocSrv.v0/proxy/OSB_RBS_CMF_HQ_ImportBatchAccruedClaimDocSrv?wsdl",
        targetNamespace = "http://soa.cmcc.com/OSB_RBS_CMF_HQ_ImportBatchAccruedClaimDocSrv")
public class OSBRBSCMFHQImportBatchAccruedClaimDocSrv_Service extends Service {

    public final static URL WSDL_LOCATION;

    public final static QName SERVICE = new QName("http://soa.cmcc.com/OSB_RBS_CMF_HQ_ImportBatchAccruedClaimDocSrv", "OSB_RBS_CMF_HQ_ImportBatchAccruedClaimDocSrv");
    public final static QName OSBRBSCMFHQImportBatchAccruedClaimDocSrvPort = new QName("http://soa.cmcc.com/OSB_RBS_CMF_HQ_ImportBatchAccruedClaimDocSrv", "OSB_RBS_CMF_HQ_ImportBatchAccruedClaimDocSrvPort");

    static {
        URL url = null;
        try {
            url = new URL("http://172.16.194.159:8100/RBS/OSB_RBS_CMF_HQ_ImportBatchAccruedClaimDocSrv.v0/proxy/OSB_RBS_CMF_HQ_ImportBatchAccruedClaimDocSrv?wsdl");
        } catch (MalformedURLException e) {
            java.util.logging.Logger.getLogger(OSBRBSCMFHQImportBatchAccruedClaimDocSrv_Service.class.getName())
                    .log(java.util.logging.Level.INFO,
                            "Can not initialize the default wsdl from {0}", "http://172.16.194.159:8100/RBS/OSB_RBS_CMF_HQ_ImportBatchAccruedClaimDocSrv.v0/proxy/OSB_RBS_CMF_HQ_ImportBatchAccruedClaimDocSrv?wsdl");
        }
        WSDL_LOCATION = url;
    }

    public OSBRBSCMFHQImportBatchAccruedClaimDocSrv_Service(URL wsdlLocation) {
        super(wsdlLocation, SERVICE);
    }

    public OSBRBSCMFHQImportBatchAccruedClaimDocSrv_Service(URL wsdlLocation, QName serviceName) {
        super(wsdlLocation, serviceName);
    }

    public OSBRBSCMFHQImportBatchAccruedClaimDocSrv_Service() {
        super(WSDL_LOCATION, SERVICE);
    }

    public OSBRBSCMFHQImportBatchAccruedClaimDocSrv_Service(WebServiceFeature... features) {
        super(WSDL_LOCATION, SERVICE, features);
    }

    public OSBRBSCMFHQImportBatchAccruedClaimDocSrv_Service(URL wsdlLocation, WebServiceFeature... features) {
        super(wsdlLocation, SERVICE, features);
    }

    public OSBRBSCMFHQImportBatchAccruedClaimDocSrv_Service(URL wsdlLocation, QName serviceName, WebServiceFeature... features) {
        super(wsdlLocation, serviceName, features);
    }


    /**
     * @return returns OSBRBSCMFHQImportBatchAccruedClaimDocSrv
     */
    @WebEndpoint(name = "OSB_RBS_CMF_HQ_ImportBatchAccruedClaimDocSrvPort")
    public OSBRBSCMFHQImportBatchAccruedClaimDocSrv getOSBRBSCMFHQImportBatchAccruedClaimDocSrvPort() {
        return super.getPort(OSBRBSCMFHQImportBatchAccruedClaimDocSrvPort, OSBRBSCMFHQImportBatchAccruedClaimDocSrv.class);
    }

    /**
     * @param features A list of {@link WebServiceFeature} to configure on the proxy.  Supported features not in the <code>features</code> parameter will have their default values.
     * @return returns OSBRBSCMFHQImportBatchAccruedClaimDocSrv
     */
    @WebEndpoint(name = "OSB_RBS_CMF_HQ_ImportBatchAccruedClaimDocSrvPort")
    public OSBRBSCMFHQImportBatchAccruedClaimDocSrv getOSBRBSCMFHQImportBatchAccruedClaimDocSrvPort(WebServiceFeature... features) {
        return super.getPort(OSBRBSCMFHQImportBatchAccruedClaimDocSrvPort, OSBRBSCMFHQImportBatchAccruedClaimDocSrv.class, features);
    }

}
