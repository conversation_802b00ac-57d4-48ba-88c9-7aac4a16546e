package com.xunge.dao.statistics;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

import com.xunge.model.statistics.TeleMeterDayConsumption;

public interface MeterConsumptionMapper {

	List<TeleMeterDayConsumption> queryList(Map<String, Object> param);

	List<TeleMeterDayConsumption> getPreg(@Param("queryPrvId") String queryPrvId);

	List<TeleMeterDayConsumption> getReg(@Param("queryPrvId")String queryPrvId, @Param("pregId")String pregId);
	/**
	 * 分路计量带出分摊比例
	 */
	List<TeleMeterDayConsumption> selectList(@Param("meterCodes") List<String> meterCodes, @Param("start") String start, @Param("end") String end);

}
