package com.xunge.dao.selfelec;

import com.xunge.model.selfelec.VEleBillamount;
import com.xunge.model.selfelec.VEleBillamountExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface VEleBillamountMapper {
    
    int countByExample(VEleBillamountExample example);

    
    int deleteByExample(VEleBillamountExample example);

    
    int insert(VEleBillamount record);

    
    int insertSelective(VEleBillamount record);

    
    List<VEleBillamount> selectByExample(VEleBillamountExample example);

    List<VEleBillamount> selectByExampleSelf(VEleBillamount record);

    
    int updateByExampleSelective(@Param("record") VEleBillamount record, @Param("example") VEleBillamountExample example);

    
    int updateByExample(@Param("record") VEleBillamount record, @Param("example") VEleBillamountExample example);

    /**
     * 报账汇总
     *
     * @param record
     * @return
     */
    List<VEleBillamount> selectByBillamountList(VEleBillamount record);

    /**
     * 查询报账总数
     *
     * @param record
     * @return
     */
    int selectByBillamountCount(VEleBillamount record);
}