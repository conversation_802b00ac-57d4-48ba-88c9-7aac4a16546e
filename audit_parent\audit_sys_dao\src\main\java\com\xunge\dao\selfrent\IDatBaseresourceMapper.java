package com.xunge.dao.selfrent;

import com.xunge.model.selfrent.billAccount.RentExternalbenchmarkVO;
import com.xunge.model.selfrent.vo.RentBenchmarkParaInfoVO;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2020/11/25
 */
public interface IDatBaseresourceMapper {

    public int queryBaseresourceCountByAccountId(@Param("billAccountId") String billAccountId);

    //根据经纬度查找符合条件的最近区域信息
    public List<RentExternalbenchmarkVO> queryRentExternalbenchmarkVos
    (@Param("pregName") String pregName, @Param("regName") String regName, @Param("propertyArea") BigDecimal propertyArea, @Param("longitude") BigDecimal longitude, @Param("latitude") BigDecimal latitude, @Param("distance") Integer distance);

    //新增标杆详情信息
    public int insertBenchMarkeInfo(RentBenchmarkParaInfoVO rentBenchmarkParaInfoVO);

    //查询标杆详情
    public RentBenchmarkParaInfoVO queryBenchMarkeInfoVOS(@Param("paymentId") String paymentId, @Param("billaccountId") String billaccountId, @Param("baseresourceId") String baseresourceId);

    //修改标杆详情
    public int updateBenchMarkeInfo(RentBenchmarkParaInfoVO rentBenchmarkParaInfoVO);

    //通过报账点id查询外部标杆详情信息
    public List<RentBenchmarkParaInfoVO> querybenchmarkInfoByAccountId(@Param("billAccountId") String billAccountId);
}
