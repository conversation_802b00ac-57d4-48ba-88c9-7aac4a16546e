package com.xunge.dao.selfelec.accrualBillmount;

import com.xunge.model.finance.ext.accClaim.accrual.EleAccrualSecondBillamountDetail;
import com.xunge.model.selfelec.accrualBillamount.AccrualSecondBillamount;
import com.xunge.model.selfelec.accrualBillamount.AccrualSecondCondition;
import com.xunge.model.selfelec.accrualBillamount.AccrualSecondExport;

import java.util.List;
import org.apache.ibatis.annotations.Param;


public interface AccrualSecondBillamountMapper {

    int insertBach(List<AccrualSecondBillamount> accrualSecondBillamounts);

    
    int deleteByPrimaryKey(AccrualSecondBillamount key);

    
    int insert(AccrualSecondBillamount record);

    
    int insertSelective(AccrualSecondBillamount record);

    
    AccrualSecondBillamount selectByPrimaryKey(AccrualSecondBillamount key);

    
    int updateByPrimaryKeySelective(AccrualSecondBillamount record);

    
    int updateByPrimaryKey(AccrualSecondBillamount record);


	AccrualSecondCondition querySecondBillamountConditionByUser(String userId);


	void insertOrUpdate(AccrualSecondCondition con);


	/**根据billamountdetailIds和billamountId查询出二次汇总信息
	 * @param billamountdetailIds
	 * @param billamountId
	 * @return
	 */
	List<AccrualSecondBillamount> queryByBillamountIddetailIds(@Param("list")List<String> detailIds, @Param("billamountId")String billamountId);

	void updateSecondsData(EleAccrualSecondBillamountDetail esd);


	/**
	 * 导出二次汇总明细
	 * @param billamountIds
	 * @return
	 */
	List<AccrualSecondExport> exportSecondDetail(List<String> billamountIds);
}