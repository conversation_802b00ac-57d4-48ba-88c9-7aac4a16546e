package com.xunge.comm.utils;

import lombok.extern.slf4j.Slf4j;
import org.joda.time.*;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2018年05月18日
 */
@Slf4j
public class ToolDate {

    public static final String YEAR = "YEAR";
    public static final String MONTH = "MONTH";
    public static final String DAY = "DAY";
    public static final String HOUR = "HOUR";
    public static final String MINUTE = "MINUTE";
    public static final String SECOND = "SECOND";

    /**
     * 获取时间具体所在月份月末的天数
     *
     * @param time yyyy-MM-dd
     * @return
     * @date 2018年05月19日
     * <AUTHOR>
     */
    public static int getEndMonthDays(String time) {
        DateTime start = new DateTime(time);
        DateTime end = start.dayOfMonth().withMaximumValue();
        return Days.daysBetween(start, end).getDays() + 1;
    }

    /**
     * 获取时间具体所在月份月初的天数
     *
     * @param time yyyy-MM-dd
     * @return
     * @date 2018年05月19日
     * <AUTHOR>
     */
    public static int getStartMonthDays(String time) {
        DateTime start = new DateTime(time);
        DateTime end = start.dayOfMonth().withMinimumValue();
        return Days.daysBetween(end, start).getDays() + 1;
    }

    /**
     * 获取两个时间点之间的月份数、天数、小时数、分钟数、秒数(月份差改造为单单月份的比较)
     *
     * @param start yyyy-MM-dd
     * @param end   yyyy-MM-dd
     * @param type
     * @return
     * @date 2018年05月19日
     * <AUTHOR>
     */
    public static int diffTime(String start, String end, String type) {
        if (type.equals(MONTH)) {
            start = start.substring(0, 7) + "-01";
            end = end.substring(0, 7) + "-01";
        }
        DateTime dt1 = new DateTime(start);
        DateTime dt2 = new DateTime(end);
        switch (type) {
            case MONTH:
                return Months.monthsBetween(dt1, dt2).getMonths();
            case DAY:
                return Days.daysBetween(dt1, dt2).getDays();
            case HOUR:
                return Hours.hoursBetween(dt1, dt2).getHours();
            case MINUTE:
                return Minutes.minutesBetween(dt1, dt2).getMinutes();
            case SECOND:
                return Seconds.secondsBetween(dt1, dt2).getSeconds();
            default:
                return 0;
        }
    }

    /**
     * 获取时间点所在月份的天数
     *
     * @param time yyyy-MM-dd
     * @return
     * @date 2018年05月19日
     * <AUTHOR>
     */
    public static int getDaysOfMonth(String time) {
        DateTime dateTime = new DateTime(time);
        return dateTime.dayOfMonth().getMaximumValue();
    }

    /**
     * 计算两个时间点之间时间差
     *
     * @param time
     * @param type
     * @param num
     * @return
     * @date 2018年05月19日
     * <AUTHOR>
     */
    public static String calcDate(String time, String type, int num) {
        DateTime dt = new DateTime(time);
        DateTime dt1 = new DateTime();
        if (num != 0) {
            switch (type) {
                case YEAR:
                    dt1 = num > 0 ? dt.plusYears(num) : dt.minusYears(-num);
                    break;
                case MONTH:
                    dt1 = num > 0 ? dt.plusMonths(num) : dt.minusMonths(-num);
                    break;
                case DAY:
                    dt1 = num > 0 ? dt.plusDays(num) : dt.minusDays(-num);
                    break;
                default:
                    dt1 = dt;
                    break;
            }
            return dt1.toString("yyyy-MM-dd");
        } else {
            return time;
        }
    }

    public static boolean isBefore(Object dt1, Object dt2) {
        try {
            DateTime date1 = new DateTime(dt1);
            DateTime date2 = new DateTime(dt2);
            return date1.isBefore(date2);
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 获取两个时间点的日期集合
     *
     * @param ifNow 是否包含结束时间点
     * @return
     * @throws ParseException
     */
    public static List<String> getDates(String startDate, String endDate, boolean ifNow) {
        List<String> list = new ArrayList<>();
        try {
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
            Date d1 = format.parse(startDate);// 定义起始日期
            Date d2 = format.parse(endDate);// 定义结束日期
            Calendar dd = Calendar.getInstance();// 定义日期实例
            dd.setTime(d1);// 设置日期起始时间
            while (dd.getTime().before(d2)) {// 判断是否到结束日期
                String str = format.format(dd.getTime());
                dd.add(Calendar.DATE, 1);// 进行当前日期月份加1
                list.add(str);
            }
            if (ifNow) {
                list.add(endDate);
            }
        } catch (Exception e) {
            log.error("ToolDate 出错", e);
        }
        return list;
    }

    public static void main(String[] args) {
        System.out.println(isBefore("2018-06-21", new Date()));
    }

}
