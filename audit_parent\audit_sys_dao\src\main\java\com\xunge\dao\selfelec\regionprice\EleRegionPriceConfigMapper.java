package com.xunge.dao.selfelec.regionprice;

import java.util.Map;

/**
 * 区域单价颗粒度配置
 * <AUTHOR>
 */
public interface EleRegionPriceConfigMapper {

    /**
     * 保存颗粒度配置
     * @param map
     * @return
     */
    int saveEleRegionPriceConfig(Map<String, Object> map);

    /**
     * 删除颗粒度配置
     * @return
     */
    int delEleRegionPriceConfig();

    /**
     * 获取颗粒度配置
     * @param map
     * @return
     */
    String getEleRegionPriceConfig(Map<String, Object> map);

}
