package com.xunge.dao.basedata.power;

import com.xunge.model.basedata.power.SmartMeter;

import java.util.List;
import java.util.Map;

/**
 * 描述：
 * Created on 2019/6/13.
 * <p>Title:</p>
 * <p>Copyright:Copyright (c) 2017</p>
 * <p>Company:科大国创软件股份有限公司</p>
 * <p>Department:NBC</p>
 *
 * <AUTHOR> <EMAIL>
 * @version 1.0
 * @update
 */
public interface SmartMeterMapper {

    List<SmartMeter> selectSmartMetersByCondition(SmartMeter smartMeter);

    /**
     * 电费录入时自动带入缴费型智能电表读数 （20210402453969 ）
     *
     * @param map
     * @return
     */
    List<SmartMeter> selectListByCondition(Map<String, Object> map);
}
