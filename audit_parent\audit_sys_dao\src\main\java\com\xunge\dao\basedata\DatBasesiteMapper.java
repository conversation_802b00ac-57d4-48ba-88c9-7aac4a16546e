package com.xunge.dao.basedata;

import com.xunge.model.basedata.DatBasesite;
import com.xunge.model.basedata.DatBasesiteExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface DatBasesiteMapper {
    
    int countByExample(DatBasesiteExample example);

    
    int deleteByExample(DatBasesiteExample example);

    
    int deleteByPrimaryKey(String basesiteId);

    
    int insert(DatBasesite record);

    
    int insertSelective(DatBasesite record);

    
    List<DatBasesite> selectByExample(DatBasesiteExample example);

    
    DatBasesite selectByPrimaryKey(String basesiteId);

    
    int updateByExampleSelective(@Param("record") DatBasesite record, @Param("example") DatBasesiteExample example);

    
    int updateByExample(@Param("record") DatBasesite record, @Param("example") DatBasesiteExample example);

    
    int updateByPrimaryKeySelective(DatBasesite record);

    
    int updateByPrimaryKey(DatBasesite record);
}