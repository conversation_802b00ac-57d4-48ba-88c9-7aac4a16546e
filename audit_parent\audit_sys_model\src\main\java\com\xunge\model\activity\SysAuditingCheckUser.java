/**
* @Title: SysAuditingCheckUser.java 
* @Package com.xunge.model.activity 
* <AUTHOR>   
* @date 2025年5月20日 下午3:24:16 
* @version V1.0   
*/ 
package com.xunge.model.activity;

import java.util.Date;

import lombok.Data;

/** 
* @ClassName: SysAuditingCheckUser 
* @Description: 流程审批人记忆
* @Author：tian
* @Date：2025年5月20日 
*/
@Data
public class SysAuditingCheckUser {

	/**
	 * 主键id
	 */
	private long id;
	/**
	 * 操作人id
	 */
	private String userId;
	/**
	 * 流程标识
	 */
	private String procDefKey;
	/**
	 * 业务名称
	 */
	private String title;
	/**
	 * 流程环节
	 */
	private String procUnit;
	/**
	 * 审批人id
	 */
	private String auditingUserId;
	/**
	 * 审批人名称
	 */
	private String auditingUserName;
	/**
	 * 使用次数
	 */
	private Integer totalUse;
	/**
	 * 创建时间
	 */
	private Date createTime;
	/**
	 * 更新时间
	 */
	private Date updateTime;
	/**
	 * 审批人状态
	 */
	private Integer userState;
	/**
	 * 业务类别：1：电费，2：租费，3：铁塔服务费，4：公共
	 */
	private Integer type;
	
	public Date getLastTime() {
		return updateTime==null?createTime:updateTime;
	}
}
