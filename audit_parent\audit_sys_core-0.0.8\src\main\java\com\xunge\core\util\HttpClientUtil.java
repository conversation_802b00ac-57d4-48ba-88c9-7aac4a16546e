package com.xunge.core.util;

import com.xunge.core.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpResponse;
import org.apache.http.HttpStatus;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLConnection;
import java.util.HashMap;
import java.util.Map;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;

@Slf4j
public class HttpClientUtil {
	/*	@SuppressWarnings("all")
		private static MemcachedUtil memcachedUtil;
		static {
			memcachedUtil = (MemcachedUtil) SpringContextUtil.getBean("memcachedUtil");
		}*/

    public static String get(String path) throws Exception {
        HttpURLConnection httpConn = null;
        BufferedReader in = null;
        try {
            URL url = new URL(path);
            httpConn = (HttpURLConnection) url.openConnection();
            httpConn.setConnectTimeout(3000);
            httpConn.setReadTimeout(4000);

            if (httpConn.getResponseCode() == HttpURLConnection.HTTP_OK) {
                StringBuffer content = new StringBuffer();
                String tempStr = "";
                in = new BufferedReader(new InputStreamReader(httpConn.getInputStream(), "utf-8"));
                while ((tempStr = in.readLine()) != null) {
                    content.append(tempStr);
                }
                return content.toString();
            }
        } catch (Exception e) {
            log.error("HttpClientUtil 出错", e);
            if(httpConn != null) {
                throw new BusinessException("[" + httpConn.getResponseCode() + "]连接异常:" + e.getMessage());
            }
        } finally {
            if (in != null) {
                in.close();
            }
            if (httpConn != null) {
                httpConn.disconnect();
            }
        }
        return null;
    }

    public static String post(String ip, String params) throws Exception {
        HttpURLConnection httpConn = null;
        BufferedReader in = null;
        PrintWriter out = null;
        try {
            URL url = new URL(ip);
            httpConn = (HttpURLConnection) url.openConnection();
            httpConn.setRequestMethod("POST");
            httpConn.setDoInput(true);
            httpConn.setDoOutput(true);
            httpConn.setConnectTimeout(3000);
            httpConn.setReadTimeout(4000);

            out = new PrintWriter(httpConn.getOutputStream());
            out.println(params.toString());
            out.flush();

            if (httpConn.getResponseCode() == HttpURLConnection.HTTP_OK) {
                StringBuffer content = new StringBuffer();
                String tempStr = "";
                in = new BufferedReader(new InputStreamReader(httpConn.getInputStream()));
                while ((tempStr = in.readLine()) != null) {
                    content.append(tempStr);
                }
                return content.toString();
            }
        } catch (Exception e) {
            log.error("HttpClientUtil 出错", e);
            if(httpConn != null){
                 throw new BusinessException("[" + httpConn.getResponseCode() + "]连接异常:" + e.getMessage());
            }
        }finally {
            if (in!=null) {
                in.close();
            }
            if (out!=null) {
                out.close();
            }
            if (httpConn!=null) {
                httpConn.disconnect();
            }
        }
        return null;
    }

    public static String post(String ip, String params, String sessionId) throws Exception {
        String result = "";
        HttpPost post = new HttpPost(ip);
        try {
            CloseableHttpClient httpClient = HttpClients.createDefault();

            post.setHeader("Content-Type", "application/json;charset=utf-8");
            post.addHeader("Cookie", "NCMS_SESSION="+sessionId);
            post.addHeader("Referer", "/NCMS");
            StringEntity postingString = new StringEntity(params, "utf-8");
            post.setEntity(postingString);
            HttpResponse response = httpClient.execute(post);
            InputStream in = response.getEntity().getContent();
            BufferedReader br = new BufferedReader(new InputStreamReader(in, "utf-8"));
            StringBuilder strber = new StringBuilder();
            String line = null;
            while ((line = br.readLine()) != null) {
                strber.append(line + '\n');
            }
            br.close();
            in.close();
            result = strber.toString();
            if (response.getStatusLine().getStatusCode() != HttpStatus.SC_OK) {
                result = "服务器异常";
            }
        } catch (Exception e) {
            log.error("HttpClientUtil 出错", e);
            throw new RuntimeException(e);
        } finally {
            post.abort();
        }
        return result;
    }


    /**
     * 将url参数转换成map
     *
     * @param param aa=11&bb=22&cc=33
     * @return
     */
    public static Map<String, Object> getUrlParams(String param) {
        Map<String, Object> map = new HashMap<String, Object>(0);
        if (StrUtil.isBlank(param)) {
            return map;
        }
        String[] params = param.split("&");
        for (int i = 0; i < params.length; i++) {
            String[] p = params[i].split("=");
            if (p.length == 2) {
                map.put(p[0], p[1]);
            }
        }
        return map;
    }

    /**
     * 将map转换成url
     *
     * @param map
     * @return
     */
    public static String getUrlParamsByMap(Map<String, Object> map) {
        if (map == null) {
            return "";
        }
        StringBuffer sb = new StringBuffer();
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            sb.append(entry.getKey() + "=" + entry.getValue());
            sb.append("&");
        }
        String s = sb.toString();
        if (s.endsWith("&")) {
            s = org.apache.commons.lang.StringUtils.substringBeforeLast(s, "&");
        }
        return s;
    }

    public static String sendPost(String url, String param, String sessionId) {
        PrintWriter out = null;
        BufferedReader in = null;
        String result = "";
        try {
            URL realUrl = new URL(url);
            // 打开和URL之间的连接
            URLConnection conn = realUrl.openConnection();
            // 设置通用的请求属性
            conn.setRequestProperty("accept", "*/*");
            conn.setRequestProperty("connection", "Keep-Alive");
            conn.setRequestProperty("user-agent",
                    "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1;SV1)");
            conn.setRequestProperty("Cookie", sessionId);
            // 发送POST请求必须设置如下两行
            conn.setDoOutput(true);
            conn.setDoInput(true);
            conn.setReadTimeout(10000);
            // 获取URLConnection对象对应的输出流
            out = new PrintWriter(conn.getOutputStream());
            // 发送请求参数
            out.print(param);
            // flush输出流的缓冲
            out.flush();
            // 定义BufferedReader输入流来读取URL的响应
            in = new BufferedReader(
                    new InputStreamReader(conn.getInputStream()));
            String line;
            while ((line = in.readLine()) != null) {
                result += line;
            }
        } catch (Exception e) {
            log.error("HttpClientUtil 出错", e);
        }
        //使用finally块来关闭输出流、输入流
        finally {
            try {
                if (out != null) {
                    out.close();
                }
                if (in != null) {
                    in.close();
                }
            } catch (IOException e) {
                log.error("HttpClientUtil 出错", e);
            }
        }
        return result;
    }
    
    /**
    * @Description: 获取NCMS_SESSION
    * <AUTHOR>   
    * @date 2025年5月26日 下午2:17:18 
    * @param request
    * @return
     */
    public static String getNcmsSessionId(HttpServletRequest request) {
    	Cookie[] cookies = request.getCookies();
		String sessionId = request.getSession().getId();
		for(int i=0;i<cookies.length;i++) {
			if(cookies[i].getName().equals("NCMS_SESSION")) {
				sessionId = cookies[i].getValue();
			}
		}
		return sessionId;
    }

    public static void main(String[] args) throws Exception {
        String url = "http://localhost:8085/NCMS-TELE/asserts/tpl/tele/billaccount/queryPowerBenchmark?billaccountId=2a89271dec034e4fb41c663cbff3ebc0&prvCode=ZJ";
        String result = HttpClientUtil.post(url, "billaccountId=01ab765facf4460587a50c6f3f4ebe3f&prvCode=ZJ", "20a7688b-10f0-4f65-906f-c24b58bd655d");
        System.err.println(result);
    }
}
