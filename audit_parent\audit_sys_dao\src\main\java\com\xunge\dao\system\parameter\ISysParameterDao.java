package com.xunge.dao.system.parameter;

import com.xunge.core.page.Page;
import com.xunge.model.system.parameter.SysParameterVO;

import java.util.List;
import java.util.Map;

public interface ISysParameterDao {
    /**
     * 修改系统参数
     *
     * @param sysParameterVO
     * @return
     * <AUTHOR>
     */
    public int updateParameter(SysParameterVO sysParameterVO);

    /**
     * 系统参数模糊查询
     *
     * @param paraMap
     * @param pageNumber
     * @param pageSize
     * @return
     * <AUTHOR>
     */
    public Page<List<SysParameterVO>> queryParameter(Map<String, Object> paraMap, int pageNumber, int pageSize);

    /**
     * 根据系统参数id查询一个参数详情
     *
     * @param paraId
     * @return
     * <AUTHOR>
     */
    public SysParameterVO getParameter(String paraId);

    /**
     * 根据系统参数Code查询一个参数详情
     *
     * @param paraMap
     * @return
     */
    public SysParameterVO getParameterByCode(Map<String, Object> paraMap) ;

    /**
     * 启用系统参数
     *
     * @param paraId
     * @return
     * <AUTHOR>
     */
    public int openParameter(String paraId);

    /**
     * 停用系统参数
     *
     * @param paraId
     * @return
     * <AUTHOR>
     */
    public int stopParameter(String paraId);

    /**
     * 根据省份prvId paraCode 查询
     *
     * @param paraMap
     * @return
     */
    public SysParameterVO queryParameter(Map<String, Object> paraMap);
}
