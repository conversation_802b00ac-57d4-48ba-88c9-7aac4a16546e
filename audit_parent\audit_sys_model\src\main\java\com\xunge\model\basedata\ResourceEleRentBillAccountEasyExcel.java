package com.xunge.model.basedata;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.Objects;

@Data
public class ResourceEleRentBillAccountEasyExcel {

    @ExcelProperty(value = {"资源信息", "审核状态"}, index = 1)
    @Getter
    @Setter
    private String auditingState;

    @ExcelProperty(value = {"资源信息", "资源标识"} , index = 2)
    private String baseResourceCuid;
    @ExcelProperty(value = {"资源信息", "资源Id"} , index = 0)
    private String baseResourceId;
    @ExcelProperty(value = {"资源信息", "资源名称"} , index = 3)
    private String baseResourceName;

    @ExcelProperty(value = {"资源信息", "省份标识"} , index = 4)
    private String prvSname;

    @ExcelProperty(value = {"资源信息", "所属地市"} , index = 5)
    private String pregName;

    @ExcelProperty(value = {"资源信息", "所属区县"} , index = 6)
    private String regName;

    @ExcelProperty(value = {"资源信息", "所属部门"} , index = 7)
    private String belongDept;
    @ExcelProperty(value = {"资源信息", "位置点/机房类型"} , index = 8)
    @Getter
    @Setter
    private String baseresourceCategory;

    @ExcelProperty(value =  "资源类型" )
    private String baseresourceType;


    @ExcelProperty(value = {"资源信息", "生命周期状态"} , index = 9)
    @Getter
    @Setter
    private String baseresourceState;

    @ExcelProperty(value = {"资源信息", "业务类型"} , index = 10)
    @Getter
    @Setter
    private String serviceSiteType;

    @ExcelProperty(value = {"资源信息", "所属铁塔公司站点编码"} , index = 11)
    private String towerSiteCode;

    @ExcelProperty(value = {"资源信息", "站点cuid"} , index = 12)
    private String basesiteCuid;

    @ExcelProperty(value = {"资源信息", "资源地址"}  , index = 13)
    private String baseresourceAddress;

    @ExcelProperty(value = {"资源信息", "经度"} , index = 14)
    private String baseresourceLongitude;

    @ExcelProperty(value = {"资源信息", "纬度"} , index = 15)
    private String baseresourceLatitude;

    @ExcelProperty(value = {"资源信息", "主设备总额定功率"}, index = 16)
    private String equipmentPower;

    @ExcelProperty(value = {"资源信息", "空调总额定功率"} , index = 17)
    private String airconditionerPower;

    @ExcelProperty(value = {"资源信息", "最近功率变动"} , index = 18)
    private String latestChangeRange;

    @ExcelProperty(value = "是否免电费" )
    private String freeFlag;

    @ExcelProperty(value = {"资源信息", "是否免电费"}, index = 19)
    private String freeEleFlag;

    @ExcelProperty(value = {"资源信息", "是否免租费"}, index = 20)
    private String freeRentFlag;

    @ExcelProperty(value = {"资源信息", "数据来源"}, index = 21)
    @Getter
    @Setter
    private String dataFrom;

    @ExcelProperty(value = {"资源信息", "入网时间"}, index = 22)
    private String baseresourceOpendate;

    @ExcelProperty(value = {"资源信息", "退网时间"}, index = 23)
    private String baseresourceStopdate;

    @ExcelProperty(value = {"资源信息", "产权性质"}, index = 24)
    @Getter
    @Setter
    private String roomProperty;

    @ExcelProperty(value = {"资源信息", "产权单位"}, index = 25)
    @Getter
    @Setter
    private String roomOwner;

    @ExcelProperty(value = {"资源信息", "备注"}, index = 26)
    private String baseresourceNote;

    @ExcelProperty(value = {"网络电费报账点信息", "电费报账点类型"}, index = 27)
    @Getter
    @Setter
    private String eleBillAccountType;

    @ExcelProperty(value = {"网络电费报账点信息", "报账点状态"}, index = 28)
    @Getter
    @Setter
    private String eleBillaccountState;

    @ExcelProperty(value = {"网络电费报账点信息", "报账点编码"}, index = 29)
    private String eleBillAccountCode;

    @ExcelProperty(value = {"网络电费报账点信息", "报账点名称"}, index = 30)
    private String eleBillAccountName;

    @ExcelProperty(value = {"网络电费报账点信息", "报账点审核状态"}, index = 31)
    @Getter
    @Setter
    private String eleAuditingState;

    @ExcelProperty(value = {"网络电费报账点信息", "所属成本中心"}, index = 32)
    private String eleCostCenterName;

    @ExcelProperty(value = {"网络电费报账点信息", "成本中心编码"}, index = 33)
    private String eleCostCenter;

    @ExcelProperty(value = {"网络电费报账点信息", "备注"} , index = 34)
    private String eleBillaccountNote;

    @ExcelProperty(value = {"网络电费报账点信息", "合同或固化编码"}, index = 35)
    private String eleContractCode;

    @ExcelProperty(value = {"网络电费报账点信息", "合同或固化名称"}, index = 36)
    private String eleContractName;

    @ExcelProperty(value = {"网络电费报账点信息", "合同或固化期始"}, index = 37)
    private String eleContractStartdate;

    @ExcelProperty(value = {"网络电费报账点信息", "合同或固化期终"}, index = 38)
    private String eleContractEnddate;

    @ExcelProperty(value = {"网络电费报账点信息", "供电类型"}, index = 39)
    @Getter
    @Setter
    private String eleSupplyMethod;

    @ExcelProperty(value = {"网络电费报账点信息", "是否包干"}, index = 40)
    @Getter
    @Setter
    private String eleIsIncludeAll;

    @ExcelProperty(value = {"网络电费报账点信息", "供应商名称"}, index = 41)
    private String eleSupplierName;

    @ExcelProperty(value = {"网络电费报账点信息", "供应商编码"}, index = 42)
    private String eleSupplierCode;
    @ExcelProperty(value = "电费报账点Id")
    private String eleBillAccountId;


    @ExcelProperty(value = {"特殊电费报账点信息", "报账点类型"}, index = 43)
    @Getter
    @Setter
    private String specialEleBillAccountType;

    @ExcelProperty(value = {"特殊电费报账点信息", "报账点状态"}, index = 44)
    @Getter
    @Setter
    private String specialEleBillaccountState;

    @ExcelProperty(value = {"特殊电费报账点信息", "报账点编码"}, index = 45)
    private String specialEleBillAccountCode;
    @ExcelProperty(value = "特殊电费报账点Id")
    private String specialEleBillAccountId;
    @ExcelProperty(value = {"特殊电费报账点信息", "报账点名称"}, index = 46)
    private String specialEleBillAccountName;

    @ExcelProperty(value = {"特殊电费报账点信息", "报账点审核状态"}, index = 47)
    @Getter
    @Setter
    private String specialEleAuditingState;

    @ExcelProperty(value = {"特殊电费报账点信息", "所属成本中心"}, index = 48)
    private String specialEleCostCenterName;

    @ExcelProperty(value = {"特殊电费报账点信息", "成本中心编码"}, index = 49)
    private String specialEleCostCenter;

    @ExcelProperty(value = {"特殊电费报账点信息", "备注"}, index = 50)
    private String specialEleBillaccountNote;

    @ExcelProperty(value = {"特殊电费报账点信息", "合同或固化编码"}, index = 51)
    private String specialEleContractCode;

    @ExcelProperty(value = {"特殊电费报账点信息", "合同或固化名称"}, index = 52)
    private String specialEleContractName;

    @ExcelProperty(value = {"特殊电费报账点信息", "合同或固化期始"}, index = 53)
    private String specialEleContractStartdate;

    @ExcelProperty(value = {"特殊电费报账点信息", "合同或固化期终"}, index = 54)
    private String specialEleContractEnddate;

    @ExcelProperty(value = {"特殊电费报账点信息", "供电类型"}, index = 55)
    @Getter
    @Setter
    private String specialEleSupplyMethod;

    @ExcelProperty(value = {"特殊电费报账点信息", "是否包干"}, index = 56)
    @Getter
    @Setter
    private String specialEleIsIncludeAll;

    @ExcelProperty(value = {"特殊电费报账点信息", "供应商名称"}, index = 57)
    private String specialEleSupplierName;

    @ExcelProperty(value = {"特殊电费报账点信息", "供应商编码"}, index = 58)
    private String specialEleSupplierCode;



    @ExcelProperty(value = {"塔维电费报账点信息", "报账点类型"}, index = 59)
    @Getter
    @Setter
    private String teleBillAccountType;

    @ExcelProperty(value = {"塔维电费报账点信息", "报账点状态"}, index = 60)
    @Getter
    @Setter
    private String teleBillaccountState;

    @ExcelProperty(value = {"塔维电费报账点信息", "报账点编码"}, index = 61)
    private String teleBillAccountCode;

    @ExcelProperty(value = {"塔维电费报账点信息", "报账点名称"}, index = 62)
    private String teleBillAccountName;

    @ExcelProperty(value = {"塔维电费报账点信息", "报账点审核状态"}, index = 63)
    @Getter
    @Setter
    private String teleAuditingState;

    @ExcelProperty(value = {"塔维电费报账点信息", "所属成本中心"}, index = 64)
    private String teleCostCenterName;

    @ExcelProperty(value = {"塔维电费报账点信息", "成本中心编码"}, index = 65)
    private String teleCostCenter;

    @ExcelProperty(value = {"塔维电费报账点信息", "备注"}, index = 66)
    private String teleBillaccountNote;

    @ExcelProperty(value = {"塔维电费报账点信息", "合同或固化编码"}, index = 67)
    private String teleContractCode;

    @ExcelProperty(value = {"塔维电费报账点信息", "合同或固化名称"}, index = 68)
    private String teleContractName;

    @ExcelProperty(value = {"塔维电费报账点信息", "合同或固化期始"}, index = 69)
    private String teleContractStartdate;

    @ExcelProperty(value = {"塔维电费报账点信息", "合同或固化期终"}, index = 70)
    private String teleContractEnddate;

    @ExcelProperty(value = {"塔维电费报账点信息", "供电类型"}, index = 71)
    @Getter
    @Setter
    private String teleSupplyMethod;

    @ExcelProperty(value = {"塔维电费报账点信息", "是否包干"}, index = 72)
    @Getter
    @Setter
    private String teleIsIncludeAll;

    @ExcelProperty(value = {"塔维电费报账点信息", "供应商名称"}, index = 73)
    private String teleSupplierName;

    @ExcelProperty(value = {"塔维电费报账点信息", "供应商编码"}, index = 74)
    private String teleSupplierCode;
    @ExcelProperty(value = "塔维电费报账点Id")
    private String teleBillAccountId;
    @ExcelProperty(value = {"租费报账点信息", "报账点类型"},index = 75)
    @Getter
    @Setter
    private String rentBillAccountType;
    @ExcelProperty(value = {"租费报账点信息", "报账点状态"},index = 76)
    @Getter
    @Setter
    private String rentBillaccountState;
    @ExcelProperty(value = {"租费报账点信息", "报账点编码"},index = 77)
    private String rentBillAccountCode;
    @ExcelProperty(value = {"租费报账点信息", "报账点名称"},index = 78)
    private String rentBillAccountName;
    @ExcelProperty(value = {"租费报账点信息", "报账点审核状态"},index = 79)
    @Getter
    @Setter
    private String rentAuditingState;
    @ExcelProperty(value = {"租费报账点信息", "所属成本中心"},index = 80)
    private String rentCostCenterName;
    @ExcelProperty(value = {"租费报账点信息", "成本中心编码"},index = 81)
    private String rentCostCenter;
    @ExcelProperty(value = {"租费报账点信息", "备注"},index = 82)
    private String rentBillaccountNote;
    @ExcelProperty(value = {"租费报账点信息", "合同或固化编码"},index = 83)
    private String rentContractCode;
    @ExcelProperty(value = {"租费报账点信息", "合同或固化名称"},index = 84)
    private String rentContractName;
    @ExcelProperty(value = {"租费报账点信息", "合同或固化期始"},index = 85)
    private String rentContractStartdate;
    @ExcelProperty(value = {"租费报账点信息", "合同或固化期终"},index = 86)
    private String rentContractEnddate;
    @ExcelProperty(value = {"租费报账点信息", "供电类型"},index = 87)
    @Getter
    @Setter
    private String rentSupplyMethod;
    @ExcelProperty(value = {"租费报账点信息", "是否包干"},index = 88)
    private String rentIsIncludeAll;
    @ExcelProperty(value = {"租费报账点信息", "供应商名称"},index = 89)
    private String rentSupplierName;
    @ExcelProperty(value = {"租费报账点信息", "供应商编码"},index = 90)
    private String rentSupplierCode;
    @ExcelProperty(value = "租费报账点Id")
    private String rentBillAccountId;

    @ExcelProperty(value = {"特殊租费报账点信息", "报账点类型"},index = 91)
    @Getter
    @Setter
    private String specialRentBillAccountType;
    @ExcelProperty(value = {"特殊租费报账点信息", "报账点状态"},index = 92)
    @Getter
    @Setter
    private String specialRentBillaccountState;
    @ExcelProperty(value = {"特殊租费报账点信息", "报账点编码"},index = 93)
    private String specialRentBillAccountCode;
    @ExcelProperty(value = {"特殊租费报账点信息", "报账点名称"},index = 94)
    private String specialRentBillAccountName;
    @ExcelProperty(value = {"特殊租费报账点信息", "报账点审核状态"},index = 95)
    @Getter
    @Setter
    private String specialRentAuditingState;
    @ExcelProperty(value = {"特殊租费报账点信息", "所属成本中心"},index = 96)
    private String specialRentCostCenterName;
    @ExcelProperty(value = {"特殊租费报账点信息", "成本中心编码"},index = 97)
    private String specialRentCostCenter;
    @ExcelProperty(value = {"特殊租费报账点信息", "备注"},index = 98)
    private String specialRentBillaccountNote;
    @ExcelProperty(value = {"特殊租费报账点信息", "合同或固化编码"},index = 99)
    private String specialRentContractCode;
    @ExcelProperty(value = {"特殊租费报账点信息", "合同或固化名称"},index = 100)
    private String specialRentContractName;
    @ExcelProperty(value = {"特殊租费报账点信息", "合同或固化期始"},index = 101)
    private String specialRentContractStartdate;
    @ExcelProperty(value = {"特殊租费报账点信息", "合同或固化期终"},index = 102)
    private String specialRentContractEnddate;
    @ExcelProperty(value = {"特殊租费报账点信息", "供电类型"},index = 103)
    @Getter
    @Setter
    private String specialRentSupplyMethod;
    @ExcelProperty(value = {"特殊租费报账点信息", "是否包干"},index = 104)
    @Getter
    @Setter
    private String specialRentIsIncludeAll;
    @ExcelProperty(value = {"特殊租费报账点信息", "供应商名称"},index = 105)
    private String specialRentSupplierName;
    @ExcelProperty(value = {"特殊租费报账点信息", "供应商编码"},index = 106)
    private String specialRentSupplierCode;
    @ExcelProperty(value = "特殊租费报账点Id")
    private String specialRentBillAccountId;
    // 重写 getter 和 setter 方法
    public String getAuditingState() {
        // 如果需要对 getter 进行自定义处理，可以在这里实现
        return auditingState;
    }

    public void setAuditingState(String auditingState) {
        // 映射逻辑
        if ("0".equals(auditingState)) {
            this.auditingState = "审核通过";
        } else if ("8".equals(auditingState)) {
            this.auditingState = "审核失败";
        } else if ("9".equals(auditingState)) {
            this.auditingState = "审核中";
        } else if ("-1".equals(auditingState)) {
            this.auditingState = "未提交";
        } else if (auditingState == null) {
            this.auditingState = "_";
        } else {
            // 默认情况下保留原始值
            this.auditingState = auditingState;
        }
    }
    public String getBaseresourceCategory() {
        return baseresourceCategory; // Default case if no match is found.
    }

    /**
     * Setter method that accepts a string and sets the internal state based on the provided string.
     * @param baseresourceCategory The string representation of the category.
     */
    public void setBaseresourceCategory(String baseresourceCategory) {
        if (Objects.equals(baseresourceCategory, null)) this.baseresourceCategory = "_";
        else if (Objects.equals(baseresourceCategory, "1")) this.baseresourceCategory = "传输机房";
        else if (Objects.equals(baseresourceCategory, "2")) this.baseresourceCategory = "无线机房";
        else if (Objects.equals(baseresourceCategory, "3")) this.baseresourceCategory = "核心网机房";
        else if (Objects.equals(baseresourceCategory, "4")) this.baseresourceCategory = "数据网机房";
        else if (Objects.equals(baseresourceCategory, "5")) this.baseresourceCategory = "动力环境机房";
        else if (Objects.equals(baseresourceCategory, "6")) this.baseresourceCategory = "用户机房";
        else if (Objects.equals(baseresourceCategory, "7")) this.baseresourceCategory = "IDC机房";
        else if (Objects.equals(baseresourceCategory, "8")) this.baseresourceCategory = "地下进线室";
        else if (Objects.equals(baseresourceCategory, "9")) this.baseresourceCategory = "综合机房";
        else if (Objects.equals(baseresourceCategory, "10")) this.baseresourceCategory = "接入机房";
        else if (Objects.equals(baseresourceCategory, "11")) this.baseresourceCategory = "交换机房";
        else if (Objects.equals(baseresourceCategory, "12")) this.baseresourceCategory = "网管机房";
        else if (Objects.equals(baseresourceCategory, "13")) this.baseresourceCategory = "微波机房";
        else if (Objects.equals(baseresourceCategory, "14")) this.baseresourceCategory = "MDC机房";
        else if (Objects.equals(baseresourceCategory, "15")) this.baseresourceCategory = "MDF机房";
        else if (Objects.equals(baseresourceCategory, "16")) this.baseresourceCategory = "新业务机房";
        else if (Objects.equals(baseresourceCategory, "17")) this.baseresourceCategory = "室外综合机柜";
        else if (Objects.equals(baseresourceCategory, "18")) this.baseresourceCategory = "备件机房";
        else if (Objects.equals(baseresourceCategory, "19")) this.baseresourceCategory = "其他";
        else if (Objects.equals(baseresourceCategory, "20")) this.baseresourceCategory = "室外无线位置点";
        else if (Objects.equals(baseresourceCategory, "21")) this.baseresourceCategory = "室外传输位置点";
        else if (Objects.equals(baseresourceCategory, "22")) this.baseresourceCategory = "全业务位置点";
        else this.baseresourceCategory = null; // Default case if no match is found.
    }
    public String getServiceSiteType() {
        return serviceSiteType;
    }
    public void setServiceSiteType(String serviceSiteType) {
        if ("1".equals(serviceSiteType)) {
            this.serviceSiteType = "核心机房";
        } else if ("2".equals(serviceSiteType)) {
            this.serviceSiteType = "汇聚传输站点";
        } else if ("3".equals(serviceSiteType)) {
            this.serviceSiteType = "基站";
        } else if ("4".equals(serviceSiteType)) {
            this.serviceSiteType = "室分及WLAN";
        } else if ("5".equals(serviceSiteType)) {
            this.serviceSiteType = "家客集客";
        } else if ("6".equals(serviceSiteType)) {
            this.serviceSiteType = "IDC机房";
        } else if ("7".equals(serviceSiteType)) {
            this.serviceSiteType = "基地";
        } else if ("8".equals(serviceSiteType)) {
            this.serviceSiteType = "其他";
        }
        else if (serviceSiteType == null) {
            this.serviceSiteType = "_";
        } else {
            // 默认情况下保留原始值
            this.serviceSiteType = auditingState;
        }
    }
    public String getBaseresourceState() {
        return baseresourceState;
    }
    public void setBaseresourceState(String baseresourceState) {
        if ("1".equals(auditingState)) {
            this.auditingState = "在网";
        } else if ("2".equals(auditingState)) {
            this.auditingState = "工程";
        } else if ("3".equals(auditingState)) {
            this.auditingState = "退网";
        } else if ("-1".equals(auditingState)) {
            this.auditingState = "删除";
        } else if (auditingState == null) {
            this.auditingState = "_";
        } else {
            // 默认情况下保留原始值
            this.auditingState = auditingState;
        }
    }
    public String getDataFrom() {
        return dataFrom;
    }
    public void setDataFrom(String dataFrom) {
        if ("0".equals(dataFrom)) {
            this.dataFrom = "系统录入";
        } else if ("1".equals(dataFrom)) {
            this.dataFrom = "系统导入";
        } else if ("2".equals(dataFrom)) {
            this.dataFrom = "接口采集";
        }  else if (dataFrom == null) {
            this.dataFrom = "_";
        } else {
            // 默认情况下保留原始值
            this.dataFrom = dataFrom;
        }
    }
    public String getRoomProperty() {
        return roomProperty;
    }
    public void setRoomProperty(String roomProperty) {
        if ("1".equals(roomProperty)) {
            this.roomProperty = "自有（自建)";
        } else if ("2".equals(roomProperty)) {
            this.roomProperty = "自有（合建）";
        } else if ("3".equals(roomProperty)) {
            this.roomProperty = "自有（购买）";
        } else if ("4".equals(roomProperty)) {
            this.roomProperty = "租用";
        } else if ("5".equals(roomProperty)) {
            this.roomProperty = "用户所有";
        } else if ("6".equals(roomProperty)) {
            this.roomProperty = "其他";
        } else if ("7".equals(roomProperty)) {
            this.roomProperty = "共建";
        } else if ("8".equals(roomProperty)) {
            this.roomProperty = "置换";
        } else if ("9".equals(roomProperty)) {
            this.roomProperty = "移动自有";
        } else if ("10".equals(roomProperty)) {
            this.roomProperty = "移交铁塔";
        } else if ("11".equals(roomProperty)) {
            this.roomProperty = "租用铁塔";
        } else if ("12".equals(roomProperty)) {
            this.roomProperty = "长租";
        } else if ("13".equals(roomProperty)) {
            this.roomProperty = "借用";
        } else if ("14".equals(roomProperty)) {
            this.roomProperty = "共享";
        }  else if (roomProperty == null) {
            this.roomProperty = "_";
        } else {
            // 默认情况下保留原始值
            this.roomProperty = roomProperty;
        }
    }

    public String getRoomOwner() {
        return roomOwner;
    }
    public void setRoomOwner(String roomOwner) {
        if ("1".equals(roomOwner)) {
            this.roomOwner = "中国铁塔";
        } else if ("2".equals(roomOwner)) {
            this.roomOwner = "中国移动";
        } else if ("3".equals(roomOwner)) {
            this.roomOwner = "中国联通";
        } else if ("4".equals(roomOwner)) {
            this.roomOwner = "中国电信";
        } else if ("5".equals(roomOwner)) {
            this.roomOwner = "中国铁通";
        } else if ("6".equals(roomOwner)) {
            this.roomOwner = "中国广电";
        } else if ("7".equals(roomOwner)) {
            this.roomOwner = "业主";
        } else if ("8".equals(roomOwner)) {
            this.roomOwner = "其他";
        } else if (roomOwner == null) {
            this.roomOwner = "_";
        } else {
            // 默认情况下保留原始值
            this.roomOwner = roomOwner;
        }
    }
    public String getEleBillAccountType() {
        return eleBillAccountType;
    }
    public void setEleBillAccountType(String eleBillAccountType) {
        if ("0".equals(eleBillAccountType)) {
            this.eleBillAccountType = "租费报账点";
        } else if ("1".equals(eleBillAccountType)) {
            this.eleBillAccountType = "自维电费报账点";
        } else if ("2".equals(eleBillAccountType)) {
            this.eleBillAccountType = "铁塔电费报账点";
        }  else if ("3".equals(eleBillAccountType)) {
            this.eleBillAccountType = "代持电费报账点";
        }  else if (eleBillAccountType == null) {
            this.eleBillAccountType = "_";
        } else {
            // 默认情况下保留原始值
            this.eleBillAccountType = eleBillAccountType;
        }
    }
    public String getEleAuditingState() {
        return eleAuditingState;
    }
    public void setEleAuditingState(String eleAuditingState) {
        if ("0".equals(eleAuditingState)) {
            this.eleAuditingState = "审核通过";
        } else if ("-1".equals(eleAuditingState)) {
            this.eleAuditingState = "未提交";
        } else if ("3".equals(eleAuditingState)) {
            this.eleAuditingState = "删除审核中";
        }  else if ("4".equals(eleAuditingState)) {
            this.eleAuditingState = "删除审核通过";
        }  else if ("6".equals(eleAuditingState)) {
            this.eleAuditingState = "删除审核不通过";
        }  else if ("8".equals(eleAuditingState)) {
            this.eleAuditingState = "审核不通过";
        }  else if ("9".equals(eleAuditingState)) {
            this.eleAuditingState = "审核中";
        }  else if (eleAuditingState == null) {
            this.eleAuditingState = "_";
        } else {
            // 默认情况下保留原始值
            this.eleAuditingState = eleAuditingState;
        }
    }
    public String getEleSupplyMethod() {
        return eleSupplyMethod;
    }
    public void setEleSupplyMethod(String eleSupplyMethod) {
        if ("2".equals(eleSupplyMethod)) {
            this.eleSupplyMethod = "转供电";
        } else if ("1".equals(eleSupplyMethod)) {
            this.eleSupplyMethod = "直供电";
        }   else if (eleSupplyMethod == null) {
            this.eleSupplyMethod = "_";
        } else {
            // 默认情况下保留原始值
            this.eleSupplyMethod = eleSupplyMethod;
        }
    }
    public String getEleIsIncludeAll() {
        return eleIsIncludeAll;
    }
    public void setEleIsIncludeAll(String eleIsIncludeAll) {
        if ("0".equals(eleIsIncludeAll)) {
            this.eleIsIncludeAll = "否";
        } else if ("1".equals(eleIsIncludeAll)) {
            this.eleIsIncludeAll = "是";
        }   else if (eleIsIncludeAll == null) {
            this.eleIsIncludeAll = "_";
        } else {
            // 默认情况下保留原始值
            this.eleIsIncludeAll = eleIsIncludeAll;
        }
    }
    public String getSpecialEleBillAccountType() {
        return specialEleBillAccountType;
    }
    public void setSpecialEleBillAccountType(String specialEleBillAccountType) {
        if ("0".equals(specialEleBillAccountType)) {
            this.specialEleBillAccountType = "租费报账点";
        } else if ("1".equals(specialEleBillAccountType)) {
            this.specialEleBillAccountType = "电费特殊报账点";
        } else if ("4".equals(specialEleBillAccountType)) {
            this.specialEleBillAccountType = "一站多路电特殊报账点";
        }   else if (specialEleBillAccountType == null) {
            this.specialEleBillAccountType = "_";
        } else {
            // 默认情况下保留原始值
            this.specialEleBillAccountType = specialEleBillAccountType;
        }
    }
    public String getEleBillaccountState() {
        return eleBillaccountState;
    }
    public void setEleBillaccountState(String eleBillaccountState) {
        if ("0".equals(eleBillaccountState)) {
            this.eleBillaccountState = "启用";
        } else if ("9".equals(eleBillaccountState)) {
            this.eleBillaccountState = "停用";
        } else if ("10".equals(eleBillaccountState)) {
            this.eleBillaccountState = "终止";
        }   else if (eleBillaccountState == null) {
            this.eleBillaccountState = "_";
        } else {
            // 默认情况下保留原始值
            this.eleBillaccountState = eleBillaccountState;
        }
    }
    public String getSpecialEleBillaccountState() {
        return specialEleBillaccountState;
    }
    public void setSpecialEleBillaccountState(String specialEleBillaccountState) {
        if ("0".equals(specialEleBillaccountState)) {
            this.specialEleBillaccountState = "启用";
        } else if ("9".equals(specialEleBillaccountState)) {
            this.specialEleBillaccountState = "停用";
        } else if ("10".equals(specialEleBillaccountState)) {
            this.specialEleBillaccountState = "终止";
        }   else if (specialEleBillaccountState == null) {
            this.specialEleBillaccountState = "_";
        } else {
            // 默认情况下保留原始值
            this.specialEleBillaccountState = specialEleBillaccountState;
        }
    }
    public String getSpecialEleAuditingState() {
        return specialEleAuditingState;
    }
    public void setSpecialEleAuditingState(String specialEleAuditingState) {
        if ("0".equals(specialEleAuditingState)) {
            this.specialEleAuditingState = "审核通过";
        } else if ("-1".equals(specialEleAuditingState)) {
            this.specialEleAuditingState = "未提交";
        } else if ("3".equals(specialEleAuditingState)) {
            this.specialEleAuditingState = "删除审核中";
        }  else if ("4".equals(specialEleAuditingState)) {
            this.specialEleAuditingState = "删除审核通过";
        }  else if ("6".equals(specialEleAuditingState)) {
            this.specialEleAuditingState = "删除审核不通过";
        }  else if ("8".equals(specialEleAuditingState)) {
            this.specialEleAuditingState = "审核不通过";
        }  else if ("9".equals(specialEleAuditingState)) {
            this.specialEleAuditingState = "审核中";
        }  else if (specialEleAuditingState == null) {
            this.specialEleAuditingState = "_";
        } else {
            // 默认情况下保留原始值
            this.specialEleAuditingState = specialEleAuditingState;
        }
    }
    public String getSpecialEleSupplyMethod() {
        return specialEleSupplyMethod;
    }
    public void setSpecialEleSupplyMethod(String specialEleSupplyMethod) {
        if ("2".equals(specialEleSupplyMethod)) {
            this.specialEleSupplyMethod = "转供电";
        } else if ("1".equals(specialEleSupplyMethod)) {
            this.specialEleSupplyMethod = "直供电";
        }   else if (specialEleSupplyMethod == null) {
            this.specialEleSupplyMethod = "_";
        } else {
            // 默认情况下保留原始值
            this.specialEleSupplyMethod = specialEleSupplyMethod;
        }
    }
    public String getSpecialEleIsIncludeAll() {
        return specialEleIsIncludeAll;
    }
    public void setSpecialEleIsIncludeAll(String specialEleIsIncludeAll) {
        if ("0".equals(specialEleIsIncludeAll)) {
            this.specialEleIsIncludeAll = "否";
        } else if ("1".equals(specialEleIsIncludeAll)) {
            this.specialEleIsIncludeAll = "是";
        }   else if (specialEleIsIncludeAll == null) {
            this.specialEleIsIncludeAll= "_";
        } else {
            // 默认情况下保留原始值
            this.specialEleIsIncludeAll = specialEleIsIncludeAll;
        }
    }
    public String getTeleBillaccountState() {
        return teleBillaccountState;
    }
    public void setTeleBillaccountState(String teleBillaccountState) {
        if ("0".equals(teleBillaccountState)) {
            this.teleBillaccountState = "启用";
        } else if ("9".equals(teleBillaccountState)) {
            this.teleBillaccountState = "停用";
        } else if ("10".equals(teleBillaccountState)) {
            this.teleBillaccountState = "终止";
        }   else if (teleBillaccountState == null) {
            this.teleBillaccountState = "_";
        } else {
            // 默认情况下保留原始值
            this.teleBillaccountState = teleBillaccountState;
        }
    }
    public String getTeleAuditingState() {
        return teleAuditingState;
    }
    public void setTeleAuditingState(String teleAuditingState) {
        if ("0".equals(teleAuditingState)) {
            this.teleAuditingState = "审核通过";
        } else if ("-1".equals(teleAuditingState)) {
            this.teleAuditingState = "未提交";
        } else if ("3".equals(teleAuditingState)) {
            this.teleAuditingState = "删除审核中";
        }  else if ("4".equals(teleAuditingState)) {
            this.teleAuditingState = "删除审核通过";
        }  else if ("6".equals(teleAuditingState)) {
            this.teleAuditingState = "删除审核不通过";
        }  else if ("8".equals(teleAuditingState)) {
            this.teleAuditingState = "审核不通过";
        }  else if ("9".equals(teleAuditingState)) {
            this.teleAuditingState = "审核中";
        }  else if (teleAuditingState == null) {
            this.teleAuditingState = "_";
        } else {
            // 默认情况下保留原始值
            this.teleAuditingState = teleAuditingState;
        }
    }

    public String getTeleSupplyMethod() {
        return teleSupplyMethod;
    }
    public void setTeleSupplyMethod(String teleSupplyMethod) {
        if ("2".equals(teleSupplyMethod)) {
            this.teleSupplyMethod = "转供电";
        } else if ("1".equals(teleSupplyMethod)) {
            this.teleSupplyMethod = "直供电";
        }   else if (teleSupplyMethod == null) {
            this.teleSupplyMethod = "_";
        } else {
            // 默认情况下保留原始值
            this.teleSupplyMethod = teleSupplyMethod;
        }
    }
    public String getTeleIsIncludeAll() {
        return teleIsIncludeAll;
    }
    public void setTeleIsIncludeAll(String teleIsIncludeAll) {
        if ("0".equals(teleIsIncludeAll)) {
            this.teleIsIncludeAll = "否";
        } else if ("1".equals(teleIsIncludeAll)) {
            this.teleIsIncludeAll = "是";
        }   else if (teleIsIncludeAll == null) {
            this.teleIsIncludeAll= "_";
        } else {
            // 默认情况下保留原始值
            this.teleIsIncludeAll = teleIsIncludeAll;
        }
    }
    public String getRentBillAccountType() {
        return rentBillAccountType;
    }
    public void setRentBillAccountType(String rentBillAccountType) {
        if ("0".equals(rentBillAccountType)) {
            this.rentBillAccountType = "塔维租费报账点";
        } else if ("1".equals(rentBillAccountType)) {
            this.rentBillAccountType = "自维租费报账点";
        } else if ("4".equals(rentBillAccountType)) {
            this.rentBillAccountType = "三方塔服务费报账点";
        }   else if (rentBillAccountType == null) {
            this.rentBillAccountType = "_";
        } else {
            // 默认情况下保留原始值
            this.specialEleBillAccountType = specialEleBillAccountType;
        }
    }
    public String getRentBillaccountState() {
        return rentBillaccountState;
    }
    public void setRentBillaccountState(String rentBillaccountState) {
        if ("0".equals(rentBillaccountState)) {
            this.rentBillaccountState = "启用";
        } else if ("9".equals(rentBillaccountState)) {
            this.rentBillaccountState = "停用";
        } else if ("10".equals(rentBillaccountState)) {
            this.rentBillaccountState = "终止";
        }   else if (rentBillaccountState == null) {
            this.rentBillaccountState = "_";
        } else {
            // 默认情况下保留原始值
            this.rentBillaccountState = rentBillaccountState;
        }
    }
    public String getRentAuditingState() {
        return rentAuditingState;
    }
    public void setRentAuditingState(String rentAuditingState) {
        if ("0".equals(rentAuditingState)) {
            this.rentAuditingState = "审核通过";
        } else if ("-1".equals(rentAuditingState)) {
            this.rentAuditingState = "未提交";
        } else if ("3".equals(rentAuditingState)) {
            this.rentAuditingState = "删除审核中";
        }  else if ("4".equals(rentAuditingState)) {
            this.rentAuditingState = "删除审核通过";
        }  else if ("6".equals(rentAuditingState)) {
            this.rentAuditingState= "删除审核不通过";
        }  else if ("8".equals(rentAuditingState)) {
            this.rentAuditingState = "审核不通过";
        }  else if ("9".equals(rentAuditingState)) {
            this.rentAuditingState = "审核中";
        }  else if (rentAuditingState == null) {
            this.rentAuditingState = "_";
        } else {
            // 默认情况下保留原始值
            this.rentAuditingState = rentAuditingState;
        }
    }
    public String getRentSupplyMethod() {
        return rentSupplyMethod;
    }
    public void setRentSupplyMethod(String rentSupplyMethod) {
        if ("2".equals(rentSupplyMethod)) {
            this.rentSupplyMethod = "转供电";
        } else if ("1".equals(rentSupplyMethod)) {
            this.rentSupplyMethod = "直供电";
        }   else if (rentSupplyMethod == null) {
            this.rentSupplyMethod = "_";
        } else {
            // 默认情况下保留原始值
            this.rentSupplyMethod = rentSupplyMethod;
        }
    }
    public String getRentIsIncludeAll() {
        return rentIsIncludeAll;
    }
    public void setRentIsIncludeAll(String RentIsIncludeAll) {
        if ("0".equals(rentIsIncludeAll)) {
            this.rentIsIncludeAll = "否";
        } else if ("1".equals(rentIsIncludeAll)) {
            this.rentIsIncludeAll = "是";
        }   else if (rentIsIncludeAll == null) {
            this.rentIsIncludeAll= "_";
        } else {
            // 默认情况下保留原始值
            this.rentIsIncludeAll = rentIsIncludeAll;
        }
    }
    public String getSpecialRentBillAccountType() {
        return specialRentBillAccountType;
    }
    public void setSpecialRentBillAccountType(String specialRentBillAccountType) {
        if ("0".equals(specialRentBillAccountType)) {
            this.specialRentBillAccountType = "塔维租费报账点";
        } else if ("1".equals(specialRentBillAccountType)) {
            this.specialRentBillAccountType = "租费特殊报账点";
        } else if ("2".equals(specialRentBillAccountType)) {
            this.specialRentBillAccountType = "三方塔特殊报账点";
        } else if ("3".equals(specialRentBillAccountType)) {
            this.specialRentBillAccountType = "一站多合同特殊报账点";
        }   else if (specialRentBillAccountType == null) {
            this.specialRentBillAccountType = "_";
        } else {
            // 默认情况下保留原始值
            this.specialEleBillAccountType = specialEleBillAccountType;
        }
    }
    public String getSpecialRentBillaccountState() {
        return specialRentBillaccountState;
    }
    public void setSpecialRentBillaccountState(String specialRentBillaccountState) {
        if ("0".equals(specialRentBillaccountState)) {
            this.specialRentBillaccountState = "启用";
        } else if ("9".equals(specialRentBillaccountState)) {
            this.specialRentBillaccountState = "停用";
        } else if ("10".equals(specialRentBillaccountState)) {
            this.specialRentBillaccountState = "终止";
        }   else if (specialRentBillaccountState == null) {
            this.specialRentBillaccountState = "_";
        } else {
            // 默认情况下保留原始值
            this.specialRentBillaccountState = specialRentBillaccountState;
        }
    }
    public String getSpecialRentAuditingState() {
        return specialRentAuditingState;
    }
    public void setSpecialRentAuditingState(String specialRentAuditingState) {
        if ("0".equals(specialRentAuditingState)) {
            this.specialRentAuditingState = "审核通过";
        } else if ("-1".equals(specialRentAuditingState)) {
            this.specialRentAuditingState = "未提交";
        } else if ("3".equals(specialRentAuditingState)) {
            this.specialRentAuditingState = "删除审核中";
        }  else if ("4".equals(specialRentAuditingState)) {
            this.specialRentAuditingState = "删除审核通过";
        }  else if ("6".equals(specialRentAuditingState)) {
            this.specialRentAuditingState= "删除审核不通过";
        }  else if ("8".equals(specialRentAuditingState)) {
            this.specialRentAuditingState = "审核不通过";
        }  else if ("9".equals(specialRentAuditingState)) {
            this.specialRentAuditingState = "审核中";
        }  else if (specialRentAuditingState == null) {
            this.specialRentAuditingState = "_";
        } else {
            // 默认情况下保留原始值
            this.specialRentAuditingState = specialRentAuditingState;
        }
    }
    public String getSpecialRentSupplyMethod() {
        return specialRentSupplyMethod;
    }
    public void setSpecialRentSupplyMethod(String specialRentSupplyMethod) {
        if ("2".equals(specialRentSupplyMethod)) {
            this.specialRentSupplyMethod = "转供电";
        } else if ("1".equals(specialRentSupplyMethod)) {
            this.specialRentSupplyMethod = "直供电";
        }   else if (specialRentSupplyMethod == null) {
            this.specialRentSupplyMethod = "_";
        } else {
            // 默认情况下保留原始值
            this.specialRentSupplyMethod = specialRentSupplyMethod;
        }
    }
    public String getSpecialRentIsIncludeAll() {
        return specialRentIsIncludeAll;
    }
    public void setSpecialRentIsIncludeAll(String specialRentIsIncludeAll) {
        if ("0".equals(specialRentIsIncludeAll)) {
            this.specialRentIsIncludeAll = "否";
        } else if ("1".equals(specialRentIsIncludeAll)) {
            this.specialRentIsIncludeAll = "是";
        }   else if (specialRentIsIncludeAll == null) {
            this.specialRentIsIncludeAll= "_";
        } else {
            // 默认情况下保留原始值
            this.specialRentIsIncludeAll = specialRentIsIncludeAll;
        }
    }

    // Getters and setters omitted for brevity
}
