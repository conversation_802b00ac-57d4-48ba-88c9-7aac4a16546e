package com.xunge.dao.selfelec.accrualoffs;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

import com.xunge.model.selfelec.accrualoffs.AccrualOffsDetail;


public interface AccrualoffsBanlanceMapper {

	/** 
	* @Description: 查询二次汇总明细 （差额冲销）
	* <AUTHOR>   
	* @date 2023年9月22日 上午10:47:14 
	* @param params 查询参数
	* @return  
	*/ 
	List<AccrualOffsDetail> querySecondDetailBanlance(Map<String, Object> params);

	/**
	 * 保存草稿明细（差额冲销）
	 * @param details 明细
	 */
	void insertOffsDraftDetail(List<AccrualOffsDetail> details);

	/**
	 * 获取草稿明细（差额冲销）
	 * @param params 参数
	 * @return
	 */
	List<AccrualOffsDetail> queryDraftDetail(Map<String, Object> params);

	/**
	 * 获取冲销总金额（差额冲销）
	 * @param params 参数
	 * @return
	 */
	BigDecimal queryTotalOffsAmount(Map<String, Object> params);

	/**
	 * 查询冲销明细（差额冲销）
	 * @param offsId 冲销单id
	 * @param secondBillamountId 二次汇总id
	 * @return
	 */
	AccrualOffsDetail queryAccrualOffsDetail(@Param("offsId")String offsId, @Param("secondBillamountId")String secondBillamountId);

	/**
	 * 还原二次汇总冲销金额（差额冲销）
	 * @param secondBillamountId 二次汇总id
	 * @param offsAmount 冲销金额
	 */
	void returnSecondBillamounts(@Param("secondBillamountId")String secondBillamountId, @Param("offsAmount")BigDecimal offsAmount);

	/**
	 * 还原汇总冲销金额（差额冲销）
	 * @param billamountId 汇总id
	 * @param offsAmount 冲销金额
	 */
	void returnBillamounts(@Param("billamountId")String billamountId, @Param("offsAmount")BigDecimal offsAmount);

	/**
	 * 删除冲销草稿（差额冲销）
	 * @param offsId 冲销单id
	 * @param secondBillamountId 二次汇总id
	 * @return
	 */
	void deleteOffsDraftDetail(@Param("offsId")String offsId, @Param("secondBillamountId")String secondBillamountId);

	/**
	 * 删除冲销明细（差额冲销）
	 * @param detail 明细
	 */
	void deleteOffsDetail(AccrualOffsDetail detail);

	/**
	 * 更新二次汇总冲销金额
	 * @param secondBillamountId 二次汇总id
	 * @param offsAmount 冲销金额
	 */
	void updateSecondBillamounts(@Param("secondBillamountId")String secondBillamountId, @Param("offsAmount")BigDecimal offsAmount);

	/**
	 * 更新汇总冲销金额
	 * @param billamountId 汇总id
	 * @param offsAmount 冲销金额
	 */
	void updateBillamounts(@Param("billamountId")String billamountId, @Param("offsAmount")BigDecimal offsAmount);

	/**
	 * 根据冲销单id删除冲销草稿明细（差额冲销）
	 * @param offsId
	 */
	void deleteDetailDraft(String offsId);

	/**
	 * 根据冲销单id查询冲销明细
	 * @param offsId
	 * @return
	 */
	List<AccrualOffsDetail> queryOffsDetail(String offsId);

	/**
	 * 查询汇总单明细是否全部包含
	 * @param params
	 * @return
	 */
	List<String> queryBanlanceBillamounts(Map<String, Object> params);

	/**
	 * 查询可冲销的二次汇总单
	 * @param params
	 * @return
	 */
	List<AccrualOffsDetail> querySecondBillamounts(Map<String, Object> params);

	/**
	 * 查询二次汇总剩余可冲销金额
	 * @param secondBillamountId
	 * @return
	 */
	BigDecimal querySecondLeftAmount(String secondBillamountId);
    
}