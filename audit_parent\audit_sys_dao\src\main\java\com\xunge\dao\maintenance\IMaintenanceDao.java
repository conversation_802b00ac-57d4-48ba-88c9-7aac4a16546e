package com.xunge.dao.maintenance;

import com.xunge.model.selfrent.billamount.BillamountLogVO;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

public interface IMaintenanceDao {

    Map<String, Object> queryRentNum(String auditOrderCode);

    Map<String, Object> queryTowerNum(String auditOrderCode);

    Map<String, Object> queryETowerNum(String auditOrderCode);

    Map<String, Object> queryEleAfterNum(String auditOrderCode);

    Map<String, Object> queryElePrepayNum(String auditOrderCode);

    Map<String, Object> queryEleSheetNum(String auditOrderCode);

    Map<String, Object> queryElePowerNum(String auditOrderCode);

    Map<String, Object> queryEleTowerNum(String auditOrderCode);

    int insertSysLog(BillamountLogVO billamountLog);

    int updateMaintenanceCheckState(Map<String, Object> updateMap);

    int updateDetailByIds(Map<String, Object> updateMap);

    int updatePaymentByIds(Map<String, Object> updateMap);

    Map<String, Object> queryCodeByBillNum(Map<String, Object> updateMap);

    int updateBillState(Map<String, Object> updateMap);

    String getBillClaimNumByBillAmountId(Map<String, Object> map);

    Map<String, Object> queryOilTowerNum(String auditOrderCode);

    List<BigDecimal> queryEleAfterDetail(String auditOrderCode);

    List<BigDecimal> queryEleTowerDetail(String auditOrderCode);

    List<BigDecimal> queryRentNumDetail(String auditOrderCode);

    List<BigDecimal> queryTowerNumDetail(String auditOrderCode);

    List<BigDecimal> queryOilTowerDetail(String auditOrderCode);

    List<BigDecimal> queryElePrepayDetail(String auditOrderCode);

    List<BigDecimal> queryEleSheetDetail(String auditOrderCode);

    List<BigDecimal> queryElePowerDetail(String auditOrderCode);

    Map<String, Object> queryEleAccTowerNum(String auditOrderCode);

    List<BigDecimal> queryEleAccTowerDetail(String auditOrderCode);

    Map<String, Object> queryEleAccCodeByBillNum(Map<String, Object> updateMap);

    int updateBillAmount(Map<String, Object> updateMap);

    int updateJTTableByIds(Map<String, Object> updateMap);

    int updateJTSnapTableByIds(Map<String, Object> updateMap);

    String getJTBillClaimNumByBillAmountId(Map<String, Object> map);

    Map<String, Object> queryRentAccrualNum(String auditOrderCode);

    List<BigDecimal> queryRentAccrualDetail(String auditOrderCode);

    Map<String, Object> queryTowerAccrualNum(String auditOrderCode);

    Map<String, Object> queryTowerAccrualManualNum(String auditOrderCode);

    Map<String, Object> queryEleOffsNum(String auditOrderCode);

    Map<String, Object> queryRentOffsNum(String auditOrderCode);

    Map<String, Object> queryTowerOffsNum(String auditOrderCode);

    Map<String, Object> queryCodeByBillNumForRentAccrual(String billNum);

    Map<String, Object> queryCodeByBillNumForTowerAccrual(String billNum);

    Map<String, Object> queryCodeByBillNumForTowerAccrualManual(String billNum);

    Map<String, Object> queryCodeByBillNumForEleOffs(String billNum);

    Map<String, Object> queryCodeByBillNumForRentOffs(String billNum);

    Map<String, Object> queryCodeByBillNumForTowerOffs(String billNum);

    int updateErpReturnCodeForRentAccrual(Map<String, Object> updateMap);

    int updateErpReturnCodeForEleOffs(Map<String, Object> updateMap);

    int updateErpReturnCodeForRentOffs(Map<String, Object> updateMap);

    int updateErpReturnCodeForTowerOffs(Map<String, Object> updateMap);

    int updatePushStateForRentAccrual(Map<String, Object> updateMap);

    int updateErpReturnCodeForTowerAccrual(Map<String, Object> updateMap);

    int updateErpReturnCodeForTowerAccrualManual(Map<String, Object> updateMap);

    int updatePushStateForTowerAccrual(Map<String, Object> updateMap);

    int updatePushStateForTowerAccrualManual(Map<String, Object> updateMap);

    int updateErpReturnCodeForETowerAccountsummary(Map<String, Object> updateMap);

    int updatePushStateForETowerAccountsummary(Map<String, Object> updateMap);

    int updatePushStateForEleOffs(Map<String, Object> updateMap) ;

    int updatePushStateFoRentOffs(Map<String, Object> updateMap) ;

    int updatePushStateForTowerOffs(Map<String, Object> updateMap);
}
