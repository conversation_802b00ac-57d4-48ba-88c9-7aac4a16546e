package com.xunge.dto.selfelec.payment;

import com.xunge.core.util.CollectionUtil;
import com.xunge.dto.selfelec.AuthorityUser;
import com.xunge.dto.selfelec.PageInfo;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.List;

/**
 * 报账点电费查询条件
 *
 * <AUTHOR>
 * @date 2021/11/9 10:13
 */
@Getter
@Setter
@ToString
public class PaymentQueryDto extends PageInfo implements Serializable {
    private static final long serialVersionUID = 1093604960800399564L;
    private String paymentIsComplete;
    private String paymentDataFrom;
    private String billaccountCodeOrName;
    private String paymentCode;
    private String contractCodeOrName;
    private String pregId;
    /**
     * 前端传数组regIds，已经没用了
     */
    private String regId;
    private List<String> regIds;
    private String auditingState;
    private String overLoss;
    private String overflow;
    private String startdateOpen;
    private String startdateClose;
    private String enddateOpen;
    private String enddateClose;
    private Integer supplyMethod;
    private Integer billaccountType;
    private String userCodeOrName;
    private String billamountDateOpen;
    private String billamountDateClose;
    private String financeDateOpen;
    private String financeDateClose;
    private String isFinance;
    private String newFlag;
    private String billamountState;
    private String dateSort;
    private String searchKeywords;
    private String overFlowType;
    private String overProof;
    private String billaccountCode;
    private String billaccountName;
    private String stayAuditingUser;
    private String imageaiResult;
    /**
     * DetailMaintain:报账点维护页面,包含更多字段
     */
    private String pageName;

    private String auditingUserId;
    private String taskDefKey;
    private String queryIdList;
    private String emergencyDegree;
    private String submitState;
    //系統計算金額
    private String billAmountSys;
    /**
     * 0：普通报账点 1：特殊报账点 ，默认为0
     */
    private Integer isSpecial = 0;
    /**
     * 导出选中缴费单id
     */
    private List<String> billaccountpaymentdetailIds;

    // 普服资源
    private Integer ifTeleCmnServ;
    // 5G标识
    private Integer fivegFlag;

    private String meterCode;

    private Integer ratioParam;

    /**
     * 当前审核节点名称
     */
    private String auditNodeName;

    private String isAttachmentFlag;

    private String baseresourceCode;

    private String siteCodeType;

    //扩展字段
    private String baseresourceState;

    private List<String> overFlowTypeList;

    private Boolean overFlowType6=false;

    private List<String> paymentCodeList;

    private AuthorityUser authorityUser;

    private List<String> meterIdList;
    /**
     * 对应user_code和user_name模糊查询结果，如果为null，则联表查询
     */
    private List<String> paymentUserIdList;
    /**
     * 当前审核用户ID
     */
    private List<String> stayAuditingUserIdList;
    /**
     * 是否为导出
     */
    private boolean isExport=false;

    /**
     * <option value="">-报账点费用类型-</option>
     * <option value="1">自维</option>
     * <option value="2">塔维</option>
     * <option value="3">代持</option>
     */
    private Integer billaccountAmountType;
    /**
     * <option value="">-新能源报账点类型-</option>
     * <option value="4">一站多路电特殊报账点</option>
     * <option value="1">光伏报账点</option>
     * <option value="2">风能报账点</option>
     * <option value="3">水电报账点</option>
     * <option value="5">其他新能源报账点</option>
     */
    private Integer newEnergyBillaccountType;

    //todo:如果需添加字段，请同步添加 ncms-exporter 项目里面的该实体类，不然实体转换会出错，影响导出功能
}
