package com.xunge.model.finance.ext.accClaim.accwsdl;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import java.math.BigDecimal;


/**
 * &lt;p&gt;WITHDRAWAL_LINE_ITEM complex type的 Java 类。
 * <p>
 * &lt;p&gt;以下模式片段指定包含在此类中的预期内容。
 * <p>
 * &lt;pre&gt;
 * &amp;lt;complexType name="WITHDRAWAL_LINE_ITEM"&amp;gt;
 * &amp;lt;complexContent&amp;gt;
 * &amp;lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&amp;gt;
 * &amp;lt;sequence&amp;gt;
 * &amp;lt;element name="PRI_KEY" type="{http://www.w3.org/2001/XMLSchema}string"/&amp;gt;
 * &amp;lt;element name="SOURCE_DOC_LINE_NUM" type="{http://www.w3.org/2001/XMLSchema}string"/&amp;gt;
 * &amp;lt;element name="CHARGE_PARTY_TYPE_CODE" type="{http://www.w3.org/2001/XMLSchema}string"/&amp;gt;
 * &amp;lt;element name="CHARGE_PARTY_NUM" type="{http://www.w3.org/2001/XMLSchema}string"/&amp;gt;
 * &amp;lt;element name="BUSINESS_MAJOR_CLASS_CODE" type="{http://www.w3.org/2001/XMLSchema}string"/&amp;gt;
 * &amp;lt;element name="BUSINESS_MINOR_CLASS_CODE" type="{http://www.w3.org/2001/XMLSchema}string"/&amp;gt;
 * &amp;lt;element name="BUSINESS_ACTIVITY_CODE" type="{http://www.w3.org/2001/XMLSchema}string"/&amp;gt;
 * &amp;lt;element name="BUDGET_KEY" type="{http://www.w3.org/2001/XMLSchema}string"/&amp;gt;
 * &amp;lt;element name="COST_CENTER_CODE" type="{http://www.w3.org/2001/XMLSchema}string"/&amp;gt;
 * &amp;lt;element name="CONTRACT_NUMBER" type="{http://www.w3.org/2001/XMLSchema}string"/&amp;gt;
 * &amp;lt;element name="BUDGET_CONTRACT_LINE_NUM" type="{http://www.w3.org/2001/XMLSchema}string"/&amp;gt;
 * &amp;lt;element name="SUMMARY" type="{http://www.w3.org/2001/XMLSchema}string"/&amp;gt;
 * &amp;lt;element name="BOOK_AMOUNT" type="{http://www.w3.org/2001/XMLSchema}decimal"/&amp;gt;
 * &amp;lt;element name="CURRENCY_CODE" type="{http://www.w3.org/2001/XMLSchema}string"/&amp;gt;
 * &amp;lt;element name="CURRENCY_RATE" type="{http://www.w3.org/2001/XMLSchema}decimal"/&amp;gt;
 * &amp;lt;element name="MARKET_CODE" type="{http://www.w3.org/2001/XMLSchema}string"/&amp;gt;
 * &amp;lt;element name="PRODUCT_CODE" type="{http://www.w3.org/2001/XMLSchema}string"/&amp;gt;
 * &amp;lt;element name="PURCHASE_OBJECT_CODE" type="{http://www.w3.org/2001/XMLSchema}string"/&amp;gt;
 * &amp;lt;element name="MC_BACKUP1" type="{http://www.w3.org/2001/XMLSchema}string"/&amp;gt;
 * &amp;lt;element name="MC_BACKUP2" type="{http://www.w3.org/2001/XMLSchema}string"/&amp;gt;
 * &amp;lt;element name="MC_BACKUP3" type="{http://www.w3.org/2001/XMLSchema}string"/&amp;gt;
 * &amp;lt;element name="PROJECT_NUM" type="{http://www.w3.org/2001/XMLSchema}string"/&amp;gt;
 * &amp;lt;element name="PO_NUMBER" type="{http://www.w3.org/2001/XMLSchema}string"/&amp;gt;
 * &amp;lt;element name="PO_LINE_NUM" type="{http://www.w3.org/2001/XMLSchema}decimal"/&amp;gt;
 * &amp;lt;element name="SOURCE_SCM_ORDER_NUM" type="{http://www.w3.org/2001/XMLSchema}string"/&amp;gt;
 * &amp;lt;element name="SOURCE_SCM_LINE_NUM" type="{http://www.w3.org/2001/XMLSchema}string"/&amp;gt;
 * &amp;lt;element name="DESCRIPTION" type="{http://www.w3.org/2001/XMLSchema}string"/&amp;gt;
 * &amp;lt;element name="ATTRIBUTE1" type="{http://www.w3.org/2001/XMLSchema}string"/&amp;gt;
 * &amp;lt;element name="ATTRIBUTE2" type="{http://www.w3.org/2001/XMLSchema}string"/&amp;gt;
 * &amp;lt;element name="ATTRIBUTE3" type="{http://www.w3.org/2001/XMLSchema}string"/&amp;gt;
 * &amp;lt;element name="ATTRIBUTE4" type="{http://www.w3.org/2001/XMLSchema}string"/&amp;gt;
 * &amp;lt;element name="ATTRIBUTE5" type="{http://www.w3.org/2001/XMLSchema}string"/&amp;gt;
 * &amp;lt;element name="ATTRIBUTE6" type="{http://www.w3.org/2001/XMLSchema}string"/&amp;gt;
 * &amp;lt;element name="ATTRIBUTE7" type="{http://www.w3.org/2001/XMLSchema}string"/&amp;gt;
 * &amp;lt;element name="ATTRIBUTE8" type="{http://www.w3.org/2001/XMLSchema}string"/&amp;gt;
 * &amp;lt;element name="ATTRIBUTE9" type="{http://www.w3.org/2001/XMLSchema}string"/&amp;gt;
 * &amp;lt;element name="ATTRIBUTE10" type="{http://www.w3.org/2001/XMLSchema}string"/&amp;gt;
 * &amp;lt;element name="ATTRIBUTE11" type="{http://www.w3.org/2001/XMLSchema}string"/&amp;gt;
 * &amp;lt;element name="ATTRIBUTE12" type="{http://www.w3.org/2001/XMLSchema}string"/&amp;gt;
 * &amp;lt;element name="ATTRIBUTE13" type="{http://www.w3.org/2001/XMLSchema}string"/&amp;gt;
 * &amp;lt;element name="ATTRIBUTE14" type="{http://www.w3.org/2001/XMLSchema}string"/&amp;gt;
 * &amp;lt;element name="ATTRIBUTE15" type="{http://www.w3.org/2001/XMLSchema}string"/&amp;gt;
 * &amp;lt;element name="INPUT_EXT" type="{http://www.w3.org/2001/XMLSchema}string"/&amp;gt;
 * &amp;lt;/sequence&amp;gt;
 * &amp;lt;/restriction&amp;gt;
 * &amp;lt;/complexContent&amp;gt;
 * &amp;lt;/complexType&amp;gt;
 * &lt;/pre&gt;
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "WITHDRAWAL_LINE_ITEM", propOrder = {
        "prikey",
        "sourcedoclinenum",
        "chargepartytypecode",
        "chargepartynum",
        "businessmajorclasscode",
        "businessminorclasscode",
        "businessactivitycode",
        "budgetkey",
        "costcentercode",
        "contractnumber",
        "budgetcontractlinenum",
        "summary",
        "bookamount",
        "currencycode",
        "currencyrate",
        "marketcode",
        "productcode",
        "purchaseobjectcode",
        "mcbackup1",
        "mcbackup2",
        "mcbackup3",
        "projectnum",
        "ponumber",
        "polinenum",
        "sourcescmordernum",
        "sourcescmlinenum",
        "description",
        "attribute1",
        "attribute2",
        "attribute3",
        "attribute4",
        "attribute5",
        "attribute6",
        "attribute7",
        "attribute8",
        "attribute9",
        "attribute10",
        "attribute11",
        "attribute12",
        "attribute13",
        "attribute14",
        "attribute15",
        "inputext"
})
public class WITHDRAWALLINEITEM {

    @XmlElement(name = "PRI_KEY", required = true)
    protected String prikey;
    @XmlElement(name = "SOURCE_DOC_LINE_NUM", required = true)
    protected String sourcedoclinenum;
    @XmlElement(name = "CHARGE_PARTY_TYPE_CODE", required = true)
    protected String chargepartytypecode;
    @XmlElement(name = "CHARGE_PARTY_NUM", required = true)
    protected String chargepartynum;
    @XmlElement(name = "BUSINESS_MAJOR_CLASS_CODE", required = true)
    protected String businessmajorclasscode;
    @XmlElement(name = "BUSINESS_MINOR_CLASS_CODE", required = true)
    protected String businessminorclasscode;
    @XmlElement(name = "BUSINESS_ACTIVITY_CODE", required = true)
    protected String businessactivitycode;
    @XmlElement(name = "BUDGET_KEY", required = true, nillable = true)
    protected String budgetkey;
    @XmlElement(name = "COST_CENTER_CODE", required = true, nillable = true)
    protected String costcentercode;
    @XmlElement(name = "CONTRACT_NUMBER", required = true, nillable = true)
    protected String contractnumber;
    @XmlElement(name = "BUDGET_CONTRACT_LINE_NUM", required = true, nillable = true)
    protected String budgetcontractlinenum;
    @XmlElement(name = "SUMMARY", required = true, nillable = true)
    protected String summary;
    @XmlElement(name = "BOOK_AMOUNT", required = true)
    protected BigDecimal bookamount;
    @XmlElement(name = "CURRENCY_CODE", required = true)
    protected String currencycode;
    @XmlElement(name = "CURRENCY_RATE", required = true, nillable = true)
    protected BigDecimal currencyrate;
    @XmlElement(name = "MARKET_CODE", required = true, nillable = true)
    protected String marketcode;
    @XmlElement(name = "PRODUCT_CODE", required = true, nillable = true)
    protected String productcode;
    @XmlElement(name = "PURCHASE_OBJECT_CODE", required = true, nillable = true)
    protected String purchaseobjectcode;
    @XmlElement(name = "MC_BACKUP1", required = true, nillable = true)
    protected String mcbackup1;
    @XmlElement(name = "MC_BACKUP2", required = true, nillable = true)
    protected String mcbackup2;
    @XmlElement(name = "MC_BACKUP3", required = true, nillable = true)
    protected String mcbackup3;
    @XmlElement(name = "PROJECT_NUM", required = true, nillable = true)
    protected String projectnum;
    @XmlElement(name = "PO_NUMBER", required = true, nillable = true)
    protected String ponumber;
    @XmlElement(name = "PO_LINE_NUM", required = true, nillable = true)
    protected BigDecimal polinenum;
    @XmlElement(name = "SOURCE_SCM_ORDER_NUM", required = true, nillable = true)
    protected String sourcescmordernum;
    @XmlElement(name = "SOURCE_SCM_LINE_NUM", required = true, nillable = true)
    protected String sourcescmlinenum;
    @XmlElement(name = "DESCRIPTION", required = true, nillable = true)
    protected String description;
    @XmlElement(name = "ATTRIBUTE1", required = true, nillable = true)
    protected String attribute1;
    @XmlElement(name = "ATTRIBUTE2", required = true, nillable = true)
    protected String attribute2;
    @XmlElement(name = "ATTRIBUTE3", required = true, nillable = true)
    protected String attribute3;
    @XmlElement(name = "ATTRIBUTE4", required = true, nillable = true)
    protected String attribute4;
    @XmlElement(name = "ATTRIBUTE5", required = true, nillable = true)
    protected String attribute5;
    @XmlElement(name = "ATTRIBUTE6", required = true, nillable = true)
    protected String attribute6;
    @XmlElement(name = "ATTRIBUTE7", required = true, nillable = true)
    protected String attribute7;
    @XmlElement(name = "ATTRIBUTE8", required = true, nillable = true)
    protected String attribute8;
    @XmlElement(name = "ATTRIBUTE9", required = true, nillable = true)
    protected String attribute9;
    @XmlElement(name = "ATTRIBUTE10", required = true, nillable = true)
    protected String attribute10;
    @XmlElement(name = "ATTRIBUTE11", required = true, nillable = true)
    protected String attribute11;
    @XmlElement(name = "ATTRIBUTE12", required = true, nillable = true)
    protected String attribute12;
    @XmlElement(name = "ATTRIBUTE13", required = true, nillable = true)
    protected String attribute13;
    @XmlElement(name = "ATTRIBUTE14", required = true, nillable = true)
    protected String attribute14;
    @XmlElement(name = "ATTRIBUTE15", required = true, nillable = true)
    protected String attribute15;
    @XmlElement(name = "INPUT_EXT", required = true, nillable = true)
    protected String inputext;

    /**
     * 获取prikey属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getPRIKEY() {
        return prikey;
    }

    /**
     * 设置prikey属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setPRIKEY(String value) {
        this.prikey = value;
    }

    /**
     * 获取sourcedoclinenum属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getSOURCEDOCLINENUM() {
        return sourcedoclinenum;
    }

    /**
     * 设置sourcedoclinenum属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setSOURCEDOCLINENUM(String value) {
        this.sourcedoclinenum = value;
    }

    /**
     * 获取chargepartytypecode属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getCHARGEPARTYTYPECODE() {
        return chargepartytypecode;
    }

    /**
     * 设置chargepartytypecode属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setCHARGEPARTYTYPECODE(String value) {
        this.chargepartytypecode = value;
    }

    /**
     * 获取chargepartynum属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getCHARGEPARTYNUM() {
        return chargepartynum;
    }

    /**
     * 设置chargepartynum属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setCHARGEPARTYNUM(String value) {
        this.chargepartynum = value;
    }

    /**
     * 获取businessmajorclasscode属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getBUSINESSMAJORCLASSCODE() {
        return businessmajorclasscode;
    }

    /**
     * 设置businessmajorclasscode属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setBUSINESSMAJORCLASSCODE(String value) {
        this.businessmajorclasscode = value;
    }

    /**
     * 获取businessminorclasscode属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getBUSINESSMINORCLASSCODE() {
        return businessminorclasscode;
    }

    /**
     * 设置businessminorclasscode属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setBUSINESSMINORCLASSCODE(String value) {
        this.businessminorclasscode = value;
    }

    /**
     * 获取businessactivitycode属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getBUSINESSACTIVITYCODE() {
        return businessactivitycode;
    }

    /**
     * 设置businessactivitycode属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setBUSINESSACTIVITYCODE(String value) {
        this.businessactivitycode = value;
    }

    /**
     * 获取budgetkey属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getBUDGETKEY() {
        return budgetkey;
    }

    /**
     * 设置budgetkey属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setBUDGETKEY(String value) {
        this.budgetkey = value;
    }

    /**
     * 获取costcentercode属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getCOSTCENTERCODE() {
        return costcentercode;
    }

    /**
     * 设置costcentercode属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setCOSTCENTERCODE(String value) {
        this.costcentercode = value;
    }

    /**
     * 获取contractnumber属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getCONTRACTNUMBER() {
        return contractnumber;
    }

    /**
     * 设置contractnumber属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setCONTRACTNUMBER(String value) {
        this.contractnumber = value;
    }

    /**
     * 获取budgetcontractlinenum属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getBUDGETCONTRACTLINENUM() {
        return budgetcontractlinenum;
    }

    /**
     * 设置budgetcontractlinenum属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setBUDGETCONTRACTLINENUM(String value) {
        this.budgetcontractlinenum = value;
    }

    /**
     * 获取summary属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getSUMMARY() {
        return summary;
    }

    /**
     * 设置summary属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setSUMMARY(String value) {
        this.summary = value;
    }

    /**
     * 获取bookamount属性的值。
     *
     * @return possible object is
     * {@link BigDecimal }
     */
    public BigDecimal getBOOKAMOUNT() {
        return bookamount;
    }

    /**
     * 设置bookamount属性的值。
     *
     * @param value allowed object is
     *              {@link BigDecimal }
     */
    public void setBOOKAMOUNT(BigDecimal value) {
        this.bookamount = value;
    }

    /**
     * 获取currencycode属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getCURRENCYCODE() {
        return currencycode;
    }

    /**
     * 设置currencycode属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setCURRENCYCODE(String value) {
        this.currencycode = value;
    }

    /**
     * 获取currencyrate属性的值。
     *
     * @return possible object is
     * {@link BigDecimal }
     */
    public BigDecimal getCURRENCYRATE() {
        return currencyrate;
    }

    /**
     * 设置currencyrate属性的值。
     *
     * @param value allowed object is
     *              {@link BigDecimal }
     */
    public void setCURRENCYRATE(BigDecimal value) {
        this.currencyrate = value;
    }

    /**
     * 获取marketcode属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getMARKETCODE() {
        return marketcode;
    }

    /**
     * 设置marketcode属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setMARKETCODE(String value) {
        this.marketcode = value;
    }

    /**
     * 获取productcode属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getPRODUCTCODE() {
        return productcode;
    }

    /**
     * 设置productcode属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setPRODUCTCODE(String value) {
        this.productcode = value;
    }

    /**
     * 获取purchaseobjectcode属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getPURCHASEOBJECTCODE() {
        return purchaseobjectcode;
    }

    /**
     * 设置purchaseobjectcode属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setPURCHASEOBJECTCODE(String value) {
        this.purchaseobjectcode = value;
    }

    /**
     * 获取mcbackup1属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getMCBACKUP1() {
        return mcbackup1;
    }

    /**
     * 设置mcbackup1属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setMCBACKUP1(String value) {
        this.mcbackup1 = value;
    }

    /**
     * 获取mcbackup2属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getMCBACKUP2() {
        return mcbackup2;
    }

    /**
     * 设置mcbackup2属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setMCBACKUP2(String value) {
        this.mcbackup2 = value;
    }

    /**
     * 获取mcbackup3属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getMCBACKUP3() {
        return mcbackup3;
    }

    /**
     * 设置mcbackup3属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setMCBACKUP3(String value) {
        this.mcbackup3 = value;
    }

    /**
     * 获取projectnum属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getPROJECTNUM() {
        return projectnum;
    }

    /**
     * 设置projectnum属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setPROJECTNUM(String value) {
        this.projectnum = value;
    }

    /**
     * 获取ponumber属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getPONUMBER() {
        return ponumber;
    }

    /**
     * 设置ponumber属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setPONUMBER(String value) {
        this.ponumber = value;
    }

    /**
     * 获取polinenum属性的值。
     *
     * @return possible object is
     * {@link BigDecimal }
     */
    public BigDecimal getPOLINENUM() {
        return polinenum;
    }

    /**
     * 设置polinenum属性的值。
     *
     * @param value allowed object is
     *              {@link BigDecimal }
     */
    public void setPOLINENUM(BigDecimal value) {
        this.polinenum = value;
    }

    /**
     * 获取sourcescmordernum属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getSOURCESCMORDERNUM() {
        return sourcescmordernum;
    }

    /**
     * 设置sourcescmordernum属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setSOURCESCMORDERNUM(String value) {
        this.sourcescmordernum = value;
    }

    /**
     * 获取sourcescmlinenum属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getSOURCESCMLINENUM() {
        return sourcescmlinenum;
    }

    /**
     * 设置sourcescmlinenum属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setSOURCESCMLINENUM(String value) {
        this.sourcescmlinenum = value;
    }

    /**
     * 获取description属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getDESCRIPTION() {
        return description;
    }

    /**
     * 设置description属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setDESCRIPTION(String value) {
        this.description = value;
    }

    /**
     * 获取attribute1属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getATTRIBUTE1() {
        return attribute1;
    }

    /**
     * 设置attribute1属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setATTRIBUTE1(String value) {
        this.attribute1 = value;
    }

    /**
     * 获取attribute2属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getATTRIBUTE2() {
        return attribute2;
    }

    /**
     * 设置attribute2属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setATTRIBUTE2(String value) {
        this.attribute2 = value;
    }

    /**
     * 获取attribute3属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getATTRIBUTE3() {
        return attribute3;
    }

    /**
     * 设置attribute3属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setATTRIBUTE3(String value) {
        this.attribute3 = value;
    }

    /**
     * 获取attribute4属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getATTRIBUTE4() {
        return attribute4;
    }

    /**
     * 设置attribute4属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setATTRIBUTE4(String value) {
        this.attribute4 = value;
    }

    /**
     * 获取attribute5属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getATTRIBUTE5() {
        return attribute5;
    }

    /**
     * 设置attribute5属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setATTRIBUTE5(String value) {
        this.attribute5 = value;
    }

    /**
     * 获取attribute6属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getATTRIBUTE6() {
        return attribute6;
    }

    /**
     * 设置attribute6属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setATTRIBUTE6(String value) {
        this.attribute6 = value;
    }

    /**
     * 获取attribute7属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getATTRIBUTE7() {
        return attribute7;
    }

    /**
     * 设置attribute7属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setATTRIBUTE7(String value) {
        this.attribute7 = value;
    }

    /**
     * 获取attribute8属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getATTRIBUTE8() {
        return attribute8;
    }

    /**
     * 设置attribute8属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setATTRIBUTE8(String value) {
        this.attribute8 = value;
    }

    /**
     * 获取attribute9属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getATTRIBUTE9() {
        return attribute9;
    }

    /**
     * 设置attribute9属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setATTRIBUTE9(String value) {
        this.attribute9 = value;
    }

    /**
     * 获取attribute10属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getATTRIBUTE10() {
        return attribute10;
    }

    /**
     * 设置attribute10属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setATTRIBUTE10(String value) {
        this.attribute10 = value;
    }

    /**
     * 获取attribute11属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getATTRIBUTE11() {
        return attribute11;
    }

    /**
     * 设置attribute11属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setATTRIBUTE11(String value) {
        this.attribute11 = value;
    }

    /**
     * 获取attribute12属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getATTRIBUTE12() {
        return attribute12;
    }

    /**
     * 设置attribute12属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setATTRIBUTE12(String value) {
        this.attribute12 = value;
    }

    /**
     * 获取attribute13属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getATTRIBUTE13() {
        return attribute13;
    }

    /**
     * 设置attribute13属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setATTRIBUTE13(String value) {
        this.attribute13 = value;
    }

    /**
     * 获取attribute14属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getATTRIBUTE14() {
        return attribute14;
    }

    /**
     * 设置attribute14属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setATTRIBUTE14(String value) {
        this.attribute14 = value;
    }

    /**
     * 获取attribute15属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getATTRIBUTE15() {
        return attribute15;
    }

    /**
     * 设置attribute15属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setATTRIBUTE15(String value) {
        this.attribute15 = value;
    }

    /**
     * 获取inputext属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getINPUTEXT() {
        return inputext;
    }

    /**
     * 设置inputext属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setINPUTEXT(String value) {
        this.inputext = value;
    }

}
