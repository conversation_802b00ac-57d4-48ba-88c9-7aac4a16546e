package com.xunge.dao.budget.ele;

import com.xunge.model.budget.ele.BudgetEleParamVO;
import com.xunge.model.budget.ele.BudgetElePayableInfoVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * @author: LiangCheng
 * Date: 2022/7/4 15:15
 * Description:
 */
public interface BudgetElePayableInfoMapper {

    /**
     * 查询电费应付表，获取单个省每月生成的应付数据，按省份，年份，月份查询
     * @param prvId 省份
     * @param onYear 年份
     * @param onMonth 月份
     * @return 按省份，站点类型分组返回应付数据List
     */
    List<BudgetElePayableInfoVO> getElePayableInfoByOnMonth(@Param("prvId")String prvId,@Param("onYear")Integer onYear,@Param("onMonth")Integer onMonth);

    /**
     * 保存应付数据
     * @param payableList，电费应付数据表返回的集合
     */
    void saveBudgetElePayableInfo(@Param("payableList") List<BudgetElePayableInfoVO> payableList,@Param("onYear")Integer onYear,@Param("workOrderId") String workOrderId);

    /**
     * 查询应付数据
     * @return 应付数据表的集合
     */
    List<BudgetElePayableInfoVO> getBudgetElePayableInfo(BudgetEleParamVO budgetEleParamVO);

    /**
     * 查询应付数据是否存在
     * @param prvId 省份
     * @param onYear 年份
     * @return >0存在数据，不再生成，=0不存在数据，可生成
     */
    Integer checkElePayableInfoIsExist(@Param("prvId")String prvId,@Param("onYear")Integer onYear);

    void delBudgetEleData(BudgetEleParamVO budgetEleParamVO);

}
