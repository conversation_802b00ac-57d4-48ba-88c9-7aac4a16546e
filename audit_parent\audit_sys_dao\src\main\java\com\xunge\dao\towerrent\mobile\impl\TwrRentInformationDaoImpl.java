package com.xunge.dao.towerrent.mobile.impl;

import com.xunge.core.page.Page;
import com.xunge.core.util.DateUtil;
import com.xunge.dao.AbstractBaseDao;
import com.xunge.dao.towerrent.mobile.ITwrRentInformationDao;
import com.xunge.filter.PageInterceptor;
import com.xunge.model.basedata.DatAttachment;
import com.xunge.model.towerrent.mobile.TwrRentInformationVO;
import com.xunge.model.towerrent.rentmanager.TowerResourceInfoVO;
import com.xunge.model.towerrent.rentmanager.TwrRentVsResourceLinkInfo;
import com.xunge.model.towerrent.settlement.TwrUploadVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.*;
public class TwrRentInformationDaoImpl extends AbstractBaseDao implements ITwrRentInformationDao {
    private static Logger LOGGER = LoggerFactory.getLogger(TwrRentInformationDaoImpl.class);
    final String Namespace = "com.xunge.dao.TwrRentInformationVOMapper.";
    final String NapmspaceHistory = "com.xunge.dao.TowerRentInformationHistoryVOMapper.";
    final String NapmspaceChange = "com.xunge.dao.towerrent.rentinformation.TwrRentInformationChangeVODao.";
    final String NamespaceBackup = "com.xunge.dao.TwrRentinformationBackupMapper.";

    @SuppressWarnings("unchecked")
    @Override
    public Page<TwrRentInformationVO> queryTwrRentInformation(Map<String, Object> map, int pageNum, int pageSize) {
        PageInterceptor.startPage(pageNum, pageSize);
        this.getSqlSession().selectList(Namespace + "queryTwrRentInformation", map);
        return PageInterceptor.endPage();
    }

    @Override
    public List<TwrRentInformationVO> queryTwrRentInformationList(Map<String, Object> map) {
        // TODO Auto-generated method stub
        return this.getSqlSession().selectList(Namespace + "queryTwrRentInformation", map);
    }

    @Override
    public int updateTwrRentInformationByBizChange(Map<String, Object> paraMap) {
        return this.getSqlSession().update(Namespace + "updateTwrRentInformationByBizChange", paraMap);
    }

    @Override
    public int updateHisTwrRentInformationByBizChange(Map<String, Object> paraMap) {
        return this.getSqlSession().update(NamespaceBackup + "updateHisTwrRentInformationByBizChange", paraMap);
    }

    @Override
    public List<TowerResourceInfoVO> queryTwrRentInformation(Map<String, Object> map) {
        return this.getSqlSession().selectList(Namespace + "queryTowerResourceInfo", map);
    }

    @Override
    public Long getCountAllTowerResourceInfoByPrvId(String prvId) {
        return this.getSqlSession().selectOne(Namespace + "getCountAllTowerResourceInfoByPrvId", prvId);
    }

    @Override
    public TwrRentInformationVO queryByRentinformationId(String id) {
        return this.getSqlSession().selectOne(Namespace + "selectByPrimaryKey", id);
    }

    @Override
    public TwrRentInformationVO queryByHisRentinformationId(String id) {
        return this.getSqlSession().selectOne(NamespaceBackup + "selectByPrimaryKey", id);
    }


    @Override
    public TwrRentInformationVO queryTime(Map<String, Object> paraMap) {
        return this.getSqlSession().selectOne(Namespace + "selectTime", paraMap);
    }

    @Override
    public List<TwrRentInformationVO> queryExportTwrRentInformation(Map<String, Object> paraMap) {
        return this.getSqlSession().selectList(Namespace + "queryExportTwrRentInformation", paraMap);
    }

    /**
     * 根据业务确认单号 铁塔站址编码 生效日期判断是否在服务有效期查找TwrRentInformation
     *
     * @param paraMap
     * @return
     * <AUTHOR>
     */
    @Override
    public TwrRentInformationVO queryTwrRentInformationByBusinessNumDate(Map<String, Object> paraMap) {
        return this.getSqlSession().selectOne(Namespace + "queryTwrRentInformationBybusinessConfirmNumber", paraMap);
    }

    @Override
    public TwrRentInformationVO queryHisTwrRentInformationByBusinessNumDate(Map<String, Object> paraMap) {
        return this.getSqlSession().selectOne(NamespaceBackup + "queryHisTwrRentInformationBybusinessConfirmNumber", paraMap);
    }

    @Override
    public List<TwrRentInformationVO> queryMsgByTwrStaCode(Map<String, Object> paraMap) {
        return this.getSqlSession().selectList(Namespace + "queryMsgByTwrStaCode", paraMap);
    }

    @Override
    public List<TwrRentInformationVO> queryMobileRentByPrvId(String prvId) {
        return this.getSqlSession().selectList(Namespace + "queryMobileRentBill", prvId);
    }

    @Override
    public int setRecordDeptId(String[] twrRentinformationArr, String deptIds) {
        Map<String, Object> paramMap = new HashMap<String, Object>();
        paramMap.put("deptIds", deptIds);
        paramMap.put("twrRentinformationArr", twrRentinformationArr);
        return this.getSqlSession().update(Namespace + "setRecordDetp", paramMap);
    }

    @Override
    public Page<TwrRentInformationVO> queryByPage(Map<String, Object> map, int pageNum, int pageSize) {
        PageInterceptor.startPage(pageNum, pageSize);
        this.getSqlSession().selectList(Namespace + "queryByCondition", map);
        return PageInterceptor.endPage();
    }

    @Override
    public Page<TwrRentInformationVO> queryByPageHistory(Map<String, Object> map, int pageNum, int pageSize) {
        PageInterceptor.startPage(pageNum, pageSize);
        this.getSqlSession().selectList(NamespaceBackup + "queryHisByCondition", map);
        return PageInterceptor.endPage();
    }

    @Override
    public List<TwrRentInformationVO> queryByCondition(Map<String, Object> map) {
        return this.getSqlSession().selectList(Namespace + "queryByCondition", map);
    }

    @Override
    public List<TwrRentInformationVO> queryHisByCondition(Map<String, Object> map) {
        return this.getSqlSession().selectList(NamespaceBackup + "queryHisByCondition", map);
    }

    @Override
    public int queryTwrRentInfoCountByPriKey(TwrRentInformationVO twrRentInformationVO) {
        return this.getSqlSession().selectOne(Namespace + "queryTwrRentInfoCountByPriKey", twrRentInformationVO);
    }

    @Override
    public int queryOldIsLink(TwrRentInformationVO twrRentInformationVO) {
        return this.getSqlSession().selectOne(Namespace + "queryOldIsLink", twrRentInformationVO);
    }

    @Override
    public int queryNewIsLink(TwrRentInformationVO twrRentInformationVO) {
        return this.getSqlSession().selectOne(Namespace + "queryNewIsLink", twrRentInformationVO);
    }

    @Override
    public int updateMobileRentinfirmation(Map<String, Object> map) {
        return this.getSqlSession().update(Namespace + "updateMobileRentinfirmation", map);
    }

    @Override
    public int updateHisMobileRentinfirmation(Map<String, Object> map) {
        return this.getSqlSession().update(NamespaceBackup + "updateHisMobileRentinfirmation", map);
    }

    @Override
    public int deleteRentInformationChangeByIdAndState(Map<String, Object> map) {
        return this.getSqlSession().delete(NapmspaceChange + "deleteRentInformationChangeByIdAndState", map);
    }

    @Override
    public int deleteHisRentInformationChangeByIdAndState(Map<String, Object> map) {
        return this.getSqlSession().delete(NapmspaceChange + "deleteHisRentInformationChangeByIdAndState", map);
    }

    @Override
    public List<Map<String, Integer>> queryOldResBaseState(Map<String, String> param) {
        return this.getSqlSession().selectList(Namespace + "queryOldResBaseState", param);
    }

    @Override
    public List<Map<String, Integer>> queryNewResBaseState(Map<String, String> param) {
        return this.getSqlSession().selectList(Namespace + "queryNewResBaseState", param);
    }

    @Override
    public TwrRentVsResourceLinkInfo getResourceLinkInfoByTowerStationCode(Map<String, String> param) {
        return this.getSqlSession().selectOne(Namespace + "getResourceLinkInfoByTowerStationCode", param);
    }

    @Override
    public List<TwrRentInformationVO> selectRentInformationList(Map<String, Object> map) {
        return this.getSqlSession().selectList(Namespace + "selectRentInformationList", map);
    }

    @Override
    public List<TwrRentInformationVO> selectHisRentInformationList(Map<String, Object> map) {
        return this.getSqlSession().selectList(NamespaceBackup + "selectHisRentInformationList", map);
    }

    @Override
    public List findRentAndBillByBussPriKey(List param) {
        return this.getSqlSession().selectList(Namespace + "findRentAndBillByBussPriKey", param);
    }

    @Override
    public List findTowerRentIdsByMobileParam(Map<String, Object> param) {
        return this.getSqlSession().selectList(Namespace + "findTowerRentIdsByMobileParam", param);
    }

    @Override
    public List<Map> findHisRentAndBillByBussPriKey(List historyMonthList) {
        return this.getSqlSession().selectList(Namespace + "findHisRentAndBillByBussPriKey", historyMonthList);
    }

    @Override
    public List<TwrRentInformationVO> selectRentInformationListConfig(Map<String, Object> map) {
        return this.getSqlSession().selectList(Namespace + "selectRentInformationListConfig", map);
    }

    @Override
    public List<TwrRentInformationVO> selectHisRentInformationListConfig(Map<String, Object> map) {
        return this.getSqlSession().selectList(NamespaceBackup + "selectHisRentInformationListConfig", map);
    }

    @Override
    public int deleteMobileRentById(List list) {
        return this.getSqlSession().delete(Namespace + "deleteMobileRentById", list);
    }

    @Override
    public int deleteHisMobileRentById(List list) {
        return this.getSqlSession().delete(Namespace + "deleteHisMobileRentById", list);
    }

    @Override
    public List<String> queryFiles(String businessId, String businessType, String yearmonth) {
        Map<String, String> map = new HashMap<>();
        map.put("businessId", businessId);
        map.put("businessType", businessType);
        map.put("yearmonth", yearmonth.substring(0, 4) + yearmonth.substring(5));
        return this.getSqlSession().selectList(NapmspaceChange + "queryFiles", map);
    }

    @Override
    public List<String> queryMobileFiles(String towerStationCode, String businessConfirmNumber, String businessType, String accountPeroid) {
        Map<String, String> map = new HashMap<>();
        map.put("towerStationCode", towerStationCode);
        map.put("businessConfirmNumber", businessConfirmNumber);
        map.put("businessType", businessType);

        SimpleDateFormat format = new SimpleDateFormat("yyyyMM");
        Calendar c = Calendar.getInstance();
        c.setTime(new Date());
        c.add(Calendar.MONTH, -1);
        String mon = format.format(c.getTime());

        if (mon.equals(accountPeroid)) {
            return this.getSqlSession().selectList(NapmspaceChange + "queryMobileFiles", map);
        } else {
            map.put("accountPeroid", accountPeroid);
            return this.getSqlSession().selectList(NamespaceBackup + "queryHisMobileFiles", map);
        }
    }

    @Override
    public List<String> queryAccountFiles(String accountsummaryId, String businessType, String yearmonth) {
        Map<String, String> map = new HashMap<>();
        map.put("accountsummaryId", accountsummaryId);
        map.put("businessType", businessType);
        map.put("yearmonth", yearmonth);
        SimpleDateFormat format = new SimpleDateFormat("yyyyMM");
        Calendar c = Calendar.getInstance();
        c.setTime(new Date());
        c.add(Calendar.MONTH, -1);
        String mon = format.format(c.getTime());
        if (mon.equals(yearmonth)) {
            return this.getSqlSession().selectList(NapmspaceChange + "queryAccountFiles", map);
        } else {
            return this.getSqlSession().selectList(NapmspaceChange + "queryHisAccountFiles", map);
        }
    }

    @Override
    public List<Map> queryAccountFiles2(String accountsummaryId, String[] businessTypes) {
        Map<String, Object> map = new HashMap<>();
        map.put("accountsummaryId", accountsummaryId);
        map.put("businessTypes", businessTypes);
        Calendar c = Calendar.getInstance();
        c.add(Calendar.MONTH, -1);
        String prvMonth = DateUtil.format(c.getTime(), "yyyyMM");
        map.put("prvAccountPeroid", prvMonth);
        return this.getSqlSession().selectList(NapmspaceChange + "queryHisAccountFiles2", map);
    }

	/*
	@Override
	public List<String> queryAccountFilesConfig(String accountsummaryId, String businessType) {
		Map<String,String> map = new HashMap<>();
		map.put("accountsummaryId",accountsummaryId);
		map.put("businessType",businessType);
		return this.getSqlSession().selectList(NapmspaceChange+"queryAccountFilesConfig", map);
	}
	*/

    @Override
    public String queryFileName(String businessId, String yearmonth, String showType) {
        Map<String, Object> map = new HashMap<>();
        map.put("businessId", businessId);
        if ("1".equals(showType)) {
            map.put("yearmonth", yearmonth);
            return this.getSqlSession().selectOne(NapmspaceChange + "queryFileNameHis", map);
        } else {
            return this.getSqlSession().selectOne(NapmspaceChange + "queryFileName", map);
        }
    }

    @Override
    public int insert(DatAttachment record) {
        return this.getSqlSession().insert(NapmspaceChange + "insertAttachement", record);
    }

    public Page<TwrUploadVO> queryDownFiles(Map<String, Object> paraMap, int pageNumber, int pageSize) {
        PageInterceptor.startPage(pageNumber, pageSize);
        String yearmonth = paraMap.get("yearmonth").toString();
        SimpleDateFormat format = new SimpleDateFormat("yyyyMM");
        Calendar c = Calendar.getInstance();
        c.setTime(new Date());
        c.add(Calendar.MONTH, -1);
        String mon = format.format(c.getTime());
        if (mon.equals(yearmonth)) {
            this.getSqlSession().selectList(NapmspaceChange + "queryDownFiles", paraMap);
        } else {
            this.getSqlSession().selectList(NapmspaceChange + "queryHisDownFiles", paraMap);
        }
        return PageInterceptor.endPage();
    }

    @Override
    public int batchSave(List<TwrRentInformationVO> temp) {
        Map param = new HashMap();
        param.put("saveList", temp);
        return this.getSqlSession().insert(Namespace + "insertTwrRentInfoDetails", param);
    }

    @Override
    public int hisBatchSave(List<TwrRentInformationVO> temp) {
        Map param = new HashMap();
        param.put("saveList", temp);
        return this.getSqlSession().insert(NamespaceBackup + "insertHisTwrRentInfoDetails", param);
    }

    @Override
    public TwrRentInformationVO queryBeanById(Map<String, Object> param) {
        return this.getSqlSession().selectOne(Namespace + "queryBeanById", param);
    }

    @Override
    public TwrRentInformationVO queryHisBeanById(Map<String, Object> param) {
        return this.getSqlSession().selectOne(NamespaceBackup + "queryHisBeanById", param);
    }

    @Override
    public List<Map<String, String>> judgeRepetition(String showType, Map<String, Object> paraMap) {
        if ("1".equals(showType)) {
            return this.getSqlSession().selectList(NamespaceBackup + "judgeRepetition", paraMap);
        } else {
            return this.getSqlSession().selectList(Namespace + "judgeRepetition", paraMap);
        }
    }
}
