package com.xunge.model.budget.twr;

import lombok.Data;

import java.util.List;

/**
 * @author: xue
 * Description: 铁塔服务费预算 参数vo
 */
@Data
public class BudgetTwrParamVO {

    /**
     * 省份字符串
     */
    private String prvIds;

    /**
     * 年份
     */
    private Integer onYear;

    /**
     * 月份
     */
    private Integer onMonth;

    /**
     * 来源，0集团草稿，1省侧填报
     */
    private Integer formType;

    /**
     * 节点
     */
    private Integer nodeType;

    private Integer stepType;

    /**
     * 工单id
     */
    private String workOrderId;

    private Integer groupType;

    private String workName;

    private String redisKey;

    private String budgetTime;

    private Integer flowType;

    private String auditInfoId;

    private String isSpecial;

    private List<String> prvIdList;

    /**
     * 1.预算总览
     */
    private List<BudgetTwrCountVo> countList;

    /**
     * 2.本年1-9月存量订单在新年度的费用
     */
    private List<BudgetTwrStockOrderFeeVO> stockOrderFeeList;

    /**
     * 3.本年起租订单在新年补足费用
     */
    private List<BudgetTwrComplementVo> complementList;

    /**
     * 4.本年10-12月新起租订单在新年度的费用
     */
    private List<BudgetTwrSiteCurrentYearVO> siteCurrentYearList;

    /**
     * 5.新年新建站在新年的费用
     */
    private List<BudgetTwrNewSiteNewYearVO> newSiteNewYearList;

    /**
     * 6.提升共享范围
     */
    private List<BudgetTwrPromoteVO> promoteList;


    /**
     * 7.站址退网
     */
    private List<BudgetTwrLogoutVO> logoutList;

    /**
     * 8.免除普遍服务站址
     */
    private List<BudgetTwrUniversalServiceVO> universalServiceList;


    /**
     * 9.预算调整补足
     */
    private List<BudgetTwrSupplementVo> supplementList;


    /**
     * 10.油机发电(非包干)
     * 存在油机发电大集中的才导出
     */
    private List<BudgetTwrOilVO> oilList;

}
