package com.xunge.dao.selfelec.inspect.ai;

import com.xunge.model.selfelec.inspect.ai.EleInspectAiEval;
import com.xunge.model.selfelec.inspect.ai.infor.EleInspectAiInfoApply;
import com.xunge.model.selfelec.inspect.ai.infor.InforApplyAi;
import com.xunge.model.selfelec.inspect.province.EleInspect;
import com.xunge.model.selfelec.inspect.province.EleInspectWeb;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * ele_inspect_ai_info_apply
 */
public interface EleInspectAiInfoApplyMapper {

    /**
     * 新增ele_inspect_ai_info_apply
     *
     * @param eleInspectAiInfoApply 新增数据
     */
    void insertEleInspectAiInfoApply(EleInspectAiInfoApply eleInspectAiInfoApply);

    /**
     * 更新ele_inspect
     *
     * @param eleInspectAiInfoApply 更新参数
     */
    void updateEleInspectAiInfoApplyByEleInspectAiInfoApply(EleInspectAiInfoApply eleInspectAiInfoApply);

    /**
     * 稽核报账基础数据信息及返回结果(审核开始)
     *
     * @param eleInspectAiInfoApply 查询条件
     * @return 结果
     */
    List<EleInspectAiInfoApply> getEleInspectAiInfoApplyByEleInspectAiInfoApply(EleInspectAiInfoApply eleInspectAiInfoApply);

    /**
     * 获取主数据(后付费)
     *
     * @param billaccountpaymentdetailId 缴费单id
     * @return 结果
     */
    InforApplyAi getInforApply(@Param("billaccountpaymentdetailId") String billaccountpaymentdetailId, @Param("paymentCode") String paymentCode);

    /**
     * 获取主数据（核销）
     *
     * @param billaccountpaymentdetailId 缴费单id
     * @return 结果
     */
    InforApplyAi getInforApplyVerification(@Param("billaccountpaymentdetailId") String billaccountpaymentdetailId, @Param("paymentCode") String paymentCode);

    /**
     * 更新点击时间，是否有效标识
     *
     * @param eleInspect 更新数据
     */
    void updateEleInspectAiInfoApplyForWeb(EleInspect eleInspect);

    /**
     * 页面获取稽核结果
     *
     * @param eleInspect 查询条件
     * @return 结果
     */
    EleInspectWeb queryEleInspectInfoForWeb(EleInspect eleInspect);

    Map<String, Object> queryLevelName(String businessKey);

    void insertAiImageEval(EleInspectAiEval eval);

    void recordClickNum(String imageId);

    /**
     * 获取主数据 查询固化时查询塔维电费表
     * @param billaccountpaymentdetailId 缴费单id
     * @return 结果
     */
    InforApplyAi getInforApplyTele(@Param("billaccountpaymentdetailId") String billaccountpaymentdetailId, @Param("paymentCode") String paymentCode);
    /**
     * 获取主数据  查询合同时查询电费表
     * @param billaccountpaymentdetailId 缴费单id
     * @return 结果
     */
    InforApplyAi getInforApplyFromEle(@Param("billaccountpaymentdetailId") String billaccountpaymentdetailId, @Param("paymentCode") String paymentCode);
}
