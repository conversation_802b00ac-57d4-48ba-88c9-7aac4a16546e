package com.xunge.dao.selfrent.billaccount;

import com.xunge.model.selfrent.billAccount.RentPaymentVO;
import com.xunge.model.selfrent.billamount.PaymentDetailExportVo;
import org.apache.ibatis.cursor.Cursor;

import java.util.List;
import java.util.Map;

/**
 * @创建人 LiXs
 * @创建时间 2019/11/12
 * @描述：
 */
public interface RentPaymentMapper {

    /**
     * 根据缴费单编码查询缴费记录
     *
     * @param rentPaymentCode
     * @return
     */
    List<RentPaymentVO> queryRentPaymentVOByPaymentCode(String rentPaymentCode);

    /**
     * 查询数据库中最大数值的code
     *
     * @param param
     * @return
     */
    Map<String, Object> queryMaxCode(Map<String, Object> param);

    /**
     * @Description 校验是否存在电费缴费单
     * @param billaccountId
     * @return int
     */
    int checkHasElecFee(String billaccountId);

    /**
     * 查询租费缴费明细Cursor
     * @param paramMap
     * @return
     */
    Cursor<PaymentDetailExportVo> queryRentDetail(Map<String, Object> paramMap);

}
