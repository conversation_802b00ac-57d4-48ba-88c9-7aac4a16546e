package com.xunge.dao.report;

import java.util.List;
import java.util.Map;

public interface IRptEleprotocolDao {
    /**
     * 根据省份id查询各地市电费固话数据
     *
     * @return
     * <AUTHOR>
     */
    public List<Map<String, Object>> queryRptEleprotocolByPrvid(Map<String, Object> map);

    /**
     * 根据地市id查询区县电费固话数据
     *
     * @return
     * <AUTHOR>
     */
    public List<Map<String, Object>> queryRptEleprotocolByPregid(Map<String, Object> map);

    public List<Map<String, Object>> queryRptEleprotocol(Map<String, Object> map);
}
