package com.xunge.dao.selfelec;

import com.xunge.model.selfelec.EleBillaccountbaseresource;
import com.xunge.model.selfelec.EleBillaccountbaseresourceExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface EleBillaccountbaseresourceMapper {
    
    int countByExample(EleBillaccountbaseresourceExample example);

    int countByExampleForTW(EleBillaccountbaseresource example);

    
    int deleteByExample(EleBillaccountbaseresourceExample example);

    
    int deleteByPrimaryKey(String billaccountbaseresourceId);

    
    int insert(EleBillaccountbaseresource record);

    
    int insertSelective(EleBillaccountbaseresource record);

    
    List<EleBillaccountbaseresource> selectByExample(EleBillaccountbaseresourceExample example);

    
    EleBillaccountbaseresource selectByPrimaryKey(String billaccountbaseresourceId);

    
    int updateByExampleSelective(@Param("record") EleBillaccountbaseresource record, @Param("example") EleBillaccountbaseresourceExample example);

    
    int updateByExample(@Param("record") EleBillaccountbaseresource record, @Param("example") EleBillaccountbaseresourceExample example);

    
    int updateByPrimaryKeySelective(EleBillaccountbaseresource record);

    
    int updateByPrimaryKey(EleBillaccountbaseresource record);

    /**
     * 批量删除资源点与合同的关联关系
     *
     * @param paraMap
     * @return
     */
    public int deleteSpecialBillaccountResource(Map<String, Object> paraMap);

    /**
     * 根据资源点Id以及报账点Id修改报账点与资源点的关联关联
     *
     * @param paraMap
     * @return
     */
    public int updateEleBillaccountResourceById(Map<String, Object> paraMap);

    /**
     * 当审核通过时更新当前报账点资源关联关系
     */
    void updateWhenAuditPass(@Param("auditPassDate") String auditPassDate, @Param("serviceSiteType") Integer serviceSiteType, @Param("baseresourceState") Integer baseresourceState, @Param("billaccountbaseresourceId") String billaccountbaseresourceId);

    /**
     * 根据报账点Id查询当前未显示的关联关系
     * @param billaccountId
     * @return
     */
    List<EleBillaccountbaseresource> selectNoShowByBillaccountId(@Param("billaccountId") String billaccountId);

    /**
     * 根据资源点Id查询关联需要的资源信息
     * @param baseresourceId
     * @return
     */
    EleBillaccountbaseresource queryResourceForRelation(@Param("baseresourceId") String baseresourceId);

    /**
     * 当审核通过时若之前有审核后删除的记录，记录解除关联时间
     *
     * @param paraMap
     */
    void updateLastRelationEnddate(Map<String, Object> paraMap);

    //根据资源id查询报账点编号
    List<EleBillaccountbaseresource> queryBillaccountCodeByResouceId(@Param("baseresourceIds") List<String> baseresourceIds);
}