package com.xunge.model.basedata.vo;

import com.xunge.core.model.UserLoginInfo;

import java.io.Serializable;

public class TowerQueryVO extends BaseDataVO implements Serializable {

    /**
     *
     */
    private static final long serialVersionUID = 5557836253198588968L;

    // 模糊查询
    private String towerReg;

    // 铁塔类型
    private String towerType;

    private String prvId;

    private Integer queryType;

    private String[] towerIds;

    private String depId;

    private String auditStatus;

    private Integer dataFrom;
    private String dataFroms;
    private String towerSiteCode;
    // 登录用户信息
    private UserLoginInfo loginUser;
    // 普服资源
    private Integer ifTeleCmnServ;

    public void setIfTeleCmnServ(Integer ifTeleCmnServ) {
        this.ifTeleCmnServ = ifTeleCmnServ;
    }

    public Integer getIfTeleCmnServ() {
        return ifTeleCmnServ;
    }

    public String getTowerSiteCode() {
        return towerSiteCode;
    }

    public void setTowerSiteCode(String towerSiteCode) {
        this.towerSiteCode = towerSiteCode;
    }

    public String getDataFroms() {
        return dataFroms;
    }

    public void setDataFroms(String dataFroms) {
        this.dataFroms = dataFroms;
    }

    public Integer getDataFrom() {
        return dataFrom;
    }

    public void setDataFrom(Integer dataFrom) {
        this.dataFrom = dataFrom;
    }

    public String getTowerReg() {
        return towerReg;
    }

    public void setTowerReg(String towerReg) {
        this.towerReg = towerReg;
    }

    public String getTowerType() {
        return towerType;
    }

    public void setTowerType(String towerType) {
        this.towerType = towerType;
    }

    public String getPrvId() {
        return prvId;
    }

    public void setPrvId(String prvId) {
        this.prvId = prvId;
    }

    public UserLoginInfo getLoginUser() {
        return loginUser;
    }

    public void setLoginUser(UserLoginInfo loginUser) {
        this.loginUser = loginUser;
    }

    public Integer getQueryType() {
        return queryType;
    }

    public void setQueryType(Integer queryType) {
        this.queryType = queryType;
    }

    public String[] getTowerIds() {
        return towerIds;
    }

    public void setTowerIds(String[] towerIds) {
        this.towerIds = towerIds;
    }

    public String getDepId() {
        return depId;
    }

    public void setDepId(String depId) {
        this.depId = depId;
    }

    public String getAuditStatus() {
        return auditStatus;
    }

    public void setAuditStatus(String auditStatus) {
        this.auditStatus = auditStatus;
    }
}
