package com.xunge.dao.report;

import com.xunge.model.report.RptPrvTeleSiteAccrualVO;

import java.util.List;
import java.util.Map;

public interface RptPrvTeleSiteAccrualMapper {
    /**
     * 查询省份站点月计提数据
     *
     * @param map
     * @return
     */
    List<RptPrvTeleSiteAccrualVO> queryTeleSiteAccrualAll(Map<String, Object> map);

    /**
     * 查询地市站点月计提数据
     *
     * @param map
     * @return
     */
    List<RptPrvTeleSiteAccrualVO> queryTeleSiteAccrualByPrvId(Map<String, Object> map);

    /**
     * 查询区县站点月计提数据
     *
     * @param map
     * @return
     */
    List<RptPrvTeleSiteAccrualVO> queryTeleSiteAccrualByPregId(Map<String, Object> map);
}
