package com.xunge.dao.selfelec.focuscontract;

import com.xunge.model.selfelec.EleContract;
import com.xunge.model.selfelec.EleContractExample;
import com.xunge.model.selfelec.ElePaymentAuditinfo;
import com.xunge.model.selfelec.focuscontract.FocusEleContract;
import com.xunge.model.selfelec.focuscontract.FocusEleContractVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface FocusEleContractMapper {

    int countByExample(EleContractExample example);


    int deleteByExample(EleContractExample example);


    int deleteByPrimaryKey(String elecontractId);


    int insert(FocusEleContract eleContract);


    int insertSelective(EleContract record);


    List<EleContract> selectByExample(EleContractExample example);


    FocusEleContract selectByPrimaryKey(String elecontractId);


    int updateByExampleSelective(@Param("record") EleContract record, @Param("example") EleContractExample example);


    int updateByExample(@Param("record") EleContract record, @Param("example") EleContractExample example);


    int updateByPrimaryKeySelective(FocusEleContract ec);


    int updateByPrimaryKey(EleContract record);

    List<Map<String, Object>> selectContractNumByCondition(Map<String, Object> param);

    /**
     * 查询未关联合同数
     *
     * @param param
     * @return
     */
    int selectNolinkCont(Map<String, Object> param);

    /**
     * 查询所有合同数
     *
     * @param param
     * @return
     */
    int selectAllContract(Map<String, Object> param);

    /**
     * 根据报账点id查询合同
     *
     * @param
     * @return
     * @date 2018年08月29日
     * <AUTHOR>
     */
    EleContract queryContractByBillaccountId(String billaccountId);

    /**
     * according to oldContractId find the old elecontract's supplier
     *
     * @param oldContractId
     * @return FocusEleContract 返回类型
     * @description <描述>
     * <AUTHOR>
     * @version V1.0
     * @date 2018年9月7日
     * @email <EMAIL>
     */
    FocusEleContract selectByOldContractId(String oldContractId);

    /**
     * 物理删除合同
     *
     * @param paraMap
     * @return void 返回类型
     * @description <描述>
     * <AUTHOR>
     * @version V1.0
     * @date 2018年9月17日
     * @email <EMAIL>
     */
    void deleteByContractIds(Map<String, Object> paraMap);


    /**
     * @param @param  ec
     * @param @return 设定文件
     * @return int    返回类型
     * @throws
     * @Title: updateByContractId
     * @Description: TODO(这里用一句话描述这个方法的作用)
     */

    int updateByContractId(FocusEleContract ec);


    /**
     * @param @param supplierId
     * @param @param supplierIds    设定文件
     * @return void    返回类型
     * @throws
     * @Title: updateSupplierInfo
     * @Description: TODO(这里用一句话描述这个方法的作用)
     */

    void updateSupplierInfo(@Param("supplierId") String supplierId, @Param("supplierIds") List<String> supplierIds);


    /**
     * @param @param elecontractIds    设定文件
     * @return void    返回类型
     * @throws
     * @Title: deleteContracts
     * @Description: TODO(这里用一句话描述这个方法的作用)
     */

    void deleteContracts(@Param("elecontractIds") List<String> elecontractIds);


    /**
     * @param @return 设定文件
     * @return List<FocusEleContract>    返回类型
     * @throws
     * @Title: queryEleContractList
     * @Description: TODO(这里用一句话描述这个方法的作用)
     */

    List<FocusEleContract> queryEleContractList();


    /**
     * @param @param  contractId
     * @param @return 设定文件
     * @return List<FocusEleContract>    返回类型
     * @throws
     * @Title: queryEleContracts
     * @Description: TODO(这里用一句话描述这个方法的作用)
     */

    List<FocusEleContract> queryEleContracts(@Param("contractId") String contractId);


    /**
     * @param @param contractId
     * @param @param contractIds    设定文件
     * @return void    返回类型
     * @throws
     * @Title: updateComtractInfo
     * @Description: TODO(这里用一句话描述这个方法的作用)
     */

    void updateComtractInfo(@Param("contractId") String contractId, @Param("contractIds") List<String> contractIds);


    /**
     * @param @param ec    设定文件
     * @return void    返回类型
     * @throws
     * @Title: updateById
     * @Description: TODO(这里用一句话描述这个方法的作用)
     */

    void updateById(FocusEleContract ec);


    /**
     * @param @param ec    设定文件
     * @return void    返回类型
     * @throws
     * @Title: FocusEleContractVO
     * @Description: TODO(这里用一句话描述这个方法的作用)
     */

    void insertFocusEleContractVO(FocusEleContractVO ec);


    /**
     * @param @param ec    设定文件
     * @return void    返回类型
     * @throws
     * @Title: updateById
     * @Description: TODO(这里用一句话描述这个方法的作用)
     */

    void updateFocusEleContractVOById(FocusEleContractVO ec);


}