package com.xunge.dao.selfelec;

import com.xunge.model.basedata.PowerDataForPaymentVO;
import com.xunge.model.selfelec.*;
import com.xunge.model.selfelec.eleverificate.EleVerificatedetail;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

public interface EleBillaccountPaymentdetailMapper {

    Map<String, Object> queryVerificationBillamountActual(Map<String, Object> map);

    Map<String, Object> queryEleBillamountActual(Map<String, Object> map);

    Map<String, Object> queryOtherMap(Map<String, Object> map);

    Map<String, Object> queryEleLoss(Map<String, Object> map);

    Map<String, Object> queryIfDraftType(Map<String, Object> map);

    int queryPregIdExists(@Param("pregId") String pregId);

    int queryPregIdExistsWithType(@Param("pregId") String pregId, @Param("type") String type);

    int countByExample(EleBillaccountPaymentdetailExample example);


    int deleteByExample(EleBillaccountPaymentdetailExample example);


    int deleteByPrimaryKey(String billaccountpaymentdetailId);


    int insert(EleBillaccountPaymentdetail record);


    int insertSelective(EleBillaccountPaymentdetail record);

    int insertOperateTime(EleBillaccountPaymentdetail record);


    List<EleBillaccountPaymentdetail> selectByExample(EleBillaccountPaymentdetailExample example);


    EleBillaccountPaymentdetail selectByPrimaryKey(String billaccountpaymentdetailId);

    EleBillaccountPaymentdetail getEleBillaccountDetailByIdForTW(String billaccountpaymentdetailId);

    /**
     * 根据id查询信息
     *
     * @param billaccountpaymentdetailId
     * @return
     * <AUTHOR>
     */
    public EleBillaccountPaymentdetail queryById(String billaccountpaymentdetailId);


    int updateByExampleSelective(@Param("record") EleBillaccountPaymentdetail record,
                                 @Param("example") EleBillaccountPaymentdetailExample example);


    int updateByExample(@Param("record") EleBillaccountPaymentdetail record, @Param("example") EleBillaccountPaymentdetailExample example);


    int updateByPrimaryKeySelective(EleBillaccountPaymentdetail record);

    @Update("UPDATE ele_payment SET over_flow=#{overFlow} WHERE billaccountpaymentdetail_id=#{billaccountpaymentdetailId}")
    void updateOverFlow(@Param("billaccountpaymentdetailId") String billaccountpaymentdetailId, @Param("overFlow") int overFlow);

    int updateByPrimaryKeySelectiveMore(EleBillaccountPaymentdetail record);

    int updateByPrimaryKeySelectiveBydraft(EleBillaccountPaymentdetail record);


    int updateByPrimaryKey(EleBillaccountPaymentdetail record);

    /**
     * 修改流程状态
     *
     * @param hashMaps
     * @return
     * <AUTHOR>
     */
    int updateActivityCommit(Map<String, Object> hashMaps);

    int updateActivityCommitTele(Map<String, Object> hashMaps);

    int updateActivityCommitRentPayment(Map<String, Object> hashMaps);

    int updateActivityCommitElecontract(Map<String, Object> hashMaps);

    int updateActivityCommitTelecontract(Map<String, Object> hashMaps);

    int updateActivityCommitRentcontract(Map<String, Object> hashMaps);

    int updateActivityCommitRentcontractGH(Map<String, Object> hashMaps);

    int updateActivityCommitTwrAccountSummary(Map<String, Object> hashMaps);

    int updateActivityCommitTwrOilSummary(Map<String, Object> hashMaps);

    int updateActivityCommitVerification(Map<String, Object> hashMaps);

    int updateActivityCommitLoan(Map<String, Object> hashMaps);

    /**
     * 更新上次提交审核时间
     *
     * <AUTHOR>
     */
    int updateLastAuditingDate(Map<String, Object> hashMaps);

    int updatePaymentState(Map<String, Object> hashMap);

    /**
     * 根据报账点id查询缴费记录信息
     *
     * <AUTHOR>
     */
    List<EleBillaccountPaymentdetail> queryPaymentByBillAccountId(String id);

    /**
     * GUYGMMAN-958
     * 根据报账点id查询缴费记录信息(允许导入历史缴费)
     *
     * <AUTHOR>
     */
    List<EleBillaccountPaymentdetail> queryPaymentByBillAccountIdNew(@Param("billaccountId") String billaccountId);

    List<EleBillaccountPaymentdetail> queryPaymentByBillAccountIdForTW(String id);

    /**
     * GUYGMMAN-958
     * tw根据报账点id查询缴费记录信息(允许导入历史缴费)
     *
     * <AUTHOR>
     */
    List<EleBillaccountPaymentdetail> queryPaymentByBillAccountIdForTWNew(@Param("billaccountId") String billaccountId);

    /**
     * 根据报账点编码查询此最大缴费期终的信息
     *
     * @param
     * @return
     */
    public VEleBillaccountPaymentInfo queryMaxBillAccountEnded(String billaccountId);

    /**
     * 根据报账点id查询缴费记录信息
     *
     * @param billaccountId
     * @return
     * <AUTHOR>
     */
    List<Map<String, Object>> queryResourcePaymentByBillaccountId(Map<String, Object> billaccountId);

    List<Map<String, Object>> queryResourcePaymentByBillaccountIdForTW(String billaccountId);

    /**
     * 查询报账点下最近的一笔缴费
     *
     * @param
     * @return
     * @date 2018年08月07日
     * <AUTHOR>
     */
    EleBillaccountPaymentdetail queryLastPayment(String billaccountId);

    /**
     * 更新收款方银行账户、收款方银行行号、收款方名称
     *
     * @param record
     * @return
     */
    int updateReceiptInfo(EleBillaccountPaymentdetail record);


    /**
     * @param @param supplierId
     * @param @param supplierIds    设定文件
     * @return void    返回类型
     * @throws
     * @Title: updateSupplierInfo
     * @Description: TODO(这里用一句话描述这个方法的作用)
     */

    void updateSupplierInfo(@Param("supplierId") String supplierId, @Param("supplierIds") List<String> supplierIds);


    /**
     * @param @param elecontractId
     * @param @param elecontractIds    设定文件
     * @return void    返回类型
     * @throws
     * @Title: updateComtractInfo
     * @Description: TODO(这里用一句话描述这个方法的作用)
     */

    void updateComtractInfo(@Param("elecontractId") String elecontractId, @Param("elecontractIds") List<String> elecontractIds);

    int queryPaymentByStartTime(Map<String, String> map);

    int queryPaymentByTimeDate(Map<String, String> map);

    List<Map<String, Object>> queryMeterPaymentByBillaccountId(String billaccountId);

    int queryNextIncludePayment(Map<String, String> map);


    /**
     * insertOtherCost
     *
     * @param obj
     * @return void    返回类型
     * @description 新增其他费用
     * @version V1.0
     */
    void insertOtherCost(EleOtherAmountFinance obj);

    /**
     * updateOtherCost
     *
     * @param obj
     * @return void    返回类型
     * @description 修改其他费用
     * @version V1.0
     */
    void updateOtherCost(EleOtherAmountFinance obj);

    void deleteOtherCostByPaymentId(String billaccountpaymentdetailId);

    List<EleOtherAmountFinance> selectOtherCost(String billaccountpaymentdetailId);
    List<String> selectOtherCostIds(String billaccountpaymentdetailId);

    List<EleOtherAmountFinance> selectOtherCostByBillamountId(String billamountId);

    List<EleOtherAmountFinance> selectOtherCostByBillamountDetailId(String billamountdetailId);

    List<EleOtherAmountFinance> selectInOtherCost(List<String> item);

    List<EleOtherAmountFinance> selectInTeleOtherCost(List<String> item);

    void insertOtherCostRent(EleOtherAmountFinance obj);

    void updateOtherCostRent(EleOtherAmountFinance obj);

    void deleteOtherCostByPaymentIdRent(String billaccountpaymentdetailId);

    List<EleOtherAmountFinance> selectOtherCostRent(String billaccountpaymentdetailId);

    List<EleBillaccountPaymentdetail> selectUserList(Map<String, String> map);

    List<EleBillaccountPaymentdetail> selectUserListByLoginName(Map<String, String> map);

    List<EleBillaccountPaymentdetail> selectCostCenterList(Map<String, String> map);

    List<EleBillaccountPaymentdetail> selectCostCenterListByCode(List<String> list);

    List<EleBillaccountPaymentdetail> selectCostCenterListNotLike(Map<String, String> map);

    List<EleBillaccountPaymentdetail> queryUserAndCenterList(Map<String, String> map);

    EleBillaccountPaymentdetail queryPaymentDraft(String billaccountId);

    /**
     * 根据缴费id查询当前缴费的其他费用
     *
     * @param map
     * @return
     */
    List<EleOtherAmountFinance> queryOtherCostByPayment(Map<String, Object> map);

    List<EleOtherAmountFinance> queryOtherCostByPaymentId(@Param("paymentIdList") List<String> paymentIdList);

    public Map<String, Object> queryPayExceptionResource(Map<String, Object> param);

    public Map<String, Object> queryLoanExceptionResource(Map<String, Object> param);

    public Map<String, Object> queryerExceptionResource(Map<String, Object> param);

    public Map<String, Object> querRentExceptionResource(Map<String, Object> param);

    List<Map<String, Object>> queryMeterPaymentByBillaccountIdForTW(String billaccountId);

    List<EleBillaccountPaymentdetail> queryNextMeterByPayment(Map<String, Object> param);

    List<EleBillaccountPaymentdetail> queryLastMeterByPayment(Map<String, Object> param);

    List<EleBillaccountPaymentdetail> queryClassCodeMainOtherDetailByIds(@Param("billaccountpaymentdetailIds") List<String> billaccountpaymentdetailIds);

    List<EleBillaccountPaymentdetail> queryRentClassCodeMainOtherDetailByIds(@Param("billaccountRentpaymentdetailIds") List<String> billaccountRentpaymentdetailIds);

    public Map<String, Object> queryPrvNewflagIsUse(@Param("prvCode") String prvCode);

    List<EleBillaccountPaymentdetail> queryNowMeterByPayment(Map<String, Object> param);

    /**
     * 更新数据
     *
     * @param record 需要更新的数据
     * @return 条数
     */
    int updateById(EleBillaccountPaymentdetail record);

    int updateSelective(EleBillaccountPaymentdetail record);

    List<Map<String, Object>> queryElePaymentDetail(String billaccountpaymentdetailId);

    List<Map<String, Object>> queryEleVerificationDetail(String verificationId);

    List<EleBillaccountPaymentdetail> queryPaymentByBillAccount(Map<String, Object> billaccountId);

    List<VEleBillaccountInfo> queryEleBaseresourceByBillaccountId(Map<String, Object> cond);

    int updatePaymentUserId(Map<String, Object> pmMaps);

    String queryAdditionalRemark(@Param("billaccountpaymentdetailId") String billaccountpaymentdetailId, @Param("businessTable") String businessTable);

    /**
     * 根据缴费id查转改直信息
     *
     * @param billaccountpaymentdetailId
     * @return
     */
    EleBillaccountPaymentdetail queryTransfertostraightInfo(@Param("billaccountpaymentdetailId") String billaccountpaymentdetailId);

    EleBillaccountPaymentdetail queryVerificateTransfertostraightInfo(@Param("verificationId") String verificationId);

	EleBillaccountPaymentdetail queryLastInfoByBillaccount(String billaccountId);

	List<EleBillaccountPaymentdetail> queryLastMeterNumByBillaccount(@Param("billaccountId") String billaccountId,
                                                                     @Param("id") String id,
                                                                     @Param("startDate") String startDate,
                                                                     @Param("createDataTime") String createDataTime);

    List<EleBillaccountPaymentdetail> queryLastMeterNumByBillaccount4Import(@Param("billaccountId") String billaccountId,
                                                                            @Param("billamountDate")String billamountDate,
                                                                            @Param("startDate")String startDate);

    List<PowerDataForPaymentVO> selectPowerDataByPaymentCode(Map<String, Object> paraMap);

    EleBillaccountPaymentdetail queryColumnById(@Param("billaccountpaymentdetailId") String billaccountpaymentdetailId);

    void updateNote(@Param("billaccountpaymentdetailId") String billaccountpaymentdetailId, @Param("paymentdetailNote") String paymentdetailNote);

    void updateAttachmentNecessary(@Param("billaccountpaymentdetailId") String billaccountpaymentdetailId, @Param("attachmentNecessary") String attachmentNecessary);

    Set<Integer> getPaymentOverFlowTypeList(@Param("billaccountpaymentdetailId") String billaccountpaymentdetailId);

    List<EleVerificatedetail> queryMeterIsPayment(@Param("meterIds") List<String> meterIds, @Param("billaccountPaymentDetailId") String billaccountPaymentDetailId);

    Date queryPaymentCreateTimeById(@Param("id") String id, @Param("type") Integer type);

    List<ElePaymentdetail> queryElePaymentMeterIds(@Param("paymentId") String paymentId);

    List<ElePaymentdetail> queryAppMeterDegreeRecord(@Param("meterIds") List<String> meterIds, @Param("operDate") String operDate);

    String queryPaymentTime(@Param("id") String id);
}