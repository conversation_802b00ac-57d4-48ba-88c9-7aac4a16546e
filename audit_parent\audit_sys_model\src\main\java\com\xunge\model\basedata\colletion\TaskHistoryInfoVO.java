package com.xunge.model.basedata.colletion;

import java.math.BigDecimal;
import java.util.Date;

public class TaskHistoryInfoVO {
    String taskHistoryId; //任务历史表编码
    String taskInfoId; //任务信息表编码
    String prvId; //省份编码
    String prvName;
    Date startDatetime; //启动时间
    String comment; //反馈记录
    String taskName;
    String ftpUser;
    String fileName;
    Integer compareInsert;//对比新增/更新
    Integer compareUpdate;
    Integer compareDelete;//对比删除
    Integer verifySuccess;//验证成功
    Integer verifyFailed;//验证失败
    Integer databaseInsertSuccess; //新增入库结果成功
    Integer databaseInsertFailed;//新增入库结果失败
    Integer databaseUpdateSuccess;//更新入库结果成功
    Integer databaseUpdateFailed;//更新入库结果成功

    Integer elecontractInsertSuccess;//电费合同新增入库结果	新增成功
    Integer elecontractInsertFailed;//电费合同新增入库结果 新增失败
    Integer elecontractUpdateSuccess;//电费合同更新入库结果 成功
    Integer elecontractUpdateFailed;//电费合同更新入库结果 失败
    Integer rentcontractInsertSuccess;//租费合同新增入库结果 新增成功
    Integer rentcontractInsertFailed;//租费合同新增入库结果 新增失败
    Integer rentcontractUpdateSuccess;//租费合同更新入库结果 更新成功
    Integer rentcontractUpdateFailed;//租费合同新增入库结果 新增失败
    Integer dataTotal;
    BigDecimal verifySuccessRate;
    Integer collectionType;//采集类型

    public Integer getDataTotal() {
        return dataTotal;
    }

    public void setDataTotal(Integer dataTotal) {
        this.dataTotal = dataTotal;
    }

    public BigDecimal getVerifySuccessRate() {
        return verifySuccessRate;
    }

    public void setVerifySuccessRate(BigDecimal verifySuccessRate) {
        this.verifySuccessRate = verifySuccessRate;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public Integer getCompareInsert() {
        return compareInsert;
    }

    public void setCompareInsert(Integer compareInsert) {
        this.compareInsert = compareInsert;
    }

    public Integer getCompareUpdate() {
        return compareUpdate;
    }

    public void setCompareUpdate(Integer compareUpdate) {
        this.compareUpdate = compareUpdate;
    }

    public Integer getCompareDelete() {
        return compareDelete;
    }

    public void setCompareDelete(Integer compareDelete) {
        this.compareDelete = compareDelete;
    }

    public Integer getVerifySuccess() {
        return verifySuccess;
    }

    public void setVerifySuccess(Integer verifySuccess) {
        this.verifySuccess = verifySuccess;
    }

    public Integer getVerifyFailed() {
        return verifyFailed;
    }

    public void setVerifyFailed(Integer verifyFailed) {
        this.verifyFailed = verifyFailed;
    }

    public Integer getDatabaseInsertSuccess() {
        return databaseInsertSuccess;
    }

    public void setDatabaseInsertSuccess(Integer databaseInsertSuccess) {
        this.databaseInsertSuccess = databaseInsertSuccess;
    }

    public Integer getDatabaseInsertFailed() {
        return databaseInsertFailed;
    }

    public void setDatabaseInsertFailed(Integer databaseInsertFailed) {
        this.databaseInsertFailed = databaseInsertFailed;
    }

    public Integer getDatabaseUpdateSuccess() {
        return databaseUpdateSuccess;
    }

    public void setDatabaseUpdateSuccess(Integer databaseUpdateSuccess) {
        this.databaseUpdateSuccess = databaseUpdateSuccess;
    }

    public Integer getDatabaseUpdateFailed() {
        return databaseUpdateFailed;
    }

    public void setDatabaseUpdateFailed(Integer databaseUpdateFailed) {
        this.databaseUpdateFailed = databaseUpdateFailed;
    }

    public Integer getElecontractInsertSuccess() {
        return elecontractInsertSuccess;
    }

    public void setElecontractInsertSuccess(Integer elecontractInsertSuccess) {
        this.elecontractInsertSuccess = elecontractInsertSuccess;
    }

    public Integer getElecontractInsertFailed() {
        return elecontractInsertFailed;
    }

    public void setElecontractInsertFailed(Integer elecontractInsertFailed) {
        this.elecontractInsertFailed = elecontractInsertFailed;
    }

    public Integer getElecontractUpdateSuccess() {
        return elecontractUpdateSuccess;
    }

    public void setElecontractUpdateSuccess(Integer elecontractUpdateSuccess) {
        this.elecontractUpdateSuccess = elecontractUpdateSuccess;
    }

    public Integer getElecontractUpdateFailed() {
        return elecontractUpdateFailed;
    }

    public void setElecontractUpdateFailed(Integer elecontractUpdateFailed) {
        this.elecontractUpdateFailed = elecontractUpdateFailed;
    }

    public Integer getRentcontractInsertSuccess() {
        return rentcontractInsertSuccess;
    }

    public void setRentcontractInsertSuccess(Integer rentcontractInsertSuccess) {
        this.rentcontractInsertSuccess = rentcontractInsertSuccess;
    }

    public Integer getRentcontractInsertFailed() {
        return rentcontractInsertFailed;
    }

    public void setRentcontractInsertFailed(Integer rentcontractInsertFailed) {
        this.rentcontractInsertFailed = rentcontractInsertFailed;
    }

    public Integer getRentcontractUpdateSuccess() {
        return rentcontractUpdateSuccess;
    }

    public void setRentcontractUpdateSuccess(Integer rentcontractUpdateSuccess) {
        this.rentcontractUpdateSuccess = rentcontractUpdateSuccess;
    }

    public Integer getRentcontractUpdateFailed() {
        return rentcontractUpdateFailed;
    }

    public void setRentcontractUpdateFailed(Integer rentcontractUpdateFailed) {
        this.rentcontractUpdateFailed = rentcontractUpdateFailed;
    }

    public String getTaskHistoryId() {
        return taskHistoryId;
    }

    public void setTaskHistoryId(String taskHistoryId) {
        this.taskHistoryId = taskHistoryId;
    }

    public String getTaskInfoId() {
        return taskInfoId;
    }

    public void setTaskInfoId(String taskInfoId) {
        this.taskInfoId = taskInfoId;
    }

    public String getPrvId() {
        return prvId;
    }

    public void setPrvId(String prvId) {
        this.prvId = prvId;
    }

    public Date getStartDatetime() {
        return startDatetime;
    }

    public void setStartDatetime(Date startDatetime) {
        this.startDatetime = startDatetime;
    }

    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }

    public String getTaskName() {
        return taskName;
    }

    public void setTaskName(String taskName) {
        this.taskName = taskName;
    }

    public String getFtpUser() {
        return ftpUser;
    }

    public void setFtpUser(String ftpUser) {
        this.ftpUser = ftpUser;
    }

    public String getPrvName() {
        return prvName;
    }

    public void setPrvName(String prvName) {
        this.prvName = prvName;
    }

    public Integer getCollectionType() {
        return collectionType;
    }

    public void setCollectionType(Integer collectionType) {
        this.collectionType = collectionType;
    }

}
