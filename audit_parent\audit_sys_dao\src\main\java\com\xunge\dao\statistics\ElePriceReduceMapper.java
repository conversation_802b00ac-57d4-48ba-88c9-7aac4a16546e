package com.xunge.dao.statistics;

import com.xunge.model.statistics.ElePriceReduce;

import java.util.List;
import java.util.Map;

public interface ElePriceReduceMapper {

    List<ElePriceReduce> queryList(Map<String, Object> param);

    ElePriceReduce queryDetail(Map<String, Object> param);

    Integer insert(ElePriceReduce ncInfo);

    Integer update(ElePriceReduce ncInfo);

    ElePriceReduce findFill(ElePriceReduce ncInfo);

    void insertFill(ElePriceReduce ncInfo);

    ElePriceReduce queryCalculate(Map<String, Object> param);

    ElePriceReduce getLastReason();

    void updateCalculate(ElePriceReduce ncInfo);

}
