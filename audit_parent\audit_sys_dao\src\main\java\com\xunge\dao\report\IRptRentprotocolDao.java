package com.xunge.dao.report;

import java.util.List;
import java.util.Map;

public interface IRptRentprotocolDao {
    /**
     * 根据省份id查询各地市租费固话数据
     *
     * @return
     * <AUTHOR>
     */
    public List<Map<String, Object>> queryRptRentprotocolByPrvid(Map<String, Object> map);

    /**
     * 根据地市id查询区县租费固话数据
     *
     * @return
     * <AUTHOR>
     */
    public List<Map<String, Object>> queryRptRentprotocolByPregid(Map<String, Object> map);

    public List<Map<String, Object>> queryRptRentprotocol(Map<String, Object> map);
}
