package com.xunge.dao.selfrent.billaccount;

import com.xunge.model.selfrent.billAccount.RentPaymentVO;
import com.xunge.model.selfrent.billAccount.RentPaymentdetailVO;

import java.util.Map;

public interface IRentPaymentdetailDao {
    /**
     * 查询房屋缴费明细
     *
     * <AUTHOR>
     */
    public RentPaymentdetailVO queryPayMentDetailByBaseId(Map<String, Object> map);

    /**
     * 添加房屋缴费明细
     *
     * <AUTHOR>
     */
    public int insertRentPaymentdetail(Map<String, Object> map);

    /**
     * 修改房屋缴费明细
     */
    public int updateRentPaymentdetail(RentPaymentdetailVO rentpaymentdetailVO);

    RentPaymentVO queryLastRentPayment(Map<String, String> map);
}