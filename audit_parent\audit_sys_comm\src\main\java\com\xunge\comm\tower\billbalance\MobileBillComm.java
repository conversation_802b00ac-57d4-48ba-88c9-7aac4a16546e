package com.xunge.comm.tower.billbalance;

/**
 * 移动账单常量类
 *
 * <AUTHOR> 2017-10-13 10:39:48
 */
public class MobileBillComm {
    /**
     * 1：原产权方(03 存量产权方 ) 业务确认单编号:CTC-CRM-yyyy-000000
     */
    public static final String RESOURCE_ORI_RIGHT = "0301";
    /**
     * 2：既有共享(04 存量既有共享)
     */
    public static final String RESOURCE_SHARE = "04";
    /**
     * 3：存量自改(03 存量产权方 ) 业务确认单编号:CTC-CRM-(省code+市code)-yyyy-000000
     */
    public static final String RESOURCE_SELF_CORRECT = "0302";
    /**
     * 4：存量改造(05 存量新增共享)
     */
    public static final String RESOURCE_REMAKE = "05";
    /**
     * 5：新建铁塔(01 新建首家)
     */
    public static final String RESOURCE_NEW_TOWER = "01";
    /**
     * 5：新建铁塔(02 新建共享)
     */
    public static final String RESOURCE_NEW_SHARE = "02";
    /**
     * 03 存量产权方
     */
    public static final String RESOURCE_RIGHT = "03";

    /**
     * 维护等级 最高等级 01 高等级 02 标准 03 高山海岛站 04 不考核站
     */
    public static final String MAINTENANCE_LEVEL = "01";

    /**
     * 1110 普通地面塔
     */
    public static final String TOWER_COMMON_GROUND = "1110";
    /**
     * 1130 简易塔
     */
    public static final String TOWER_SIMPLE = "1130";
    /**
     * 1220 楼面抱杆
     */
    public static final String TOWER_FLOOR_POLE = "1220";
    /**
     * 1120 景观塔
     */
    public static final String TOWER_PRODUCT = "1120";
    /**
     * 1210 普通楼面塔
     */
    public static final String TOWER_COMMON_FLOOR = "1210";
    /**
     * -1 无铁塔
     */
    public static final String TOWER_NONE = "-1";

    /**
     * 油机发电服务费模式（0包干，3按次） 0 包干 3 按次
     */
    public static final String OILGENERATE_ELECTRIC_METHOD = "0";
    /**
     * 电力保障服务费模式 1 协助缴费（包干） 2 协助缴费（转售） 3 协助缴费（传导） 4 协助缴费（代垫）
     */
    public static final String ELECTRIC_PROTECTION_METHOD = "1";
    /**
     * 景观塔 最大挂高值
     */
    public static final double SCENERY_MAX_HIGH = 40;
    /**
     * 普通地面塔 最大挂高值
     */
    public static final double COMMON_GROUND_MAX_HIGH = 50;

    /**
     * RRU不上塔折扣
     */
    public static final String RRU_NOT_UP_TOWER = "OtherDiscountNotRRU";

    /**
     * 铁塔折旧年限
     */
    public static final String TOWER_DEPRECIABLE_LIFE = "TowerDepreciableLife";

    /**
     * 自有机房折旧年限
     */
    public static final String OWN_ROOM_DEPRECIABLE_LIFE = "OwnRoomDepreciableLife";

    /**
     * 其他机房折旧年限
     */
    public static final String OTHER_ROOM_DEPRECIABLE_LIFE = "OtherRoomDepreciableLife";
    /**
     * 电力引入折旧年限
     */
    public static final String ELECTRIC_IMPORT_DEPRECIABLE_LIFE = "ElectricImportDepreciableLife";

    /**
     * 存量调整比例
     */
    public static final String STOCK_DISCOUNT_NUMERIC = "StockDiscountNumeric";

    /**
     * 增量调整比例
     */
    public static final String INCREMENT_DISCOUNT_NUMERIC = "IncrementDiscountNumeric";

    /**
     * 配套折旧年限
     */
    public static final String SUPPORT_DEPRECIATION_TIME = "SupportingDepreciableLife";

    /**
     * 折损率
     */
    public static final String DERATING_RATE = "DeratingRate";

    /**
     * 毛利率加成
     */
    public static final String GROSS_PROFIT_RATIO = "GrossProfitRatio";
    /**
     * 自有机房
     */
    public static final String OWNER_ROOM = "自有机房";
    /**
     * 租赁机房
     */
    public static final String RENT_ROOM = "租赁机房";
    /**
     * 一体化机柜
     */
    public static final String CABINET_ROOM = "一体化机柜（仅基础）";
    /**
     * rru拉远
     */
    public static final String RRU = "RRU拉远（仅基础）";
    /**
     * 机房配套
     */
    public static final String ROOM_SUPPORTING = "机房配套";
    /**
     * rru拉远配套
     */
    public static final String RRU_SUPPORTING = "RRU拉远配置";
    /**
     * 一体化机柜配套
     */
    public static final String CABINET_SUPPORTING = "一体化机柜配置";

}
