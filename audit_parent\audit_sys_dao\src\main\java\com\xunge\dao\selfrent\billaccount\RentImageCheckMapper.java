package com.xunge.dao.selfrent.billaccount;

import com.xunge.model.selfrent.billAccount.RentImageCheckMeterVO;
import com.xunge.model.towerrent.settlement.TwrImageCheckMeterVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Description:
 * @Author: dxd
 * @Date: 2023/5/17 9:32
 */
public interface RentImageCheckMapper {

    List<RentImageCheckMeterVO> queryRentMeterImage(@Param("businessId")String businessId,@Param("businessType") String businessType);

    String queryImageUrlByAttachmentId(@Param("attachmentId") String attachmentId);

    String queryImageUrlByAttachmentIdThreeTower(@Param("attachmentId") String attachmentId);

    int saveRentImageMeter(List<RentImageCheckMeterVO> imageList);

    void updateRentImageCheckData(@Param("businessId") String businessId);

    List<String> getDatAttachmentCompressIds(String attachmentId);

    void updateRentImageCheckState(@Param("businessType")String businessType,
                                  @Param("businessId")String businessId);
}
