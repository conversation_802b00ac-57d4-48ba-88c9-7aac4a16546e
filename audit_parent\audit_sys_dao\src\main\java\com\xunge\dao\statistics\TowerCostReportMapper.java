package com.xunge.dao.statistics;

import com.xunge.model.statistics.RptPrvTowerCostReport;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @ClassName TowerCostReportMapper
 * @Description TODO
 * <AUTHOR>
 */
public interface TowerCostReportMapper {
    List<RptPrvTowerCostReport> queryJtList(@Param("year") String year, @Param("month")String month);

    List<RptPrvTowerCostReport> queryPrvList(@Param("prvId")String prvId,@Param("year")String year,@Param("month")String month);
}
