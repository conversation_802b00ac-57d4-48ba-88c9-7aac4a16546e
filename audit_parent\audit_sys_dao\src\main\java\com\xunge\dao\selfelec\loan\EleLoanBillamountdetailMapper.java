package com.xunge.dao.selfelec.loan;

import com.xunge.model.selfelec.loan.*;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface EleLoanBillamountdetailMapper {
    int countByExample(EleLoanBillamountdetailExample example);

    int deleteByExample(EleLoanBillamountdetailExample example);

    int deleteByPrimaryKey(String billamountdetailId);

    int insert(EleLoanBillamountdetail record);

    int insertSelective(EleLoanBillamountdetail record);

    List<EleLoanBillamountdetail> selectByExample(EleLoanBillamountdetailExample example);

    EleLoanBillamountdetail selectByPrimaryKey(String billamountdetailId);

    int updateByExampleSelective(@Param("record") EleLoanBillamountdetail record, @Param("example") EleLoanBillamountdetailExample example);

    int updateByExample(@Param("record") EleLoanBillamountdetail record, @Param("example") EleLoanBillamountdetailExample example);

    int updateByPrimaryKeySelective(EleLoanBillamountdetail record);

    int updateByPrimaryKey(EleLoanBillamountdetail record);

    List<EleLoanBillamountdetail> queryEleLoanBillamountdetailList(EleLoan eleLoan);

    List<EleLoanBillamountdetail> queryFinanceEleLoanBillamountdetailList(EleLoan eleLoan);

    List<EleLoanBillamountdetail> queryBillamountdetailList(Map<String, Object> maps);

    void deleteByBillamountIds(@Param("ids") List<String> ids);

    List<EleLoanBillamountdetail> queryBillamountDetailByPrimaryKey(Map<String, Object> maps);

    List<EleLoanBillamountdetail> queryBillamountDetail(Map<String, Object> maps);

    List<EleLoanBillamountdetailFinance> queryBillamountDetailFinance(Map<String, Object> maps);

    List<EleLoanBillamountdetail> queryBillamountDetailVendor(@Param("billamountId") String billamountId);

    void deleteByBillamountdetailIds(@Param("ids") List<String> ids);

    int updateByBillamountId(EleLoanBillamount record);

    /**
     * @Title: queryRegNameAndRegId @Description: TODO(根据汇总单查询区域) @param @param
     * billamountId @param @return 设定文件 @return List<EleLoanBillamountdetail> 返回类型 @throws
     */

    List<EleLoanBillamountdetail> queryRegNameAndRegId(@Param("billamountId") String billamountId);

    List<EleLoanBillamountdetail> querySupplierName(@Param("billamountId") String billamountId);

    List<EleLoanBillamountdetail> querySupplierInfo(@Param("billamountId") String billamountId);

    void editPayment(EleLoanBillamount record);


}