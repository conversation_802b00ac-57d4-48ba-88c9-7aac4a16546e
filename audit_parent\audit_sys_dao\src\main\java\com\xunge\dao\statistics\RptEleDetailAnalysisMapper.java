package com.xunge.dao.statistics;

import com.xunge.model.statistics.RptEleDetailAnalysis;
import com.xunge.model.statistics.YearMonthWeekVO;

import java.util.List;
import java.util.Map;

/**
 * @ClassName:    RptEleDetailAnalysisMapper
 * @Description:  ${description}
 * @Date:         2021/12/28 9:03
 * @Version:      1.0
 */
public interface RptEleDetailAnalysisMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(RptEleDetailAnalysis record);

    int insertSelective(RptEleDetailAnalysis record);

    RptEleDetailAnalysis selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(RptEleDetailAnalysis record);

    int updateByPrimaryKey(RptEleDetailAnalysis record);

    List<YearMonthWeekVO> queryYearMonthWeek(Map<String,Object> map);

    List<RptEleDetailAnalysis> queryRentWeeklyReportList(Map<String,Object> map);

    List<RptEleDetailAnalysis> queryRentWeeklyReportSumList(Map<String,Object> map);
}
