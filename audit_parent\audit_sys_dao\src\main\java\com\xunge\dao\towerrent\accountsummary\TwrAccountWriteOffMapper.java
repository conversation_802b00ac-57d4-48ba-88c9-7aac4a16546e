package com.xunge.dao.towerrent.accountsummary;

import com.xunge.model.towerrent.accountsummary.TwrAccrualPushDetail;
import com.xunge.model.towerrent.accountsummary.WriteOffDetail;
import com.xunge.model.towerrent.accountsummary.query.TwrAccrualWriteOffQueryDto;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

public interface TwrAccountWriteOffMapper {

    /**
     * 根据summaryId获取冲销明细
     * @param twrAccrualWriteOffQueryDto
     * @return
     */
    List<TwrAccrualPushDetail> queryAccrualWriteOffSummaryDetail(@Param("dto") TwrAccrualWriteOffQueryDto twrAccrualWriteOffQueryDto);

    /**
     * 根据summaryId获取累提冲销金额
     * @param twrAccrualWriteOffQueryDto
     * @return
     */
    List<TwrAccrualPushDetail> getWriteOffBookAmountList(@Param("dto") TwrAccrualWriteOffQueryDto twrAccrualWriteOffQueryDto);

    /**
     * 根据prvId判断当时省份计提是否为全省计提
     * @param prvId
     * @return
     */
    String getWholePrvConfig(@Param("prvId") String prvId);

    /**
     * 查询冲销明细列表数据展示
     * @param twrAccrualWriteOffQueryDto
     * @return
     */
    List<TwrAccrualPushDetail> queryTwrWriteOffGenerateDataDetail(TwrAccrualWriteOffQueryDto twrAccrualWriteOffQueryDto);

    /**
     * 根据summaryId 或者 pushDetailId删除关联关系
     * @param twrAccrualWriteOffQueryDto
     */
    void deleteWriteOffDetail(TwrAccrualWriteOffQueryDto twrAccrualWriteOffQueryDto);

    /**
     * 报账汇总页面批量汇总单时删除冲销明细
     * @param params
     */
    void delTwrWriteOffAccrualDetail(Map<String, Object> params);

    /**
     * 更新报账关联冲销单审核状态
     * @param pushState
     * @param accountsummaryCode
     */
    void updateWriteOffDetailState(@Param("pushState") int pushState, @Param("accountsummaryCode") String accountsummaryCode);

    /**
     * 查询可冲销金额
     * @param pushDetailId  计提明细ID
     * @param writeOffDetailId 冲销明细ID
     * @return
     */
    BigDecimal queryAllowWriteOffAmount(@Param("pushDetailId")String pushDetailId,@Param("writeOffDetailId")String writeOffDetailId);

    /**
     * 冲销明细入库
     * @param list
     */
    void insertWriteOffDetail(@Param("list")  List<WriteOffDetail> list);

    void deleteWriteOffDetailById(TwrAccrualWriteOffQueryDto twrAccrualWriteOffQueryDto);
}
