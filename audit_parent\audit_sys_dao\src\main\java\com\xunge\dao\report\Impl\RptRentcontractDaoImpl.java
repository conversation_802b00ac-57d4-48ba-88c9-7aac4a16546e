package com.xunge.dao.report.Impl;

import com.xunge.core.page.Page;
import com.xunge.dao.AbstractBaseDao;
import com.xunge.dao.report.IRptRentContractDao;
import com.xunge.filter.PageInterceptor;
import com.xunge.model.report.RptTimelyVO;

import java.util.List;
import java.util.Map;

public class RptRentcontractDaoImpl extends AbstractBaseDao implements IRptRentContractDao {

    final String Namespace = "com.xunge.mapping.RptRentcontractVOMapper.";
    final String Namespace1 = "com.xunge.mapping.RptTimelyVOMapper.";

    @Override
    public List<Map<String, Object>> queryRptRentcontractByPrvid(Map<String, Object> map) {
        return this.getSqlSession().selectList(Namespace + "queryRptRentcontractByPrvid", map);
    }

    @Override
    public List<Map<String, Object>> queryRptRentcontractByPregid(Map<String, Object> map) {
        return this.getSqlSession().selectList(Namespace + "queryRptRentcontractByPregid", map);
    }

    @Override
    public List<Map<String, Object>> queryRptRentcontract(Map<String, Object> map) {
        return this.getSqlSession().selectList(Namespace + "queryRptRentcontract", map);
    }

    @Override
    public Page<List<RptTimelyVO>> queryTimely(Map<String, Object> paraMap, int pageNumber, int pageSize) {
        PageInterceptor.startPage(pageNumber, pageSize);
        this.getSqlSession().selectList(Namespace1 + "queryTimely", paraMap);
        return PageInterceptor.endPage();
    }
}
