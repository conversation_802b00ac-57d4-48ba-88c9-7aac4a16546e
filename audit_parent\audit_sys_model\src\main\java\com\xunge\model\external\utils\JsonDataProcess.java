package com.xunge.model.external.utils;

import com.alibaba.fastjson.JSONObject;
import com.xunge.model.FeedBackObject;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description json转换工具
 * <AUTHOR>
 * @Date 2021/1/27
 * @modifier ZXX
 * @date 2021/1/27
 * @Version 1.0
 **/
@Slf4j
public class JsonDataProcess {
    public static <T> T jsonDataCovert(String json, FeedBackObject r, Class<T> clazz) {
        T t = null;
        try {
            t = JSONObject.parseObject(json, clazz);
            r.setSuccess("1");
        } catch (Exception e) {
            r.setSuccess("0");
            r.setMsg(e.getMessage());
            log.error("json转换异常", e.getMessage());
        }
        return t;
    }
}
