package com.xunge.model.app.basenum;

/**
 * @Auther: hxh
 * @Date: 2018/8/24 14:49
 * @Description: 定义错误类型
 */
public enum ResultCode {

    error_500("500", "异常"),

    error_404("404", "异常"),

    success("0000", "成功"),

    failed("1001", "失败"),

    failedLogin("2001", "登录失败"),

    failed_userNotExixt("200101", "用户不存在或已被禁用"),

    failed_userNotExixtInPrv("200102", "该省用户不存在，或省份选择错误"),

    failed_password("200103", "密码错误"),

    failed_userStop("200104", "账户已停用！重新启用请联系管理员!"),

    failed_userDelete("200105", "账户已删除！重新启用请联系管理员!"),

    failed_userRegionError("200106", "此账户所属区县不存在或存在异常!"),

    failed_verifyCode("200107", "图片验证码错误"),

    failed_smsCodeEepire("200108", "短信验证码已过期"),

    failed_smsCodeError("200109", "短信验证码错误"),

    getFailed_notPression("200110", "此账户无APP使用权限!"),

    failedLogout("2002", "登出失败"),

    failedToken("2003", "token验证失败"),

    failedChangePWD("2004", "修改密码失败"),

    failed_oldPWDError("200402", "原始密码错误!"),

    failed_newPWDError("200403", "密码需同时包含数字，字母，特殊符号"),

    failed_newAndOldPWDError("200404", "新密和原密码一致，请重新填写新密码"),

    failedSendSms("2005", "获取短信验证码失败"),

    failed_tooMoreTimes("2005001", "操作太频繁"),

    failedfindPWD("2006", "找回密码失败"),

    failed_errorUsername("2006001", "登录账号错误"),

    errorParam("3002", "参数错误"),

    errorSystem("3003", "系统错误"),

    errorDataBase("3004", "数据库错误"),

    errorTimeOut_login("3005", "登陆超时"),

    failUpload("4001", "上传文件失败"),

    failUpload_max("400101", "上传文件过大"),

    failUpload_notExist("400102", "上传文件为空"),

    failUpload_notSupport("4001003", "不支持的文件格式"),

    failSign("5001", "签名验证失败"),

    failRecordDegree("6001", "电表度数超过规定最大值"),

    remarkOverSize("6002", "备注内容超长");


    private String code;

    private String msg;

    ResultCode(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public String getCode() {
        return code;
    }

//    public void setCode(String code) {
//        this.code = code;
//    }

    public String getMsg() {
        return msg;
    }

//    public void setMsg(String msg) {
//        this.msg = msg;
//    }
}
