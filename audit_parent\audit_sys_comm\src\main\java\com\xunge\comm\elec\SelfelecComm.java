package com.xunge.comm.elec;

import java.math.BigDecimal;

public class SelfelecComm {
    //999999999999.999999
    public final static BigDecimal NUMBER_BENCHMARK_MAX = new BigDecimal("999999999999.999999");
    //-999999999999.999999
    public final static BigDecimal NUMBER_BENCHMARK_MIN = new BigDecimal("-999999999999.999999");
    //省份与url分割符
    public static final String PRV_CODE_URL_SPLIT = "@";
    //省份之间分割符
    public static final String PRV_CODE_SPLIT = ",";
    //缴费单分隔符
    public static final String PAYMENT_CODE_SPLIT = "-";
    /**
     * 操作成功:0
     */
    public static int STATUS_0 = 0;
    /**
     * 操作失败:1
     */
    public static int STATUS_1 = 1;
    /**
     * 日期格式---yyyyMM
     */
    public static String FORMAT = "yyyyMM";
    /**
     * number---"01"
     */
    public static String NUMBER_STR = "01";
    /**
     * 日期格式:yyyyMMddHHmmss
     */
    public static String FORMAT_yyyyMMddHHmmss = "yyyyMMddHHmmss";
    /**
     * 日期格式:yyyyMMddHHmmssSSS
     */
    public static String FORMAT_yyyyMMddHHmmssSSS = "yyyyMMddHHmmssSSS";
    /**
     * 日期格式:yyyy-MM-dd
     */
    public static String FORMAT_yyyyMMdd = "yyyy-MM-dd";
    /**
     * 日期格式:yyyy/MM/dd
     */
    public static String FORMAT_yyyy_MM_dd = "yyyy/MM/dd";
    /**
     * 数字 -0
     */
    public static String NUMBER_0 = "0";
    /**
     * 数字 -10
     */
    public static String NUMBER_STR_10 = "10";
    /**
     * 数字 -2
     */
    public static int NUMBER_2 = 2;
    /**
     * 数字 -"2"
     */
    public static String NUMBER_STR_2 = "2";
    /**
     * 数字 -3
     */
    public static int NUMBER_3 = 3;
    /**
     * 数字 -1
     */
    public static int NUMBER_1 = 1;

    public static int NUMBER_ZERO = 0;
    /**
     * 数字 -6
     */
    public static int NUMBER_6 = 6;
    /**
     * 数字 -6
     */
    public static int NUMBER_7 = 7;
    /**
     * 数字 -21
     */
    public static int NUMBER_21 = 21;
    /**
     * 数字 -24
     */
    public static int NUMBER_24 = 24;
    /**
     * 数字 -8
     */
    public static int NUMBER_8 = 8;
    /**
     * 数字 -9
     */
    public static int NUMBER_9 = 9;
    /**
     * 数字 -12
     */
    public static int NUMBER_12 = 12;
    /**
     * 数字 -30
     */
    public static int NUMBER_30 = 30;
    /**
     * 数字 -365
     */
    public static int NUMBER_365 = 365;
    /**
     * 数字 -3600000
     */
    public static int NUMBER_3600000 = 3600000;
    /**
     * 数字 -60
     */
    public static int NUMBER_60 = 60;
    /**
     * 数字 -72
     */
    public static int NUMBER_72 = 72;
    /**
     * 数字 -10
     */
    public static int NUMBER_10 = 10;
    /**
     * 数字 -4
     */
    public static int NUMBER_4 = 4;
    /**
     * 数字 -5
     */
    public static int NUMBER_5 = 5;
    /**
     * 数字 -500
     */
    public static int NUMBER_500 = 500;
    /**
     * 数字 -1000
     */
    public static int NUMBER_1000 = 1000;
    /**
     * 数字 -20
     */
    public static int NUMBER_20 = 20;
    /**
     * 数字 -90
     */
    public static int NUMBER_90 = 90;
    /**
     * 数字 -100
     */
    public static int NUMBER_100 = 100;
    /**
     * 数字 -1024*1024
     */
    public static long NUMBER_1024 = 1024 * 1024;
    /**
     * 是否缴费状态 IS_PAY_YES-已缴费
     */
    public static String IS_PAY_YES = "已缴费";
    /**
     * 是否缴费状态 IS_PAY_NO-待缴费
     */
    public static String IS_PAY_NO = "待缴费";
    /**
     * 不可重复缴费
     */
    public static String DONOT_AGAIN_PAY = "不可重复缴费！";
    /**
     * 日期格式:yyyy/MM
     */
    public static String FORMAT_yyyy_MM = "yyyy/MM";
    /**
     * 日期格式:yyyy
     */
    public static String FORMAT_yyyy = "yyyy";
    /**
     * 日期格式:yyyy-MM-dd
     */
    public static String FORMAT_yyyy_MM_dd_HH_mm_ss = "yyyy-MM-dd";
    /**
     * 浮点数 0.0000
     */
    public static double NUMBER_FORMAT = 0.0000;
    /**
     * 浮点数 1.06
     */
    public static double NUMBER_FORMAT_1_06 = 1.06;
    /**
     * 6位小数开始 000001
     */
    public static String SIX_NUM_ONE = "000001";
    /**
     * hbase标杆表名
     */
    public static String BENCHMARK_BILLACCOUNT = "ele_benchmark_billaccount_";
    /**
     * 标杆列族名
     */
    public static String FAMILY_KEY = "benchmark";
    /**
     * 日期格式:yyyy-MM-dd HH:mm:ss
     */
    public static String FORMAT_yyyy_MM_dd_HH_mm_ss1 = "yyyy-MM-dd HH:mm:ss";
    /**
     * 缴费单来源 “1”:录入 ”2“：导入 ”3“：自动建单
     */
    public static String DATA_FROM_TYPE_1 = "1";
    public static String DATA_FROM_TYPE_2 = "2";
    public static String DATA_FROM_TYPE_3 = "3";

    /**
     * 冗余price_model标记 0：不用冗余 1：需要冗余
     */
    public static String PIRCEMODELFLAG = "PIRCEMODELFLAG";
    public static String PIRCEMODELFLAG_0 = "0";
    public static String PIRCEMODELFLAG_1 = "1";

    /**
     * 业务活动编码字段
     */
    public static String activityCode = "activityCode";

    /**
     * 业务活动需省稽核
     */
    public static String SEARCHKEYWORDS_FORAUDIT = "业务活动需省稽核；";

    /**
     * 缴费单编码字段
     */
    public static String paymentCode = "paymentCode";
    /**
     * 缴费类型；0：本单，1后付费，2核销，3IC卡，4新能源，5塔维
     */
    public static String paymentType = "paymentType";

    /**
     * 报账点类型字段（新能源类型标识）
     */
    public static String billaccountTypeFlag = "billaccountTypeFlag";
    public static String LINKAGEAUDITNOTE = "该报账点关联的资源涉及多个新能源报账点，不进行联动稽核。";
    public static String LINKAGEAUDITNOTE_NEW = "该报账点关联的资源涉及多个普通报账点，不进行联动稽核。";




}
