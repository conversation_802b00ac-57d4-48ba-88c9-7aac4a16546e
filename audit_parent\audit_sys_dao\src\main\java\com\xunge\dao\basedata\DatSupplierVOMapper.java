package com.xunge.dao.basedata;

import com.xunge.model.basedata.DatSupplierVO;
import com.xunge.model.basedata.DatSupplierVOExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface DatSupplierVOMapper {
    //    int countByExample(DatSupplierVOExample example);

    //    int deleteByExample(DatSupplierVOExample example);

    int deleteByPrimaryKey(Map<String, Object> paramMap);

    int insert(DatSupplierVO record);

    int insertSelective(DatSupplierVO record);

    List<DatSupplierVO> selectByExample(DatSupplierVOExample example);

    DatSupplierVO selectByPrimaryKey(Map<String, Object> map);

    DatSupplierVO selectById(String supplierId);

    //    DatSupplierVO selectByParam(DatSupplierVO datSupplierVO);

    //    List<String> selectByIdlist(List<String> ids);

    //    int updateByExampleSelective(@Param("record") DatSupplierVO record, @Param("example") DatSupplierVOExample example);

    //    int updateByExample(@Param("record") DatSupplierVO record, @Param("example") DatSupplierVOExample example);

    int updateByPrimaryKeySelective(DatSupplierVO record);

    int updateByPrimaryKey(DatSupplierVO record);

    public List<DatSupplierVO> queryBindingContract(Map<String, Object> supplierObj);

    public List<DatSupplierVO> queryBindingRentContract(Map<String, Object> supplierObj);

    //    boolean batchInsert(List<DatSupplierVO> record);

    //    boolean batchUpdate(List<DatSupplierVO> record);

    List<DatSupplierVO> querySupplierVO(Map<String, Object> paramMap);

    List<DatSupplierVO> queryTowerSupplierVO(Map<String, Object> paramMap);

    List<DatSupplierVO> querySupplierByPregId(Map<String, Object> paramMap);

    List<DatSupplierVO> cherkTowerSupplier(Map<String, Object> paramMap);

    int queryEleContract(String supplierId);

    int queryRentContract(String supplierId);

    List<String> queryDatSupplierByPregId(Map<String, Object> maps);

    //根据预付费查询供应商
    List<DatSupplierVO> querySupplierList(Map<String, Object> maps);

    List<String> querySupplierByVerification(Map<String, Object> maps);

    List<String> querySupplierByPayment(Map<String, Object> maps);

    //业务主键
    List<DatSupplierVO> selectSupplierBusinessOnly(Map<String, Object> paramMap);

    //物理删除
    int deletePhysicalByPrimaryKey(Map<String, Object> paramMap);

    DatSupplierVO querySupplierInfo(DatSupplierVO ds);

    List<DatSupplierVO> querySupplierByVO(DatSupplierVO ds);

    DatSupplierVO queryEleSupplierByContractId(String contractId);

    DatSupplierVO queryRentSupplierByContractId(String contractId);


    /**
     * @param @param  ds
     * @param @return 设定文件
     * @return DatSupplierVO    返回类型
     * @throws
     * @Title: querySupplierOrderBytime
     * @Description: TODO(这里用一句话描述这个方法的作用)
     */

    DatSupplierVO querySupplierOrderBytime(DatSupplierVO ds);


    /**
     * @param @return 设定文件
     * @return List<DatSupplierVO>    返回类型
     * @throws
     * @Title: queryRepeatSupplierInfo
     * @Description: TODO(这里用一句话描述这个方法的作用)
     */

    List<DatSupplierVO> queryRepeatSupplierInfo();


    /**
     * @param datSupplierVO
     * @param @return       设定文件
     * @return List<DatSupplierVO>    返回类型
     * @throws
     * @Title: querySupplierByCodeAndName
     * @Description: TODO(这里用一句话描述这个方法的作用)
     */

    List<DatSupplierVO> querySupplierByCodeAndName(DatSupplierVO datSupplierVO);


    /**
     * @param @param supplierId    设定文件
     * @return void    返回类型
     * @throws
     * @Title: updateRegidById
     * @Description: TODO(这里用一句话描述这个方法的作用)
     */

    void updateRegById(@Param("supplierId") String supplierId);


    /**
     * @param @param supplierIds    设定文件
     * @return void    返回类型
     * @throws
     * @Title: deleteSuppliers
     * @Description: TODO(这里用一句话描述这个方法的作用)
     */

    void deleteSuppliers(@Param("supplierIds") List<String> supplierIds);

    void updateByPrimaryKeySelectiveFocus(DatSupplierVO datSupplierVO);

    List<DatSupplierVO> queryDatContractSuppliers(@Param("ids") List<String> ids);

    List<DatSupplierVO> queryInnerFlagBySupplierid(Map<String, Object> cond);

    /**
     * 更新供应商是否为内部供应商
     *
     * @param DatSupplierVO
     */
    void updateSupplierInner(DatSupplierVO d);

    int querySupplierCount();

    List<DatSupplierVO> querySupplierPage(@Param("start") int start, @Param("limitSize") int limitSize);

    List<DatSupplierVO> getSupplierByCode(Map<String, Object> map);
}
