package com.xunge.dao.selfelec.inspect.gd;

import com.xunge.model.selfelec.inspect.gd.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2021-10-26 14:11
 * @description:
 */
public interface InspectInfoApplyResultMapper {

    /**
     * canton_result_apply_info
     *
     * @param infoApplyResult 新增主信息数据
     */
    void insertInspectInfoApplyResult(InfoApplyResult infoApplyResult);

    /**
     * canton_result_check_info
     *
     * @param checkResult 新增稽核数据
     */
    void insertInspectInfoCheckResult(CheckResult checkResult);

    /**
     * canton_result_file_info
     *
     * @param fileList 新增附件信息数据
     */
    void insertInspectInfoFileList(FileList fileList);

    /**
     * canton_result_audit_info
     *
     * @param auditInformation 新增审核信息数据
     */
    void insertInspectInfoAuditInformation(AuditInformation auditInformation);

    /**
     * canton_push_info_log
     *
     */
    void insertInspectPushInfoLog(InspectPushInfoLog pushInfoLog);


    /**
     * 获取稽核结果
     * @param map
     * @return
     */
    List<InfoApplyResult> selectInspectInfoApplyResult(Map<String,Object> map);

    /**
     * 修改稽核记录版本号
     * @param map
     * @return
     */
    int updateTwrInspectVersion(Map<String,Object> map);

    /**
     *
     *
     */
    void insertElectricPredictionInfoLog(InspectPushInfoLog pushInfoLog);

}
