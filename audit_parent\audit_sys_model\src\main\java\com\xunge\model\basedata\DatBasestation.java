package com.xunge.model.basedata;

import java.util.Date;

public class DatBasestation {
    
    private String basestationId;

    
    private String prvId;

    private String regId;
    private String pregId;

    
    private String baseresourceId;

    
    private String baseresourceCuid;

    
    private String basestationCode;

    
    private String basestationName;

    
    private Integer basestationCategory;

    
    private Integer basestationType;

    
    private Date basestationOpendate;

    
    private Date basestationStopdate;

    
    private Integer basestationVendor;

    
    private String basestationModel;

    
    private Long basestationPower;

    
    private Integer basestationState;

    
    private Integer basestationCarrier;

    
    private Integer basestationCovertype;

    
    private Integer dataFrom;

    
    public String getBasestationId() {
        return basestationId;
    }

    
    public void setBasestationId(String basestationId) {
        this.basestationId = basestationId == null ? null : basestationId.trim();
    }

    
    public String getPrvId() {
        return prvId;
    }

    
    public void setPrvId(String prvId) {
        this.prvId = prvId == null ? null : prvId.trim();
    }

    
    public String getBaseresourceId() {
        return baseresourceId;
    }

    
    public void setBaseresourceId(String baseresourceId) {
        this.baseresourceId = baseresourceId == null ? null : baseresourceId.trim();
    }

    
    public String getBaseresourceCuid() {
        return baseresourceCuid;
    }

    
    public void setBaseresourceCuid(String baseresourceCuid) {
        this.baseresourceCuid = baseresourceCuid == null ? null : baseresourceCuid.trim();
    }

    
    public String getBasestationCode() {
        return basestationCode;
    }

    
    public void setBasestationCode(String basestationCode) {
        this.basestationCode = basestationCode == null ? null : basestationCode.trim();
    }

    
    public String getBasestationName() {
        return basestationName;
    }

    
    public void setBasestationName(String basestationName) {
        this.basestationName = basestationName == null ? null : basestationName.trim();
    }

    
    public Integer getBasestationCategory() {
        return basestationCategory;
    }

    
    public void setBasestationCategory(Integer basestationCategory) {
        this.basestationCategory = basestationCategory;
    }

    
    public Integer getBasestationType() {
        return basestationType;
    }

    
    public void setBasestationType(Integer basestationType) {
        this.basestationType = basestationType;
    }

    
    public Date getBasestationOpendate() {
        return basestationOpendate;
    }

    
    public void setBasestationOpendate(Date basestationOpendate) {
        this.basestationOpendate = basestationOpendate;
    }

    public String getRegId() {
        return regId;
    }

    public void setRegId(String regId) {
        this.regId = regId;
    }

    public String getPregId() {
        return pregId;
    }

    public void setPregId(String pregId) {
        this.pregId = pregId;
    }

    
    public Date getBasestationStopdate() {
        return basestationStopdate;
    }

    
    public void setBasestationStopdate(Date basestationStopdate) {
        this.basestationStopdate = basestationStopdate;
    }

    
    public Integer getBasestationVendor() {
        return basestationVendor;
    }

    
    public void setBasestationVendor(Integer basestationVendor) {
        this.basestationVendor = basestationVendor;
    }

    
    public String getBasestationModel() {
        return basestationModel;
    }

    
    public void setBasestationModel(String basestationModel) {
        this.basestationModel = basestationModel == null ? null : basestationModel.trim();
    }

    
    public Long getBasestationPower() {
        return basestationPower;
    }

    
    public void setBasestationPower(Long basestationPower) {
        this.basestationPower = basestationPower;
    }

    
    public Integer getBasestationState() {
        return basestationState;
    }

    
    public void setBasestationState(Integer basestationState) {
        this.basestationState = basestationState;
    }

    
    public Integer getBasestationCarrier() {
        return basestationCarrier;
    }

    
    public void setBasestationCarrier(Integer basestationCarrier) {
        this.basestationCarrier = basestationCarrier;
    }

    
    public Integer getBasestationCovertype() {
        return basestationCovertype;
    }

    
    public void setBasestationCovertype(Integer basestationCovertype) {
        this.basestationCovertype = basestationCovertype;
    }

    
    public Integer getDataFrom() {
        return dataFrom;
    }

    
    public void setDataFrom(Integer dataFrom) {
        this.dataFrom = dataFrom;
    }
}