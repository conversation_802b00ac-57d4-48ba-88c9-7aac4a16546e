package com.xunge.dao.selfelec;

import com.xunge.model.selfelec.EleTowerContract;

import java.util.List;
import java.util.Map;

public interface EleTowerContractMapper {

    int deleteByPrimaryKey(String contractId);

    int insert(EleTowerContract record);

    EleTowerContract selectByPrimaryKey(String contractId);

    int updateByPrimaryKeySelective(EleTowerContract record);

    int updateByPrimaryKey(EleTowerContract record);

    /**
     * @param paramMap 铁塔电费合同查询
     * @return
     */
    List<EleTowerContract> queryTowerEleccontractList(Map<String, Object> paramMap);

    /**
     * @description 批量删除合同
     */
    public boolean updateTowerContractState(Map<String, Object> paramMap);
}