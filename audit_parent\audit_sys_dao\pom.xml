<?xml version="1.0"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.xunge</groupId>
        <artifactId>NCMS</artifactId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>
    <groupId>com.xunge</groupId>
    <artifactId>audit_sys_dao</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <name>audit_sys_dao</name>
    <url>http://maven.apache.org</url>
    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>
    <dependencies>
        <dependency>
            <groupId>com.xunge</groupId>
            <artifactId>audit_sys_comm</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.xunge</groupId>
            <artifactId>audit_sys_model</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.xunge</groupId>
            <artifactId>audit_sys_core</artifactId>
            <version>0.0.9</version>
        </dependency>
    </dependencies>
    <build>
        <finalName>audit_sys_dao-${project.version}</finalName>
        <plugins>
            <!-- Compiler 插件, 设定JDK版本 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>2.3.2</version>
                <configuration>
                    <source>${jdk.version}</source>
                    <target>${jdk.version}</target>
                    <showWarnings>true</showWarnings>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
