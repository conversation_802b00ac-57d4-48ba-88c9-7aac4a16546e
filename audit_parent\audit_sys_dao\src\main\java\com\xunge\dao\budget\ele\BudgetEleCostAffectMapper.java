package com.xunge.dao.budget.ele;

import com.xunge.model.budget.ele.BudgetEleCostAffectVO;
import com.xunge.model.budget.ele.BudgetEleParamVO;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * @author: LiangCheng
 * Date: 2022/7/5 16:53
 * Description:成本影响
 */
public interface BudgetEleCostAffectMapper {

    /**
     * 保存成本影响
     * @param costAffectList 成本影响集合
     * @param nodeType 节点
     */
    void saveEleCostAffect(BudgetEleParamVO budgetEleParamVO);

    List<BudgetEleCostAffectVO> getEleCostAffect(BudgetEleParamVO budgetEleParamVO);

    void editEleCostAffect(BudgetEleParamVO budgetEleParamVO);

    BudgetEleCostAffectVO getEleCostAffectSum(BudgetEleParamVO budgetEleParamVO);

    void delBudgetEleData(BudgetEleParamVO budgetEleParamVO);

    void delEleCostAffect(BudgetEleParamVO budgetEleParamVO);

}
