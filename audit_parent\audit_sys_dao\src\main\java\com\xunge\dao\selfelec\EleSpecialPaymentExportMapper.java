package com.xunge.dao.selfelec;


import com.xunge.model.basedata.DatAttachment;
import com.xunge.model.selfelec.EleSpecialPaymentExport;

import java.util.List;
import java.util.Map;

public interface EleSpecialPaymentExportMapper {
    List<EleSpecialPaymentExport> selectByExample(Map<String, Object> map);

    List<DatAttachment> selectInBusnissId(List<String> ids);

    List<EleSpecialPaymentExport> selectByExampleNew(Map<String, Object> map);

    List<EleSpecialPaymentExport> selectByExampleTele(Map<String, Object> map);

    List<DatAttachment> selectTeleInBusnissId(List<String> ids);
}