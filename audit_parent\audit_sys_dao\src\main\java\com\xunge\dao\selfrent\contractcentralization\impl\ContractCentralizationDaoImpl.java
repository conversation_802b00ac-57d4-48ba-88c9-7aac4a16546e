package com.xunge.dao.selfrent.contractcentralization.impl;

import com.xunge.core.page.Page;
import com.xunge.dao.AbstractBaseDao;
import com.xunge.dao.selfrent.contractcentralization.ContractCentralizationDao;
import com.xunge.filter.PageInterceptor;
import com.xunge.model.selfrent.contract.DatContractVO;
import com.xunge.model.selfrent.contract.DatSupplierVO;
import com.xunge.model.selfrent.contract.RentContractVO;
import com.xunge.model.selfrent.contractcentralization.RentContract;
import com.xunge.model.selfrent.contractcentralization.RentContractCentralizationVO;
import com.xunge.model.system.user.SysUserVO;

import java.util.List;
import java.util.Map;

/**
 * @创建人 LiXs
 * @创建时间 2018/9/20
 * @描述：
 */
public class ContractCentralizationDaoImpl extends AbstractBaseDao implements ContractCentralizationDao {

    final String RentContractNamespace = "com.xunge.mapping.selfrent.contractcentralization.ContractCentralizationMapper.";

    @Override
    public int insertDatContractVO(DatContractVO datContractVO) {
        return this.getSqlSession().insert(RentContractNamespace + "insertDatContractVO", datContractVO);
    }

    @Override
    public int updateDatContractVO(DatContractVO datContractVO) {
        return this.getSqlSession().update(RentContractNamespace + "updateDatContractVO", datContractVO);
    }

    @Override
    public int updateRelieveDatContractVO(DatContractVO datContractVO) {
        return this.getSqlSession().update(RentContractNamespace + "updateRelieveDatContractVO", datContractVO);
    }

    @Override
    public int insertRentContractVO(RentContractVO rentContractVO) {
        return this.getSqlSession().insert(RentContractNamespace + "insertRentContractVO", rentContractVO);
    }

    @Override
    public int insertRentContractInfo(RentContractVO rentContractVO) {
        return this.getSqlSession().insert(RentContractNamespace + "insertRentContractInfo", rentContractVO);
    }

    @Override
    public int updatePushState(Map<String, Object> paraMap) {
        return this.getSqlSession().update(RentContractNamespace + "updatePushState", paraMap);
    }

    @Override
    public int updateRentContractVO(RentContractVO rentContractVO) {
        return this.getSqlSession().update(RentContractNamespace + "updateRentContractVO", rentContractVO);
    }

    @Override
    public int updateByPrimaryKeySelective(RentContractVO rentContractVO) {
        return this.getSqlSession().update(RentContractNamespace + "updateByPrimaryKeySelective", rentContractVO);
    }

    @SuppressWarnings("unchecked")
    @Override
    public Page<RentContractVO> queryRentContractVO(Map<String, Object> paraMap, int pageNumber, int pageSize) {
        PageInterceptor.startPage(pageNumber, pageSize);
        this.getSqlSession().selectList(RentContractNamespace + "queryRentContractVO", paraMap);
        return PageInterceptor.endPage();
    }

    public List<RentContractVO> queryRentContractVoList(Map<String, Object> paraMap){
        return  this.getSqlSession().selectList(RentContractNamespace + "queryRentContractVO", paraMap);
    }

    @Override
    public DatSupplierVO queryDatSupplierById(Map<String, Object> paraMap) {
        return this.getSqlSession().selectOne(RentContractNamespace + "queryDatSupplierById", paraMap);
    }

    /**
     * 分页查询供应商信息
     *
     * @return
     * <AUTHOR>
     */
    @Override
    public Page<List<DatSupplierVO>> queryDatSupplierByPrvID(Map<String, Object> paraMap,
                                                             int pageNumber, int pageSize) {
        PageInterceptor.startPage(pageNumber, pageSize);
        this.getSqlSession().selectList(RentContractNamespace + "queryDatSupplierByPrvID", paraMap);
        return PageInterceptor.endPage();
    }

    ;

    @Override
    public DatContractVO queryDatContractById(Map<String, Object> paraMap) {
        return this.getSqlSession().selectOne(RentContractNamespace + "queryDatContractById", paraMap);
    }


    @Override
    public RentContractVO queryOldDatContractVO(Map<String, Object> paraMap) {
        return this.getSqlSession().selectOne(RentContractNamespace + "queryOldDatContractVO", paraMap);
    }

    @Override
    public List<RentContractVO> queryOldDatContractVOList(Map<String, Object> paraMap) {
        return this.getSqlSession().selectList(RentContractNamespace + "queryOldDatContractVOList", paraMap);
    }

    @Override
    public RentContractVO queryRentContractById(Map<String, Object> paraMap) {
        return this.getSqlSession().selectOne(RentContractNamespace + "queryRentContractById", paraMap);
    }

    @Override
    public List<DatContractVO> checkContractCode(Map<String, Object> map) {
        return this.getSqlSession().selectList(RentContractNamespace + "checkContractCode", map);
    }

    /**
     * @description 修改租费合同审核状态  租费专用
     * <AUTHOR>
     * @date 创建时间：2017年11月7日
     */
    @Override
    public int updateRentContractAuditState(Map<String, Object> maps) {
        return this.getSqlSession().update(RentContractNamespace + "updateRentContractAuditState", maps);
    }

    ;

    /**
     * @description 修改主合同审核状态  租费专用
     * <AUTHOR>
     * @date 创建时间：2017年11月7日
     */
    @Override
    public int updateDatContractAuditState(Map<String, Object> maps) {
        return this.getSqlSession().update(RentContractNamespace + "updateDatContractAuditState", maps);
    }

    ;

    @Override
    public List<RentContractCentralizationVO> queryRentContractCentralizationVO(Map<String, Object> paraMap) {
        return this.getSqlSession().selectList(RentContractNamespace + "queryRentContractCentralizationVO", paraMap);
    }

    @Override
    public RentContractVO selectByOldContractId(String oldContractId) {
        return this.getSqlSession().selectOne(RentContractNamespace + "selectByOldContractId", oldContractId);
    }

    @Override
    public RentContractVO queryRentContractByDatId(Map<String, Object> paraMap) {
        return this.getSqlSession().selectOne(RentContractNamespace + "queryRentContractByDatId", paraMap);
    }

    /**
     * 修改合同审核状态为未审核通过 推送状态为未推送 同状态为null
     *
     * @param parmMap
     * @return
     */
    @Override
    public int updateDatContractState(Map<String, Object> parmMap) {
        return this.getSqlSession().update(RentContractNamespace + "updateDatContractState", parmMap);
    }

    /**
     * 修改合同审核状态为未审核通过
     *
     * @param parmMap
     * @return
     */
    @Override
    public int updateRentContractState(Map<String, Object> parmMap) {
        return this.getSqlSession().update(RentContractNamespace + "updateRentContractState", parmMap);
    }

    @Override
    public List<RentContractVO> queryRentContractList(Map<String, Object> paraMap) {
        return this.getSqlSession().selectList(RentContractNamespace + "queryRentContractList", paraMap);
    }

    @Override
    public Page<List<SysUserVO>> queryAllUserByRoleName(Map<String, Object> paraMap, int pageNumber, int pageSize) {
        PageInterceptor.startPage(pageNumber, pageSize);
        this.getSqlSession().selectList(RentContractNamespace + "queryAllUserByRoleName", paraMap);
        return PageInterceptor.endPage();
    }

    @Override
    public String queryUserIdByUserSampId(String sampId) {
        return this.getSqlSession().selectOne(RentContractNamespace + "queryUserIdByUserSampId", sampId);
    }

    @Override
    public SysUserVO querySmapInfoBySmapId(String smapId) {
        return this.getSqlSession().selectOne(RentContractNamespace + "querySmapInfoBySmapId", smapId);
    }

    @Override
    public int deleteRentContractByRentContractIds(Map<String, Object> paramMap) {
        return this.getSqlSession().delete(RentContractNamespace + "deleteRentContractByRentContractIds", paramMap);
    }

    @Override
    public int deleteDatContractByContractIds(Map<String, Object> paramMap) {
        return this.getSqlSession().delete(RentContractNamespace + "deleteDatContractByContractIds", paramMap);
    }

    @Override
    public int updateByContractId(RentContractVO rentContractVO) {
        return this.getSqlSession().update(RentContractNamespace + "updateByContractId", rentContractVO);
    }

    @Override
    public RentContractVO queryByRentContractId(String rentContractId) {
        return this.getSqlSession().selectOne(RentContractNamespace + "queryByRentContractId", rentContractId);
    }

    @Override
    public RentContractVO queryByDatContractId(String datContractId) {
        return this.getSqlSession().selectOne(RentContractNamespace + "queryByDatContractId", datContractId);
    }

    /* (non Javadoc)
     * Title: updateSupplierInfo
     * Description:
     * @param paraMap
     * @see com.xunge.dao.selfrent.contractcentralization.ContractCentralizationDao#updateSupplierInfo(java.util.Map)
     */

    @Override
    public int updateSupplierInfo(Map<String, Object> paraMap) {
        return this.getSqlSession().update(RentContractNamespace + "updateSupplierInfo", paraMap);

    }


    /* (non Javadoc)
     * Title: queryRentContractList
     * Description:
     * @return
     * @see com.xunge.dao.selfrent.contractcentralization.ContractCentralizationDao#queryRentContractList()
     */

    @Override
    public List<RentContractVO> queryContractList() {
        return this.getSqlSession().selectList(RentContractNamespace + "queryContractList", null);
    }


    @Override
    public List<RentContractVO> queryRentContracts(Map<String, Object> paraMap) {
        return this.getSqlSession().selectList(RentContractNamespace + "queryRentContracts", paraMap);
    }


    /* (non Javadoc)
     * Title: updateComtractInfo
     * Description:
     * @param contractId
     * @param contractIds
     * @return
     * @see com.xunge.dao.selfrent.contractcentralization.ContractCentralizationDao#updateComtractInfo(java.lang.String, java.util.List)
     */

    @Override
    public int updateComtractInfo(Map<String, Object> paraMap) {
        return this.getSqlSession().update(RentContractNamespace + "updateComtractInfo", paraMap);
    }


    /* (non Javadoc)
     * Title: updateById
     * Description:
     * @param rentContractVO
     * @return
     * @see com.xunge.dao.selfrent.contractcentralization.ContractCentralizationDao#updateById(com.xunge.model.selfrent.contract.RentContractVO)
     */

    @Override
    public int updateById(RentContractVO rentContractVO) {
        return this.getSqlSession().update(RentContractNamespace + "updateById", rentContractVO);
    }


    /* (non Javadoc)
     * Title: deleteRentContractByContractIds
     * Description:
     * @param paraMap
     * @return
     * @see com.xunge.dao.selfrent.contractcentralization.ContractCentralizationDao#deleteRentContractByContractIds(java.util.Map)
     */

    @Override
    public int deleteRentContractByContractIds(Map<String, Object> paraMap) {
        return this.getSqlSession().delete(RentContractNamespace + "deleteRentContractByContractIds", paraMap);
    }


    /* (non Javadoc)
     * Title: insertRentContract
     * Description:
     * @param rentContractVO
     * @return
     * @see com.xunge.dao.selfrent.contractcentralization.ContractCentralizationDao#insertRentContract(com.xunge.model.selfrent.contractcentralization.RentContract)
     */

    @Override
    public int insertRentContract(RentContract rentContractVO) {

        return this.getSqlSession().insert(RentContractNamespace + "insertRentContract", rentContractVO);
    }


    /* (non Javadoc)
     * Title: updateRentContractById
     * Description:
     * @param rentContractVO
     * @return
     * @see com.xunge.dao.selfrent.contractcentralization.ContractCentralizationDao#updateRentContractById(com.xunge.model.selfrent.contractcentralization.RentContract)
     */

    @Override
    public int updateRentContractById(RentContract rentContractVO) {
        // TODO Auto-generated method stub
        return this.getSqlSession().update(RentContractNamespace + "updateRentContractById", rentContractVO);
    }

    @Override
    public int countById(Map<String, Object> paraMap) {
        return this.getSqlSession().selectOne(RentContractNamespace + "countById", paraMap);
    }

    @Override
    public Map<String, Object> queryBeanById(Map<String, Object> paraMap) {
        return this.getSqlSession().selectOne(RentContractNamespace + "queryBeanById", paraMap);
    }


}
