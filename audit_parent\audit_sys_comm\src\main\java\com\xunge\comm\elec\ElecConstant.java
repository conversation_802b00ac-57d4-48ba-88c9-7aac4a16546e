package com.xunge.comm.elec;

/**
 * @author: lee
 * @date: 2023-10-24 10:51
 * @desc:
 */
public class ElecConstant {

    /**
     * 成本中心-缺省
     */
    public static final String COSTER_CENTER_DEFAULT = "缺省";
    /**
     * 后付费转办类型
     */
    public static final String ELE_PAYMENT = "1";
    /**
     * 预付费转办类型
     */
    public static final String ELE_LOAN = "2";

    /**
     * 先款后票预付费转办类型
     */
    public static final String ELE_LOAN_PAYFIRST = "3";

    /**
     * 特殊缴费转办类型
     */
    public static final String ELE_SPECIAL = "4";

    /**
     * 核销转办类型
     */
    public static final String ELE_VERIFICATION = "5";

    /**
     * IC核销转办类型
     */
    public static final String ELE_IC_VERIFICATION = "6";

    /**
     * 预付费退款
     */
    public static final String ELE_PRE_REFUND = "7";


    /**
     * 自维报账点
     */
    public static final String BILLACCOUNT_ZW = "1";
    /**
     * 塔维报账点
     */
    public static final String BILLACCOUNT_TW = "2";

    /**
     * 代持报账点
     */
    public static final String BILLACCOUNT_DC = "3";

}
