package com.xunge.dao.report;

import com.xunge.model.report.RptRentExecutionProgressVO;

import java.util.List;
import java.util.Map;

/**
 * @创建人 LiangCheng
 * @创建时间 2018/12/27 0027
 * @描述：
 */
public interface IRptRentExecutionProgressDao {

    List<RptRentExecutionProgressVO> queryRentExecutionProgressProvince(Map<String, Object> map);

    List<RptRentExecutionProgressVO> queryRentExecutionProgressCity(Map<String, Object> map);

    List<RptRentExecutionProgressVO> queryRentExecutionProgressGroup(Map<String, Object> map);

    List<RptRentExecutionProgressVO> queryRentPayableExecutionProgressProvince(Map<String, Object> map);

    List<RptRentExecutionProgressVO> queryRentPayableExecutionProgressCity(Map<String, Object> map);

    List<RptRentExecutionProgressVO> queryRentPayableExecutionProgressGroup(Map<String, Object> map);

}
