package com.xunge.model.app;

/**
 * <AUTHOR>
 * @since 2019
 */
public enum ErrorCode {
    /**
     * 请求成功
     */
    SUCCESS("0", "当前请求被成功处理"),

    /**
     * 当前请求处理失败
     */
    FAILED("1", "当前请求处理失败"),
    FAILED_DATA_NULL("1", "参数为空"),
    FAILED_DATA_PROVICE("1", "省份prvCode为空或错误"),
    FAILED_DATA_USER("1", "用户userId为空"),
    FAILED_DATA_BUSSINESSKEY("1", "businessId业务表主键为空"),
    FAILED_DATA_COMMENT("1", "审核不通过时候，审核意见为空"),
    FAILED_DATA_STATE("1", "state审核状态错误或者为空"),
    FAILED_DATA_TASKID("1", "taskId为空"),
    FAILED_DATA_TABLE("1", "流程类型tableEnum枚举值为空或错误"),
    FAILED_NULLNEXTUSER("1", "获取下一级审核人失败"),
    FAILED_NULLNEXTUSER_HAVE("1", "获取下一级审核人失败,该任务可能已完成,请刷新待办列表"),
    FAILED_COMPLATE("1", "完成任务失败"),
    FAILED_STARTPROCESS("1", "提交审核失败，请检查该省是否配置巡检流程或者该数据已在审核中"),
    FAILED_COMPLATE_HAVE("1", "完成任务失败,该任务可能已完成,请刷新待办列表"),
    FAILED_REHID_HAVE("1", "用户权限卡控的地市或者区县ID集合不能同时为空"),
    FAILED_NEXTUSER("1", "下级审核人为空"),
    FAILED_TOKEN("1", "accessToken验证失败"),
    NOT_USER("1", "该省不存在该用户");

    /**
     * 错误码
     */
    public String code;

    /**
     * 错误码描述
     */
    public String msg;

    ErrorCode(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    ErrorCode(String msg) {
        this.code = "1";
        this.msg = msg;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }
}
