package com.xunge.model.budget;

import java.util.Calendar;

public class BudgetCommonInfo {

    /**
     * 集团状态 工单-草稿
     */
    public static int JT_STATE_DRAFT = 0;

    /**
     * 集团状态 工单-集团已下发，省测处理中
     */
    public static int JT_STATE_SEND = 1;

    /**
     * 集团状态 工单-已完结
     */
    public static int JT_STATE_FINISH = 6;

    /**
     * 集团状态 已完结工单汇总
     */
    public static int JT_STATE_SUMMARY = 7;

    /**
     * 集团状态 最终预算-草稿
     */
    public static int JT_STATE_FINAL_DRAFT = 8;

    /**
     * 集团状态 最终预算-已下发
     */
    public static int JT_STATE_FINAL_SEND = 9;

    /**
     * 省份配置状态 集团已下发，省测处理中
     */
    public static int PRV_STATE_NOT_WRITE = 1;

    /**
     * 省份配置状态 省侧审批中
     */
    public static int PRV_STATE_NOT_CHECKING = 2;

    /**
     * 省份配置状态 省侧驳回
     */
    public static int PRV_STATE_NOT_PASS = 3;

    /**
     * 省份配置状态 省侧已提交，集团审批中
     */
    public static int PRV_STATE_PASS = 4;

    /**
     * 省份配置状态 集团驳回
     */
    public static int PRV_STATE_REJECT = 5;

    /**
     * 省份配置状态 已完结
     */
    public static int PRV_STATE_FINISH = 6;

    /**
     * 租费、三方塔预算-节点类型-集团确认完结前/省侧上报集团前
     */
    public static int RENT_FLOW_STATE_ORDER = 1;

    /**
     * 租费、三方塔预算-节点类型-每次省侧提交集团或被集团驳回的快照
     */
    public static int RENT_FLOW_STATE_SNAPSHOOT = 2;

    /**
     * 租费、三方塔预算-节点类型-集团确认完结后
     */
    public static int RENT_FLOW_STATE_FINISH = 3;

    /**
     * 租费、三方塔预算-节点类型-最终预算
     */
    public static int RENT_FLOW_STATE_FINAL = 4;

    /**
     * 租费、三方塔预算-节点类型-省侧预算测算
     */
    public static int RENT_FLOW_STATE_IMITATE = 5;

    /**
     * 审批动作：创建
     */
    public static String AUDIT_ACTION_CREATE = "创建";

    /**
     * 审批动作：审核通过
     */
    public static String AUDIT_ACTION_PASS = "审核通过";
    /**
     * 审批动作：审核不通过
     */
    public static String AUDIT_ACTION_NO_PASS = "审核不通过";

    /**
     * 审批动作：归档
     */
    public static String AUDIT_ACTION_FINISH = "归档";

    /**
     * 操作动作：提交
     */
    public static String AUDIT_ACTION_SUBMIT = "提交";

    /**
     * 操作动作：驳回
     */
    public static String AUDIT_ACTION_REJECT = "驳回";

    /**
     * 操作节点：省侧填报
     */
    public static String AUDIT_NODE_SUBMIT = "省侧提交";

    /**
     * 操作节点：集团驳回
     */
    public static String AUDIT_NODE_REJECT = "集团驳回";

    /**
     * 完结后的汇总工单固定名称-电费
     */
    public static String SUMMARY_ORDER_NAME_ELE = "31省2022年网络电费预算";

    /**
     * 完结后的汇总工单固定名称-电费
     */
    public static String SUMMARY_ORDER_NAME_RENT = "31省2022年自维租赁费预算";

    /**
     * 完结后的汇总工单固定名称-电费
     */
    public static String SUMMARY_ORDER_NAME_THREE = "31省2022年三方塔服务费预算";

    /**
     * 完结后的汇总工单固定名称-电费
     */
    public static String SUMMARY_ORDER_NAME_TOWER = "31省2022年铁塔服务费预算";

    /**
     * 发送短信状态 是
     */
    public static Integer STATE_SEND_MESSAGE_YES = 1;

    /**
     * 发送短信状态 否
     */
    public static Integer STATE_SEND_MESSAGE_NO = 0;

    /**
     * 工单删除状态 是
     */
    public static Integer STATE_DELETE_YES = 0;

    /**
     * 工单删除状态 否
     */
    public static Integer STATE_DELETE_NO = 1;

    public static String RENT_DATA_REDIS_KEY = "rentBudget";

    public static String THREE_TOWER_DATA_REDIS_KEY = "threeBudget";

    public static String RENT_DATA_FLAG_SUMMARY = "rentSummaryData";

    public static String RENT_DATA_FLAG_EXIST = "rentExistData";

    public static String RENT_DATA_FLAG_ADD = "rentAddData";

    public static String RENT_DATA_FLAG_DEL = "rentDelData";

    public static String RENT_DATA_FLAG_SUPPLEMENT = "rentSupplementData";

    public static String THREE_DATA_FLAG_SUMMARY = "threeSummaryData";

    public static String THREE_DATA_FLAG_EXIST = "threeExistData";

    public static String THREE_DATA_FLAG_ADD = "threeAddData";

    public static String THREE_DATA_FLAG_DEL = "threeDelData";

    public static String TOWER_DATA_REDIS_KEY = "towerBudget";

    /**
     * 铁塔服务费预算-节点类型-集团确认完结前/省侧上报集团前
     */
    public static int TOWER_FLOW_STATE_ORDER = 1;

    /**
     * 铁塔服务费预算-节点类型-每次省侧提交集团或被集团驳回的快照
     */
    public static int TOWER_FLOW_STATE_SNAPSHOOT = 2;

    /**
     * 铁塔服务费预算-节点类型-集团确认完结后
     */
    public static int TOWER_FLOW_STATE_FINISH = 3;

    /**
     * 铁塔服务费预算-节点类型-最终预算
     */
    public static int TOWER_FLOW_STATE_FINAL = 4;

    /**
     * 铁塔服务费预算-节点类型-省侧预算测算
     */
    public static int TOWER_FLOW_STATE_IMITATE = 5;


    /**
     * 电费预算-节点类型-集团确认完结前/省侧上报集团前
     */
    public static int ELE_FLOW_STATE_ORDER = 1;

    /**
     * 电费预算-节点类型-每次省侧提交集团或被集团驳回的快照
     */
    public static int ELE_FLOW_STATE_SNAPSHOOT = 2;

    /**
     * 电费预算-节点类型-集团确认完结后
     */
    public static int ELE_FLOW_STATE_FINISH = 3;

    /**
     * 电费预算-节点类型-最终预算
     */
    public static int ELE_FLOW_STATE_FINAL = 4;

    /**
     * 电费预算-节点类型-省侧预算测算
     */
    public static int ELE_FLOW_STATE_IMITATE = 5;


    /**
     * 租费三方塔代表合计行的业务类型type
     */
    public static int RENT_TOTAL_TYPE = 9;

    /**
     * 短信发送类型 - 预算填报
     */
    public static int SEND_SMS_BUDGET_WRITE=1;

    /**
     * 短信发送类型 - 预算驳回
     */
    public static int SEND_SMS_BUDGET_AUDIT=2;

    /**
     * 短信发送类型 - 最终预算查看
     */
    public static int SEND_SMS_BUDGET_VIEW=3;

    public static String[] allRentSiteTypeNames = {"传输位置点","综合位置点","核心机楼","汇聚传输站点","基站","室分及WLAN","家客集客","IDC机房","基地","其他","合计"};

    /**
     * 站点类型枚举值转中文名称
     * @param siteType 站点类型枚举值
     * @return 中文名称
     */
    public static String siteTypeToNameRent(Integer siteType){
        if (siteType == 1) {
            return "核心机楼";
        } else if (siteType == 2) {
            return "汇聚传输站点";
        } else if (siteType == 3) {
            return "基站";
        } else if (siteType == 4) {
            return "室分及WLAN";
        } else if (siteType == 5) {
            return "家客集客";
        } else if (siteType == 6) {
            return "IDC机房";
        } else if (siteType == 7) {
            return "基地";
        } else if (siteType == 8) {
            return "其他";
        } else if (siteType == 9) {
            return "合计";
        } else if (siteType == -1) {
            return "传输位置点";
        } else if (siteType == 0) {
            return "综合位置点";
        }
        return "-";
    }

    /**
     * 站点类型中文名称转枚举值
     * @param siteName 站点名称
     * @return 枚举值
     */
    public static Integer siteNameToTypeRent(String siteName){
        if (siteName.equals("核心机楼")) {
            return 1;
        } else if (siteName.equals("汇聚传输站点")) {
            return 2;
        } else if (siteName.equals("基站")) {
            return 3;
        } else if (siteName.equals("室分及WLAN")) {
            return 4;
        } else if (siteName.equals("家客集客")) {
            return 5;
        } else if (siteName.equals("IDC机房")) {
            return 6;
        } else if (siteName.equals("基地")) {
            return 7;
        } else if (siteName.equals("其他")) {
            return 8;
        }else if (siteName.equals("合计")) {
            return 9;
        }else if (siteName.equals("传输位置点")) {
            return -1;
        }else if (siteName.equals("综合位置点")) {
            return 0;
        }
        return null;
    }

    /**
     * 获取三方塔sheetName
     * @param sheetType
     * @param budgetTime
     * @return
     */
    public static String getSheetNameForThreeTower(Integer sheetType,String budgetTime){
        switch(sheetType){
            case 0 :
                return "预算总览";
            case 1:
                return Integer.parseInt(budgetTime)-1+"年底存量";
            case 2:
                return budgetTime+"年新增";
            case 3:
                return budgetTime+"年退网";
            default:
                return "";
        }
    }

    /**
     * 获取租费sheetName
     * @param sheetType
     * @param budgetTime
     * @return
     */
    public static String getSheetNameForRent(Integer sheetType,String budgetTime,Integer createMonth){
        switch (sheetType) {
            case 0:
                return "预算总览";
            case 1:
                //eg：2024年1-9月存量站址在2025年的预算金额
                return Integer.parseInt(budgetTime) - 1 + "年1-" + (createMonth - 1) + "月存量站址在" + budgetTime + "年的预算金额";
            case 2:
                //eg：2025年新增站址在2025年的费用
                return budgetTime + "年新增站址在" + budgetTime + "年的费用";
            case 3:
                //eg：2025年退网
                return budgetTime + "年退网";
            case 4:
                //eg：2024年10-12月新增站址在2025年的费用
                return Integer.parseInt(budgetTime) - 1 + "年" + createMonth + "-12月新增站址在" + budgetTime + "年的费用";
            default:
                return "";
        }
    }

    /**
     * 获取sheetName（静态）
     * @param sheetType
     * @return
     */
    public static String getStaticSheetName(Integer sheetType){
        switch (sheetType) {
            case 0:
                return "预算总览";
            case 1:
                return "本年存量站在次年预算";
            case 2:
                return "次年新增站在次年预算";
            case 3:
                return "次年退网站在次年退网预算";
            case 4:
                return "本年底新增站在次年预算";
            default:
                return "";
        }
    }


    public static Integer transSheetNameToIntForRent(String sheetName){
        if (sheetName.contains("存量")){
            return 1;
        }else if (sheetName.contains("年新增")){
            return 2;
        }else if (sheetName.contains("退网")){
            return 3;
        }else if (sheetName.contains("月新增")){
            return 4;
        }else {
            return 0;
        }
    }

    public static Integer transSheetNameToIntForThreeTower(String sheetName){
        if (sheetName.contains("存量")){
            return 1;
        }else if (sheetName.contains("新增")){
            return 2;
        }else if (sheetName.contains("退网")){
            return 3;
        }else {
            return 0;
        }
    }

    /**
     * 获取租费导出文件名
     * @param type
     * @return
     */
    public static String getFileNameByPage(String type){
        switch(type){
                //预算草稿页面
            case "0":
                return "预算草稿";
                //省侧页面
            case "1":
                return "预算工单";
                //预算数据维护 有核减金额
            case "2":
                return "预算维护";
                //最终预算 有调整金额
            case "3":
                return "最终预算";
                //预算测算
            case "4":
                return "预算测算";
            default:
                return "预算工单";
        }
    }

    /**
     * 根据页面得到节点类型
     * @param type
     * @return
     */
    public static Integer getNodeTypeByPage(String type){
        //快照数据
        switch(type){
            case "-1":
                return RENT_FLOW_STATE_SNAPSHOOT;
            //预算草稿页面
            case "0":
            //省侧页面
            case "1":
            //集团查看页面
            case "5":
                return RENT_FLOW_STATE_ORDER;
            //预算数据维护 有核减金额
            case "2":
                return RENT_FLOW_STATE_FINISH;
            //最终预算 有调整金额
            case "3":
                return RENT_FLOW_STATE_FINAL;
            //预算测算
            case "4":
                return RENT_FLOW_STATE_IMITATE;
             default:
                return null;
        }
    }

}
