package com.xunge.dao.selfrent.billaccount;

import com.xunge.model.selfrent.billAccount.DatBaseresourceVO;
import com.xunge.model.selfrent.billAccount.DatBaseresourcesVO;

import java.util.List;
import java.util.Map;

public interface IDatBaseresourceDao {
    /**
     * 根据报账点id查询资源点信息集合
     *
     * @param billAccountId
     * @return
     * <AUTHOR>
     */
    public List<DatBaseresourceVO> queryDatBaseresourceByBillAccountId(String billAccountId);

    public List<DatBaseresourcesVO> queryBaseresourceByAccountId(String billAccountId);

    /**
     * 根据paymentid查询缴费当时关联机房信息
     *
     * @param map
     * @return
     * <AUTHOR>
     */
    public List<DatBaseresourceVO> queryOldResVO(Map<String, Object> map);
}