package com.xunge.dao.selfelec;

import com.xunge.model.selfelec.VDatBasestation;
import com.xunge.model.selfelec.VDatBasestationExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface VDatBasestationMapper {
    
    int countByExample(VDatBasestationExample example);

    
    int deleteByExample(VDatBasestationExample example);

    
    int insert(VDatBasestation record);

    
    int insertSelective(VDatBasestation record);

    
    List<VDatBasestation> selectByExample(VDatBasestationExample example);

    
    int updateByExampleSelective(@Param("record") VDatBasestation record, @Param("example") VDatBasestationExample example);

    
    int updateByExample(@Param("record") VDatBasestation record, @Param("example") VDatBasestationExample example);
}