package com.xunge.model.budget.twr;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @Description:历年规模单价总览
 * @Author: dxd
 * @Date: 2024/2/1 15:59
 */
@Data
public class BudgetHistoryVo {

    /**
     * 省份ID
     */
    private String prvId;

    /**
     * 省份名称
     */
    private String prvName;
    /**
     * 业务类型({塔类,1}, {室分,2}, {微站,3}, {传输,4}, {非标,5}, {合计,6})
     */
    private Integer productType;


    // 前一年执行金额
    private BigDecimal historyOneExecutionFee;

    // 前一年租赁站址数
    private Integer historyOneSiteNumber;

    // 前一年单站服务费
    private BigDecimal historyOneSiteFee;

    // 前两年执行金额
    private BigDecimal historyTwoExecutionFee;

    // 前两年租赁站址数
    private Integer historyTwoSiteNumber;

    // 前两年单站服务费
    private BigDecimal historyTwoSiteFee;

    // 前三年执行金额
    private BigDecimal historyThreeExecutionFee;

    // 前三年租赁站址数
    private Integer historyThreeSiteNumber;

    // 前三年单站服务费
    private BigDecimal historyThreeSiteFee;

    // 当年起租站址到达数
    private Integer historyCurrentSiteNumber;

    // 当年单站服务费
    private BigDecimal historyCurrentSiteFee;

    // 下一年起租站址到达数
    private Integer historyNextSiteNumber;

    // 下一年单站服务费
    private BigDecimal historyNextSiteFee;

    private String historyRemark;

}
