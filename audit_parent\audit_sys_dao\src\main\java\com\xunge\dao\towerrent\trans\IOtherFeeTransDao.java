package com.xunge.dao.towerrent.trans;

import com.xunge.core.page.Page;
import com.xunge.model.towerrent.settlement.OtherFeeTransVO;
import com.xunge.model.towerrent.settlement.TowerAndMobileBillTransConfirmVO;
import com.xunge.model.towerrent.settlement.TowerBillBalanceTransVO;

import java.util.List;
import java.util.Map;

/**
 * TODO: 接口描述
 *
 * <AUTHOR>
 * @date 2019/4/23 14:59
 */
public interface IOtherFeeTransDao {
    List<TowerBillBalanceTransVO> queryTransBill(Map<String, Object> paramMap);

    List<TowerAndMobileBillTransConfirmVO> queryAccountedTransBill(Map<String, Object> paramMap);

    Page<List<TowerAndMobileBillTransConfirmVO>> queryAccountedTransBillByPage(Map<String, Object> paraMap, int pageNumber, int pageSize);

    int updateTransFeeSumcodeToNull(Map<String, Object> map);

    int updateOtherFeeTransSumcodeToNull(Map<String, Object> map);

    int updateTransBillSetSumcode(TowerBillBalanceTransVO towerBillBalanceTransVO);

    String updateTransOtherById(OtherFeeTransVO otherFeeTransVO);
}
