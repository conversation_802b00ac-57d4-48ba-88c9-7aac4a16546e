package com.xunge.dao.selfelec;


import com.xunge.model.selfelec.ElePaymentDownloadList;

import java.util.List;
import java.util.Map;

public interface ElePaymentDownloadListMapper {
    int deleteByPrimaryKey(String id);

    int insert(ElePaymentDownloadList record);

    int insertSelective(ElePaymentDownloadList record);

    ElePaymentDownloadList selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(ElePaymentDownloadList record);

    int updateByPrimaryKey(ElePaymentDownloadList record);

    List<ElePaymentDownloadList> selectByCondition(Map<String, Object> map);
}