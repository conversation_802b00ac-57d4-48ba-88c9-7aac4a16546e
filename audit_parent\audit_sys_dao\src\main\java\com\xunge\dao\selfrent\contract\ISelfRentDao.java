package com.xunge.dao.selfrent.contract;

import com.github.pagehelper.PageInfo;
import com.xunge.core.page.Page;
import com.xunge.model.selfrent.common.AccrualInfoImportVO;
import com.xunge.model.selfrent.contract.*;
import com.xunge.model.selfrent.rebursepoint.RentBillaccountVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface ISelfRentDao {
    /**
     * 查询所有主合同信息
     *
     * <AUTHOR>
     */
    public Page<RentContractVO> queryRentContractVO(Map<String, Object> paraMap, int pageNumber, int pageSize);


    public List<RentContractVO> queryRentContractVoList(Map<String, Object> paraMap);

    /**
     * 查询所有主合同信息
     *
     * <AUTHOR>
     */
    public List<RentContractVO> queryRentContractVO(Map<String, Object> paraMap);

    Map<String, Object> queryTeleContract(@Param("contractId") String contractId);

    Map<String, Object> queryTelePayment(@Param("billaccountpaymentdetailId") String billaccountpaymentdetailId);

    Map<String, Object> queryTeleBillaccount(@Param("billaccountId") String billaccountId);

    /**
     * 根据房屋租赁合同表中的供应商id查询供应商对象
     *
     * <AUTHOR>
     */
    public DatSupplierVO queryDatSupplierById(Map<String, Object> paraMap);

    /**
     * 根据省份查询供应商
     *
     * @param paraMap
     * @param paraMap -prvId 省份id
     * @return
     */
    public List<DatSupplierVO> queryDatSupplierByPrvID(Map<String, Object> paraMap);

    /**
     * 分页查询供应商信息
     *
     * @param rentcontractId
     * @return
     * <AUTHOR>
     */
    public Page<List<DatSupplierVO>> queryDatSupplierByPrvID(Map<String, Object> paraMap, int pageNumber, int pageSize);

    /**
     * 根据房屋租赁合同表中的主合同id查询主合同对象
     *
     * <AUTHOR>
     */
    public DatContractVO queryDatContractById(Map<String, Object> paraMap);

    /**
     * 根据房屋租赁合同id查询房租对象
     *
     * <AUTHOR>
     */
    public RentContractVO queryRentContractById(Map<String, Object> paraMap);

    /**
     * 根据房屋租赁合同id查询房租合同、主合同以及区域信息对象
     *
     * @param rentContractId 屋租赁合同id
     * @return
     * <AUTHOR>
     */
    public RentContractVO queryAllRentContractById(Map<String, Object> paraMap);

    /**
     * 新增主合同信息
     *
     * <AUTHOR>
     */
    public int insertDatContractVO(DatContractVO datContractVO);

    /**
     * 修改主合同信息
     *
     * @param datContractVO
     * @return
     * <AUTHOR>
     */
    public int updateDatContractVO(DatContractVO datContractVO);

    /**
     * 新增房屋租租赁合同
     *
     * @param rentContractVO
     * @return
     * <AUTHOR>
     */
    public int insertRentContractVO(RentContractVO rentContractVO);

    /**
     * 修改房租合同信息
     *
     * @param rentContractVO
     * @return
     * <AUTHOR>
     */
    public int updateRentContractVO(RentContractVO rentContractVO);

    /**
     * 新增供应商信息
     *
     * @param datSupplierVO
     * @return
     * <AUTHOR>
     */
    public int insertDatSupplierVO(DatSupplierVO datSupplierVO);

    /**
     * 修改供应商信息
     *
     * @param datSupplierVO
     * @return
     * <AUTHOR>
     */
    public int updateDatSupplierVO(DatSupplierVO datSupplierVO);

    /**
     * 补录页面提交审核
     *
     * @param map
     * @return
     * <AUTHOR>
     */
    public int updateCommit(Map<String, Object> map);

    /**
     * 查询合同代码
     *
     * @param map
     * @return
     */
    public List<DatContractVO> checkContractCode(Map<String, Object> map);

    /**
     * 批量终止合同
     */
    public int stopContract(Map<String, Object> map);

    /**
     * 批量删除房租主合同信息
     */
    public int deleteContract(Map<String, Object> map);

    /**
     * 批量 删除房租合同
     *
     * @param parmMap
     * @return
     */
    public int deleteRentContract(Map<String, Object> parmMap);

    /**
     * 查询合同最后缴费日期
     */
    public List<String> queryRentContractEndDate(Map<String, Object> paraMap);

    /**
     * 查询租费合同缴费周期
     */
    public RentContractVO getPaymentPeriodDate(Map<String, Object> paraMap);

    public RentContractVO queryRentContractByBillAccountId(String billAccountId);

    public RentContractVO queryRentFinanceContractByBillAccountId(String billAccountId);

    /**
     * 查询房租合同信息
     *
     * @param paraMap
     * @param pageNumber
     * @param pageSize
     * @return
     */
    public RentContractVO queryContractByContractId(Map<String, Object> paraMap, int pageNumber, int pageSize);

    /**
     * 查询供应商和合同信息
     *
     * @param paraMap
     * @return
     */
    public RentContractVO queryContractAndSupplier(Map<String, Object> paraMap);

    public RentContractVO queryContractAndSupplierFcontract(Map<String, Object> paraMap);


    /**
     * 根据报账点id查询供应商和合同信息
     *
     * @param paraMap
     * @return
     */
    public RentContractVO queryContAndSupByBillId(Map<String, Object> paraMap);

    /**
     * 报账点删除审核通过  合同关联关系=4
     *
     * @param paraMap
     * @return
     */
    public RentContractVO queryContAndSupByBillIdDeleteAudit(Map<String, Object> paraMap);

    public RentContractVO queryContAndSupByBillIdFcontract(Map<String, Object> paraMap);

    /**
     * 报账点删除审核通过  合同关联关系=4
     *
     * @param paraMap
     * @return
     */
    public RentContractVO queryContAndSupByBillIdFcontractDeleteAudit(Map<String, Object> paraMap);

    /**
     * 根据省份、用户权限汇总各地区合同数量
     *
     * @param paraMap
     * @return
     */
    public List<Map<String, String>> selectContractNumByCondition(Map<String, Object> paraMap);

    /**
     * @description 查询所有固化信息
     * <AUTHOR>
     * @date 创建时间：2017年11月1日
     */
    public List<CuringRentContractVO> queryCuringRentContractVO(Map<String, Object> paraMap);

    /**
     * @description 修改租费合同审核状态 租费专用
     * <AUTHOR>
     * @date 创建时间：2017年11月7日
     */
    public int updateRentContractAuditState(Map<String, Object> maps);

    /**
     * @description 查询所有需要修改系统统一编码的固化信息
     * <AUTHOR>
     * @date 创建时间：2018年1月30日
     */
    public List<RentContractVO> queryContractBySysCode(Map<String, Object> paraMap);

    /**
     * @description 修改系统统一编码的固化信息
     * <AUTHOR>
     * @date 创建时间：2018年1月30日
     */
    public int updateSysContractCode(Map<String, Object> maps);

    /**
     * @description 批量新增租费固化信息
     * <AUTHOR>
     * @date 创建时间：2018年1月30日
     */
    public int insertRentContractInfoList(List<CuringRentContractVO> list);

    /**
     * @description 批量新增主合同固化信息
     * <AUTHOR>
     * @date 创建时间：2018年1月30日
     */
    public int insertContractInfoList(List<CuringDatContractVO> list, String flag);

    /**
     * @description 批量修改租费固化信息
     * <AUTHOR>
     * @date 创建时间：2018年1月30日
     */
    public int updateRentContractInfoList(List<CuringRentContractVO> list);

    /**
     * @description 批量修改主合同固化信息
     * <AUTHOR>
     * @date 创建时间：2018年1月30日
     */
    public int updateContractInfoList(List<CuringDatContractVO> list);

    /**
     * @description 修改主合同固化信息
     * <AUTHOR>
     * @date 创建时间：2018年1月30日
     */
    public int updateContractInfo(Map<String, Object> paraMap);

    /**
     * @description 根据省份id查询所有合同
     * <AUTHOR>
     * @date 创建时间：2018年2月7日
     */
    public List<CuringRentContractVO> queryCuringRentContractVOByPrvId(Map<String, Object> paraMap);

    /**
     * @description 查询所有供应商
     * <AUTHOR>
     * @date 创建时间：2018年2月27日
     */
    public List<DatSupplierVO> queryAllDatSupplier(Map<String, Object> paraMap);

    public List<RentContractVO> queryRentContractList(Map<String, Object> paraMap);

    public int updateByContractId(RentContractVO rentContractVO);

    public DatContractVO queryDatContractByFlow(Map<String, Object> paraMap);

    public RentContractVO queryRentContractByDatId(Map<String, Object> paraMap);

    public int updatePushAndContractState(Map<String, Object> paramMap);

    PageInfo<PlatformContractVO> queryNoLinkPlatformContract(Map<String, Object> map);

    int addPlatformContractLink(Map<String, Object> map);

    int cancelPlatformContractLink(Map<String, Object> map);

    Integer selectContractTypeByRentContractId(String rentcontractId);

    List<Integer> selectContractTypeByRentContractIdRel(String rentcontractId);

    RentBillaccountVO queryBilaccountByContract(String rentcontractId);

    int updateSettledDate(Map<String, Object> paramMap);

    String queryPlatformContractMaxDate();

    List<AccrualInfoImportVO> queryAllCnontractForAccrual(String prvId);

    int updateRentAccrualInfoImport(List<AccrualInfoImportVO> list);

    List<PlatformContractVO> queryLastestPlatformContractByCode(Map<String, Object> paraMap);

    int linkPlatformContractInfo(PlatformContractVO vo);

    String queryProcessInstanceIdByBusinessKey(String businessKey);

    ActLastAuditVO queryLastAuditTimeAndLastAuditUserByProcessInstanceId(String processInstanceId);
}