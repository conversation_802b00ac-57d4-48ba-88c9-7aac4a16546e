package com.xunge.dao.selfrent.billamount.impl;

import com.google.common.collect.Maps;
import com.xunge.dao.AbstractBaseDao;
import com.xunge.dao.selfrent.billamount.BillamountLogDao;
import com.xunge.model.selfrent.billamount.BillamountLogVO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2017年6月27日 上午10:14:11
 */
public class BillamountLogDaoImpl extends AbstractBaseDao implements BillamountLogDao {

    final String Namespace = "com.xunge.dao.BillamountLogVOMapper.";

    @Override
    public int insertBillamountLog(BillamountLogVO billamountLog) {
        // TODO Auto-generated method stub
        return this.getSqlSession().insert(Namespace + "insertBillamountLog", billamountLog);
    }

    @Override
    public int updateBillamountLogById(BillamountLogVO billamountLog) {
        // TODO Auto-generated method stub
        return this.getSqlSession().update(Namespace + "updateBillamountLogById", billamountLog);
    }

    @Override
    public List<BillamountLogVO> selectByPrimaryCode(String billamountCode) {
        // TODO Auto-generated method stub
        Map<String, Object> map = Maps.newHashMap();
        map.put("billamountCode", billamountCode);
        List<BillamountLogVO> list = this.getSqlSession().selectList(Namespace + "selectByPrimaryCode", map);
        return list;
    }

    @Override
    public List<BillamountLogVO> selectBillamountPushLog(List<String> list) {
        return this.getSqlSession().selectList(Namespace + "selectBillamountPushLog", list);
    }
}
