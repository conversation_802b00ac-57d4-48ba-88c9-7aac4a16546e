package com.xunge.dao.finance.ext.accrualmapper;

import com.xunge.model.finance.ext.accClaim.accrual.EleAccrualBillamount;
import com.xunge.model.finance.ext.accClaim.accrual.EleAccrualSecondBillamountDetail;
import com.xunge.model.selfrent.accrual.AccrualSummarySecond;
import com.xunge.model.selfrent.accrual.AccrualSummarySecondCondition;
import com.xunge.model.selfrent.accrual.RentAccrualSummaryVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface RentAccrualBillamountMapper {

    /**
     * 根据计提汇总单ID，查询汇总单信息
     */
    EleAccrualBillamount getRentAccrualBillamountInfo(String billamountId);

    /**
     * 根据计提汇总单ID，查询计提单信息
     */
    List<EleAccrualSecondBillamountDetail> getRentAccrualSummaryDetail(String billamountId);

    /**
     * 根据三方塔计提汇总单ID，查询计提单信息
     */
    List<EleAccrualSecondBillamountDetail> getThreeTowerAccrualSummaryDetail(String billamountId);

    /**
     * 根据计提汇总单id，更新汇总单信息
     */
    int updateRentAccrualBillamountById(RentAccrualSummaryVO summaryVO);

    /**
     * 根据计提汇总单id，更新计提单信息
     */
    int updateRentAccrualByBillamountId(Map<String, Object> map);

    int deleteSecondSummaryInfo(@Param("billamountId") String billamountId);

    int insertSecondSummaryInfo(@Param("list") List<AccrualSummarySecond> summarySeconds);

    int updateAccrualLinkSecond(@Param("accrualId") String accrualId,@Param("secondBillamountId") String secondBillamountId);

    int deleteSecondSummaryConditionByUser(@Param("userId") String userId);

    int insertSecondSummaryConditionByUser(AccrualSummarySecondCondition summarySecondCondition);

    /**
     * 查询二次汇总信息
     * @param billamountId
     * @return
     */
    List<EleAccrualSecondBillamountDetail> getRentAccrualSecondSummary(String billamountId);

    AccrualSummarySecondCondition querySecondBillamountConditionByUser(String userId);

    int updateRentExpireAccrualByBillamountId(Map<String, Object> map);

    /**
     * 新增5G随e签信息（单条）
     * @param summaryId
     * @param summaryCode
     * @param signType
     * @param remark
     */
    void addFreeSignInfo(@Param("summaryId") String summaryId, @Param("summaryCode") String summaryCode, @Param("signType") String signType, @Param("remark") String remark);

    /**
     * 将5G随e签信息置为失效状态
     * @param summaryId
     */
    void invalidFreeSignInfo(@Param("summaryId") String summaryId);
}
