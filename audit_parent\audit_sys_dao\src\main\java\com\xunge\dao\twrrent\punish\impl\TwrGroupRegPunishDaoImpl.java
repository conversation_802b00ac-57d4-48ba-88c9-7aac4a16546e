package com.xunge.dao.twrrent.punish.impl;

import com.xunge.dao.AbstractBaseDao;
import com.xunge.dao.twrrent.punish.ITwrGroupRegPunishDao;
import com.xunge.model.towerrent.punish.TwrGroupRegPunishVO;

import java.util.Map;

/**
 * 集团既定考核指标扣罚
 *
 * <AUTHOR>
 */
public class TwrGroupRegPunishDaoImpl extends AbstractBaseDao implements ITwrGroupRegPunishDao {

    private String Namespace = "com.xunge.dao.TwrGroupRegPunishVOMapper.";

    @Override
    public int insertSelective(TwrGroupRegPunishVO twrGroupRegPunishVO) {
        return this.getSqlSession().insert(Namespace + "insertSelective", twrGroupRegPunishVO);
    }

    @Override
    public int updateByPrimaryKeySelective(TwrGroupRegPunishVO twrGroupRegPunishVO) {
        return this.getSqlSession().update(Namespace + "updateByPrimaryKeySelective", twrGroupRegPunishVO);
    }

    @Override
    public TwrGroupRegPunishVO queryGroupRegPunish(Map<String, Object> paraMap) {
        return this.getSqlSession().selectOne(Namespace + "queryGroupRegPunish", paraMap);
    }

}
