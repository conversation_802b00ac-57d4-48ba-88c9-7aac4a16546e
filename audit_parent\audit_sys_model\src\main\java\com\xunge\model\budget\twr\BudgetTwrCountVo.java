package com.xunge.model.budget.twr;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2022/7/28
 * @description 预算总览
 */
@Data
public class BudgetTwrCountVo {
    /**
     * 省份ID
     */
    private String prvId;

    /**
     * 省份名称
     */
    private String prvName;
    /**
     * 业务类型({塔类,1}, {室分,2}, {微站,3}, {传输,4}, {非标,5}, {合计,6})
     */
    private Integer productType;

    private BigDecimal stockOrderBudgetFee;
    private BigDecimal complementNewBudgetFee;
    private BigDecimal rentOrderBudgetFee;
    private BigDecimal newSiteBudgetFee;
    private BigDecimal promoteSharedBudgetFee;
    private BigDecimal hireLogoutBudgetFee;
    private BigDecimal universalServiceBudgetFee;
    private BigDecimal budgetSupplementBudgetFee;
    private BigDecimal oilBudgetFee;
    private BigDecimal budgetSupplementExecutionFee;

    private BigDecimal pandectBudgetFee;

    /**
     * 预算核减金额
     */
    private BigDecimal pandectSubtractFee;
    /**
     * 预算核减后金额
     */
    private BigDecimal pandectSubtractFeeAfter;


    private BigDecimal pandectAfterFee;
    private BigDecimal pandectAfterFeeAfter;
}
