package com.xunge.core.util;

import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigInteger;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

/**
 * <AUTHOR>
 * @Description 加密处理
 */
@Slf4j
public class MD5Util {

    public static String encode(String str) throws Exception {
        return EncoderByMd5(str);
    }

    /**
     * 利用MD5进行加密 @param str 待加密的字符串 @return 加密后的字符串 @throws NoSuchAlgorithmException
     * 没有这种产生消息摘要的算法 @throws UnsupportedEncodingException
     */
    private static String EncoderByMd5(String plainText) throws Exception {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            md.update(plainText.getBytes());
            byte b[] = md.digest();

            int i;

            StringBuffer buf = new StringBuffer("");
            for (int offset = 0; offset < b.length; offset++) {
                i = b[offset];
                if (i < 0){
                    i += 256;
                }
                if (i < 16) {
                    buf.append("0");
                }
                buf.append(Integer.toHexString(i));
            }
            //32位加密
            return buf.toString();
            // 16位的加密
            //return buf.toString().substring(8, 24);
        } catch (NoSuchAlgorithmException e) {
            log.error("MD5Util 出错", e);
            return null;
        }
    }

    // 测试
    public static void main(String[] args) throws Exception {
        String encrypt = encode(String.format("%s%s%s%s", "external_key", "external_id", "ZJ", "2021-01-26 17:10:00"));
        System.out.println(encrypt);
    }

    /**
     * 通过MessageDigest计算MD5值，经测试与Linux上计算的MD5值一样
     * @param in
     * @return
     * @throws IOException
     */
    public static String getMd5ByFile(InputStream in) throws IOException {
        String value = null;
        byte buffer[] = new byte[1024];
        MessageDigest digest = null;
        int len;
        try {
            digest = MessageDigest.getInstance("MD5");
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        }
        if (digest!=null) {
            while ((len = in.read(buffer, 0, 1024)) != -1) {
                digest.update(buffer, 0, len);
            }
            BigInteger bigInt = new BigInteger(1, digest.digest());
            value = String.format("%032x", bigInt);
        }
        return value;
    }

    /**
     * 通过MessageDigest计算MD5值，经测试与Linux上计算的MD5值一样
     * @param file
     * @return
     * @throws IOException
     */
    public static String getMd5ByFile(File file) throws IOException {
        String value = null;
        FileInputStream in = null;
        try {
            in = new FileInputStream(file);
            value = getMd5ByFile(in);
        }
        catch (IOException e){
            throw e;
        }
        catch (Exception e){
            e.printStackTrace();
        }
        finally {
            if(in != null){
                in.close();
            }
        }
        return value;
    }
}
