package com.xunge.dao.roletype;

import com.xunge.model.roletype.SysRoleTypeExclusion;
import com.xunge.model.roletype.SysRoleTypeExclusionVo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Property;

import java.util.List;
import java.util.Map;

public interface SysRoleTypeExclusionMapper {
    
    int deleteByPrimaryKey(Integer exclusionRelationId);

    
    int insert(SysRoleTypeExclusion record);

    
    int insertSelective(SysRoleTypeExclusion record);

    
    SysRoleTypeExclusion selectByPrimaryKey(Integer exclusionRelationId);

    
    int updateByPrimaryKeySelective(SysRoleTypeExclusion record);

    
    int updateByPrimaryKey(SysRoleTypeExclusion record);


    /**
     * 批量插入互斥角色大类信息
     * @param list
     * @return
     */
    int batchInsertExclusion(List<SysRoleTypeExclusion> list);


    /**
     * 根据角色大类ID 查询互斥角色大类信息
     * @param id
     * @return
     */
    List<SysRoleTypeExclusion> queryExclusionByRoleTypeId(Integer id);

    void deleteExclusionByRoleTypeId(@Param("roleTypeId") Integer roleTypeId,@Param("exclusionIds")List<Integer> exclusionIds);


    void deleteExclusionByExclusionId(@Param("roleTypeIds") List<Integer> roleTypeIds,@Param("exclusionId") Integer exclusionId);

    List<SysRoleTypeExclusionVo> queryExclusionRelationByRoleTypeIds(Map<String,Object> map);

}