package com.xunge.comm.utils;

import com.xunge.model.selfelec.loan.EleLoan;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class ListUtils {

    public static List<List> splitList(List list, int len) {
        if (list == null || list.size() == 0 || len < 1) {
            return null;
        }

        List<List> result = new ArrayList<List>();


        int size = list.size();
        int count = (size + len - 1) / len;


        for (int i = 0; i < count; i++) {
            List subList = list.subList(i * len, ((i + 1) * len > size ? size : len * (i + 1)));
            result.add(subList);
        }
        return result;
    }

    public static List getListFromContent(String content, int count) {
        List list = new ArrayList();
        // 获取String的总长度
        int contentLength = content.length();
        if (contentLength < count) {
            list.add(content);
        } else {
            int begin = 0;
            // 获取需要切割多少段
            int cutCount = contentLength / count;
            int cutCounts = contentLength % count;
            // 获取切割段的长度
            if (cutCounts != 0) {
                cutCount++;
            }
            for (int i = 1; i <= cutCount; i++) {
                String temp;
                // 不是最后一段
                if (i != cutCount) {
                    temp = content.substring(begin, count * i);
                } else {
                    temp = content.substring(begin, contentLength);
                }
                begin = count * i;
                list.add(temp);
            }
        }
        return list;
    }

    public static void setListOrder(List<String> orderRegulation,  List<EleLoan> targetList) {
        Collections.sort(targetList, ((o1, o2) -> {
            int io1 = orderRegulation.indexOf(o1.getLoanCode());
            int io2 = orderRegulation.indexOf(o2.getLoanCode());
            return io1 - io2;
        }));
    }

}
