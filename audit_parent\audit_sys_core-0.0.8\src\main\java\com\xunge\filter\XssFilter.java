package com.xunge.filter;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

public class XssFilter implements Filter {
    FilterConfig filterConfig = null;

    public void init(FilterConfig filterConfig) throws ServletException {
        this.filterConfig = filterConfig;
    }

    public void destroy() {
        this.filterConfig = null;
    }

    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {

        HttpServletResponse sresponse = (HttpServletResponse) response;
        HttpServletRequest srequest = (HttpServletRequest) request;
        String requestURI = srequest.getRequestURI();
        //		.img,.jpg,.jpeg,.bmp,.tiff  .js .css
        if (requestURI.endsWith(".css") || requestURI.endsWith(".bmp") || requestURI.endsWith(".png") || requestURI.endsWith(".jpeg") || requestURI.endsWith(".img") || requestURI.endsWith(".tiff")) {
            sresponse.setHeader("Cache-Control", "max-age=43200");//- 这里的单位为秒，设置为半天：12*60*60=43200
        }
        if (requestURI.contains("webservice")) {//webservice 请求
            chain.doFilter(request, response);
        } else {
            chain.doFilter(new XssHttpServletRequestWrapper((HttpServletRequest) request), response);
        }
//		chain.doFilter(new XssHttpServletRequestWrapper(srequest), sresponse);
//		chain.doFilter(new XssHttpServletRequestWrapper((HttpServletRequest)request), response);
    }
}