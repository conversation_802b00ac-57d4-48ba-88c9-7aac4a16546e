package com.xunge.dao.basedata;

import com.xunge.model.basedata.DatContractVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface DatContractVOMapper {

    //int deleteByPrimaryKey(String contractId);
    int deleteByPrimaryKey(Map<String, Object> paraMap);

    int insert(DatContractVO record);

    DatContractVO selectByPrimaryKey(Map<String, Object> paraMap);

    DatContractVO selectByPrimaryId(Map<String, Object> paraMap);

    DatContractVO selectAuditingStateByContractId(Map<String, Object> paraMap);

    List<DatContractVO> selectAuditingStateListByContractIds(Map<String, Object> paraMap);

    List<Map<String,String>> getDatContractVOListByContractIds(@Param(value ="contractIds") List<String> contractIds);

    int updateByPrimaryKey(DatContractVO record);

    DatContractVO selectContractByEleContractId(Map<String, Object> contractCond);

    DatContractVO selectContractByBackups(Map<String, Object> contractCond);

    /**
     * 根据合同id获取报账点code
     *
     * @param contractId
     * @return
     */
    List<String> getBillaccountCodeByContractId(String contractId);
}