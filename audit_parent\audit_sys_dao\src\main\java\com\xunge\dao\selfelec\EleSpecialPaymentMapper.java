package com.xunge.dao.selfelec;

import com.xunge.model.selfelec.EleBillaccountPaymentdetail;
import com.xunge.model.selfelec.EleSpecialPaymentVO;
import com.xunge.model.selfelec.VEleBillaccountPaymentInfo;

import java.util.Map;

public interface EleSpecialPaymentMapper {

    /**
     * 新增缴费详情费信息
     *
     * @param paraMap
     * @return
     */
    int insertSpecialPaymentDetail(Map<String, Object> paraMap);

    /**
     * 新增缴费信息
     *
     * @param eleSpecialPaymentVO
     * @return
     */
    int insertSpecialPayment(EleSpecialPaymentVO eleSpecialPaymentVO);

    /**
     * 修改缴费详情信息
     *
     * @param paraMap
     * @return
     */
    int updatePaymentDetailByPrimaryKey(Map<String, Object> paraMap);

    /**
     * 删除缴费详情信息
     *
     * @param paymentdetailId
     * @return
     */
    int deletePaymentDetailByPrimaryKey(String billaccountPaymentdetailId);

    /**
     * 根据条件查询特殊报账点缴费信息
     *
     * @return
     */
    EleBillaccountPaymentdetail querySpecialPaymentByCondition(Map<String, Object> paraMap);

    /**
     * 查询缴费记录详情
     *
     * @param map
     * @return
     */
    public VEleBillaccountPaymentInfo queryEleBillaccountPaymentDetail(Map<String, Object> map);

    public VEleBillaccountPaymentInfo queryFinanceEleBillaccountPaymentDetail(Map<String, Object> map);
}
