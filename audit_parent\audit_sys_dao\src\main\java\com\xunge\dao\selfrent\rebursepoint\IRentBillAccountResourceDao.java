package com.xunge.dao.selfrent.rebursepoint;

import com.xunge.model.selfrent.billAccount.RentBillAccountResourceVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 报账点资源关系dao
 *
 * <AUTHOR> 2017年6月25日
 */
public interface IRentBillAccountResourceDao {
    /**
     * 添加报账点资源关系数据
     *
     * @param rentBillAccountResource
     */
    void insertBillAccountResource(List<RentBillAccountResourceVO> insertResourceList);

    /**
     * 修改报账点资源关系
     *
     * @param billAccountResourceId
     */
    void updateBillAccountResource(Map<String, Object> paramMap);

    /**
     * 删除报账点资源关系
     *
     * @param billAccountResourceId
     */
    void deleteBillAccountResource(Map<String, Object> paraMap);

    /**
     * 查询报账点是否已绑定报账点
     *
     * @param resourceIds
     * @return
     */
    List<RentBillAccountResourceVO> queryResourceBindBillacc(Map<String, Object> paraMap);

    /**
     * 删除资源绑定的报账点
     *
     * @param baseresourceId
     * @return
     */
    int deleteResourcePoint(String baseresourceId);

    /**
     * 根据报账点Id查询报账点关联资源点的信息
     *
     * @param billaccountId
     * @return
     */
    List<RentBillAccountResourceVO> queryBillaccountResourceById(Map<String, Object> paraMap);

    //根据资源id查询报账点编号
    List<RentBillAccountResourceVO> queryBillaccountCodeByResouceId(@Param("baseresourceIds") List<String> baseresourceIds);
}
