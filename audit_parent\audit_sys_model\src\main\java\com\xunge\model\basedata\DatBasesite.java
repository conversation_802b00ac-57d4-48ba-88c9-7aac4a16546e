package com.xunge.model.basedata;

import java.util.Date;

public class DatBasesite {
    
    private String basesiteId;

    
    private String regId;

    
    private String basesiteCode;

    
    private String basesiteName;

    
    private String basesiteAddress;

    
    private Integer basesiteType;

    
    private Integer basesiteState;

    
    private Date basesiteOpendate;

    
    private Date basesiteStopdate;

    
    private Integer basesiteBelong;

    
    private Integer basesiteProperty;

    
    private Integer basesiteShare;

    
    private Long basesiteLongitude;

    
    private Long basesiteLatitude;

    
    private Integer dataFrom;

    
    public String getBasesiteId() {
        return basesiteId;
    }

    
    public void setBasesiteId(String basesiteId) {
        this.basesiteId = basesiteId == null ? null : basesiteId.trim();
    }

    
    public String getRegId() {
        return regId;
    }

    
    public void setRegId(String regId) {
        this.regId = regId == null ? null : regId.trim();
    }

    
    public String getBasesiteCode() {
        return basesiteCode;
    }

    
    public void setBasesiteCode(String basesiteCode) {
        this.basesiteCode = basesiteCode == null ? null : basesiteCode.trim();
    }

    
    public String getBasesiteName() {
        return basesiteName;
    }

    
    public void setBasesiteName(String basesiteName) {
        this.basesiteName = basesiteName == null ? null : basesiteName.trim();
    }

    
    public String getBasesiteAddress() {
        return basesiteAddress;
    }

    
    public void setBasesiteAddress(String basesiteAddress) {
        this.basesiteAddress = basesiteAddress == null ? null : basesiteAddress.trim();
    }

    
    public Integer getBasesiteType() {
        return basesiteType;
    }

    
    public void setBasesiteType(Integer basesiteType) {
        this.basesiteType = basesiteType;
    }

    
    public Integer getBasesiteState() {
        return basesiteState;
    }

    
    public void setBasesiteState(Integer basesiteState) {
        this.basesiteState = basesiteState;
    }

    
    public Date getBasesiteOpendate() {
        return basesiteOpendate;
    }

    
    public void setBasesiteOpendate(Date basesiteOpendate) {
        this.basesiteOpendate = basesiteOpendate;
    }

    
    public Date getBasesiteStopdate() {
        return basesiteStopdate;
    }

    
    public void setBasesiteStopdate(Date basesiteStopdate) {
        this.basesiteStopdate = basesiteStopdate;
    }

    
    public Integer getBasesiteBelong() {
        return basesiteBelong;
    }

    
    public void setBasesiteBelong(Integer basesiteBelong) {
        this.basesiteBelong = basesiteBelong;
    }

    
    public Integer getBasesiteProperty() {
        return basesiteProperty;
    }

    
    public void setBasesiteProperty(Integer basesiteProperty) {
        this.basesiteProperty = basesiteProperty;
    }

    
    public Integer getBasesiteShare() {
        return basesiteShare;
    }

    
    public void setBasesiteShare(Integer basesiteShare) {
        this.basesiteShare = basesiteShare;
    }

    
    public Long getBasesiteLongitude() {
        return basesiteLongitude;
    }

    
    public void setBasesiteLongitude(Long basesiteLongitude) {
        this.basesiteLongitude = basesiteLongitude;
    }

    
    public Long getBasesiteLatitude() {
        return basesiteLatitude;
    }

    
    public void setBasesiteLatitude(Long basesiteLatitude) {
        this.basesiteLatitude = basesiteLatitude;
    }

    
    public Integer getDataFrom() {
        return dataFrom;
    }

    
    public void setDataFrom(Integer dataFrom) {
        this.dataFrom = dataFrom;
    }
}