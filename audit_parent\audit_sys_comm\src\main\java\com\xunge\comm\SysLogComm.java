package com.xunge.comm;

/**
 * <AUTHOR>
 * @date 2017年6月8日
 * @description 系统日志枚举类
 */
public class SysLogComm {

    /**
     * 错误记录后缀：1，一般错误
     */
    public final static int ERROR_Suffix = 1;
    /**
     * 操作日志
     */
    public final static int LOG_Operate = 10;
    /**
     * 错误日志
     */
    public final static int LOG_Error = 20;
    /**
     * 登陆日志
     */
    public final static int LOG_Login = 30;

    /**
     * 系统异常日志
     */
    public final static int LOG_EXCEPTION = 99;


    /**
     * 报账点启停日志
     */
    public final static int BILLACOUNT_MANAGER = 100;

    /**
     * 电表启停日志
     */
    public final static int METER_MANAGER = 101;


    /**
     * 报账点撤销日志
     */
    public final static int BILLACOUNT_SENDBACK = 300;
    /**
     * 报账点异常审核日志
     */
    public final static int BILLACOUNT_AUDIT_ERROR = 301;

    /**
     * 用户被审核通过日志,什么时间被谁审核通过，主要影响账号启用
     */
    public final static int USER_APPROVED = 400;

}
