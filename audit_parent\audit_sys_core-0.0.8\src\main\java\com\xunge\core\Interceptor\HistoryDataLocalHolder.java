package com.xunge.core.Interceptor;

/**
 * 从割接的历史数据表中查询，通过mybatis plugin完成表名替换。
 * 应用场景：1、历史流程查询
 * <AUTHOR>
 * @date 2025/8/19 11:40
 */
public class HistoryDataLocalHolder {
    private static final ThreadLocal<String> historyDataThreadLocal = new ThreadLocal<>();

    public static boolean queryHistoryData(String holderName) {
        String localName = historyDataThreadLocal.get();
        return localName!=null&&localName.equals(holderName);
    }

    public static void setHolderName(String holderName) {
        historyDataThreadLocal.set(holderName);
    }

    public static void remove() {
        historyDataThreadLocal.remove();
    }

    /**
     * 历史流程表
     */
    public static final String HISTORY_ACTIVITY_TABLE = "history_activity";

}
