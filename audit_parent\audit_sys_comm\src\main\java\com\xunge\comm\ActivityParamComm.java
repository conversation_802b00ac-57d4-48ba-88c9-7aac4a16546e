package com.xunge.comm;

/**
 * @description 流程参数标识
 * <AUTHOR>
 * @date 创建时间：2022年6月20日
 */
public class ActivityParamComm {
	/**
	 * 转改直站点异常稽核
	 */
	public static final String TRANSFERTOSTRAIGHT_PAYMENT_FLAG = "transfertostraightPaymentFlag";

	/**
	 * 报账点转改直
	 */
	public static final String TRANSFERTOSTRAIGHT_BILLACCOUNT_FLAG = "transfertostraightBillaccountFlag";

	/**
	 * 系统计算普服比例和实际普服比例是否相等
	 */
	public static final String CMN_SER_EQUATION_FLAG = "cmnSerEquationFlag";

	/**
	 * 预付省管理员稽核标识
	 */
	public static final String Ele_Loan_Provincial_Audit_Flag = "eleLoanProvincialAuditFlag";

	/**
	 * 异常省管理员稽核标识
	 */
	public static final String useElecCost = "useElecCost";

	/**
	 * 区域单价省管理员稽核标识
	 */
	public static final String Ele_Region_Price_Provincial_Audit_Flag = "eleRegionPriceProvincialAuditFlag";

	/**
	 * 流程启动参数 模拟“审核意见”
	 */
	public static final String START_COMMENT = "startComment";

}
