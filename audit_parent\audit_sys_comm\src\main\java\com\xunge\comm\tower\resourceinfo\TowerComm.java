package com.xunge.comm.tower.resourceinfo;

/**
 * <AUTHOR>
 * @date 2017年10月17日
 * @describe 铁塔常量
 */
public class TowerComm {

    /**
     * 确认状态
     */
    public static final String CONFIRM_STATE = "确认状态";
    /**
     * 确认状态（调整）
     */
    public static final String CONFIRM_STATE_STR = "确认状态（调整）";
    /**
     * 产品大类
     */
    public static final String PRODUCT_BIG_TYPE = "产品大类";
    /**
     * 业务属性
     */
    public static final String SERVICE_ATTRIBUTE = "业务属性";// 业务属性
    /**
     * 机房产品
     */
    public static final String COMPUTER_PRODUCTS = "机房产品";//机房产品
    /**
     * 机房类型
     */
    public static final String ROOM_TYPE = "机房类型";//机房产品
    /**
     * 油机发电模式
     */
    public static final String OIL_MACHINE_MODE = "油机发电模式"; //油机发电模式
    /**
     * 订单属性
     */
    public static final String ORDER_ATTRIBUTE = "订单属性"; //订单属性
    /**
     * 产权属性
     */
    public static final String PROPERTY_RIGHTS = "产权属性";//产权属性
    /**
     * 原产权方
     */
    public static final String CARRIER_CATEGORY = "原产权方";//原产权方
    /**
     * 运营商类别
     */
    public static final String DIC_CARRIER_CATEGORY = "运营商类别";//原产权方
    /**
     * 铁塔产品
     */
    public static final String TOWER_PRODUCTS = "铁塔产品";//	铁塔产品
    /**
     * 产品类型
     */
    public static final String PRODUCTS_TYPE = "产品类型";
    public static final String TOWER_PRODUCTS_NEWS = "铁塔产品（综资）";
    /**
     * 变更项目
     */
    public static final String CHANGE_ITEM = "变更项目";
    /**
     * 铁塔共享信息
     */
    public static final String SHARE_INFO = "铁塔共享信息";
    /**
     * 机房共享信息
     */
    public static final String ROOM_SHARE_INFO = "机房共享信息";
    /**
     * 场景划分
     */
    public static final String SCENE_CLASSIFICATION = "场景划分";
    /**
     * 油机发电服务费模式
     */
    public static final String OIL_GENERATOR = "油机发电服务费模式";
    /**
     * 维护等级
     */
    public static final String MAINTENANCE_ECHELON = "维护等级";
    /**
     * 电力保障服务费模式
     */
    public static final String POWER_PROTECTE_MODE = "电力保障服务费模式";
    /**
     * 场地费模式
     */
    public static final String SITE_FEE_MODEL = "场地费模式";
    /**
     * 电力引入费模式
     */
    public static final String POWER_INTRODUCE_FEED = "电力引入费模式";
    public static final String MAINTENANCE_FEE_MODE = "维护费模式";
    public static final String SITE_SHARE_TYPE = "站址共享信息";
    public static final String SUPPORTING_SHARE_INFO = "配套共享信息";
    public static final String SUPPORTING_PRODUCT_TYPE = "配套产品";
    public static final String STAGE_FEE_SHARE_INFO = "场地费共享信息";
    /**
     * 账单类型 1 铁塔账单
     */
    public static final String TOWER_BILL = "1";
    /**
     * 账单类型 2移动塔账单
     */
    public static final String MOBILE_BILL = "2";
    public static final String TWR_MOBILE_CURRENT = "2";
    public static final String TWR_MOBILE_BACKUP = "1";
    public static final String SHOWTYPE_CURRENT = "2";
    public static final String SHOWTYPE_BACKUP = "1";
    /**
     * 26-33无塔id数组 常量数组 {"26","27","28","29","30","31","32","33"}
     */
    public static String[] NUMBER_LIST = {"26", "27", "28", "29", "30", "31", "32", "33"};

    /**
     * 周报-铁塔
     */
    public static final Integer WEEK_REPORT_TOWER = 1;
    /**
     * 周报-机房
     */
    public static final Integer WEEK_REPORT_ROOM = 2;
    /**
     * 周报-配套
     */
    public static final Integer WEEK_REPORT_PT = 3;
    /**
     * 周报-维护费
     */
    public static final Integer WEEK_REPORT_MAINTAIN = 4;
    /**
     * 周报-场地费
     */
    public static final Integer WEEK_REPORT_RENT = 5;
    /**
     * 周报-电力引入费
     */
    public static final Integer WEEK_REPORT_ELE = 6;

    public static final String Y_AXIS = "\\|";

    public static final String DICT_TOWER_TYPE = "TOWER_PRODUCTS";

    public static final String DICT_ROOM_TYPE = "COMPUTER_PRODUCTS";

    public static final String DICT_SUPPORT_TYPE = "SUPPORTING_PRODUC_TTYPE";

}
