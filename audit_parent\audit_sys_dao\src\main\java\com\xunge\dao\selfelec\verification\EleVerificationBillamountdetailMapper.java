package com.xunge.dao.selfelec.verification;

import com.xunge.model.selfelec.billamount.AccrualBillamountDetailQueryDto;
import com.xunge.model.selfelec.billamount.AccrualBillamountQueryDto;
import com.xunge.model.selfelec.billamount.AccrualSecondBillamountDto;
import com.xunge.model.selfelec.billamount.PaymentOffsDetailDto;
import com.xunge.model.selfelec.verification.*;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

public interface EleVerificationBillamountdetailMapper {
    int countByExample(EleVerificationBillamountdetailExample example);

    int deleteByExample(EleVerificationBillamountdetailExample example);

    int deleteByPrimaryKey(String billamountdetailId);

    int insert(EleVerificationBillamountdetail record);

    int insertSelective(EleVerificationBillamountdetail record);

    List<EleVerificationBillamountdetail> selectByExample(EleVerificationBillamountdetailExample example);

    EleVerificationBillamountdetail selectByPrimaryKey(String billamountdetailId);

    int updateByExampleSelective(@Param("record") EleVerificationBillamountdetail record,
                                 @Param("example") EleVerificationBillamountdetailExample example);

    int updateByExample(@Param("record") EleVerificationBillamountdetail record,
                        @Param("example") EleVerificationBillamountdetailExample example);

    int updateByPrimaryKeySelective(EleVerificationBillamountdetail record);

    int updateByPrimaryKey(EleVerificationBillamountdetail record);

    //核销汇总详情信息
    List<EleVerificationBillamountdetail> queryEleVerificationBillamountDetail(Map<String, Object> maps);

    //导出借款明细
    List<EleVerificationBillamountdetail> queryBillamountDetail(@Param("billamountId") String billamountId);

    List<EleVerificationBillamountdetailFinance> queryBillamountDetailFinance(@Param("billamountId") String billamountId);

    /**
     * @Title: queryeleVerificationBySupplierId @Description: TODO(这里用一句话描述这个方法的作用) @param @param
     * eleVerificationBillamountdetail @param @return 设定文件 @return
     * List<EleVerificationBillamountdetail> 返回类型 @throws
     */

    List<EleVerificationBillamountdetail> queryEleVerificationBySupplierId(EleVerificationBillamountdetail eleVerificationBillamountdetail);

    List<EleVerificationBillamountdetail> queryEleVerificationBySupplierIdFcontract(EleVerificationBillamountdetail eleVerificationBillamountdetail);

    /**
     * @Title: queryBillamountVerification @Description: TODO(这里用一句话描述这个方法的作用) @param @param
     * maps @param @return 设定文件 @return List<EleVerificationBillamountdetail> 返回类型 @throws
     */

    List<EleVerificationBillamountdetail> queryBillamountVerification(Map<String, Object> maps);

    /**
     * @Title: queryNumber @Description: TODO(这里用一句话描述这个方法的作用) @param @param
     * verificationId @param @return 设定文件 @return int 返回类型 @throws
     */

    int queryNumber(@Param("verificationId") String verificationId);

    /**
     * 根据汇总单ID删除明细信息
     *
     * @param billamountId
     * @return
     */
    int deleteByBillamountId(String billamountId);

    /**
     * 调整核销明细金额
     *
     * @param param
     * @return
     */
    int updateBillamountdetailAdjustById(EleVerificationBillamountdetail param);

    List<EleVerificationBillamountdetail> queryEleVerificationBySupplier(EleVerificationBillamountdetail eleVerificationBillamountdetail);

    List<EleVerificationBillamountdetail> querySupplierInfo(@Param("billamountId") String billamountId);

    EleVerification queryEleVerificationInfo(@Param("billamountId") String billamountId, @Param("billamountdetailId") String billamountdetailId);

    EleVerification queryBillAmountActual(@Param("billamountdetailId") String billamountdetailId);


    /**
     * @param @param supplierId
     * @param @param supplierIds    设定文件
     * @return void    返回类型
     * @throws
     * @Title: updateSupplierInfo
     * @Description: TODO(这里用一句话描述这个方法的作用)
     */

    void updateSupplierInfo(@Param("supplierId") String supplierId, @Param("supplierIds") List<String> supplierIds);


    /**
     * @param @param contractId
     * @param @param contractIds    设定文件
     * @return void    返回类型
     * @throws
     * @Title: updateComtractInfo
     * @Description: TODO(这里用一句话描述这个方法的作用)
     */

    void updateComtractInfo(@Param("contractId") String contractId, @Param("contractIds") List<String> contractIds);

    List<EleVerificationBillamountdetailFinanceNew> queryBillamountDetailFinanceNew(@Param("billamountId") String billamountId);

    List<EleVerificationBillamountdetail> queryEleBillamountDetailLeftJoinOther(Map<String, Object> maps);

    List<AccrualSecondBillamountDto> selectAccrualSecondDetail(AccrualBillamountQueryDto accrualBillamountQueryDto);

    int addPaymentOffsDetail(List<PaymentOffsDetailDto> list);

    /**
     * 修改电费汇总单冲销金额同时修改计提二次汇总可冲销余额
     * @param dto
     * @return
     */
    int updatePaymentOffsDetail(PaymentOffsDetailDto dto);

    /**
     * 根据汇总单冲销明细id查询二次汇总id
     * @param id
     * @return
     */
    String selectAccrualSecondDetailId(String id);

    /**
     * 删除汇总单前先恢复计提二次汇总可冲销余额
     * @param dto
     * @return
     */
    int revertPaymentOffsDetail(PaymentOffsDetailDto dto);

    /**
     * 删除电费汇总单的冲销明细
     * @param id
     * @return
     */
    int deletePaymentOffsDetail(String id);

    PaymentOffsDetailDto selectOldOffsAmount(String id);

    List<AccrualSecondBillamountDto> selectPaymentOffsDetail(AccrualBillamountQueryDto accrualBillamountQueryDto);
    BigDecimal getElePaymentOffsAmount(String billamountId);

}