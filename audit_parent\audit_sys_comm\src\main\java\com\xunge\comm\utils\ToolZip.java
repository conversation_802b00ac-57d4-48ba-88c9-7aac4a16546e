package com.xunge.comm.utils;

import lombok.extern.slf4j.Slf4j;

import java.io.*;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * ToolZip
 *
 * <AUTHOR>
 * @version V1.0
 * @description 压缩文件
 * @date 2017年11月30日
 */
@Slf4j
public class ToolZip {
    /**
     * 此处填写方法名称
     *
     * @param sourceFileName 源文件（带压缩的文件或文件夹）
     * @param zipFileName    目的地Zip文件
     * @return void 返回类型
     * @throws Exception
     * @description <描述>
     * <AUTHOR>
     * @version V1.0
     * @date 2018年5月5日
     * @email <EMAIL>
     */
    public static void zip(String sourceFileName, String zipFileName) throws Exception {
        //File zipFile = new File(zipFileName);
        log.info("压缩中...：" + zipFileName);

        //创建zip输出流
        ZipOutputStream out = new ZipOutputStream(new FileOutputStream(zipFileName));

        //创建缓冲输出流
        BufferedOutputStream bos = new BufferedOutputStream(out);

        File sourceFile = new File(sourceFileName);

        //调用函数
        compress(out, bos, sourceFile, sourceFile.getName());

        bos.close();
        out.close();
        log.info("压缩完成：" + zipFileName);

    }

    /**
     * 此处填写方法名称
     *
     * @param sourceFileList
     * @param zipFilePath
     * @return void 返回类型
     * @throws Exception
     * @description <描述>
     * <AUTHOR>
     * @version V1.0
     * @date 2018年5月5日
     * @email <EMAIL>
     */
    public static void zip(List<String> sourceFileList, String subPath, String zipFilePath) throws Exception {
        log.info("压缩中...");

        //创建zip输出流
        ZipOutputStream out = new ZipOutputStream(new FileOutputStream(zipFilePath));

        //创建缓冲输出流
        BufferedOutputStream bos = new BufferedOutputStream(out);
        // 去除重复文件
        HashSet<String> names = new HashSet<>();
        //调用函数
        compress(out, bos, subPath, sourceFileList, names);

        bos.close();
        out.close();
        log.info("压缩完成");

    }

    /**
     * 无subpath
     *
     * @param sourceFileList
     * @param zipFilePath
     * @throws Exception
     */
    public static void zip(List<String> sourceFileList, String zipFilePath) throws Exception {
        log.info("压缩中...");

        //创建zip输出流
        ZipOutputStream out = new ZipOutputStream(new FileOutputStream(zipFilePath));

        //创建缓冲输出流
        BufferedOutputStream bos = new BufferedOutputStream(out);
        // 去除重复文件
        HashSet<String> names = new HashSet<>();
        //调用函数
        compress(out, bos, sourceFileList, names);

        bos.close();
        out.close();
        log.info("压缩完成");

    }


    private static void compress(ZipOutputStream out, BufferedOutputStream bos, File sourceFile, String base) throws Exception {
        //如果路径为目录（文件夹）
        if (sourceFile.isDirectory()) {

            //取出文件夹中的文件（或子文件夹）
            File[] flist = sourceFile.listFiles();

            if (flist.length == 0)//如果文件夹为空，则只需在目的地zip文件中写入一个目录进入点
            {
                log.info(base + "/");
                out.putNextEntry(new ZipEntry(base + "/"));
            } else//如果文件夹不为空，则递归调用compress，文件夹中的每一个文件（或文件夹）进行压缩
            {
                for (int i = 0; i < flist.length; i++) {
                    compress(out, bos, flist[i], base + "/" + flist[i].getName());
                }
            }
        } else//如果不是目录（文件夹），即为文件，则先写入目录进入点，之后将文件写入zip文件中
        {
            FileInputStream fos =null;
            BufferedInputStream bis =null;
            try {
                out.putNextEntry(new ZipEntry(base));
                fos = new FileInputStream(sourceFile);
                bis = new BufferedInputStream(fos);

                byte[] buff = new byte[2048];
                int bytesread;
                // 写文件
                while (-1 != (bytesread = bis.read(buff, 0, buff.length))) {
                    out.write(buff, 0, bytesread);
                }
                bos.flush();
            }catch (Exception e){

            }finally {
                try {
                    if (bis!=null){
                        bis.close();
                    }
                }catch (Exception e){
                    log.error("流关闭异常", e);
                }
                try {
                    if (fos!=null){
                        fos.close();
                    }
                }catch (Exception e){
                    log.error("流关闭异常", e);
                }
            }
        }
    }

    private static void compress(ZipOutputStream out, BufferedOutputStream bos, String subPath, List<String> sourceFileList, HashSet<String> names) {
        BufferedInputStream bis =null;
        FileInputStream fos = null;
        try {
            //如果路径为目录（文件夹）
            if (sourceFileList != null) {
                for (String filePath : sourceFileList) {

                    //取出文件夹中的文件（或子文件夹）
                    File file = new File(subPath + filePath);
                    if (!file.exists()) {
                        // 文件不存在 跳出当前压缩
                        log.info(filePath + ":文件不存在继续!~");
                        continue;
                    }
                    if (!names.add(filePath)) {
                        log.info(filePath + ":文件已经处理!~");
                        continue;
                    }
                    byte[] buf = new byte[2048];
                    out.putNextEntry(
                            new ZipEntry(file.getParentFile().getPath().substring(file.getParentFile().getPath().indexOf(subPath) + subPath.length() - 1)
                                    + "/" + file.getName()));
                    fos = new FileInputStream(file);
                    bis = new BufferedInputStream(fos);
                    int tag;
                    //将源文件写入到zip文件中
                    while ((tag = bis.read(buf, 0, buf.length)) != -1) {
                        out.write(buf, 0, tag);
                    }
                    out.closeEntry();
                    bos.flush();
                }
            }
        } catch (Exception e) {
            log.error("压缩文件异常", e);
        }finally {
            try {
                if (bis!=null){
                    bis.close();
                }
            }catch (Exception e){
                log.error("流关闭异常", e);
            }
            try {
                if (fos!=null){
                    fos.close();
                }
            }catch (Exception e){
                log.error("流关闭异常", e);
            }
        }
    }

    private static void compress(ZipOutputStream out, BufferedOutputStream bos, List<String> sourceFileList, HashSet<String> names) {
        BufferedInputStream bis =null;
        FileInputStream fos = null;
        try {
            //如果路径为目录（文件夹）
            if (sourceFileList != null) {
                for (String filePath : sourceFileList) {

                    //取出文件夹中的文件（或子文件夹）
                    File file = new File(filePath);
                    if (!file.exists()) {
                        // 文件不存在 跳出当前压缩
                        log.info(filePath + ":文件不存在继续!~");
                        continue;
                    }
                    if (!names.add(filePath)) {
                        log.info(filePath + ":文件已经处理!~");
                        continue;
                    }
                    byte[] buf = new byte[2048];
                    out.putNextEntry(
                            new ZipEntry(file.getName()));
                    fos = new FileInputStream(file);
                    bis = new BufferedInputStream(fos);
                    int tag;
                    //将源文件写入到zip文件中
                    while ((tag = bis.read(buf, 0, buf.length)) != -1) {
                        out.write(buf, 0, tag);
                    }
                    out.closeEntry();
                    bos.flush();
                }
            }
        } catch (Exception e) {
            log.error("压缩文件异常", e);
        }finally {
            try {
                if (bis!=null){
                    bis.close();
                }
            }catch (Exception e){
                log.error("流关闭异常", e);
            }
            try {
                if (fos!=null){
                    fos.close();
                }
            }catch (Exception e){
                log.error("流关闭异常", e);
            }
        }
    }

    public static void main(String[] args) throws Exception {
        List<String> alist = new ArrayList<String>();
        alist.add("files\\JT\\20180505\\11.xlsx");
        alist.add("files\\JT\\20180504\\22.xlsx");
        alist.add("files\\AH\\附件1：站点信息统计表-安徽省-9月.xlsx");
        alist.add("files\\AH\\附件1：站点信息统计表-安徽省-9月.xlsx");
        alist.add("files\\AH2\\附件1：站点信息统计表-安徽省-9月.xlsx");
        alist.add("files\\AH3\\附件1：站点信息统计表-安徽省-9月.xlsx");
        alist.add("files\\AH4\\附件1：站点信息统计表-安徽省-9月.xlsx");
        alist.add("files\\AH5\\附件1：站点信息统计表-安徽省-9月.xlsx");
        alist.add("files\\AH6\\附件1：站点信息统计表-安徽省-9月.xlsx");
        alist.add("files\\AH7\\附件1：站点信息统计表-安徽省-9月.xlsx");
        alist.add("files\\AH8\\附件1：站点信息统计表-安徽省-9月.xlsx");
        alist.add("files\\AH9\\附件1：站点信息统计表-安徽省-9月.xlsx");
        alist.add("files\\BJ\\20180504\\5555.xlsx");
        alist.add("files\\BJ\\20180504\\7777.xlsx");
        ToolZip.zip(alist, "F:\\", "F:\\temp\\8888.rar");

        //		ToolZip.zip( "/Users/<USER>/Downloads/test/", "/Users/<USER>/Downloads/2222.zip" );
    }

}
