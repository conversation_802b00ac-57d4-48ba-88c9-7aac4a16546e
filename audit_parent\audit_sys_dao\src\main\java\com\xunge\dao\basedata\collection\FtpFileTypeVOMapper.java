package com.xunge.dao.basedata.collection;

import com.xunge.model.basedata.colletion.FtpFileTypeVO;

import java.util.List;

public interface FtpFileTypeVOMapper {
    int deleteByPrimaryKey(Integer typeId);

    int insert(FtpFileTypeVO record);

    int insertSelective(FtpFileTypeVO record);

    FtpFileTypeVO selectByPrimaryKey(Integer typeId);

    int updateByPrimaryKeySelective(FtpFileTypeVO record);

    int updateByPrimaryKey(FtpFileTypeVO record);

    List<FtpFileTypeVO> getFtpFileTypebyGroupId(int typeGroupId);
}