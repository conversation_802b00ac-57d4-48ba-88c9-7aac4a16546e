package com.xunge.dao.selfrent.rebursepoint;

import com.github.pagehelper.PageInfo;
import com.xunge.core.page.Page;
import com.xunge.model.selfrent.contract.BillContractVO;
import com.xunge.model.selfrent.contract.DatContractVO;
import com.xunge.model.selfrent.contract.RentContractVO;
import com.xunge.model.selfrent.rebursepoint.RentBillaccountChangeVO;
import com.xunge.model.selfrent.rebursepoint.RentBillaccountEptVO;
import com.xunge.model.selfrent.rebursepoint.RentBillaccountVO;
import com.xunge.model.selfrent.resource.DatBaseResourceVO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface IRebursePointDao {
    /**
     * 查询报账点信息
     *
     * @param paraMap
     * @param pageNumber
     * @param pageSize
     * @return
     * @throws Exception
     */
    public PageInfo<RentBillaccountVO> queryRembursePointInfo(Map<String, Object> pMap, int pageNumber, int pageSize);

    /**
     * 导出报账点信息
     *
     * @param map
     * @return
     */
    public List<RentBillaccountVO> queryRembursePointInfo(Map<String, Object> map);

    /**
     * 导出报账点信息(含相关合同、资源点)
     *
     * @param map
     * @return
     */
    public List<RentBillaccountEptVO> queryRembursePointVOForExport(Map<String, Object> paraMap);

    /**
     * 查询与用户关联的合同信息
     *
     * @param paraMap
     * @param pageNumber
     * @param pageSize
     * @return
     */
    public Page<List<RentContractVO>> queryContractAgreement(Map<String, Object> pMap, int pageNumber, int pageSize);

    public Page<List<RentContractVO>> queryContractAgreementFcontract(Map<String, Object> pMap, int pageNumber, int pageSize);

    /**
     * 添加报账点
     *
     * @param rentBillaccount
     * <AUTHOR>
     */
    public int insertBillAcount(RentBillaccountVO rentBillaccount);

    /**
     * 添加报账点操作流程
     *
     * @param rentBillaccount
     * <AUTHOR>
     */
    public int insertOperateTime(RentBillaccountVO rentBillaccount);

    /**
     * 批量删除报账点
     *
     * @param paraMap
     */
    public void deleteBillAcount(Map<String, Object> paraMap);

    /**
     * 根据用户选择查询资源信息
     *
     * @param paraMap
     * @param pageNumber
     * @param pageSize
     * @return
     */
    public List<DatBaseResourceVO> queryContractByResourceId(Map<String, Object> paraMap);

    /**
     * 根据主合同ID查询房租合同信息
     *
     * @param paraMap
     * @return
     */
    public RentContractVO queryContractById(Map<String, Object> paraMap);

    /**
     * 通过报账点ID查询资源信息
     *
     * @param pMap
     * @return 2017年7月10日 lpw
     */
    public List<DatBaseResourceVO> queryResource(Map<String, Object> pMap);

    public List<DatBaseResourceVO> queryBatchResource(Map<String, Object> pMap);

    public List<DatBaseResourceVO> queryAssociatedBillResourceByResourceId(DatBaseResourceVO datBaseResourceVO);


    /**
     * 通过报账点ID查询报账点信息
     *
     * @param paraMap
     * @return 2017年7月10日 lpw
     */
    public RentBillaccountVO queryBillAccountById(Map<String, Object> paraMap);

    /**
     * 通过报账点ID查询报账点信息
     *
     * @param paraMap
     * @return 2017年7月10日 lpw
     */
    public List<RentBillaccountVO> queryBillAccountByIdList(List<String> ids);

    /**
     * 报账点提交审核
     *
     * @param map
     */
    public int billAccountSubmitAudit(Map<String, Object> map);

    /**
     * 查询待审批的报账点
     *
     * @param paraMap
     * @param pageNumber
     * @param pageSize
     * @return
     */
    public PageInfo<RentBillaccountVO> queryRembursePointVO(Map<String, Object> paraMap, int pageNumber, int pageSize);

    /**
     * 报账点管理查询
     *
     * @param paraMap
     * @return
     */
    public List<RentBillaccountVO> queryRembursePointVO(Map<String, Object> paraMap);

    /**
     * 根据报账点ID查询支付方式
     *
     * @param billAccountId
     * @return
     */
    public RentBillaccountVO queryPaymentMethod(String billAccountId);

    /**
     * 批量插入
     *
     * @param list
     */
    public int insertBatchSelective(List<RentBillaccountVO> list);

    /**
     * 根据报账点编码查询是否有相同编码的报账点存在
     *
     * @param billaccountCode
     * @return
     */
    public List<String> queryBillaccountByCode(String billaccountCode);

    /**
     * 批量退回
     *
     * @param paraMap
     */
    public void updateBillaccountState(Map<String, Object> paraMap);

    public List<Map<String, Object>> selectResourceRelations(Map<String, Object> map);

    /**
     * 根据报账点Id查询报账点关联的合同信息
     *
     * @param
     * @return
     */
    public RentContractVO queryBillaccountContractById(Map<String, Object> parMap);

    public RentContractVO queryBillaccountContractByIdFcontract(Map<String, Object> parMap);

    /**
     * 检查数据是否存在
     *
     * @param paraMap
     * @return
     */
    int countById(Map<String, Object> paraMap);

    /**
     * 通过合同ID查询信息
     *
     * @param paraMap
     * @return
     */
    Map<String, Object> queryBeanById(Map<String, Object> paraMap);

    int queryPaymentByBillid(String billaccountId);

    int updateBillaccountStateChange(Map paramMap);

    void deleteContractbillaccountRelation(String billaccountId);

    void deleteResourcebillaccountRelation(String billaccountId);

    void deleteBillaccountInfo(String billaccountId);

    List<RentBillaccountVO> queryRembursePointInfoByRemoveResource(Map<String, Object> pMap);

    List<RentBillaccountVO> queryRembursePointVOByRemoveResource(Map<String, Object> paraMap);

    void insertAcountTypeChangeLog(Map<String, Object> map);

    List<BillContractVO> queryContractByBillId(String billAccountId);

    int insertBillContract(List<BillContractVO> insert);

    int updateDeleteTimeByBillId(BillContractVO billContractVO);

    List<Map<String, Object>> queryAccountTypeChangeLog(String billAccountId);

    List<DatContractVO> queryContAgreementByBillId(Map<String, Object> paraMap);

    List<BillContractVO> queryContractAgreementByBillId(String billAccountId);

    Page<List<RentContractVO>> queryContractAgreementFcontractNew(Map<String, Object> pMap, int pageNumber, int pageSize);

    int insertBillAccountChange(List<Map<String, Object>> mapList);

    int updateBillAccountChange(Map<String, Object> map);

    int deleteBillAccountChange(List<String> deleteList);

    int deleteBillAccountChangeByBillaccountIdAndState(Map<String, Object> map);

    int updateBillAccountChangeAuditStateById(Map<String, Object> map);

    List<RentBillaccountChangeVO> selertBillAccountChange(Map<String, Object> map);

    List<RentContractVO> queryContractAgreementFcontractRebuild(Map<String, Object> map);

    List<RentContractVO> queryContractAgreementRebuild(Map<String, Object> map);

    List<RentContractVO> queryContractAgreementFcontractNewRebuild(Map<String, Object> map);

}
