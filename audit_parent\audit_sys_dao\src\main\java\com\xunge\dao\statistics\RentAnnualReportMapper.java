package com.xunge.dao.statistics;

import com.xunge.model.report.RptRentAnnualStatisticsVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @创建人 LiangCheng
 * @创建时间 2021/2/7 0007
 * @描述：
 */
public interface RentAnnualReportMapper {


    List<Map<String, Object>> getRentAnnaulHistogram(@Param("year") int year);

    List<RptRentAnnualStatisticsVO> getRentAnnaulHistogramData(@Param("year") int year);

    List<RptRentAnnualStatisticsVO> getRentAnnaulHistogramDataCount(@Param("year") int year);


}
