package com.xunge.comm.elec;

public enum PaymentResourceDataFromEnum {

    /**
     * 1：塔维报账点缴费；2：网络电费报账点缴费；3：特殊缴费（普通）；4：特殊缴费（一站多录）；
     * 5：普通核销；6：IC卡核销；7：先款后票核销；
     * 8：普通预付费；9：IC卡预付费；10：先款后票预付费
     */
    ONE(1, "塔维报账点缴费"),
    TWO(2, "网络电费报账点缴费"),
    THREE(3, "特殊缴费（普通）"),
    FOUR(4, "特殊缴费（一站多录）"),
    FIVE(5, "普通核销"),
    SIX(6, "IC卡核销"),
    SEVEN(7, "先款后票核销"),
    EIGHT(8, "普通预付费"),
    NINE(9, "IC卡预付费"),
    TEN(10, "先款后票预付费"),
    ELEVEN(11, "预付费退款");

    private final int code;
    private final String text;

    PaymentResourceDataFromEnum(int code, String text) {
        this.code = code;
        this.text = text;
    }

    public int getCode() {
        return code;
    }

    public String getText() {
        return text;
    }
}
