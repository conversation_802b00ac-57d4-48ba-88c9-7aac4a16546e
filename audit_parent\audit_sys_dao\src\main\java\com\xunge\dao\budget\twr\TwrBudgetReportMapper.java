package com.xunge.dao.budget.twr;

import com.xunge.model.budget.twr.BudgetTwrParamVO;
import com.xunge.model.budget.twr.TwrBudgetReport;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;


public interface TwrBudgetReportMapper {
    int deleteByPrimaryKey(String id);

    int insert(TwrBudgetReport record);

    int insertSelective(TwrBudgetReport record);

    TwrBudgetReport selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(TwrBudgetReport record);

    int updateByPrimaryKey(TwrBudgetReport record);

    List<TwrBudgetReport> queryTwrBudgetReport(BudgetTwrParamVO paramVO);

    List<TwrBudgetReport> queryTwrBudgetReportSnapshoot(Map<String,Object> paramMap);

    int deleteTwrBudgetData(BudgetTwrParamVO paramVO);

    List<TwrBudgetReport> queryTwrBudgetReportDate(BudgetTwrParamVO paramVO);

    List<TwrBudgetReport> queryTwrPrvBudgetReportDate(BudgetTwrParamVO paramVO);

}
