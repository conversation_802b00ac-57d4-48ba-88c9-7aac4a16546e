package com.xunge.dao.selfelec.paymentDate;

import com.xunge.model.basedata.DatBaseresourceVO;
import com.xunge.model.selfelec.VEleBillaccountPaymentInfo;
import com.xunge.model.selfelec.VEleContract;
import com.xunge.model.selfelec.eleverificate.VEleBillaccountVerificateInfo;
import com.xunge.model.selfelec.paymentDate.BillaccountPaymentDataVO;
import com.xunge.model.selfelec.paymentDate.PaymentDateVO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @description:
 * @author: liujx
 * @time: 2019/10/31 10:34
 */

@Component
public interface ElePaymentDateMapper {
    //List<PaymentDateVO> queryPaymentsByParam(Map paraM);


    List<PaymentDateVO> selectPaymentDateByBillaccountId(String businessId);
    PaymentDateVO selectEndDateByBillaccountId(Map paraM);

    List<PaymentDateVO> selectRentPaymentDateByBillaccountId(String businessId);

    List<PaymentDateVO> queryPaymentDateListByTime(@Param("billaccountId") String billaccountId, @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    List<PaymentDateVO> queryPaymentsByParam(Map paraM);

    List<PaymentDateVO> queryRentPaymentsByParam(Map paraM);

    VEleContract queryEleContractByBillaccountId(Map paraM);

    VEleContract queryRentContractByBillaccountId(Map paraM);

    List<DatBaseresourceVO> queryDatResourceByBillaccountId(Map paraM);

    List<Date> getPaymentEndDate(Map paraM);

    @Select("SELECT billaccount_id billaccountId,payment_code paymentCode,billamount_startdate billamountStartdate," +
            "billamount_enddate billamountEnddate,total_degree_actual totalDegreeActual,price_avg_notax priceAvgNotax," +
            "bill_amount_actual billAmountActual " +
            "FROM ele_payment WHERE billaccountpaymentdetail_id=#{paymentId}")
    VEleBillaccountPaymentInfo getBillAccountIdByPaymentId(String paymentId);

    @Select("SELECT billaccount_id billaccountId,verification_code verificationCode,billamount_startdate billamountStartdate," +
            "billamount_enddate billamountEnddate,total_degree_actual totalDegreeActual,now_degree_actual nowDegreeActual,price_avg_notax priceAvgNotax," +
            "buy_method buyMethod,billamount_date billamountDate,sum_actual sumActual " +
            "FROM ele_verification WHERE verification_id=#{verificationId}")
    VEleBillaccountVerificateInfo getBillAccountIdByVerificationId(String verificationId);

    @Select("SELECT A.price_type FROM ele_contract A INNER JOIN ele_contractbillaccount B ON A.elecontract_id=B.elecontract_id AND B.relation_state=0 WHERE B.billaccount_id=#{billAccountId} LIMIT 1")
    Integer getPriceType(String billAccountId);

    /**
     * 获取核销详情中的一条总本期读数
     *
     * @param verificationId id
     * @return 结果
     */
    @Select("select total_now_readnum from ele_verificationdetail where verification_id=#{verificationId} limit 1")
    BigDecimal selectTotalNowReadNum(String verificationId);

    List<BillaccountPaymentDataVO> queryBillaccountAllPayment(@Param("billaccountId") String billaccountId);

	VEleContract queryPaymentPeriodAndDate(@Param("paymentId") String paymentId, @Param("businessType") String businessType);
}
