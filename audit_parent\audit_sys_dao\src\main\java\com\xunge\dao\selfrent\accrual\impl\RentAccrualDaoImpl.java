package com.xunge.dao.selfrent.accrual.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.xunge.dao.AbstractBaseDao;
import com.xunge.dao.selfrent.accrual.IRentAccrualDao;
import com.xunge.model.selfrent.accrual.*;
import com.xunge.model.selfrent.contract.PlatformContractVO;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @创建人 LiangCheng
 * @创建时间 2021/8/17 0017
 * @描述：
 */
public class RentAccrualDaoImpl extends AbstractBaseDao implements IRentAccrualDao {

    final String Namespace = "com.xunge.mapping.RentAccrualVOMapper.";
    //租赁平台合同表
    final String PlatformContractNamespace = "com.xunge.mapping.PlatformContractMapper.";

    @Override
    public PageInfo<RentAccrualVO> queryRentAccrualVO(Map<String, Object> map, int pageNumber, int pageSize) {
        PageHelper.startPage(pageNumber, pageSize);
        List<RentAccrualVO> datas = this.getSqlSession().selectList(Namespace + "queryRentAccrualVO", map);
        PageInfo<RentAccrualVO> page = new PageInfo<>(datas);
        return page;
    }

    @Override
    public RentAccrualVO queryRentAccrualById(Map<String, Object> map) {
        return this.getSqlSession().selectOne(Namespace + "queryRentAccrualById", map);
    }

    @Override
    public RentAccrualVO queryRentExpireAccrualById(Map<String, Object> map) {
        return this.getSqlSession().selectOne(Namespace + "queryRentExpireAccrualById", map);
    }

    @Override
    public RentAccrualVO queryRentAccrualByCode(Map<String, Object> map) {
        return this.getSqlSession().selectOne(Namespace + "queryRentAccrualByCode", map);
    }

    @Override
    public RentAccrualVO queryRentExpireAccrualByCode(Map<String, Object> map) {
        return this.getSqlSession().selectOne(Namespace + "queryRentExpireAccrualByCode", map);
    }

    @Override
    public Map<String, Object> queryCostCenter(Map<String, Object> map) {
        return this.getSqlSession().selectOne(Namespace + "queryCostCenter", map);
    }

    @Override
    public void editRentAccrual(RentAccrualVO rentAccrual) {
        this.getSqlSession().update(Namespace + "editRentAccrual", rentAccrual);
    }

    @Override
    public void editRentExpireAccrual(RentAccrualVO rentAccrual) {
        this.getSqlSession().update(Namespace + "editRentExpireAccrual", rentAccrual);
    }

    @Override
    public void rentAccrualSubmitAudit(Map<String, Object> map) {
        this.getSqlSession().update(Namespace + "rentAccrualSubmitAudit", map);
    }

    @Override
    public List<RentAccrualVO> queryRentAccrualList(Map<String, Object> map) {
        return this.getSqlSession().selectList(Namespace + "queryRentAccrualVO", map);
    }

    @Override
    public List<RentAccrualVO> queryRentExpireAccrualList(Map<String, Object> map) {
        return this.getSqlSession().selectList(Namespace + "queryRentExpireAccrualVO", map);
    }

    @Override
    public RentAccrualVO queryRentAccrualContractByContractId(Map<String, Object> map) {
        return this.getSqlSession().selectOne(Namespace + "queryRentAccrualContractByContractId", map);
    }

    @Override
    public RentAccrualVO queryRentExpireAccrualContractByContractId(Map<String, Object> map) {
        return this.getSqlSession().selectOne(Namespace + "queryRentExpireAccrualContractByContractId", map);
    }

    @Override
    public Map<String, Object> queryContractInfoById(Map<String, Object> map) {
        return this.getSqlSession().selectOne(Namespace + "queryContractInfoById", map);
    }

    @Override
    public void editRentAccrualSummaryInfoById(Map<String, Object> map) {
        this.getSqlSession().update(Namespace + "editRentAccrualSummaryInfoById", map);
    }

    @Override
    public void delRentAccrualSummaryInfoById(Map<String, Object> map) {
        this.getSqlSession().update(Namespace + "delRentAccrualSummaryInfoById", map);
    }

    @Override
    public List<RentAccrualVO> querySumRentAccrual(Map<String, Object> map) {
        return this.getSqlSession().selectList(Namespace + "querySumRentAccrual", map);
    }

    @Override
    public List<RentAccrualConfigVO> queryAccruedConfigData(Map<String, Object> map) {
        return this.getSqlSession().selectList(Namespace + "queryAccruedConfigData", map);
    }

    @Override
    public int queryTotalConfigCount(Map<String, Object> map) {
        return this.getSqlSession().selectOne(Namespace + "queryTotalConfigCount", map);
    }

    @Override
    public void updateAccruedUpperFee(RentAccrualConfigVO vo) {
        this.getSqlSession().update(Namespace + "updateAccruedUpperFee", vo);
    }

    @Override
    public int initTotalConfig(List<RentAccrualConfigVO> list) {
        return this.getSqlSession().insert(Namespace + "initTotalConfig", list);
    }

    @Override
    public List<RentAccrualConfigVO> queryCostCenterConfig(Map<String, Object> map) {
        return this.getSqlSession().selectList(Namespace + "queryCostCenterConfig", map);
    }

    @Override
    public RentAccrualConfigVO queryCostCenterConfigByCode(Map<String, Object> map) {
        return this.getSqlSession().selectOne(Namespace + "queryCostCenterConfigByCode", map);
    }

    @Override
    public List<PlatformContractVO> queryPlatformContract(Map<String, Object> paraMap) {
        return this.getSqlSession().selectList(PlatformContractNamespace + "queryPlatformContract", paraMap);
    }

    @Override
    public List<MonthAmountAmVO> queryAccrualMonthAmountAmList(Map<String, Object> paraMap) {
        return this.getSqlSession().selectList(Namespace + "queryAccrualMonthAmountAmList", paraMap);
    }

    @Override
    public List<MonthAmountAmDetailVO> queryAccrualMonthAmountAmDetail(Map<String, Object> paraMap) {
        return this.getSqlSession().selectList(Namespace + "queryAccrualMonthAmountAmDetail", paraMap);
    }

    @Override
    public List<PlatformContractVO> queryMergePlatformDetail(Map<String, Object> paraMap) {
        return this.getSqlSession().selectList(PlatformContractNamespace + "queryMergePlatformDetail", paraMap);
    }

    @Override
    public void deleteRentAccrualById(String accrualId) {
        this.getSqlSession().delete(Namespace + "deleteRentAccrualById", accrualId);
    }

    @Override
    public void deleteRentExpireAccrualById(String accrualId) {
        this.getSqlSession().delete(Namespace + "deleteRentExpireAccrualById", accrualId);
    }

    @Override
    public List<RentAccrualVO> queryBaseAccrualByAccountIds(Integer accrualType, List<String> billAccountIds, String prvId) {
        Map<String, Object> map = new HashMap<>();
        map.put("accrualType", accrualType);
        map.put("billAccountIds", billAccountIds);
        map.put("prvId", prvId);
        return this.getSqlSession().selectList(Namespace + "queryBaseAccrualByAccountIds", map);
    }

    @Override
    public List<String> queryContractNotNeedAccrual(List<String> contractIds) {
        Map<String, Object> map = new HashMap<>();
        map.put("contractIds", contractIds);
        return this.getSqlSession().selectList(Namespace + "queryContractNotNeedAccrual", map);
    }

    @Override
    public List<RentAccrualVO> queryLastAccrualInfoByAccountIds(Integer accrualType, List<String> billAccountIds, String yearMonth) {
        Map<String, Object> map = new HashMap<>();
        map.put("accrualType", accrualType);
        map.put("billAccountIds", billAccountIds);
        map.put("yearMonth", yearMonth);
        return this.getSqlSession().selectList(Namespace + "queryLastAccrualInfoByAccountIds", map);
    }

    @Override
    public List<RentAccrualVO> queryLastPaymentInfoByAccountIds(List<String> billAccountIds) {
        Map<String, Object> map = new HashMap<>();
        map.put("billAccountIds", billAccountIds);
        return this.getSqlSession().selectList(Namespace + "queryLastPaymentInfoByAccountIds", map);
    }

    @Override
    public AccrualConfigVo queryAccrualConfig(Integer accrualType, String prvId) {
        Map<String, Object> map = new HashMap<>();
        map.put("accrualType", accrualType);
        map.put("prvId", prvId);
        return this.getSqlSession().selectOne(Namespace + "queryAccrualConfig", map);
    }

    @Override
    public int addAutoAuditingRecord(AccrualAuditingVo vo) {
        return this.getSqlSession().insert(Namespace + "addAutoAuditingRecord", vo);
    }

    @Override
    public List<AccrualAuditingVo> queryNoNeedCheckRecord(String accrualId) {
        return this.getSqlSession().selectList(Namespace + "queryNoNeedCheckRecord", accrualId);
    }

    @Override
    public List<RentAccrualCheckDto> queryRentCheckDetail(String billaccountId, Integer accrualType) {
        Map<String, Object> map = new HashMap<>();
        map.put("accrualType", accrualType);
        map.put("billaccountId", billaccountId);
        return this.getSqlSession().selectList(Namespace + "queryRentCheckDetail", map);
    }

    @Override
    public PageInfo<RentAccrualVO> queryAllRentAccrualVO(Map<String, Object> map, int pageNumber, int pageSize) {
        PageHelper.startPage(pageNumber, pageSize);
        List<RentAccrualVO> data = this.getSqlSession().selectList(Namespace + "queryAllRentAccrualVO", map);
        return new PageInfo<>(data);
    }

    @Override
    public PageInfo<RentAccrualVO> queryRentExpireAccrualVO(Map<String, Object> map, int pageNumber, int pageSize) {
        PageHelper.startPage(pageNumber, pageSize);
        List<RentAccrualVO> data = this.getSqlSession().selectList(Namespace + "queryRentExpireAccrualVO", map);
        return new PageInfo<>(data);
    }

    @Override
    public void editRentExpireAccrualSummaryInfoById(Map<String, Object> map) {
        this.getSqlSession().update(Namespace + "editRentExpireAccrualSummaryInfoById", map);
    }

    @Override
    public List<RentAccrualVO> queryAllRentAccrualList(Map<String, Object> map) {
        return this.getSqlSession().selectList(Namespace + "queryAllRentAccrualVO", map);
    }

    @Override
    public void delRentExpireAccrualSummaryInfoById(Map<String, Object> map) {
        this.getSqlSession().update(Namespace + "delRentExpireAccrualSummaryInfoById", map);
    }

    @Override
    public List<RentAccrualVO> queryRentAccrualListAll(Map<String, Object> map) {
        return this.getSqlSession().selectList(Namespace + "queryRentAccrualListAll", map);
    }
}
