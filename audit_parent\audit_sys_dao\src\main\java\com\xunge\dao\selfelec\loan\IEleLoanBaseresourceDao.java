package com.xunge.dao.selfelec.loan;

import com.xunge.dao.core.CrudDao;
import com.xunge.model.selfelec.loan.EleLoanBaseResourceVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 关联资源表DAO接口
 *
 * <AUTHOR>
 * @version 2018-03-12
 */
public interface IEleLoanBaseresourceDao extends CrudDao<EleLoanBaseResourceVO> {

    public List<EleLoanBaseResourceVO> getByLoanId(EleLoanBaseResourceVO eleLoanConfig);

    /**
     * @description 批量新增
     * <AUTHOR>
     * @date 创建时间：2018年03月05日
     */
    public int insertLondBaseresourceMsg(Map<String, Object> map);

    public void deleteList(@Param("eleLoanBaseResourceList") List<EleLoanBaseResourceVO> eleLoanBaseResourceList);

    /**
     * 根据loanIds删除
     * @param eleLoanBaseResourceList
     */
    public void deleteListByLoanId(@Param("eleLoanBaseResourceList") List<EleLoanBaseResourceVO> eleLoanBaseResourceList);

    /**
     * 根据条件查询
     *
     * @param paraMap
     * @return
     */
    public List<EleLoanBaseResourceVO> findEleLoanBaseResourceByCondition(Map<String, Object> paraMap);

    public void deleteResource(EleLoanBaseResourceVO eleLoanBaseResourceVO);
}