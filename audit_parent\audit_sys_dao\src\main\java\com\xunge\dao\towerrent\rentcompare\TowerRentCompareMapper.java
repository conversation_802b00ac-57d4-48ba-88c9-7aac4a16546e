package com.xunge.dao.towerrent.rentcompare;

import com.xunge.model.towerrent.rentcompare.TwrRentCompareReport;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Description:
 * @Author: dxd
 * @Date: 2022/9/13
 */
public interface TowerRentCompareMapper {

    /**
     * 查询起租对比分析表
     * @param prvId 省份ID
     * @param yearMonth 账期
     * @param dataType 铁塔类型
     * @param querySign 查询标识
     * @return
     */
    List<TwrRentCompareReport> queryRentCompare(@Param("prvId") String prvId,
                                                @Param("yearMonth") String yearMonth,
                                                @Param("dataType") Integer dataType,
                                                @Param("querySign") Integer querySign);
}
