package com.xunge.dao.time;

import com.xunge.model.activity.Act;
import com.xunge.model.activity.SuperAct;

import java.util.Date;
import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

public interface TimeOptimizeMapper {

    void insertOperateTime(Map<String, Object> params);

    List<Act> queryMyOrderList(Map<String, Object> map);

    int queryMyOrderCount(Map<String, Object> map);


    List<Act> queryjoinOrderCount(Map<String, Object> map);

    List<Act> querySource(Map<String, Object> map);

    List<Act> queryElecontractG(Map<String, Object> map);

    List<Act> queryElecontractH(Map<String, Object> map);

    List<Act> queryRentcontractG(Map<String, Object> map);

    List<Act> queryRentcontractH(Map<String, Object> map);

    List<Act> queryElebillacount(Map<String, Object> map);

    List<Act> queryElebillacountS(Map<String, Object> map);

    List<Act> queryRentbillacount(Map<String, Object> map);

    List<Act> queryElepayment(Map<String, Object> map);

    List<Act> queryElepaymentS(Map<String, Object> map);

    List<Act> queryRentpayment(Map<String, Object> map);

    List<Act> queryLoan(Map<String, Object> map);

    List<Act> queryVerification(Map<String, Object> map);

    List<Act> queryRentbillacountS(Map<String, Object> map);

    List<Act> queryRentpaymentS(Map<String, Object> map);

    List<Act> queryRentbillacountT(Map<String, Object> map);

    List<Act> queryRentpaymentT(Map<String, Object> map);

    List<Act> queryRentbillacountTS(Map<String, Object> map);

    List<Act> queryRentpaymentTS(Map<String, Object> map);

    List<Act> queryTelePayment(Map<String, Object> map);

    List<Act> queryTeleBillaccount(Map<String, Object> map);

    List<Act> queryTeleContractG(Map<String, Object> map);


    List<Act> getbaseresource(Map<String, Object> map);

    List<Act> getSite(Map<String, Object> map);

    List<Act> getBaseantenna(Map<String, Object> map);

    List<Act> getTower(Map<String, Object> map);

    List<Act> getEleContract(Map<String, Object> map);

    List<Act> getRentContract(Map<String, Object> map);

    List<Act> getEleBillacount(Map<String, Object> map);

    List<Act> getRentBillacount(Map<String, Object> map);

    List<Act> getElePayment(Map<String, Object> map);

    List<Act> getRentPayment(Map<String, Object> map);

    List<Act> getLoan(Map<String, Object> map);

    List<Act> getVerification(Map<String, Object> map);

    List<Act> getTelePayment(Map<String, Object> map);

    List<Act> getTeleContract(Map<String, Object> map);

    List<Act> getTeleBillacount(Map<String, Object> map);


    List<SuperAct> querySuperiorByInstId(String procInstId);

    SuperAct queryProcinstIdByBusinessKey(String procInstId);

    List<SuperAct> querySuperior(String procInstId);

    int querySourceCount(Map<String, Object> map); //查询资源数量

    int queryElecontractCount(Map<String, Object> map);//查询电费合同数量

    int queryRentcontractCount(Map<String, Object> map);//查询租费合同数量

    int queryElebillacountCount(Map<String, Object> map);//电费报账点数量

    int queryRentbillacountCount(Map<String, Object> map);//租费报账点数量

    int queryElepaymentCount(Map<String, Object> map);

    int queryRentpaymentCount(Map<String, Object> map);

    int queryLoanCount(Map<String, Object> map);

    int queryVerificationCount(Map<String, Object> map);

    int queryDsElecontractCount(Map<String, Object> map);//查询电费合同数量

    int queryDsElebillacountCount(Map<String, Object> map);//电费报账点数量

    int queryDsElepaymentCount(Map<String, Object> map);

    List<Act> queryInspection(Map<String, Object> map);

    List<Act> queryEleAccrual(Map<String, Object> map);

    List<Act> queryRentAccrual(Map<String, Object> map);
    List<Act> queryRentAccrualT(Map<String, Object> map);

    List<Act> getEleAccrual(Map<String, Object> map);

    List<Act> getRentAccrual(Map<String, Object> map);

    List<Act> getInspection(Map<String, Object> map);

	List<Act> querySysUser(Map<String, Object> map);

	List<Act> queryEleRegionPrice(Map<String, Object> map);
}
