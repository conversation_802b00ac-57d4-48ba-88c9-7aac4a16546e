package com.xunge.dao.twrrent.settlement.impl;

import com.xunge.core.page.Page;
import com.xunge.dao.AbstractBaseDao;
import com.xunge.dao.twrrent.settlement.ITowerBillbalanceDao;
import com.xunge.filter.PageInterceptor;
import com.xunge.model.towerrent.settlement.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2017年7月6日 下午2:07:49
 */
public class TowerBillbalanceDaoImpl extends AbstractBaseDao implements ITowerBillbalanceDao {

    final String TowerBillbalanceNamespace = "com.xunge.mapping.TowerBillbalanceVOMapper.";
    final String TowerMobilerBillbalanceConfirmNamespace = "com.xunge.dao.TowerMobilerBillbalanceConfirmVOMapper.";
    final String NamespaceBackup = "com.xunge.dao.TwrRentinformationBackupMapper.";

    @Override
    public int deleteByPrimaryKey(String towerbillbalanceId) {
        // TODO Auto-generated method stub
        return this.getSqlSession().update(TowerBillbalanceNamespace + "deleteByPrimaryKey", towerbillbalanceId);
    }

    @Override
    public int insertTowerBillbalance(TowerBillbalanceVO entity) {
        // TODO Auto-generated method stub
        return this.getSqlSession().insert(TowerBillbalanceNamespace + "insertTowerBillbalance", entity);
    }

    @Override
    public int insertSelective(TowerBillbalanceVO entity) {
        // TODO Auto-generated method stub
        return this.getSqlSession().insert(TowerBillbalanceNamespace + "insertSelective", entity);
    }

    @Override
    public TowerBillbalanceVO selectByPrimaryKey(String towerbillbalanceId) {
        // TODO Auto-generated method stub
        return this.getSqlSession().selectOne(TowerBillbalanceNamespace + "selectByPrimaryKey", towerbillbalanceId);
    }

    @Override
    public int updateByPrimaryKeySelective(TowerBillbalanceVO entity) {
        // TODO Auto-generated method stub
        return this.getSqlSession().update(TowerBillbalanceNamespace + "updateByPrimaryKeySelective", entity);
    }

    @Override
    public int updateByPrimaryKey(TowerBillbalanceVO entity) {
        // TODO Auto-generated method stub
        return this.getSqlSession().update(TowerBillbalanceNamespace + "updateByPrimaryKey", entity);
    }

    @SuppressWarnings("unchecked")
    @Override
    public Page<TowerBillbalanceVO> queryPageTowerBillbalance(Map<String, Object> map) {
        // TODO Auto-generated method stub
        PageInterceptor.startPage(Integer.parseInt(map.get("pageNumber").toString()), Integer.parseInt(map.get("pageSize").toString()));
        this.getSqlSession().selectList(TowerBillbalanceNamespace + "queryPageTowerBillbalance", map);
        return PageInterceptor.endPage();
    }

    @Override
    public Page<TowerBillbalanceVO> queryPageTowerBillbalanceByState(Map<String, Object> map) {
        PageInterceptor.startPage(Integer.parseInt(map.get("pageNumber").toString()), Integer.parseInt(map.get("pageSize").toString()));
        this.getSqlSession().selectList(TowerBillbalanceNamespace + "queryPageTowerBillbalanceByState", map);
        return PageInterceptor.endPage();
    }

    public List<TowerBillbalanceVO> queryTowerBillbalance(Map<String, Object> map) {
        // TODO Auto-generated method stub
        return this.getSqlSession().selectList(TowerBillbalanceNamespace + "queryPageTowerBillbalance", map);
    }

    public List<TowerBillbalanceVO> queryTowerBillbalanceByState(Map<String, Object> map) {
        // TODO Auto-generated method stub
        return this.getSqlSession().selectList(TowerBillbalanceNamespace + "queryPageTowerBillbalanceByState", map);
    }


    @Override
    public List<TowerAndMobileBillVO> queryAccountsummaryBillbalance(Map<String, Object> map) {
        // TODO Auto-generated method stub
        return this.getSqlSession().selectList(TowerBillbalanceNamespace + "queryAccountsummaryBillbalance", map);
    }

    @Override
    public List<TowerAndMobileBillVO> queryAccountsummaryBillbalanceConfig(Map<String, Object> map) {
        // TODO Auto-generated method stub
        return this.getSqlSession().selectList(TowerBillbalanceNamespace + "queryAccountsummaryBillbalanceConfig", map);
    }

    @Override
    public List<TowerBillbalanceVO> queryTowerBillbalanceByPrvId(String prvId) {
        return this.getSqlSession().selectList(TowerBillbalanceNamespace + "getTowerBillByPrvId", prvId);
    }

    @Override
    public int insertBatchSelective(List<?> list) {
        // TODO Auto-generated method stub
        return this.getSqlSession().insert(TowerBillbalanceNamespace + "insertBatchSelective", list);
    }

    @Override
    public int updateBatchByPrimaryKeySelective(TowerBillbalanceVO vo) {
        // TODO Auto-generated method stub
        return this.getSqlSession().update(TowerBillbalanceNamespace + "updateBatchByPrimaryKey", vo);
    }

    @Override
    public int updateConfirmByPrimaryKey(TowerConfirmBillbalanceVO vo) {
        // TODO Auto-generated method stub
        return this.getSqlSession().update(TowerBillbalanceNamespace + "updateConfirmByPrimaryKey", vo);
    }

    @SuppressWarnings("unchecked")
    @Override
    public Page<TowerBillbalanceVO> queryPageMobileBillbalance(Map<String, Object> paramMap) {
        PageInterceptor.startPage(Integer.parseInt(paramMap.get("pageNumber").toString()), Integer.parseInt(paramMap.get("pageSize").toString()));
        this.getSqlSession().selectList(TowerBillbalanceNamespace + "queryPageMobileBillbalance", paramMap);
        return PageInterceptor.endPage();
    }

    @SuppressWarnings("unchecked")
    @Override
    public Page<TowerBillbalanceVO> queryPageMobileBillbalanceConfig(Map<String, Object> paramMap) {
        PageInterceptor.startPage(Integer.parseInt(paramMap.get("pageNumber").toString()), Integer.parseInt(paramMap.get("pageSize").toString()));
        this.getSqlSession().selectList(TowerBillbalanceNamespace + "queryPageMobileBillbalanceConfig", paramMap);
        return PageInterceptor.endPage();
    }

    @Override
    public List<TowerBillbalanceVO> queryTowerOrMobileBillbalance(Map<String, Object> paramMap) {
        return this.getSqlSession().selectList(TowerBillbalanceNamespace + "queryPageMobileBillbalance", paramMap);
    }

    @Override
    public int insertBatchMobileBill(List<TowerBillbalanceVO> listVo) {
        return this.getSqlSession().insert(TowerBillbalanceNamespace + "insertBatchMobileBill", listVo);
    }

    @Override
    public int insertBatchMobileBillConfig(List<TowerBillbalanceVO> listVo) {
        return this.getSqlSession().insert(TowerBillbalanceNamespace + "insertBatchMobileBillConfig", listVo);
    }

    @Override
    public int updateBatchMobileBill(TowerBillbalanceVO vo) {
        return this.getSqlSession().update(TowerBillbalanceNamespace + "updateBatchMobileBill", vo);
    }

    @Override
    public int updateBatchMobileBillConfig(TowerBillbalanceVO vo) {
        return this.getSqlSession().update(TowerBillbalanceNamespace + "updateBatchMobileBillConfig", vo);
    }

    @Override
    public List<TowerBillbalanceVO> queryMobileBill(Map<String, Object> paramMap) {
        return this.getSqlSession().selectList(TowerBillbalanceNamespace + "queryMobielBill", paramMap);
    }

    @Override
    public List<TowerBillbalanceVO> queryMobileBillConfig(Map<String, Object> paramMap) {
        return this.getSqlSession().selectList(TowerBillbalanceNamespace + "queryMobielBillConfig", paramMap);
    }

    @Override
    public List<String> selectAccountPeroid(Map<String, Object> paramMap) {
        return this.getSqlSession().selectList(TowerBillbalanceNamespace + "selectAccountPeroid", paramMap);
    }

    @Override
    public String queryBalance(Map<String, Object> paraMap) {
        return this.getSqlSession().selectOne(TowerBillbalanceNamespace + "queryBalance", paraMap);
    }

    @Override
    public List<TowerBillbalanceVO> queryParameter(Map<String, Object> paraMap) {
        return this.getSqlSession().selectList(TowerBillbalanceNamespace + "queryParameter", paraMap);
    }

    //批量修改的账单导出_起租表
    @Override
    public List<TowerBillbalanceVO> exportModify(Map<String, Object> paraMap) {
        return this.getSqlSession().selectList(TowerBillbalanceNamespace + "exportModify", paraMap);

    }

    //批量修改的账单导出_新综资
    @Override
    public List<TowerBillbalanceVO> exportModifyConfig(Map<String, Object> paraMap) {
        return this.getSqlSession().selectList(TowerBillbalanceNamespace + "exportModifyConfig", paraMap);
    }

    @Override
    public int updateBillSetSumcode(Map<String, Object> map) {
        return this.getSqlSession().update(TowerBillbalanceNamespace + "updateBillSetSumcode", map);
    }

    @Override
    public int updateBillSumcodeToNull(Map<String, Object> map) {
        return this.getSqlSession().update(TowerBillbalanceNamespace + "updateBillSumcodeToNull", map);
    }

    @Override
    public List<TowerBillbalanceVO> selectById(Map<String, Object> map) {
        return this.getSqlSession().selectList(TowerBillbalanceNamespace + "selectById", map);
    }

    @Override
    public List<TowerBillbalanceVO> selectByIdConfig(Map<String, Object> map) {
        return this.getSqlSession().selectList(TowerBillbalanceNamespace + "selectByIdConfig", map);
    }

    @Override
    public int insertBillbalanceChanges(List<MobileBillbalanceChangeVO> tbList) {
        return this.getSqlSession().insert(TowerBillbalanceNamespace + "insertBillbalanceChanges", tbList);
    }

    @Override
    public int updateMobileBill(TowerBillbalanceVO vo) {
        return this.getSqlSession().update(TowerBillbalanceNamespace + "updateMobileBill", vo);
    }

    @Override
    public int updateMobileBillConfig(TowerBillbalanceVO vo) {
        return this.getSqlSession().update(TowerBillbalanceNamespace + "updateMobileBillConfig", vo);
    }

    @Override
    public List<MobileBillbalanceChangeVO> queryChangeVo(Map<String, Object> map) {
        return this.getSqlSession().selectList(TowerMobilerBillbalanceConfirmNamespace + "queryChangeVo", map);
    }

    @Override
    public int deleteChange(Map<String, Object> map) {
        return this.getSqlSession().delete(TowerMobilerBillbalanceConfirmNamespace + "deleteChange", map);
    }

    @Override
    public Page<TowerBillBalanceChargeBackVO> queryPageChargeBackList(Map<String, Object> paramMap) {
        PageInterceptor.startPage(Integer.parseInt(paramMap.get("pageNumber").toString()), Integer.parseInt(paramMap.get("pageSize").toString()));
        this.getSqlSession().selectList(TowerBillbalanceNamespace + "queryPageChargeBackList", paramMap);
        return PageInterceptor.endPage();
    }

    @Override
    public List<TowerBillBalanceChargeBackVO> queryChargeBackList(Map<String, Object> paramMap) {
        return this.getSqlSession().selectList(TowerBillbalanceNamespace + "queryPageChargeBackList", paramMap);
    }

    @Override
    public int addChargeBack(TowerBillBalanceChargeBackVO chargeBackVO) {
        return this.getSqlSession().insert(TowerBillbalanceNamespace + "addChargeBack", chargeBackVO);
    }

    @Override
    public int deleteChargeBack(Map<String, Object> paramMap) {
        return this.getSqlSession().delete(TowerBillbalanceNamespace + "deleteChargeBack", paramMap);
    }

    @Override
    public int updateChargeBackState(Map<String, Object> paramMap) {
        return this.getSqlSession().update(TowerBillbalanceNamespace + "updateChargeBackState", paramMap);
    }

    @Override
    public List<TowerChargeBackVo> exportBillBalanceToChargeBack(Map<String, Object> paramMap) {
        return this.getSqlSession().selectList(TowerBillbalanceNamespace + "exportBillBalanceToChargeBack", paramMap);
    }

    @Override
    public List<TowerChargeBackToExcelVo> exportTowerChargeBackToExcel(Map<String, Object> paramMap) {
        return this.getSqlSession().selectList(TowerBillbalanceNamespace + "exportTowerChargeBackToExcel", paramMap);
    }

    @Override
    public List<TowerBillbalanceVO> queryTowerOrMobileBillbalanceConfig(Map<String, Object> paramMap) {
        return this.getSqlSession().selectList(TowerBillbalanceNamespace + "queryPageMobileBillbalanceConfig", paramMap);
    }

    @Override
    public List<String> queryHistoryDeleteIds(Map<String, Object> map) {
        return this.getSqlSession().selectList(TowerBillbalanceNamespace + "queryHistoryDeleteIds", map);

    }

    @Override
    public List<String> queryDeleteIds(Map<String, Object> map) {
        return this.getSqlSession().selectList(TowerBillbalanceNamespace + "queryDeleteIds", map);

    }

    @Override
    public int deleteByMobileId(List<String> list) {
        return this.getSqlSession().delete(TowerBillbalanceNamespace + "deleteByMobileId", list);
    }

    @Override
    public List<String> queryHistoryDeleteIdsConfig(Map<String, Object> map) {
        return this.getSqlSession().selectList(TowerBillbalanceNamespace + "queryHistoryDeleteIdsConfig", map);

    }

    @Override
    public List<String> queryDeleteIdsConfig(Map<String, Object> map) {
        return this.getSqlSession().selectList(TowerBillbalanceNamespace + "queryDeleteIdsConfig", map);

    }

    @Override
    public int deleteByMobileIdConfig(List<String> list) {
        return this.getSqlSession().delete(TowerBillbalanceNamespace + "deleteByMobileIdConfig", list);
    }

    @Override
    public List<TowerBillbalanceModifyVO> exportTowerBillbalanceModify(Map<String, Object> paraMap) {
        return this.getSqlSession().selectList(TowerBillbalanceNamespace + "exportTowerBillbalanceModify", paraMap);

    }

    @Override
    public List<TowerBillbalanceModifyVO> exportTowerBillbalanceModifyConfig(Map<String, Object> paraMap) {
        return this.getSqlSession().selectList(TowerBillbalanceNamespace + "exportTowerBillbalanceModifyConfig", paraMap);

    }

    @Override
    public void updateTowerRentAllFeeTaxToNull(Map<String, Object> params) {
        this.getSqlSession().update(TowerBillbalanceNamespace + "updateTowerRentAllFeeTaxToNull", params);
    }

    @Override
    public List<TowerBillSummaryVO> queryBillBalanceSummaryList(Map<String, Object> paraMap) {
        return this.getSqlSession().selectList(TowerBillbalanceNamespace + "selectBillBalanceSummaryList", paraMap);

    }

    @Override
    public List<TwrOilConfig> getOilConfigList() {
        return this.getSqlSession().selectList(TowerBillbalanceNamespace + "getOilConfigList");
    }
}
