package com.xunge.comm.basedata;

public enum BaseSiteTypeEnum {

    CORE_SITE("核心站点", 1), USER_SITE("用户站点", 2), ACCESS_SITE("接入站点", 3), CONVERGE_SITE("汇聚站点", 4), BACKBONE_SITE("骨干站点",
            5), OTHER_SITES("其他站点", 6);

    private String desc;
    private Integer value;

    private BaseSiteTypeEnum(String desc, Integer value) {
        this.desc = desc;
        this.value = value;
    }

    public String getDesc() {
        return desc;
    }

    public static BaseSiteTypeEnum setDesc(String desc) {
        switch (desc) {
            case "核心站点":
                return CORE_SITE;
            case "用户站点":
                return USER_SITE;
            case "接入站点":
                return ACCESS_SITE;
            case "汇聚站点":
                return CONVERGE_SITE;
            case "骨干站点":
                return BACKBONE_SITE;
            case "其他站点":
                return OTHER_SITES;
            default:
                return OTHER_SITES;
        }
    }

    public Integer getValue() {
        return value;
    }

    public static BaseSiteTypeEnum setValue(Integer value) {
        switch (value) {
            case 1:
                return CORE_SITE;
            case 2:
                return USER_SITE;
            case 3:
                return ACCESS_SITE;
            case 4:
                return CONVERGE_SITE;
            case 5:
                return BACKBONE_SITE;
            case 6:
                return OTHER_SITES;
            default:
                return OTHER_SITES;
        }
    }
}
