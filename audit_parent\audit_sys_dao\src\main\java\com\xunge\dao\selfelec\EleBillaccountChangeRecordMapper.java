package com.xunge.dao.selfelec;


import com.xunge.model.selfelec.EleBillaccountUpdateRecordDto;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 针对表【ele_billaccount_change_record】的数据库操作Mapper
 * @createDate 2024-03-06 15:05:32
 * @Entity generator.domain.EleBillaccountChangeRecord
 */
public interface EleBillaccountChangeRecordMapper {

    int deleteByPrimaryKey(Long id);

    int insert(EleBillaccountUpdateRecordDto record);

    int insertSelective(EleBillaccountUpdateRecordDto record);

    int insertSelectiveForeach(List<EleBillaccountUpdateRecordDto> record);

    EleBillaccountUpdateRecordDto selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(EleBillaccountUpdateRecordDto record);

    int updateByPrimaryKey(EleBillaccountUpdateRecordDto record);

    List<EleBillaccountUpdateRecordDto> queryEleBillAccountApprovedChangeRecord(Map<String, Object> map);
}
