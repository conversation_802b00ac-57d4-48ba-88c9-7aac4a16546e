package com.xunge.comm.elec;

public enum OverProofEnum {
    OVERPROOF_2(2, "电损占比超标"),
    OVERPROOF_4(4, "其他费用超标"),
    OVERPROOF_8(8, "实际分摊比例环比增大超限"),
    OVERPROOF_16(16, "实际分摊比例环比减小超限"),
    OVERPROOF_32(32, "用电成本超限"),
    OVERPROOF_64(64, "共享数量与分摊比例不匹配"),
    OVERPROOF_128(128, "铁塔站点退租缴纳电费"),
    OVERPROOF_256(256, "换了电表"),
    OVERPROOF_512(512, "系统计算金额与实际值差异超限"),
    OVERPROOF_1024(1024, "实际报账金额超限"),
    OVERPROOF_2048(2048, "历史缴费管控"),
    OVERPROOF_4096(4096, "入网前或退网后或工程状态资源缴费"),
    OVERPROOF_8192(8192, "生产用电拆分异常"),
    OVERPROOF_16384(16384, "缴费周期不连续"),
    OVERPROOF_32768(32768, "电表读数不连续"),
    OVERPROOF_65536(65536, "首次报账电表读数不为0"),
    OVERPROOF_131072(131072, "实际电量超出AI预测电量"),
    OVERPROOF_262144(262144, "缴费期始早于电表安装日期");

    private final int code;
    private final String text;

    OverProofEnum(int code, String text) {
        this.code = code;
        this.text = text;
    }

    public int getCode() {
        return code;
    }

    public String getText() {
        return text;
    }
}
