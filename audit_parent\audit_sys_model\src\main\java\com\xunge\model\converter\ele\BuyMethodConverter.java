package com.xunge.model.converter.ele;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;

//购电方式 "后付费_0", "预付费_1", "IC卡_2","先款后票_3", "_null"
public class BuyMethodConverter implements Converter<Integer> {
    @Override
    public Integer convertToJavaData(ReadCellData<?> cellData, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        String value = cellData.getStringValue();
        if (value.equals("后付费")){
            return 0;
        }else if (value.equals("预付费")){
            return 1;
        }else if (value.equals("IC卡")){
            return 2;
        }else if (value.equals("先款后票")){
            return 3;
        }
        return null;
    }

    @Override
    public WriteCellData<?> convertToExcelData(Integer value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        if (value == null){
            return new WriteCellData<>("");
        }else if (value == 0){
            return new WriteCellData<>("后付费");
        }else if (value == 1){
            return new WriteCellData<>("预付费");
        }else if (value == 2){
            return new WriteCellData<>("IC卡");
        }else if (value == 3){
            return new WriteCellData<>("先款后票");
        }
        return new  WriteCellData<>("");
    }
}
