package com.xunge.dao.home;

import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface PrvIndexReportMapper {

    //   省页第一面头部
    Map<String, Object> getYearCountPrv(Map<String, Object> map);

    Map<String, Object> getYearCountPrvTx(Map<String, Object> map);

    //   省页第一面头部
    Map<String, Object> getMonCountPrv(Map<String, Object> map);

    Map<String, Object> getMonCountPrvTx(Map<String, Object> map);

    //   省页第一面地图
    List<Map<String, Object>> getMapDateYearPrv(Map<String, Object> map);

    List<Map<String, Object>> getMapDateYearPrvTx(Map<String, Object> map);

    //   省页第一面地图
    List<Map<String, Object>> getMapDateMonPrv(Map<String, Object> map);

    List<Map<String, Object>> getMapDateMonPrvTx(Map<String, Object> map);

    //省首页电费柱状图
    List<Map<String, Object>> getEleMonthPrv(@Param("year") int year, @Param("month") int month, @Param("prvId") String prvId, @Param("Municipality") boolean Municipality);

    //省首页租费柱状图
    List<Map<String, Object>> getRentMonthPrv(@Param("year") int year, @Param("month") int month, @Param("prvId") String prvId, @Param("Municipality") boolean Municipality);

    //省首页铁塔柱状图
    List<Map<String, Object>> getTowerMonthPrv(@Param("year") int year, @Param("month") int month, @Param("prvId") String prvId, @Param("Municipality") boolean Municipality);

    List<Map<String, Object>> getEleReportMon(@Param("year") int year, @Param("month") int month, @Param("prvId") String prvId);

    Map<String, Object> getEleReportMonSum(@Param("year") int year, @Param("month") int month, @Param("prvId") String prvId);

    List<Map<String, Object>> getRentReportMon(@Param("year") int year, @Param("month") int month, @Param("prvId") String prvId);

    Map<String, Object> getRentReportMonSum(@Param("year") int year, @Param("month") int month, @Param("prvId") String prvId);

    List<Map<String, Object>> getTowerReportMon(@Param("year") int year, @Param("month") int month, @Param("prvId") String prvId);

    Map<String, Object> getTowerReportMonSum(@Param("year") int year, @Param("month") int month, @Param("prvId") String prvId);


    Map<String, Object> getShowDate(@Param("type")Integer type);
}
