package com.xunge.dao.selfelec.eleverificate.impl;

import com.xunge.dao.AbstractBaseDao;
import com.xunge.dao.selfelec.eleverificate.IEleVerificateBenchmarkDao;
import com.xunge.model.selfelec.eleverificate.EleVerificateBenchmark;
import com.xunge.model.selfelec.verification.ElecVerificationBenchmarkInfo;

import java.util.List;
import java.util.Map;

public class EleVerificateBenchmarkDaoImpl extends AbstractBaseDao implements IEleVerificateBenchmarkDao {

    final String namespace = "com.xunge.dao.selfelec.EleVerificateBenchmarkMapper.";

    @Override
    public List<EleVerificateBenchmark> queryAllByForginKey(Map<String, Object> paramMap) {
        List<EleVerificateBenchmark> elePayBenchMarkList = this.getSqlSession().selectList(namespace + "queryAllByForginKey", paramMap);
        return elePayBenchMarkList;
    }

    @Override
    public int insertBenchmarkInfo(List<EleVerificateBenchmark> paramMap) {
        return this.getSqlSession().insert(namespace + "insertBenchmarkInfo", paramMap);
    }

    @Override
    public int delVerificateBenchmark(String verificationId) {
        return this.getSqlSession().delete(namespace + "delVerificateBenchmark", verificationId);

    }

    @Override
    public List<ElecVerificationBenchmarkInfo> getRecorBenchmarkData() {
        List<ElecVerificationBenchmarkInfo> elecVerificationBenchmarkInfoList = this.getSqlSession().selectList(namespace + "getRecorBenchmarkData");
        return elecVerificationBenchmarkInfoList;
    }

}
