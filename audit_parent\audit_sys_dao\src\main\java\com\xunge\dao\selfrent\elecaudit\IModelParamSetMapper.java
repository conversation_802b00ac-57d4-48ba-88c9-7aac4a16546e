package com.xunge.dao.selfrent.elecaudit;

import com.xunge.model.selfrent.vo.RentBenchmarkParaConfigVO;

import java.util.List;
import java.util.Map;

public interface IModelParamSetMapper {
    Map<String, Object> selectELeConfig();

    /**
     * 查询每省配置的标杆配置信息
     *
     * @param paramMap
     * @return
     */
    public List<RentBenchmarkParaConfigVO> queryBenchmarkParaConfig(Map<String, Object> paramMap);

    public List<RentBenchmarkParaConfigVO> queryBenchmarkParaConfigType(Map<String, Object> paramMap);

    /**
     * 新增每省配置的标杆信息
     *
     * @param ids
     * @param prvId
     * @return
     */
    public int insertBenchmarkParaConfig(RentBenchmarkParaConfigVO eleBenchmarkParaConfigVO);

    /**
     * 修改每省配置的标杆信息
     *
     * @param ids
     * @param prvId
     * @return
     */
    public int updateBenchmarkParaConfig(RentBenchmarkParaConfigVO eleBenchmarkParaConfigVO);

    int deleteAll();

    int insert(Map<String, Object> map);

    int insertLog(Map<String, Object> map);

    List<Map<String, Object>> selectLog();
}
