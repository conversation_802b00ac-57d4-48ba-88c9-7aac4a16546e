package com.xunge.model.basedata.colletion;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/3/29 16:27
 */
@Getter
@Setter
@ToString
public class TaskInfo implements Serializable {
    private static final long serialVersionUID = 7627019354387482272L;
    /**
     * ele_payment:业务id
     */
    private String businessKey;
    /**
     * 业务id
     */
    private String businessId;
    /**
     * 审核人名称
     */
    private String userName;
    /**
     * 审核用户类型 候选人还是group
     */
    private String type;
    /**
     * 候选组id
     */
    private String groupId;
    /**
     * 审核开始时间
     */
    private Date createTime;

    /**
     * 审核节点
     */
    private String taskName;
}
