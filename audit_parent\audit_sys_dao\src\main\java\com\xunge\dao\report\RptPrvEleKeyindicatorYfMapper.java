package com.xunge.dao.report;

import com.xunge.model.report.RptPrvEleKeyindicatorYf;
import com.xunge.model.report.RptPrvEleKeyindicatorYfExample;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Component
public interface RptPrvEleKeyindicatorYfMapper {

    public List<RptPrvEleKeyindicatorYf> selectByExample(RptPrvEleKeyindicatorYfExample example);

    public List<RptPrvEleKeyindicatorYf> selectProvinceLevel(Map<String, Object> map);

    public List<RptPrvEleKeyindicatorYf> selectCityLevel(Map<String, Object> map);

    public List<RptPrvEleKeyindicatorYf> selectDistrictLevel(Map<String, Object> map);

}