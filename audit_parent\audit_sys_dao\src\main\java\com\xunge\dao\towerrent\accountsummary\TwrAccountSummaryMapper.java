package com.xunge.dao.towerrent.accountsummary;

import com.xunge.model.basedata.DatAttachment;
import com.xunge.model.towerrent.accountsummary.TowerAccountSummaryAuditMuniVo;
import com.xunge.model.towerrent.accountsummary.TowerAccountSummaryAuditVo;
import com.xunge.model.towerrent.accountsummary.TowerAccountSummaryMuniVo;
import com.xunge.model.towerrent.accountsummary.TowerAccountSummaryVo;
import com.xunge.model.towerrent.accountsummary.query.TwrAccountSummaryQueryDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @ClassName: TwrAccountSummaryMapper
 * @Description: 铁塔服务费报账汇总查询Mapper
 * <AUTHOR>
 * @Date: 2023/11/20 020 17:06
 * @Version V1.0.0
 * @Since 1.8
 */
public interface TwrAccountSummaryMapper {
    /**
     * 根据铁塔服务费报账点汇总查询DTO，查询铁塔服务费费用汇总信息
     * @param twrAccountSummaryQueryDto 铁塔服务费报账点汇总查询DTO
     * @return List<TowerAccountSummaryVo> 铁塔服务费费用汇总对象集合
     */
    List<TowerAccountSummaryVo> queryTwrAccountSummaryList(@Param("dto") TwrAccountSummaryQueryDto twrAccountSummaryQueryDto);

    /**
     * 根据铁塔服务费报账点汇总查询DTO，查询铁塔服务费费用审核信息
     * @param twrAccountSummaryQueryDto 铁塔服务费报账点汇总查询DTO
     * @return List<TowerAccountSummaryAuditVo> 铁塔服务费费用审核对象集合
     */
    List<TowerAccountSummaryAuditVo> queryTwrAccountSummaryAuditList(@Param("dto") TwrAccountSummaryQueryDto twrAccountSummaryQueryDto);

    /**
     * 根据铁塔服务费报账点汇总查询DTO，查询铁塔服务费费用汇总(直辖市)信息
     * @param twrAccountSummaryQueryDto 铁塔服务费报账点汇总查询DTO
     * @return List<TowerAccountSummaryMuniVo> 铁塔服务费费用汇总对象(直辖市)集合
     */
    List<TowerAccountSummaryMuniVo> queryTwrAccountSummaryListMuni(@Param("dto") TwrAccountSummaryQueryDto twrAccountSummaryQueryDto);

    /**
     * 根据铁塔服务费报账点汇总查询DTO，查询铁塔服务费费用审核(直辖市)信息
     * @param twrAccountSummaryQueryDto 铁塔服务费报账点汇总查询DTO
     * @return List<TowerAccountSummaryAuditMuniVo> 铁塔服务费费用审核对象(直辖市)集合
     */
    List<TowerAccountSummaryAuditMuniVo> queryTwrAccountSummaryAuditListMuni(@Param("dto") TwrAccountSummaryQueryDto twrAccountSummaryQueryDto);


    /**
     * 根据报账汇总ID，查询附件信息
     * @param accountsummaryId 报账汇总ID
     * @return List<DatAttachment> 附件对象集合
     */
    List<DatAttachment> queryTwrAccountSummaryFiles(@Param("accountsummaryId") String accountsummaryId);
}
