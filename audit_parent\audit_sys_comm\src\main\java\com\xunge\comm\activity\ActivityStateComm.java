package com.xunge.comm.activity;

/**
 * <AUTHOR>
 * @date 2017年7月4日
 * @description 流程状态公有变量
 */
public class ActivityStateComm {
    /**
     * 审核状态变量名 "state"
     */
    public static String ACTIVITY_VARIABLE_NAME = "state";

    /**
     * 当前审核人变量名 "currUserId"
     */
    public static String ACTIVITY_VARIABLE_CURRUSER = "currUserId";

    /**
     * 下级审核人变量名 "nextUserId"
     */
    public static String ACTIVITY_VARIABLE_NEXTUSER = "nextUserId";
    /**
     * 审核通过、正常状态数据:0
     */
    public static Integer STATE_NORMAL = 0;
    /**
     * 审核通过、正常状态数据:0
     */
    public static Integer STATE_UNPUSH = 4;

    /**
     * 审核未通过、直接终止:8
     */
    public static Integer STATE_UNAPPROVE = 8;
    /**
     * 审核未通过、直接终止:5(预付款核销处使用)
     */
    public static Integer STATE_PASS = 5;

    /**
     * 审核中:9
     */
    public static Integer STATE_AUDIT = 9;
    /**
     * 撤销审核中:7
     */
    public static Integer STATE_SENDBACK = 7;

    /**
     * 未提交:-1
     */
    public static Integer STATE_UNCOMMITTED = -1;

    //报账点删除审批使用#########开始
    /**
     * 删除审批中
     */
    public static Integer STATE_DELETE_AUDIT = 3;
    /**
     * 删除审批通过：终止
     */
    public static Integer STATE_DELETE_AUDIT_PASS = 4;
    /**
     * 删除审批未通过:终止
     */
    public static Integer STATE_DELETE_AUDIT_UNAPPROVE = 6;
    //报账点删除审批使用#########结束

    //预警信息审批使用#########开始
    public static String RECOVER_STATE_NOSUBMIT = "-1";
    public static String RECOVER_STATE_SUBMIT = "9";
    public static String RECOVER_STATE_SUBMIT_PASS = "0";
    public static String RECOVER_STATE_SUBMIT_NOT_PASS = "8";
    public static String PASS = "0";
    public static String UPASS = "8";
    //预警信息审批使用#########开始


    /**
     * 审核通过
     */
    public static String AUDIT_NORMAL = "审核通过";

    /**
     * 审核未通过
     */
    public static String AUDIT_UNAPPROVE = "审核未通过";

    /**
     * 审核中
     */
    public static String AUDIT_AUDIT = "审核中";

    /**
     * 未提交
     */
    public static String AUDIT_UNCOMMITTED = "未提交";

}
