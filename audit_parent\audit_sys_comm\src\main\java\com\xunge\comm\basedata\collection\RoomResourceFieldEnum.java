package com.xunge.comm.basedata.collection;

/*
* 机房枚举字段常量(对应dat_baseresource_enum表中的字段)
*
* 
* */
public class RoomResourceFieldEnum {

    //机房级别
    public static final String ROOM_LEVEL = "roomLevel";
    //机房产权性质
    public static final String ROOM_PROPERTY = "roomProperty";
    //机房产权单位
    public static final String ROOM_OWNER = "roomOwner";
    //铁塔机房产品分类
    public static final String CTC_ROOM_CATEGORY = "ctcRoomCategory";
    //机房共享单位
    public static final String ROOM_SHARE_ATTRIBUTE = "roomShareAttribute";
    //机房类型
    public static final String ROOM_TYPE = "roomType";
    //机房生命周期状态
    public static final String ROOM_BASERESOUCE_STATE = "roomBaseresouceState";

}
