package com.xunge.dto.selfelec.payment;

import com.xunge.dto.selfelec.PageInfo;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/11/9 14:54
 */
@Getter
@Setter
public class SpecialPaymentQueryDto extends PageInfo implements Serializable {

    private static final long serialVersionUID = 2633829019952524541L;

    String billaccountCodeOrName;
    String paymentCode;
    String contractCodeOrName;
    String pregId;
    String regId;
    String auditingState;
    String startdateOpen;
    String startdateClose;
    String enddateOpen;
    String enddateClose;
    String userCodeOrName;
    String supplyMethod;
    String billamountDateOpen;
    String billamountDateClose;
    String isFinance;
    String billamountState;
    String dateSort;
    String billaccountType;
    String searchKeywords;
    String billaccountCode;
    String billaccountName;
    String stayAuditingUser;
    /**
     * DetailMaintain:明细维护页面
     */
    String pageName;
    String auditingUserId;
    String submitState;
    String taskDefKey;
    String emergencyDegree;
    String dataIdList;
    /**
     * 特殊报账点标记
     */
    int isSpecial=1;

    // 普服资源
    Integer ifTeleCmnServ;
    // 5G标识
    Integer fivegFlag;
    /**
     * 当前审核节点名称
     */
    private String auditNodeName;

    /**
     * 导出选中缴费单id
     */
    List<String> billaccountpaymentdetailIds;
    private String baseresourceCode;
    private String meterCode;
    private String siteCodeType;
    private String imageaiResult;
    /**
     * 是否超标，1：是，0：否
     */
    private String overflow;

    /**
     * 超标类型
     */
    private String overFlowType;
    /**
     * <option value="">-报账点费用类型-</option>
     * <option value="1">自维</option>
     * <option value="2">塔维</option>
     * <option value="3">代持</option>
     */
    private Integer billaccountAmountType;
    /**
     * <option value="">-新能源报账点类型-</option>
     * <option value="4">一站多路电特殊报账点</option>
     * <option value="1">光伏报账点</option>
     * <option value="2">风能报账点</option>
     * <option value="3">水电报账点</option>
     * <option value="5">其他新能源报账点</option>
     */
    private Integer newEnergyBillaccountType;

    /**
     * 是否为导出
     */
    private boolean isExport = false;

    /**
     * 财务审核通过日期,期始、期终
     */
    private String financeDateOpen;
    private String financeDateClose;

    /**
     * 有无附件
     * <option value="">---未筛选---</option>
     * <option value="0">无附件</option>
     * <option value="1">有附件</option>
     */
    private String isAttachmentFlag;
    
    private String overProof;

    public boolean isDefaultQuery() {
        return
                StringUtils.isBlank(paymentCode)
                        && StringUtils.isBlank(billaccountCodeOrName)
                        && StringUtils.isBlank(contractCodeOrName)
                        && StringUtils.isBlank(baseresourceCode)
                        && StringUtils.isBlank(meterCode)
                        && StringUtils.isBlank(pregId)
                        && StringUtils.isBlank(regId)
                        && StringUtils.isBlank(auditingState)
                        && StringUtils.isBlank(billamountState)
                        && StringUtils.isBlank(userCodeOrName)

                        && StringUtils.isBlank(startdateOpen)
                        && StringUtils.isBlank(startdateClose)
                        && StringUtils.isBlank(enddateOpen)
                        && StringUtils.isBlank(enddateClose)

                        && StringUtils.isBlank(billamountDateOpen)
                        && StringUtils.isBlank(billamountDateClose)

                        && StringUtils.isBlank(financeDateOpen)
                        && StringUtils.isBlank(financeDateClose)

                        && StringUtils.isBlank(imageaiResult)
                        && StringUtils.isBlank(searchKeywords)
                        && null == ifTeleCmnServ
                        && null == fivegFlag
                        && null == billaccountAmountType
                        && null == newEnergyBillaccountType
                        && StringUtils.isBlank(overflow)
                        && StringUtils.isBlank(supplyMethod)
                        && StringUtils.isBlank(overFlowType)
                        && StringUtils.isBlank(overProof)
                        && StringUtils.isBlank(isAttachmentFlag)

                        && StringUtils.isBlank(stayAuditingUser)
                        && StringUtils.isBlank(auditNodeName);
    }
}
