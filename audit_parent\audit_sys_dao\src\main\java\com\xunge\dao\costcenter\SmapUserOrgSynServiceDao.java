package com.xunge.dao.costcenter;

import com.xunge.model.basedata.colletion.TaskHistoryInfoVO;
import com.xunge.model.contract.SynConfig;
import com.xunge.model.costcenter.SmapUserOrgVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * Created by liuxiao on 2019/8/7.
 */
public interface SmapUserOrgSynServiceDao {
    void updateLastSynTime(@Param("updateTime") String updateTime);

    SynConfig getSnyConfig();

    List<Integer> getDataId(@Param("codes") Set<Integer> codes);

    void insertTaskInfo(TaskHistoryInfoVO taskHistory);

    int insertSmapOrgInfo(List<SmapUserOrgVO> list);

    int updateSmapOrgInfo(SmapUserOrgVO smapUserOrgVO);
}
