package com.xunge.model.external.pojo;


import java.io.Serializable;
import java.math.BigDecimal;

public class ExtOtherPayment implements Serializable {

    private static final long serialVersionUID = 5960623983664590326L;

    private String otherPaymentName;           //费用名称
    private Integer otherInvoiceType;           //票据类型	0、增值税专票；1、增值税普票；2、收据；
    private BigDecimal otherPrice;                 //价款 票据类型为“0、增值税专票”时必传。
    private BigDecimal otherTaxRate;               //税率 票据类型为“0、增值税专票”时必传。0、0%；1、1%；2、1.5%；3、3%；4、5%；5、6%；6、9%；7、10%；8、11%；9、13%；10、16%；11、17%；
    private BigDecimal otherAmountTax;             //税金 票据类型为“0、增值税专票”时必传。
    private BigDecimal otherBillamountTax;         //含税金额	票据类型为“1、增值税普票”或“2、收据”时必传。
    private String classCode;                  //业务大类
    private String classSmCode;                //业务小类
    private String activityCode;               //业务活动

    private String otherPaymentId;
    private String className;
    private String classSmName;
    private String activityName;

    public String getOtherPaymentName() {
        return otherPaymentName;
    }

    public void setOtherPaymentName(String otherPaymentName) {
        this.otherPaymentName = otherPaymentName;
    }

    public Integer getOtherInvoiceType() {
        return otherInvoiceType;
    }

    public void setOtherInvoiceType(Integer otherInvoiceType) {
        this.otherInvoiceType = otherInvoiceType;
    }

    public BigDecimal getOtherPrice() {
        return otherPrice;
    }

    public void setOtherPrice(BigDecimal otherPrice) {
        this.otherPrice = otherPrice;
    }

    public BigDecimal getOtherTaxRate() {
        return otherTaxRate;
    }

    public void setOtherTaxRate(BigDecimal otherTaxRate) {
        this.otherTaxRate = otherTaxRate;
    }

    public BigDecimal getOtherAmountTax() {
        return otherAmountTax;
    }

    public void setOtherAmountTax(BigDecimal otherAmountTax) {
        this.otherAmountTax = otherAmountTax;
    }

    public BigDecimal getOtherBillamountTax() {
        return otherBillamountTax;
    }

    public void setOtherBillamountTax(BigDecimal otherBillamountTax) {
        this.otherBillamountTax = otherBillamountTax;
    }

    public String getClassCode() {
        return classCode;
    }

    public void setClassCode(String classCode) {
        this.classCode = classCode;
    }

    public String getClassSmCode() {
        return classSmCode;
    }

    public void setClassSmCode(String classSmCode) {
        this.classSmCode = classSmCode;
    }

    public String getActivityCode() {
        return activityCode;
    }

    public void setActivityCode(String activityCode) {
        this.activityCode = activityCode;
    }

    public String getOtherPaymentId() {
        return otherPaymentId;
    }

    public void setOtherPaymentId(String otherPaymentId) {
        this.otherPaymentId = otherPaymentId;
    }

    public String getClassName() {
        return className;
    }

    public void setClassName(String className) {
        this.className = className;
    }

    public String getClassSmName() {
        return classSmName;
    }

    public void setClassSmName(String classSmName) {
        this.classSmName = classSmName;
    }

    public String getActivityName() {
        return activityName;
    }

    public void setActivityName(String activityName) {
        this.activityName = activityName;
    }
}
