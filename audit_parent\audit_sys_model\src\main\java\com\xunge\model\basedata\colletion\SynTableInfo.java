package com.xunge.model.basedata.colletion;

import java.util.Date;

public class SynTableInfo {
    
    private String guid;

    
    private String name;

    
    private String status;
    private Date createTime;
    private Date updateTime;
    private String remarks;
    private String taskId;

    
    public String getGuid() {
        return guid;
    }

    
    public void setGuid(String guid) {
        this.guid = guid == null ? null : guid.trim();
    }

    
    public String getName() {
        return name;
    }

    
    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    
    public String getStatus() {
        return status;
    }

    
    public void setStatus(String status) {
        this.status = status == null ? null : status.trim();
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }
}