package com.xunge.dao.twrrent.monthlyReport.impl;

import com.xunge.dao.AbstractBaseDao;
import com.xunge.dao.twrrent.monthlyReport.IMonthlyReportKeyIndexDao;
import com.xunge.model.towerrent.monthlyReport.KeyIndexTowerVO;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public class MonthlyReportKeyIndexDaoImpl extends AbstractBaseDao implements IMonthlyReportKeyIndexDao {

    private final String nameSpace = "com.xunge.dao.twrrent.monthlyReport.TwrMonthlyReportKeyIndexMapper.";

    @Override
    public List<KeyIndexTowerVO> getBaseKeyIndexAmoritizeDataPrv(Map<String, Object> paraMap) {
        return this.getSqlSession().selectList(nameSpace + "getBaseKeyIndexAmoritizeDataPrv", paraMap);
    }

    @Override
    public List<KeyIndexTowerVO> getBaseKeyIndexAmoritizeDataPreg(Map<String, Object> paraMap) {
        return this.getSqlSession().selectList(nameSpace + "getBaseKeyIndexAmoritizeDataPreg", paraMap);
    }

    @Override
    public List<KeyIndexTowerVO> getBaseKeyIndexAmoritizeDataReg(Map<String, Object> paraMap) {
        return this.getSqlSession().selectList(nameSpace + "getBaseKeyIndexAmoritizeDataReg", paraMap);
    }

    @Override
    public List<KeyIndexTowerVO> getBaseKeyIndexAmoritizeDataWhole(Map<String, Object> paraMap) {
        return this.getSqlSession().selectList(nameSpace + "getBaseKeyIndexAmoritizeDataWhole", paraMap);
    }
}
