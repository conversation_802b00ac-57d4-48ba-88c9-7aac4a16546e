package com.xunge.dao.towerrent.inspection;

import com.xunge.model.towerrent.inspection.InspectionPushLog;

import java.util.List;
import java.util.Map;

public interface InspectionPushLogMapper {
    int deleteByPrimaryKey(String pushId);

    int insert(InspectionPushLog record);

    int insertSelective(InspectionPushLog record);

    InspectionPushLog selectByPrimaryKey(String pushId);

    int updateByPrimaryKeySelective(InspectionPushLog record);

    int updateByPrimaryKey(InspectionPushLog record);

    List<InspectionPushLog> selectByMap(Map<String, Object> paramMap);
}