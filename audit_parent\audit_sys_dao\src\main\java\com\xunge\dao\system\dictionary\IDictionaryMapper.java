package com.xunge.dao.system.dictionary;

import com.xunge.model.system.dictionary.DictionaryVO;
import org.apache.ibatis.annotations.Param;

public interface IDictionaryMapper {

    /**
     * 查询业务开关
     * @param dictName
     * @return
     */
    DictionaryVO selectBusinessSwitch(String dictName);

    DictionaryVO selectByBussinessId(String dictName);

    /**
     * 根据字典组编码和字典名称查询字典值
     * @param groupCode
     * @param dictName
     * @return
     */
    String selectValueByGroupCodeAndDictName(@Param("groupCode") String groupCode, @Param("dictName") String dictName);

    /**
     * 根据字典名称查询字典值
     * @param dictName
     * @return
     */
    String selectByDictName(@Param("dictName")String dictName);
}
