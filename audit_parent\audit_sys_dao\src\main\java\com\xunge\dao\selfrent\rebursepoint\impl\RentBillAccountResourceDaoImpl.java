package com.xunge.dao.selfrent.rebursepoint.impl;

import com.xunge.dao.AbstractBaseDao;
import com.xunge.dao.selfrent.rebursepoint.IRentBillAccountResourceDao;
import com.xunge.model.selfrent.billAccount.RentBillAccountResourceVO;

import java.util.List;
import java.util.Map;

public class RentBillAccountResourceDaoImpl extends AbstractBaseDao implements IRentBillAccountResourceDao {
    final String RentBillAccountResourceNamespace = "com.xunge.mapping.RentBillAccountResourceVOMapper.";//报账点表

    @Override
    public void insertBillAccountResource(List<RentBillAccountResourceVO> insertResourceList) {
        this.getSqlSession().insert(RentBillAccountResourceNamespace + "insertBillAccountResource", insertResourceList);
    }

    @Override
    public void updateBillAccountResource(Map<String, Object> paraMap) {
        this.getSqlSession().update(RentBillAccountResourceNamespace + "updateBillAccountResource", paraMap);
    }

    @Override
    public void deleteBillAccountResource(Map<String, Object> paraMap) {
        this.getSqlSession().delete(RentBillAccountResourceNamespace + "deleteBillAccountResource", paraMap);
    }

    @Override
    public List<RentBillAccountResourceVO> queryResourceBindBillacc(Map<String, Object> paraMap) {
        return this.getSqlSession().selectList(RentBillAccountResourceNamespace + "queryResourceBindBillacc", paraMap);
    }

    @Override
    public int deleteResourcePoint(String baseresourceId) {
        return this.getSqlSession().delete(RentBillAccountResourceNamespace + "deleteResourcePoint", baseresourceId);
    }

    @Override
    public List<RentBillAccountResourceVO> queryBillaccountResourceById(Map<String, Object> paraMap) {
        return this.getSqlSession().selectList(RentBillAccountResourceNamespace + "queryBillaccountResourceById", paraMap);
    }

    @Override
    public List<RentBillAccountResourceVO> queryBillaccountCodeByResouceId(List<String> baseresourceIds) {
        return this.getSqlSession().selectList(RentBillAccountResourceNamespace + "queryBillaccountCodeByResouceId", baseresourceIds);
    }

}
