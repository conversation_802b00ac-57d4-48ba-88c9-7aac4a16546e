package com.xunge.comm;

/**
 * <AUTHOR>
 * @date 2022/5/12
 * @description 用户流转记录常量类
 */
public class UserProcessComm {

    public static String SEGMENT_CREATE = "账号创建";

    public static String SEGMENT_MODIFY = "账号修改";

    public static String SEGMENT_RETURN = "账号退回";

    public static String OPERATE_RESULT_SUCCESS = "审核通过";

    public static String OPERATE_RESULT_FAIL = "审核不通过";

    public static String AUDIT_SUBMIT = "提交审核";

    //审核状态 未提交-0
    public static Integer AUDIT_STATE_NS = 0;
    //审核状态 审核中-1
    public static Integer AUDIT_STATE_ING = 1;
    //审核状态 审核通过-2
    public static Integer AUDIT_STATE_SUCCESS = 2;
    //审核状态 审核不通过-9
    public static Integer AUDIT_STATE_FAIL = 9;
    //数据来源 统一门户同步-1
    public static Integer DATA_SOURCE_STATE_RELATABLE = 1;
    //数据来源 统一门户同步-0
    public static Integer DATA_SOURCE_STATE_UNRELATABLE = 0;
}
