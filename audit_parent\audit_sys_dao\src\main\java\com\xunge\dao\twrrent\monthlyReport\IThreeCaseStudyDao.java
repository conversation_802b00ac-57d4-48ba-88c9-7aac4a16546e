package com.xunge.dao.twrrent.monthlyReport;

import com.xunge.model.towerrent.monthlyReport.RptThreeCaseStudy;

import java.util.List;
import java.util.Map;

public interface IThreeCaseStudyDao {

    List<RptThreeCaseStudy> queryThreeCaseData(Map<String, Object> paraMap);

    String insertThreeById(RptThreeCaseStudy threeObj);

    String deleteThreeById(Map<String, Object> map);

    String updateThreeById(RptThreeCaseStudy threeObj);

    List<RptThreeCaseStudy> findThreeName(Map<String, Object> map);

    String findPrvNameById(String prvId);

    List<String> queryFiles(String businessId, String businessType);

    List<RptThreeCaseStudy> queryGroupThreeCaseData(Map<String, Object> paraMap);

    List<Map<String, Object>> selecThreeCasetUser();

    List<RptThreeCaseStudy> queryThreeCaseDataHis(Map<String, Object> paraMap);
}
