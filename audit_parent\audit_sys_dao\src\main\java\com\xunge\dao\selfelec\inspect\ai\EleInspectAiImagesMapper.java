package com.xunge.dao.selfelec.inspect.ai;

import com.xunge.model.selfelec.inspect.ai.FeedbackLevelFlag;
import com.xunge.model.selfelec.inspect.ai.image.EleInspectAiImage;
import com.xunge.model.selfelec.inspect.ai.image.ImagesApplyAi;
import com.xunge.model.selfelec.inspect.ai.infor.EleInspectAiInfoApply;
import com.xunge.model.selfelec.inspect.province.EleInspect;
import com.xunge.model.selfelec.inspect.province.EleInspectWeb;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * ele_inspect_ai_image
 */
public interface EleInspectAiImagesMapper {

    /**
     * 新增ele_inspect_ai_info_apply
     *
     * @param eleInspectAiInfoApply 新增数据
     */
    void insertEleInspectAiInfoApply(EleInspectAiInfoApply eleInspectAiInfoApply);

    /**
     * 更新ele_inspect
     *
     * @param eleInspectAiInfoApply 更新参数
     */
    void updateEleInspectAiInfoApplyByEleInspectAiInfoApply(EleInspectAiInfoApply eleInspectAiInfoApply);

    /**
     * 稽核报账基础数据信息及返回结果(审核开始)
     *
     * @param eleInspectAiInfoApply 查询条件
     * @return 结果
     */
    List<EleInspectAiInfoApply> getEleInspectAiInfoApplyByEleInspectAiInfoApply(EleInspectAiInfoApply eleInspectAiInfoApply);

    /**
     * 获取主数据(后付费)
     *
     * @param imagesApplyAi 查询条件
     * @return 结果
     */
    ImagesApplyAi getImagesApply(ImagesApplyAi imagesApplyAi);

    /**
     * 获取主数据（核销）
     *
     * @param imagesApplyAi 查询条件
     * @return 结果
     */
    ImagesApplyAi getImagesApplyVerification(ImagesApplyAi imagesApplyAi);

    /**
     * 更新点击时间，是否有效标识
     *
     * @param eleInspect 更新数据
     */
    void updateEleInspectAiImageApplyForWeb(EleInspect eleInspect);

    /**
     * 写详情
     *
     * @param eleInspectAiImage 数据
     */
    void insertImagesDetail(EleInspectAiImage eleInspectAiImage);

    /**
     * ele_inspect_ai_image_detail
     *
     * @param eleInspectAiImage 更新数据
     */
    void updateEleInspectAiImageDetail(EleInspectAiImage eleInspectAiImage);

    /**
     * 页面获取稽核结果
     *
     * @param eleInspect 查询条件
     * @return 结果
     */
    List<EleInspectWeb> queryEleInspectInfoForWeb(EleInspect eleInspect);

    /**
     * 更新核销AI图片稽核结果
     *
     * @param map
     */
    void updateImageaiResultByVerificationCode(Map<String, Object> map);

    /**
     * 更新后付费AI图片稽核结果
     *
     * @param map
     */
    void updateImageaiResultByPaymentCode(Map<String, Object> map);

    /**
     * 更新塔维电费AI图片稽核结果
     *
     * @param map
     */
    void updateImageaiResultByTPaymentCode(Map<String, Object> map);

    /**
     * 获取AI图片识别评价信息
     *
     * @param imageDetailId
     * @return
     */
    List<FeedbackLevelFlag> queryLevelFlagList(@Param("imageDetailId") String imageDetailId);
    /**
     * 更新后付费AI图片稽核结果-后付费
     *
     * @param map
     */
    void updateImageaiResultByPaymentId(Map<String, Object> map);
    /**
     * 更新核销AI图片稽核结果-核销
     *
     * @param map
     */
    void updateImageaiResultByVerificationId(Map<String, Object> map);
    /**
     * 更新核销AI图片稽核结果-塔维
     *
     * @param map
     */
    void updateImageaiResultByTPaymentId(Map<String, Object> map);
    /**
     * 获取主数据（塔维）
     *
     * @param imagesApplyAi 查询条件
     * @return 结果
     */
    ImagesApplyAi getImagesApplyTele(ImagesApplyAi imagesApplyAi);
}
