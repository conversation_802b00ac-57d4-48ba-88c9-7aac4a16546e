package com.xunge.dao.selfrent.contract;

import com.xunge.dto.selfelec.AuthorityUser;
import com.xunge.model.selfrent.contract.CuringRentContractAuditExportVO;
import com.xunge.model.selfrent.contract.CuringRentContractExportVO;
import com.xunge.model.selfrent.contract.RentContractQueryDto;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.cursor.Cursor;

/**
 * @Description:
 * @Author: dxd
 * @Date: 2023/5/5 10:07
 */
public interface ISelfRentVOMapper {

    Cursor<CuringRentContractExportVO> queryCuringRentContractByCursor(@Param("user") AuthorityUser userRegInfo, @Param("queryDto") RentContractQueryDto dto);

    Cursor<CuringRentContractAuditExportVO> queryCuringRentContractAuditByCursor(@Param("user") AuthorityUser userRegInfo, @Param("queryDto") RentContractQueryDto dto);
}
