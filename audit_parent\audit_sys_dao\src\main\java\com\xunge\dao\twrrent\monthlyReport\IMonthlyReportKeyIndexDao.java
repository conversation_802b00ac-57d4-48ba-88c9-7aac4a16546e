package com.xunge.dao.twrrent.monthlyReport;

import com.xunge.model.towerrent.monthlyReport.KeyIndexTowerVO;

import java.util.List;
import java.util.Map;

public interface IMonthlyReportKeyIndexDao {


    List<KeyIndexTowerVO> getBaseKeyIndexAmoritizeDataPrv(Map<String, Object> paraMap);

    List<KeyIndexTowerVO> getBaseKeyIndexAmoritizeDataPreg(Map<String, Object> paraMap);

    List<KeyIndexTowerVO> getBaseKeyIndexAmoritizeDataReg(Map<String, Object> paraMap);

    List<KeyIndexTowerVO> getBaseKeyIndexAmoritizeDataWhole(Map<String, Object> paraMap);
}
