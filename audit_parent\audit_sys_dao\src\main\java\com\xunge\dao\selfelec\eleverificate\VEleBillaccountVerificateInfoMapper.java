package com.xunge.dao.selfelec.eleverificate;

import com.xunge.model.basedata.DatBaseresource;
import com.xunge.model.selfelec.ElePaymentBenchmark;
import com.xunge.model.selfelec.VEleBillaccountPaymentInfo;
import com.xunge.model.selfelec.eleverificate.EleBillaccountVerificatedetail;
import com.xunge.model.selfelec.eleverificate.ElePushVerificate;
import com.xunge.model.selfelec.eleverificate.VEleBillaccountVerificateInfo;
import com.xunge.model.selfelec.eleverificate.VEleBillaccountVerificateInfoExample;
import org.apache.ibatis.annotations.Param;

import java.awt.*;
import java.util.List;
import java.util.Map;
import org.apache.ibatis.cursor.Cursor;

public interface VEleBillaccountVerificateInfoMapper {
    
    int countByExample(VEleBillaccountVerificateInfoExample example);

    
    int deleteByExample(VEleBillaccountVerificateInfoExample example);

    
    int insert(VEleBillaccountVerificateInfo record);

    
    int insertSelective(VEleBillaccountVerificateInfo record);

    
    List<VEleBillaccountVerificateInfo> selectByExample(VEleBillaccountVerificateInfoExample example);

    List<VEleBillaccountVerificateInfo> selectByExample(Map<String, Object> map);

    
    int updateByExampleSelective(@Param("record") VEleBillaccountVerificateInfo record,
                                 @Param("example") VEleBillaccountVerificateInfoExample example);

    
    int updateByExample(@Param("record") VEleBillaccountVerificateInfo record, @Param("example") VEleBillaccountVerificateInfoExample example);

    /**
     * 查询报账点审核页面
     *
     * @param hashMaps
     * @return
     * <AUTHOR>
     */
    public List<VEleBillaccountVerificateInfo> queryEleBillaccountVerificate(Map<String, Object> hashMaps);

    /**
     * 根据核销code查询核销单
     *
     * @param
     * @return
     * <AUTHOR>
     */
    public VEleBillaccountVerificateInfo queryEleBillaccountVerificateByCode(String code);

    /**
     * 删除明细账单
     *
     * @param
     * @return
     * <AUTHOR>
     */
    int deleteVerificateDetail(@Param("verificationId") String verificationId);

    /**
     * 删除历史标杆表
     *
     * @param
     * @return
     * <AUTHOR>
     */
    int deleteVerificateBenchmark(@Param("eleVerificateList") List<VEleBillaccountVerificateInfo> eleVerificateList);

    /**
     * 删除账单主表
     *
     * @param
     * @return
     * <AUTHOR>
     */
    int deleteVerificate(@Param("eleVerificateList") List<VEleBillaccountVerificateInfo> eleVerificateList);

    /**
     * @description 查询报账点及报账点缴费详细信息
     * <AUTHOR>
     * @date 创建时间：2017年9月21日
     */
    public List<VEleBillaccountVerificateInfo> selectBillamountVerificateDetails(Map<String, Object> map);

    /**
     * 缴费记录详情查询
     *
     * @param map
     * @return
     */
    public VEleBillaccountVerificateInfo queryEleBillaccountVerificateDetail(Map<String, Object> map);

    public VEleBillaccountVerificateInfo queryFinanceEleBillaccountVerificateDetail(Map<String, Object> map);


    /**
     * 根据报账点编码查询此最大缴费期终的信息
     *
     * @param billaccountId
     * @return
     */
    public VEleBillaccountVerificateInfo queryMaxBillAccountEnded(@Param("billaccountId") String billaccountId);

    public VEleBillaccountVerificateInfo queryMaxBillamountEnded(@Param("billaccountId") String billaccountId);

    public VEleBillaccountPaymentInfo queryMaxBillAccountEndedForVerifocate(@Param("billaccountId") String billaccountId);

    public List<VEleBillaccountVerificateInfo> queryBillaccountVerificateByCondition(Map<String, Object> map);
     Cursor<VEleBillaccountVerificateInfo> queryEleVerificateByCursor(Map<String, Object> map);

    public List<VEleBillaccountVerificateInfo> queryBillaccountVerificateByConditionFcontract(Map<String, Object> map);

    public List<VEleBillaccountVerificateInfo> queryValidateStatus(Map<String, Object> map);

    /**
     * 更加电费缴费id查询缴费明细中资源列表
     *
     * @param map
     * @return
     */
    public List<VEleBillaccountVerificateInfo> queryBaseresourceByVerificateId(Map<String, Object> map);

    /**
     * 查询核销推送汇总信息
     *
     * @param map
     * @return
     */
    public ElePushVerificate queryVerificateBillamountInfo(Map<String, Object> map);

    /**
     * 查询核销关联的资源点信息
     *
     * @return
     */
    public List<DatBaseresource> queryVerificationRescouceInfo(Map<String, Object> map);

    public List<ElePushVerificate> queryPushVerificateBillamountInfo(Map<String, Object> params);

    public void deletePush(@Param("ids") List<String> ids);

    public VEleBillaccountVerificateInfo queryEleVerificateById(Map<String, Object> params);

    public VEleBillaccountVerificateInfo queryEleVerificateByDate(@Param("billaccountId") String billaccountId, @Param("endDate") String endDate);

    public ElePushVerificate queryIcVerificateBillamountInfo(Map<String, Object> paraMap);

    public List<DatBaseresource> queryIcVerificationRescouceInfo(Map<String, Object> map);

    public VEleBillaccountVerificateInfo queryTypeById(@Param("verificationId") String verificationId);

    public VEleBillaccountVerificateInfo queryIcMaxBillAccountEnded(@Param("billaccountId") String billaccountId);

    VEleBillaccountVerificateInfo queryIcMaxBillAccountEndedMore(@Param("billaccountId") String billaccountId, @Param("verificationId") String verificationId);

    public VEleBillaccountVerificateInfo queryIcMaxBillAmountDate(@Param("billaccountId") String billaccountId);


    public Integer queryVerifIsFirst(@Param("billaccountId") String billaccountId, @Param("startDate") String startDate);

    public VEleBillaccountVerificateInfo queryIcMaxRechargeDate(@Param("billaccountId") String billaccountId);

    public VEleBillaccountVerificateInfo queryUpdateEleVerificateByDate(@Param("billaccountId") String billaccountId,
                                                                        @Param("endDate") String endDate, @Param("verificationId") String verificationId);

    /**
     * @Title: queryBaseresourceByPaymentId @Description: TODO(这里用一句话描述这个方法的作用) @param @param
     * map @param @return 设定文件 @return List<VEleBillaccountPaymentInfo> 返回类型 @throws
     */

    List<VEleBillaccountVerificateInfo> queryBaseresourceByPaymentId(Map<String, Object> map);

    /**
     * @Title: queryIcBaseresourceByPaymentId @Description: TODO(这里用一句话描述这个方法的作用) @param @param
     * map @param @return 设定文件 @return List<VEleBillaccountPaymentInfo> 返回类型 @throws
     */

    List<VEleBillaccountVerificateInfo> queryIcBaseresourceByPaymentId(Map<String, Object> map);

    /**
     * @Title: queryInfoByBillamountId @Description: TODO(这里用一句话描述这个方法的作用) @param @param
     * billamountId @param @return 设定文件 @return List<VEleBillaccountVerificateInfo>
     * 返回类型 @throws
     */

    List<VEleBillaccountVerificateInfo> queryInfoByBillamountId(String billamountId);

    /**
     * @Title: updateById @Description: TODO(这里用一句话描述这个方法的作用) @param @param
     * vEleBillaccountVerificateInfo 设定文件 @return void 返回类型 @throws
     */

    void updateById(VEleBillaccountVerificateInfo vEleBillaccountVerificateInfo);


    /**
     * @param @param supplierId
     * @param @param supplierIds    设定文件
     * @return void    返回类型
     * @throws
     * @Title: updateSupplierInfo
     * @Description: TODO(这里用一句话描述这个方法的作用)
     */

    void updateSupplierInfo(@Param("billaccountpaymentdetailId") String billaccountpaymentdetailId);


    /**
     * @param @param supplierId
     * @param @param supplierIds    设定文件
     * @return void    返回类型
     * @throws
     * @Title: updateSupplierInfo
     * @Description: TODO(这里用一句话描述这个方法的作用)
     */

    void updateSupplierInfo(@Param("supplierId") String supplierId, @Param("supplierIds") List<String> supplierIds);


    /**
     * @param @param elecontractId
     * @param @param elecontractIds    设定文件
     * @return void    返回类型
     * @throws
     * @Title: updateComtractInfo
     * @Description: TODO(这里用一句话描述这个方法的作用)
     */

    void updateComtractInfo(@Param("elecontractId") String elecontractId, @Param("elecontractIds") List<String> elecontractIds);


    /**
     * 查询核销类型
     *
     * @param info
     * @return
     */
    VEleBillaccountVerificateInfo queryVertifiTypeById(VEleBillaccountVerificateInfo info);

    /**
     * 根据ID和关键字key模糊查询
     *
     * @param paraMap
     * @return
     */
    VEleBillaccountVerificateInfo queryBeanByIdAndKey(Map<String, Object> paraMap);

    public List<VEleBillaccountVerificateInfo> queryIntersection();


    List<String> queryBaseresourceByPaymentMeterId(Map<String, Object> map);

    List<String> queryIcBaseresourceByPaymentMeterId(Map<String, Object> map);

    List<String> queryListResourceByVerificationId(Map<String, Object> map);

    String selectAppDegreeNum(Map<String, Object> map);

    List<Map<String, Object>> selectDegreeNum(String verificationId);

    List<Map<String, Object>> selectMeterId(String verificationId);

    /**
     * 将主表数据清空
     *
     * @param record 刷新数据
     */
    void updateMainForRefresh(EleBillaccountVerificatedetail record);

    void deleteIcVerification(String verificationId);

    List<String> getVerificationCodes(@Param("list") List<String> ids);

    List<VEleBillaccountVerificateInfo> queryVerificationByLoanId(Map<String, Object> map);


	VEleBillaccountVerificateInfo queryLastVerificate(@Param("billaccountId") String billaccountId, @Param("buyMethod") String buyMethod);

	VEleBillaccountVerificateInfo queryLastVerificateByBillaccountAndDate(@Param("billaccountId") String billaccountId, @Param("startDate") String startDate);

	VEleBillaccountVerificateInfo queryLastVerificateClass(@Param("billaccountId") String billaccountId, @Param("buyMethod") String buyMethod);

    List<ElePaymentBenchmark> queryPaybenchmark(String verificationId);
    List<VEleBillaccountVerificateInfo> queryCmccRatioByverificationIds(@Param("verificationIdList") List<String> verificationIdList);
}