package com.xunge.model.basedata;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class DatBasesiteExample {
    
    protected String orderByClause;

    
    protected boolean distinct;

    
    protected List<Criteria> oredCriteria;

    
    public DatBasesiteExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    
    public String getOrderByClause() {
        return orderByClause;
    }

    
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    
    public boolean isDistinct() {
        return distinct;
    }

    
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andBasesiteIdIsNull() {
            addCriterion("basesite_id is null");
            return (Criteria) this;
        }

        public Criteria andBasesiteIdIsNotNull() {
            addCriterion("basesite_id is not null");
            return (Criteria) this;
        }

        public Criteria andBasesiteIdEqualTo(String value) {
            addCriterion("basesite_id =", value, "basesiteId");
            return (Criteria) this;
        }

        public Criteria andBasesiteIdNotEqualTo(String value) {
            addCriterion("basesite_id <>", value, "basesiteId");
            return (Criteria) this;
        }

        public Criteria andBasesiteIdGreaterThan(String value) {
            addCriterion("basesite_id >", value, "basesiteId");
            return (Criteria) this;
        }

        public Criteria andBasesiteIdGreaterThanOrEqualTo(String value) {
            addCriterion("basesite_id >=", value, "basesiteId");
            return (Criteria) this;
        }

        public Criteria andBasesiteIdLessThan(String value) {
            addCriterion("basesite_id <", value, "basesiteId");
            return (Criteria) this;
        }

        public Criteria andBasesiteIdLessThanOrEqualTo(String value) {
            addCriterion("basesite_id <=", value, "basesiteId");
            return (Criteria) this;
        }

        public Criteria andBasesiteIdLike(String value) {
            addCriterion("basesite_id like", value, "basesiteId");
            return (Criteria) this;
        }

        public Criteria andBasesiteIdNotLike(String value) {
            addCriterion("basesite_id not like", value, "basesiteId");
            return (Criteria) this;
        }

        public Criteria andBasesiteIdIn(List<String> values) {
            addCriterion("basesite_id in", values, "basesiteId");
            return (Criteria) this;
        }

        public Criteria andBasesiteIdNotIn(List<String> values) {
            addCriterion("basesite_id not in", values, "basesiteId");
            return (Criteria) this;
        }

        public Criteria andBasesiteIdBetween(String value1, String value2) {
            addCriterion("basesite_id between", value1, value2, "basesiteId");
            return (Criteria) this;
        }

        public Criteria andBasesiteIdNotBetween(String value1, String value2) {
            addCriterion("basesite_id not between", value1, value2, "basesiteId");
            return (Criteria) this;
        }

        public Criteria andRegIdIsNull() {
            addCriterion("reg_id is null");
            return (Criteria) this;
        }

        public Criteria andRegIdIsNotNull() {
            addCriterion("reg_id is not null");
            return (Criteria) this;
        }

        public Criteria andRegIdEqualTo(String value) {
            addCriterion("reg_id =", value, "regId");
            return (Criteria) this;
        }

        public Criteria andRegIdNotEqualTo(String value) {
            addCriterion("reg_id <>", value, "regId");
            return (Criteria) this;
        }

        public Criteria andRegIdGreaterThan(String value) {
            addCriterion("reg_id >", value, "regId");
            return (Criteria) this;
        }

        public Criteria andRegIdGreaterThanOrEqualTo(String value) {
            addCriterion("reg_id >=", value, "regId");
            return (Criteria) this;
        }

        public Criteria andRegIdLessThan(String value) {
            addCriterion("reg_id <", value, "regId");
            return (Criteria) this;
        }

        public Criteria andRegIdLessThanOrEqualTo(String value) {
            addCriterion("reg_id <=", value, "regId");
            return (Criteria) this;
        }

        public Criteria andRegIdLike(String value) {
            addCriterion("reg_id like", value, "regId");
            return (Criteria) this;
        }

        public Criteria andRegIdNotLike(String value) {
            addCriterion("reg_id not like", value, "regId");
            return (Criteria) this;
        }

        public Criteria andRegIdIn(List<String> values) {
            addCriterion("reg_id in", values, "regId");
            return (Criteria) this;
        }

        public Criteria andRegIdNotIn(List<String> values) {
            addCriterion("reg_id not in", values, "regId");
            return (Criteria) this;
        }

        public Criteria andRegIdBetween(String value1, String value2) {
            addCriterion("reg_id between", value1, value2, "regId");
            return (Criteria) this;
        }

        public Criteria andRegIdNotBetween(String value1, String value2) {
            addCriterion("reg_id not between", value1, value2, "regId");
            return (Criteria) this;
        }

        public Criteria andBasesiteCodeIsNull() {
            addCriterion("basesite_code is null");
            return (Criteria) this;
        }

        public Criteria andBasesiteCodeIsNotNull() {
            addCriterion("basesite_code is not null");
            return (Criteria) this;
        }

        public Criteria andBasesiteCodeEqualTo(String value) {
            addCriterion("basesite_code =", value, "basesiteCode");
            return (Criteria) this;
        }

        public Criteria andBasesiteCodeNotEqualTo(String value) {
            addCriterion("basesite_code <>", value, "basesiteCode");
            return (Criteria) this;
        }

        public Criteria andBasesiteCodeGreaterThan(String value) {
            addCriterion("basesite_code >", value, "basesiteCode");
            return (Criteria) this;
        }

        public Criteria andBasesiteCodeGreaterThanOrEqualTo(String value) {
            addCriterion("basesite_code >=", value, "basesiteCode");
            return (Criteria) this;
        }

        public Criteria andBasesiteCodeLessThan(String value) {
            addCriterion("basesite_code <", value, "basesiteCode");
            return (Criteria) this;
        }

        public Criteria andBasesiteCodeLessThanOrEqualTo(String value) {
            addCriterion("basesite_code <=", value, "basesiteCode");
            return (Criteria) this;
        }

        public Criteria andBasesiteCodeLike(String value) {
            addCriterion("basesite_code like", value, "basesiteCode");
            return (Criteria) this;
        }

        public Criteria andBasesiteCodeNotLike(String value) {
            addCriterion("basesite_code not like", value, "basesiteCode");
            return (Criteria) this;
        }

        public Criteria andBasesiteCodeIn(List<String> values) {
            addCriterion("basesite_code in", values, "basesiteCode");
            return (Criteria) this;
        }

        public Criteria andBasesiteCodeNotIn(List<String> values) {
            addCriterion("basesite_code not in", values, "basesiteCode");
            return (Criteria) this;
        }

        public Criteria andBasesiteCodeBetween(String value1, String value2) {
            addCriterion("basesite_code between", value1, value2, "basesiteCode");
            return (Criteria) this;
        }

        public Criteria andBasesiteCodeNotBetween(String value1, String value2) {
            addCriterion("basesite_code not between", value1, value2, "basesiteCode");
            return (Criteria) this;
        }

        public Criteria andBasesiteNameIsNull() {
            addCriterion("basesite_name is null");
            return (Criteria) this;
        }

        public Criteria andBasesiteNameIsNotNull() {
            addCriterion("basesite_name is not null");
            return (Criteria) this;
        }

        public Criteria andBasesiteNameEqualTo(String value) {
            addCriterion("basesite_name =", value, "basesiteName");
            return (Criteria) this;
        }

        public Criteria andBasesiteNameNotEqualTo(String value) {
            addCriterion("basesite_name <>", value, "basesiteName");
            return (Criteria) this;
        }

        public Criteria andBasesiteNameGreaterThan(String value) {
            addCriterion("basesite_name >", value, "basesiteName");
            return (Criteria) this;
        }

        public Criteria andBasesiteNameGreaterThanOrEqualTo(String value) {
            addCriterion("basesite_name >=", value, "basesiteName");
            return (Criteria) this;
        }

        public Criteria andBasesiteNameLessThan(String value) {
            addCriterion("basesite_name <", value, "basesiteName");
            return (Criteria) this;
        }

        public Criteria andBasesiteNameLessThanOrEqualTo(String value) {
            addCriterion("basesite_name <=", value, "basesiteName");
            return (Criteria) this;
        }

        public Criteria andBasesiteNameLike(String value) {
            addCriterion("basesite_name like", value, "basesiteName");
            return (Criteria) this;
        }

        public Criteria andBasesiteNameNotLike(String value) {
            addCriterion("basesite_name not like", value, "basesiteName");
            return (Criteria) this;
        }

        public Criteria andBasesiteNameIn(List<String> values) {
            addCriterion("basesite_name in", values, "basesiteName");
            return (Criteria) this;
        }

        public Criteria andBasesiteNameNotIn(List<String> values) {
            addCriterion("basesite_name not in", values, "basesiteName");
            return (Criteria) this;
        }

        public Criteria andBasesiteNameBetween(String value1, String value2) {
            addCriterion("basesite_name between", value1, value2, "basesiteName");
            return (Criteria) this;
        }

        public Criteria andBasesiteNameNotBetween(String value1, String value2) {
            addCriterion("basesite_name not between", value1, value2, "basesiteName");
            return (Criteria) this;
        }

        public Criteria andBasesiteAddressIsNull() {
            addCriterion("basesite_address is null");
            return (Criteria) this;
        }

        public Criteria andBasesiteAddressIsNotNull() {
            addCriterion("basesite_address is not null");
            return (Criteria) this;
        }

        public Criteria andBasesiteAddressEqualTo(String value) {
            addCriterion("basesite_address =", value, "basesiteAddress");
            return (Criteria) this;
        }

        public Criteria andBasesiteAddressNotEqualTo(String value) {
            addCriterion("basesite_address <>", value, "basesiteAddress");
            return (Criteria) this;
        }

        public Criteria andBasesiteAddressGreaterThan(String value) {
            addCriterion("basesite_address >", value, "basesiteAddress");
            return (Criteria) this;
        }

        public Criteria andBasesiteAddressGreaterThanOrEqualTo(String value) {
            addCriterion("basesite_address >=", value, "basesiteAddress");
            return (Criteria) this;
        }

        public Criteria andBasesiteAddressLessThan(String value) {
            addCriterion("basesite_address <", value, "basesiteAddress");
            return (Criteria) this;
        }

        public Criteria andBasesiteAddressLessThanOrEqualTo(String value) {
            addCriterion("basesite_address <=", value, "basesiteAddress");
            return (Criteria) this;
        }

        public Criteria andBasesiteAddressLike(String value) {
            addCriterion("basesite_address like", value, "basesiteAddress");
            return (Criteria) this;
        }

        public Criteria andBasesiteAddressNotLike(String value) {
            addCriterion("basesite_address not like", value, "basesiteAddress");
            return (Criteria) this;
        }

        public Criteria andBasesiteAddressIn(List<String> values) {
            addCriterion("basesite_address in", values, "basesiteAddress");
            return (Criteria) this;
        }

        public Criteria andBasesiteAddressNotIn(List<String> values) {
            addCriterion("basesite_address not in", values, "basesiteAddress");
            return (Criteria) this;
        }

        public Criteria andBasesiteAddressBetween(String value1, String value2) {
            addCriterion("basesite_address between", value1, value2, "basesiteAddress");
            return (Criteria) this;
        }

        public Criteria andBasesiteAddressNotBetween(String value1, String value2) {
            addCriterion("basesite_address not between", value1, value2, "basesiteAddress");
            return (Criteria) this;
        }

        public Criteria andBasesiteTypeIsNull() {
            addCriterion("basesite_type is null");
            return (Criteria) this;
        }

        public Criteria andBasesiteTypeIsNotNull() {
            addCriterion("basesite_type is not null");
            return (Criteria) this;
        }

        public Criteria andBasesiteTypeEqualTo(Integer value) {
            addCriterion("basesite_type =", value, "basesiteType");
            return (Criteria) this;
        }

        public Criteria andBasesiteTypeNotEqualTo(Integer value) {
            addCriterion("basesite_type <>", value, "basesiteType");
            return (Criteria) this;
        }

        public Criteria andBasesiteTypeGreaterThan(Integer value) {
            addCriterion("basesite_type >", value, "basesiteType");
            return (Criteria) this;
        }

        public Criteria andBasesiteTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("basesite_type >=", value, "basesiteType");
            return (Criteria) this;
        }

        public Criteria andBasesiteTypeLessThan(Integer value) {
            addCriterion("basesite_type <", value, "basesiteType");
            return (Criteria) this;
        }

        public Criteria andBasesiteTypeLessThanOrEqualTo(Integer value) {
            addCriterion("basesite_type <=", value, "basesiteType");
            return (Criteria) this;
        }

        public Criteria andBasesiteTypeIn(List<Integer> values) {
            addCriterion("basesite_type in", values, "basesiteType");
            return (Criteria) this;
        }

        public Criteria andBasesiteTypeNotIn(List<Integer> values) {
            addCriterion("basesite_type not in", values, "basesiteType");
            return (Criteria) this;
        }

        public Criteria andBasesiteTypeBetween(Integer value1, Integer value2) {
            addCriterion("basesite_type between", value1, value2, "basesiteType");
            return (Criteria) this;
        }

        public Criteria andBasesiteTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("basesite_type not between", value1, value2, "basesiteType");
            return (Criteria) this;
        }

        public Criteria andBasesiteStateIsNull() {
            addCriterion("basesite_state is null");
            return (Criteria) this;
        }

        public Criteria andBasesiteStateIsNotNull() {
            addCriterion("basesite_state is not null");
            return (Criteria) this;
        }

        public Criteria andBasesiteStateEqualTo(Integer value) {
            addCriterion("basesite_state =", value, "basesiteState");
            return (Criteria) this;
        }

        public Criteria andBasesiteStateNotEqualTo(Integer value) {
            addCriterion("basesite_state <>", value, "basesiteState");
            return (Criteria) this;
        }

        public Criteria andBasesiteStateGreaterThan(Integer value) {
            addCriterion("basesite_state >", value, "basesiteState");
            return (Criteria) this;
        }

        public Criteria andBasesiteStateGreaterThanOrEqualTo(Integer value) {
            addCriterion("basesite_state >=", value, "basesiteState");
            return (Criteria) this;
        }

        public Criteria andBasesiteStateLessThan(Integer value) {
            addCriterion("basesite_state <", value, "basesiteState");
            return (Criteria) this;
        }

        public Criteria andBasesiteStateLessThanOrEqualTo(Integer value) {
            addCriterion("basesite_state <=", value, "basesiteState");
            return (Criteria) this;
        }

        public Criteria andBasesiteStateIn(List<Integer> values) {
            addCriterion("basesite_state in", values, "basesiteState");
            return (Criteria) this;
        }

        public Criteria andBasesiteStateNotIn(List<Integer> values) {
            addCriterion("basesite_state not in", values, "basesiteState");
            return (Criteria) this;
        }

        public Criteria andBasesiteStateBetween(Integer value1, Integer value2) {
            addCriterion("basesite_state between", value1, value2, "basesiteState");
            return (Criteria) this;
        }

        public Criteria andBasesiteStateNotBetween(Integer value1, Integer value2) {
            addCriterion("basesite_state not between", value1, value2, "basesiteState");
            return (Criteria) this;
        }

        public Criteria andBasesiteOpendateIsNull() {
            addCriterion("basesite_opendate is null");
            return (Criteria) this;
        }

        public Criteria andBasesiteOpendateIsNotNull() {
            addCriterion("basesite_opendate is not null");
            return (Criteria) this;
        }

        public Criteria andBasesiteOpendateEqualTo(Date value) {
            addCriterion("basesite_opendate =", value, "basesiteOpendate");
            return (Criteria) this;
        }

        public Criteria andBasesiteOpendateNotEqualTo(Date value) {
            addCriterion("basesite_opendate <>", value, "basesiteOpendate");
            return (Criteria) this;
        }

        public Criteria andBasesiteOpendateGreaterThan(Date value) {
            addCriterion("basesite_opendate >", value, "basesiteOpendate");
            return (Criteria) this;
        }

        public Criteria andBasesiteOpendateGreaterThanOrEqualTo(Date value) {
            addCriterion("basesite_opendate >=", value, "basesiteOpendate");
            return (Criteria) this;
        }

        public Criteria andBasesiteOpendateLessThan(Date value) {
            addCriterion("basesite_opendate <", value, "basesiteOpendate");
            return (Criteria) this;
        }

        public Criteria andBasesiteOpendateLessThanOrEqualTo(Date value) {
            addCriterion("basesite_opendate <=", value, "basesiteOpendate");
            return (Criteria) this;
        }

        public Criteria andBasesiteOpendateIn(List<Date> values) {
            addCriterion("basesite_opendate in", values, "basesiteOpendate");
            return (Criteria) this;
        }

        public Criteria andBasesiteOpendateNotIn(List<Date> values) {
            addCriterion("basesite_opendate not in", values, "basesiteOpendate");
            return (Criteria) this;
        }

        public Criteria andBasesiteOpendateBetween(Date value1, Date value2) {
            addCriterion("basesite_opendate between", value1, value2, "basesiteOpendate");
            return (Criteria) this;
        }

        public Criteria andBasesiteOpendateNotBetween(Date value1, Date value2) {
            addCriterion("basesite_opendate not between", value1, value2, "basesiteOpendate");
            return (Criteria) this;
        }

        public Criteria andBasesiteStopdateIsNull() {
            addCriterion("basesite_stopdate is null");
            return (Criteria) this;
        }

        public Criteria andBasesiteStopdateIsNotNull() {
            addCriterion("basesite_stopdate is not null");
            return (Criteria) this;
        }

        public Criteria andBasesiteStopdateEqualTo(Date value) {
            addCriterion("basesite_stopdate =", value, "basesiteStopdate");
            return (Criteria) this;
        }

        public Criteria andBasesiteStopdateNotEqualTo(Date value) {
            addCriterion("basesite_stopdate <>", value, "basesiteStopdate");
            return (Criteria) this;
        }

        public Criteria andBasesiteStopdateGreaterThan(Date value) {
            addCriterion("basesite_stopdate >", value, "basesiteStopdate");
            return (Criteria) this;
        }

        public Criteria andBasesiteStopdateGreaterThanOrEqualTo(Date value) {
            addCriterion("basesite_stopdate >=", value, "basesiteStopdate");
            return (Criteria) this;
        }

        public Criteria andBasesiteStopdateLessThan(Date value) {
            addCriterion("basesite_stopdate <", value, "basesiteStopdate");
            return (Criteria) this;
        }

        public Criteria andBasesiteStopdateLessThanOrEqualTo(Date value) {
            addCriterion("basesite_stopdate <=", value, "basesiteStopdate");
            return (Criteria) this;
        }

        public Criteria andBasesiteStopdateIn(List<Date> values) {
            addCriterion("basesite_stopdate in", values, "basesiteStopdate");
            return (Criteria) this;
        }

        public Criteria andBasesiteStopdateNotIn(List<Date> values) {
            addCriterion("basesite_stopdate not in", values, "basesiteStopdate");
            return (Criteria) this;
        }

        public Criteria andBasesiteStopdateBetween(Date value1, Date value2) {
            addCriterion("basesite_stopdate between", value1, value2, "basesiteStopdate");
            return (Criteria) this;
        }

        public Criteria andBasesiteStopdateNotBetween(Date value1, Date value2) {
            addCriterion("basesite_stopdate not between", value1, value2, "basesiteStopdate");
            return (Criteria) this;
        }

        public Criteria andBasesiteBelongIsNull() {
            addCriterion("basesite_belong is null");
            return (Criteria) this;
        }

        public Criteria andBasesiteBelongIsNotNull() {
            addCriterion("basesite_belong is not null");
            return (Criteria) this;
        }

        public Criteria andBasesiteBelongEqualTo(Integer value) {
            addCriterion("basesite_belong =", value, "basesiteBelong");
            return (Criteria) this;
        }

        public Criteria andBasesiteBelongNotEqualTo(Integer value) {
            addCriterion("basesite_belong <>", value, "basesiteBelong");
            return (Criteria) this;
        }

        public Criteria andBasesiteBelongGreaterThan(Integer value) {
            addCriterion("basesite_belong >", value, "basesiteBelong");
            return (Criteria) this;
        }

        public Criteria andBasesiteBelongGreaterThanOrEqualTo(Integer value) {
            addCriterion("basesite_belong >=", value, "basesiteBelong");
            return (Criteria) this;
        }

        public Criteria andBasesiteBelongLessThan(Integer value) {
            addCriterion("basesite_belong <", value, "basesiteBelong");
            return (Criteria) this;
        }

        public Criteria andBasesiteBelongLessThanOrEqualTo(Integer value) {
            addCriterion("basesite_belong <=", value, "basesiteBelong");
            return (Criteria) this;
        }

        public Criteria andBasesiteBelongIn(List<Integer> values) {
            addCriterion("basesite_belong in", values, "basesiteBelong");
            return (Criteria) this;
        }

        public Criteria andBasesiteBelongNotIn(List<Integer> values) {
            addCriterion("basesite_belong not in", values, "basesiteBelong");
            return (Criteria) this;
        }

        public Criteria andBasesiteBelongBetween(Integer value1, Integer value2) {
            addCriterion("basesite_belong between", value1, value2, "basesiteBelong");
            return (Criteria) this;
        }

        public Criteria andBasesiteBelongNotBetween(Integer value1, Integer value2) {
            addCriterion("basesite_belong not between", value1, value2, "basesiteBelong");
            return (Criteria) this;
        }

        public Criteria andBasesitePropertyIsNull() {
            addCriterion("basesite_property is null");
            return (Criteria) this;
        }

        public Criteria andBasesitePropertyIsNotNull() {
            addCriterion("basesite_property is not null");
            return (Criteria) this;
        }

        public Criteria andBasesitePropertyEqualTo(Integer value) {
            addCriterion("basesite_property =", value, "basesiteProperty");
            return (Criteria) this;
        }

        public Criteria andBasesitePropertyNotEqualTo(Integer value) {
            addCriterion("basesite_property <>", value, "basesiteProperty");
            return (Criteria) this;
        }

        public Criteria andBasesitePropertyGreaterThan(Integer value) {
            addCriterion("basesite_property >", value, "basesiteProperty");
            return (Criteria) this;
        }

        public Criteria andBasesitePropertyGreaterThanOrEqualTo(Integer value) {
            addCriterion("basesite_property >=", value, "basesiteProperty");
            return (Criteria) this;
        }

        public Criteria andBasesitePropertyLessThan(Integer value) {
            addCriterion("basesite_property <", value, "basesiteProperty");
            return (Criteria) this;
        }

        public Criteria andBasesitePropertyLessThanOrEqualTo(Integer value) {
            addCriterion("basesite_property <=", value, "basesiteProperty");
            return (Criteria) this;
        }

        public Criteria andBasesitePropertyIn(List<Integer> values) {
            addCriterion("basesite_property in", values, "basesiteProperty");
            return (Criteria) this;
        }

        public Criteria andBasesitePropertyNotIn(List<Integer> values) {
            addCriterion("basesite_property not in", values, "basesiteProperty");
            return (Criteria) this;
        }

        public Criteria andBasesitePropertyBetween(Integer value1, Integer value2) {
            addCriterion("basesite_property between", value1, value2, "basesiteProperty");
            return (Criteria) this;
        }

        public Criteria andBasesitePropertyNotBetween(Integer value1, Integer value2) {
            addCriterion("basesite_property not between", value1, value2, "basesiteProperty");
            return (Criteria) this;
        }

        public Criteria andBasesiteShareIsNull() {
            addCriterion("basesite_share is null");
            return (Criteria) this;
        }

        public Criteria andBasesiteShareIsNotNull() {
            addCriterion("basesite_share is not null");
            return (Criteria) this;
        }

        public Criteria andBasesiteShareEqualTo(Integer value) {
            addCriterion("basesite_share =", value, "basesiteShare");
            return (Criteria) this;
        }

        public Criteria andBasesiteShareNotEqualTo(Integer value) {
            addCriterion("basesite_share <>", value, "basesiteShare");
            return (Criteria) this;
        }

        public Criteria andBasesiteShareGreaterThan(Integer value) {
            addCriterion("basesite_share >", value, "basesiteShare");
            return (Criteria) this;
        }

        public Criteria andBasesiteShareGreaterThanOrEqualTo(Integer value) {
            addCriterion("basesite_share >=", value, "basesiteShare");
            return (Criteria) this;
        }

        public Criteria andBasesiteShareLessThan(Integer value) {
            addCriterion("basesite_share <", value, "basesiteShare");
            return (Criteria) this;
        }

        public Criteria andBasesiteShareLessThanOrEqualTo(Integer value) {
            addCriterion("basesite_share <=", value, "basesiteShare");
            return (Criteria) this;
        }

        public Criteria andBasesiteShareIn(List<Integer> values) {
            addCriterion("basesite_share in", values, "basesiteShare");
            return (Criteria) this;
        }

        public Criteria andBasesiteShareNotIn(List<Integer> values) {
            addCriterion("basesite_share not in", values, "basesiteShare");
            return (Criteria) this;
        }

        public Criteria andBasesiteShareBetween(Integer value1, Integer value2) {
            addCriterion("basesite_share between", value1, value2, "basesiteShare");
            return (Criteria) this;
        }

        public Criteria andBasesiteShareNotBetween(Integer value1, Integer value2) {
            addCriterion("basesite_share not between", value1, value2, "basesiteShare");
            return (Criteria) this;
        }

        public Criteria andBasesiteLongitudeIsNull() {
            addCriterion("basesite_longitude is null");
            return (Criteria) this;
        }

        public Criteria andBasesiteLongitudeIsNotNull() {
            addCriterion("basesite_longitude is not null");
            return (Criteria) this;
        }

        public Criteria andBasesiteLongitudeEqualTo(Long value) {
            addCriterion("basesite_longitude =", value, "basesiteLongitude");
            return (Criteria) this;
        }

        public Criteria andBasesiteLongitudeNotEqualTo(Long value) {
            addCriterion("basesite_longitude <>", value, "basesiteLongitude");
            return (Criteria) this;
        }

        public Criteria andBasesiteLongitudeGreaterThan(Long value) {
            addCriterion("basesite_longitude >", value, "basesiteLongitude");
            return (Criteria) this;
        }

        public Criteria andBasesiteLongitudeGreaterThanOrEqualTo(Long value) {
            addCriterion("basesite_longitude >=", value, "basesiteLongitude");
            return (Criteria) this;
        }

        public Criteria andBasesiteLongitudeLessThan(Long value) {
            addCriterion("basesite_longitude <", value, "basesiteLongitude");
            return (Criteria) this;
        }

        public Criteria andBasesiteLongitudeLessThanOrEqualTo(Long value) {
            addCriterion("basesite_longitude <=", value, "basesiteLongitude");
            return (Criteria) this;
        }

        public Criteria andBasesiteLongitudeIn(List<Long> values) {
            addCriterion("basesite_longitude in", values, "basesiteLongitude");
            return (Criteria) this;
        }

        public Criteria andBasesiteLongitudeNotIn(List<Long> values) {
            addCriterion("basesite_longitude not in", values, "basesiteLongitude");
            return (Criteria) this;
        }

        public Criteria andBasesiteLongitudeBetween(Long value1, Long value2) {
            addCriterion("basesite_longitude between", value1, value2, "basesiteLongitude");
            return (Criteria) this;
        }

        public Criteria andBasesiteLongitudeNotBetween(Long value1, Long value2) {
            addCriterion("basesite_longitude not between", value1, value2, "basesiteLongitude");
            return (Criteria) this;
        }

        public Criteria andBasesiteLatitudeIsNull() {
            addCriterion("basesite_latitude is null");
            return (Criteria) this;
        }

        public Criteria andBasesiteLatitudeIsNotNull() {
            addCriterion("basesite_latitude is not null");
            return (Criteria) this;
        }

        public Criteria andBasesiteLatitudeEqualTo(Long value) {
            addCriterion("basesite_latitude =", value, "basesiteLatitude");
            return (Criteria) this;
        }

        public Criteria andBasesiteLatitudeNotEqualTo(Long value) {
            addCriterion("basesite_latitude <>", value, "basesiteLatitude");
            return (Criteria) this;
        }

        public Criteria andBasesiteLatitudeGreaterThan(Long value) {
            addCriterion("basesite_latitude >", value, "basesiteLatitude");
            return (Criteria) this;
        }

        public Criteria andBasesiteLatitudeGreaterThanOrEqualTo(Long value) {
            addCriterion("basesite_latitude >=", value, "basesiteLatitude");
            return (Criteria) this;
        }

        public Criteria andBasesiteLatitudeLessThan(Long value) {
            addCriterion("basesite_latitude <", value, "basesiteLatitude");
            return (Criteria) this;
        }

        public Criteria andBasesiteLatitudeLessThanOrEqualTo(Long value) {
            addCriterion("basesite_latitude <=", value, "basesiteLatitude");
            return (Criteria) this;
        }

        public Criteria andBasesiteLatitudeIn(List<Long> values) {
            addCriterion("basesite_latitude in", values, "basesiteLatitude");
            return (Criteria) this;
        }

        public Criteria andBasesiteLatitudeNotIn(List<Long> values) {
            addCriterion("basesite_latitude not in", values, "basesiteLatitude");
            return (Criteria) this;
        }

        public Criteria andBasesiteLatitudeBetween(Long value1, Long value2) {
            addCriterion("basesite_latitude between", value1, value2, "basesiteLatitude");
            return (Criteria) this;
        }

        public Criteria andBasesiteLatitudeNotBetween(Long value1, Long value2) {
            addCriterion("basesite_latitude not between", value1, value2, "basesiteLatitude");
            return (Criteria) this;
        }

        public Criteria andDataFromIsNull() {
            addCriterion("data_from is null");
            return (Criteria) this;
        }

        public Criteria andDataFromIsNotNull() {
            addCriterion("data_from is not null");
            return (Criteria) this;
        }

        public Criteria andDataFromEqualTo(Integer value) {
            addCriterion("data_from =", value, "dataFrom");
            return (Criteria) this;
        }

        public Criteria andDataFromNotEqualTo(Integer value) {
            addCriterion("data_from <>", value, "dataFrom");
            return (Criteria) this;
        }

        public Criteria andDataFromGreaterThan(Integer value) {
            addCriterion("data_from >", value, "dataFrom");
            return (Criteria) this;
        }

        public Criteria andDataFromGreaterThanOrEqualTo(Integer value) {
            addCriterion("data_from >=", value, "dataFrom");
            return (Criteria) this;
        }

        public Criteria andDataFromLessThan(Integer value) {
            addCriterion("data_from <", value, "dataFrom");
            return (Criteria) this;
        }

        public Criteria andDataFromLessThanOrEqualTo(Integer value) {
            addCriterion("data_from <=", value, "dataFrom");
            return (Criteria) this;
        }

        public Criteria andDataFromIn(List<Integer> values) {
            addCriterion("data_from in", values, "dataFrom");
            return (Criteria) this;
        }

        public Criteria andDataFromNotIn(List<Integer> values) {
            addCriterion("data_from not in", values, "dataFrom");
            return (Criteria) this;
        }

        public Criteria andDataFromBetween(Integer value1, Integer value2) {
            addCriterion("data_from between", value1, value2, "dataFrom");
            return (Criteria) this;
        }

        public Criteria andDataFromNotBetween(Integer value1, Integer value2) {
            addCriterion("data_from not between", value1, value2, "dataFrom");
            return (Criteria) this;
        }
    }

    
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value) {
            super();
            this.condition = condition;
            this.value = value;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.betweenValue = true;
        }

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }
    }
}