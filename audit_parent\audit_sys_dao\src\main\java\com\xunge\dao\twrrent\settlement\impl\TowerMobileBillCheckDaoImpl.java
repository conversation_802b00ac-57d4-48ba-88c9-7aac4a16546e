package com.xunge.dao.twrrent.settlement.impl;

import com.xunge.core.page.Page;
import com.xunge.dao.AbstractBaseDao;
import com.xunge.dao.twrrent.settlement.ITowerMobileBillCheckDao;
import com.xunge.filter.PageInterceptor;
import com.xunge.model.towerrent.settlement.*;

import java.util.List;
import java.util.Map;

@SuppressWarnings("unchecked")
public class TowerMobileBillCheckDaoImpl extends AbstractBaseDao implements ITowerMobileBillCheckDao {
    final String TowerMobilerBillbalanceVOMapper = "com.xunge.dao.TowerMobilerBillbalanceVOMapper.";
    final String TowerMobilerBillbalanceConfirmVOMapper = "com.xunge.dao.TowerMobilerBillbalanceConfirmVOMapper.";
    final String NameSpace = "com.xunge.mapping.TowerBillbalanceVOMapper.";

    @Override
    public Page<List<TowerAndMobileBillVO>> queryTowerAndMobileFee(
            Map<String, Object> paraMap, int pageNumber, int pageSize) {
        PageInterceptor.startPage(pageNumber, pageSize);
        this.getSqlSession().selectList(TowerMobilerBillbalanceVOMapper + "queryTowerAndMobileFee", paraMap);
        return PageInterceptor.endPage();
    }

    @Override
    public Page<List<TowerAndMobileBillVO>> queryTowerAndMobileFeeConfig(
            Map<String, Object> paraMap, int pageNumber, int pageSize) {
        PageInterceptor.startPage(pageNumber, pageSize);
        this.getSqlSession().selectList(TowerMobilerBillbalanceVOMapper + "queryTowerAndMobileFeeConfig", paraMap);
        return PageInterceptor.endPage();
    }

    @Override
    public List<TowerAndMobileBillVO> queryCheckTowerAndMobileFee(
            Map<String, Object> paraMap, int pageNumber, int pageSize) {
        return this.getSqlSession().selectList(TowerMobilerBillbalanceVOMapper + "queryCheckTowerAndMobileFee", paraMap);
    }

    @Override
    public List<TowerAndMobileBillVO> queryCheckTowerAndMobileFeeConfig(
            Map<String, Object> paraMap, int pageNumber, int pageSize) {
        return this.getSqlSession().selectList(TowerMobilerBillbalanceVOMapper + "queryCheckTowerAndMobileFeeConfig", paraMap);
    }

    @Override
    public int updateCompareResult(TowerAndMobileBillVO towerAndMobileBillVO) {
        return this.getSqlSession().update(TowerMobilerBillbalanceVOMapper + "updateCompareResult", towerAndMobileBillVO);
    }

    @Override
    public int updateTowerMobileBillState(TowerBillbalanceVO vo) {
        return this.getSqlSession().update(TowerMobilerBillbalanceConfirmVOMapper + "updateTowerMobileBillState", vo);
    }

    @Override
    public int updateTowerMobileBillStateConfig(TowerBillbalanceVO vo) {
        return this.getSqlSession().update(TowerMobilerBillbalanceConfirmVOMapper + "updateTowerMobileBillState", vo);
    }

    @Override
    public int updateCancleConfirmState(Map<String, Object> paraMap) {
        return this.getSqlSession().update(TowerMobilerBillbalanceConfirmVOMapper + "updateCancleConfirmState", paraMap);
    }

    @Override
    public List<TowerAndMobileBillVO> queryAllTowerAndMobileFee(
            Map<String, Object> map) {
        return this.getSqlSession().selectList(TowerMobilerBillbalanceVOMapper + "queryTowerAndMobileFee", map);
    }

    @Override
    public List<TowerAndMobileBillVO> selectAllTowerAndMobileFee(
            Map<String, Object> map) {
        return this.getSqlSession().selectList(TowerMobilerBillbalanceVOMapper + "selectTowerAndMobileFee", map);
    }

    @Override
    public List<TowerAndMobileBillVO> selectAllTowerAndMobileFeeConfig(
            Map<String, Object> map) {
        return this.getSqlSession().selectList(TowerMobilerBillbalanceVOMapper + "selectTowerAndMobileFeeConfig", map);
    }

    @Override
    public List<TowerAndMobileBillConfirmVO> queryTowerAndMobileConfirmBill(
            Map<String, Object> paraMap) {
        return this.getSqlSession().selectList(TowerMobilerBillbalanceConfirmVOMapper + "queryTowerAndMobileConfirmBill", paraMap);
    }

    @Override
    public List<TowerAndMobileBillConfirmVO> selectTowerAndMobileConfirmBill(
            Map<String, Object> paraMap) {
        return this.getSqlSession().selectList(TowerMobilerBillbalanceConfirmVOMapper + "selectTowerAndMobileConfirmBill", paraMap);
    }

    @Override
    public List<TowerAndMobileBillConfirmVO> selectTowerAndMobileConfirmBillConfig(
            Map<String, Object> paraMap) {
        return this.getSqlSession().selectList(TowerMobilerBillbalanceConfirmVOMapper + "selectTowerAndMobileConfirmBillConfig", paraMap);
    }

    @Override
    public List<TheBillConfirmVO> selectTheBillConfirmVO(
            Map<String, Object> paraMap) {
        return this.getSqlSession().selectList(TowerMobilerBillbalanceConfirmVOMapper + "selectTheBillConfirmVO", paraMap);
    }


    @Override
    public Page<List<TowerAndMobileBillConfirmVO>> queryTowerAndMobileConfirmBalance(
            Map<String, Object> paraMap, int pageNumber, int pageSize) {
        PageInterceptor.startPage(pageNumber, pageSize);
        this.getSqlSession().selectList(TowerMobilerBillbalanceConfirmVOMapper + "queryTowerAndMobileConfirmBill", paraMap);
        return PageInterceptor.endPage();
    }


    @Override
    public Page<TowerAndMobileBillConfirmVO> queryTowerAndMobileConfirmBalanceState(
            Map<String, Object> paraMap, int pageNumber, int pageSize) {
        PageInterceptor.startPage(pageNumber, pageSize);
        this.getSqlSession().selectList(TowerMobilerBillbalanceConfirmVOMapper + "queryTowerAndMobileConfirmBillState", paraMap);
        return PageInterceptor.endPage();
    }

    @Override
    public Page<List<TowerAndMobileBillConfirmVO>> queryTowerAndMobileConfirmBalanceConfig(
            Map<String, Object> paraMap, int pageNumber, int pageSize) {
        PageInterceptor.startPage(pageNumber, pageSize);
        this.getSqlSession().selectList(TowerMobilerBillbalanceConfirmVOMapper + "queryTowerAndMobileConfirmBillConfig", paraMap);
        return PageInterceptor.endPage();
    }

    @Override
    public Page<List<TowerAndMobileBillConfirmVO>> queryTowerAndMobileConfirmBalanceConfigState(
            Map<String, Object> paraMap, int pageNumber, int pageSize) {
        PageInterceptor.startPage(pageNumber, pageSize);
        this.getSqlSession().selectList(TowerMobilerBillbalanceConfirmVOMapper + "queryTowerAndMobileConfirmBillConfigState", paraMap);
        return PageInterceptor.endPage();
    }

    @Override
    public Page<List<TowerBillbalanceVO>> queryTowerBillbalanceByIds(
            Map<String, Object> map) {
        PageInterceptor.startPage(Integer.parseInt(map.get("pageNumber").toString()), Integer.parseInt(map.get("pageSize").toString()));
        this.getSqlSession().selectList(NameSpace + "queryTowerBillbalanceByIds", map);
        return PageInterceptor.endPage();
    }

    @Override
    public Page<List<TowerBillbalanceVO>> queryTowerBillbalanceByIdsConfig(
            Map<String, Object> map) {
        PageInterceptor.startPage(Integer.parseInt(map.get("pageNumber").toString()), Integer.parseInt(map.get("pageSize").toString()));
        this.getSqlSession().selectList(NameSpace + "queryTowerBillbalanceByIdsConfig", map);
        return PageInterceptor.endPage();
    }

    @Override
    public int updateTowerMobileBillConfirmState(TowerBillbalanceVO vo) {
        return this.getSqlSession().update(TowerMobilerBillbalanceConfirmVOMapper + "updateTowerMobileBillConfirmState", vo);
    }

    @Override
    public int crossStateUpdate(TowerBillbalanceVO vo) {
        return this.getSqlSession().update(TowerMobilerBillbalanceConfirmVOMapper + "crossStateUpdate", vo);
    }

    @Override
    public int updateTowerMobileBillConfirmStateConfig(TowerBillbalanceVO vo) {
        return this.getSqlSession().update(TowerMobilerBillbalanceConfirmVOMapper + "updateTowerMobileBillConfirmState", vo);
    }

    @Override
    public List<ExportExcelServiceChargeVo> exportExcelServiceCharge(Map<String, Object> map) {
        return this.getSqlSession().selectList(TowerMobilerBillbalanceVOMapper + "exportExcelServiceCharge", map);
    }

    @Override
    public List<TowerAndMobileBillConfirmVO> selectTowerBillbalanceConfirmBill(Map<String, Object> map) {
        return this.getSqlSession().selectList(TowerMobilerBillbalanceConfirmVOMapper + "selectTowerBillbalanceConfirmBill", map);
    }

    @Override
    public List<TowerAndMobileBillConfirmVO> selectTowerBillbalanceConfirmBillConfig(Map<String, Object> map) {
        return this.getSqlSession().selectList(TowerMobilerBillbalanceConfirmVOMapper + "selectTowerBillbalanceConfirmBillConfig", map);
    }

    @Override
    public List<TowerBillbalanceVO> selectTowerBillbalanceCross(Map<String, Object> map) {
        return this.getSqlSession().selectList(TowerMobilerBillbalanceConfirmVOMapper + "selectTowerBillbalanceCross", map);
    }

    @Override
    public int queryAccountsummaryCountById(Map<String, Object> map) {
        return this.getSqlSession().selectOne(TowerMobilerBillbalanceConfirmVOMapper + "queryAccountsummaryCountById", map);
    }

    @Override
    public int comfirmStateUpdate(Map<String, Object> param) {
        return this.getSqlSession().update(TowerMobilerBillbalanceConfirmVOMapper + "comfirmStateUpdate", param);
    }

    @Override
    public Page<List<MobileBillbalanceChangeVO>> queryMobileChangeList(
            Map<String, Object> paraMap, int pageNumber, int pageSize) {
        PageInterceptor.startPage(pageNumber, pageSize);
        this.getSqlSession().selectList(TowerMobilerBillbalanceConfirmVOMapper + "queryMobileChangeList", paraMap);
        return PageInterceptor.endPage();
    }

    @Override
    public List<MobileBillbalanceChangeVO> selectMobileChangeList(
            Map<String, Object> paraMap) {
        return this.getSqlSession().selectList(TowerMobilerBillbalanceConfirmVOMapper + "queryMobileChangeList", paraMap);
    }

    @Override
    public int insertConfirmNote(Map<String, Object> map) {
        return this.getSqlSession().insert(TowerMobilerBillbalanceConfirmVOMapper + "insertConfirmNote", map);
    }

    @Override
    public Page<List<TheConfirmNoteVO>> queryBillConfirmNote(Map<String, Object> paraMap, int pageNumber, int pageSize) {
        PageInterceptor.startPage(pageNumber, pageSize);
        this.getSqlSession().selectList(TowerMobilerBillbalanceConfirmVOMapper + "queryBillConfirmNote", paraMap);
        return PageInterceptor.endPage();
    }

    @Override
    public List<BillCheckPraceFeeVO> checkPraceFee(Map<String, Object> paraMap) {
        return this.getSqlSession().selectList(TowerMobilerBillbalanceConfirmVOMapper + "checkPraceFee", paraMap);
    }
}
