package com.xunge.model.budget;

public class BudgetBaseVo {

    /**
     * 工单ID
     */
    private String workOrderId;

    /**
     * 工单编码
     */
    private String workOrderCode;

    private String prvIds;

    /**
     * 费用类型 1电费 2租费 3三方塔 4铁塔服务费
     */
    private Integer budgetType;

    /**
     * 工单年份
     */
    private String budgetTime;

    private Integer pageNum;

    private Integer pageSize;

    /**
     * 保留字段，特殊规则，当此字段有值时业务可以据此进行特殊处理
     */
    private String specialRule;

    /**
     * 保留字段，当需要传递redisKey时使用
     */
    private String redisKey;

    public String getPrvIds() {
        return prvIds;
    }

    public void setPrvIds(String prvIds) {
        this.prvIds = prvIds;
    }

    public Integer getBudgetType() {
        return budgetType;
    }

    public void setBudgetType(Integer budgetType) {
        this.budgetType = budgetType;
    }

    public String getBudgetTime() {
        return budgetTime;
    }

    public void setBudgetTime(String budgetTime) {
        this.budgetTime = budgetTime;
    }

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public String getSpecialRule() {
        return specialRule;
    }

    public void setSpecialRule(String specialRule) {
        this.specialRule = specialRule;
    }

    public String getWorkOrderId() {
        return workOrderId;
    }

    public void setWorkOrderId(String workOrderId) {
        this.workOrderId = workOrderId;
    }

    public String getRedisKey() {
        return redisKey;
    }

    public void setRedisKey(String redisKey) {
        this.redisKey = redisKey;
    }

    public String getWorkOrderCode() {
        return workOrderCode;
    }

    public void setWorkOrderCode(String workOrderCode) {
        this.workOrderCode = workOrderCode;
    }
}
