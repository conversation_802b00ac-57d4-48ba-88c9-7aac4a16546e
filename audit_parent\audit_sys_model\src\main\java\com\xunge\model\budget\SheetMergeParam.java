package com.xunge.model.budget;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class SheetMergeParam implements Serializable {
    /***
     * sheet下标
     */
    private int sheetIndex;

    /***
     * 根据相同值合并单元格列列名
     */
    private List<String> mergeColumnNames;

    /***
     * 需要依照某列合并
     */
    private List<String> conditionMergeColumnNames;

    private String conditionColumnName;
}
