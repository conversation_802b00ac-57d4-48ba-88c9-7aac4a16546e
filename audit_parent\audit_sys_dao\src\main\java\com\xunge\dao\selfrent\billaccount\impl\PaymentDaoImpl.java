package com.xunge.dao.selfrent.billaccount.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.xunge.core.page.Page;
import com.xunge.core.util.StrUtil;
import com.xunge.dao.AbstractBaseDao;
import com.xunge.dao.selfrent.billaccount.IVPaymentDao;
import com.xunge.filter.PageInterceptor;
import com.xunge.model.basedata.DatBaseresource;
import com.xunge.model.selfrent.billAccount.RentPaymentMutiContractVO;
import com.xunge.model.selfrent.billAccount.VPaymentVO;
import com.xunge.model.selfrent.billAccount.VPaymentVOExport;
import com.xunge.model.selfrent.billAccount.VSpecialPaymentVOExport;
import com.xunge.model.selfrent.billamount.BillamountLogVO;
import com.xunge.model.selfrent.billamount.PaymentOtherFeeDetailVo;
import com.xunge.model.selfrent.billamount.PaymentResourceMergeVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public class PaymentDaoImpl extends AbstractBaseDao implements IVPaymentDao {

    final String Namespace = "com.xunge.mapping.VPaymentVOMapper.";

    /*final String Namespace1="com.xunge.mapping.DatBaseresourceVOMapper.";
    final String Namespace2="com.xunge.dao.RentPaymentdetailVOMapper.";
    
    @Override
    public RentPaymentdetailVO queryPayMentDetailByBaseId(Map<String,Object> map) {
        return this.getSqlSession().selectOne(Namespace2+"queryPayMentDetailByBaseId",map);
    }
    @Override
    public List<DatBaseresourceVO> queryDatBaseresourceByBillAccountId(String billAccountId) {
        return this.getSqlSession().selectList(Namespace1+"queryDatBaseresourceByBillAccountId",billAccountId);
    }
    */
    @Override
    public Page<VPaymentVO> queryPayment(Map<String, Object> hashMaps, int pageNumber, int pageSize) {
        PageInterceptor.startPage(pageNumber, pageSize);
        this.getSqlSession().selectList(Namespace + "queryPayment", hashMaps);
        return PageInterceptor.endPage();
    }

    @Override
    public PageInfo<VPaymentVO> queryAllPayment(Map<String, Object> hashMaps, int pageNumber, int pageSize) {
        PageHelper.startPage(pageNumber,pageSize);
        List<VPaymentVO> list = this.getSqlSession().selectList(Namespace + "queryAllPayment", hashMaps);
        return new PageInfo<>(list);
    }

    @Override
    public List<VPaymentVO> queryAllPayment(Map<String, Object> hashMaps) {
        return this.getSqlSession().selectList(Namespace + "queryAllPayment", hashMaps);
    }

    @Override
    public Page<VPaymentVO> queryPaymentContract(Map<String, Object> hashMaps, int pageNumber, int pageSize) {
        PageInterceptor.startPage(pageNumber, pageSize);
        this.getSqlSession().selectList(Namespace + "queryPaymentContract", hashMaps);
        return PageInterceptor.endPage();
    }

    @Override
    public Page<VPaymentVO> queryFinancePaymentContract(Map<String, Object> hashMaps, int pageNumber, int pageSize) {
        PageInterceptor.startPage(pageNumber, pageSize);
        this.getSqlSession().selectList(Namespace + "queryFinancePaymentContract", hashMaps);
        return PageInterceptor.endPage();
    }


    @Override
    public List<VPaymentVO> queryContractPayment(Map<String, Object> hashMaps) {
        return this.getSqlSession().selectList(Namespace + "queryContractPayment", hashMaps);
    }

    @Override
    public List<VPaymentVO> queryFinanceContractPayment(Map<String, Object> hashMaps) {
        return this.getSqlSession().selectList(Namespace + "queryFinanceContractPayment", hashMaps);
    }

    @Override
    public int countById(Map<String, Object> paraMap) {
        return this.getSqlSession().selectOne(Namespace + "countById", paraMap);
    }

    @Override
    public Map<String, Object> queryBeanById(Map<String, Object> paraMap) {
        return this.getSqlSession().selectOne(Namespace + "queryBeanById", paraMap);
    }

    @Override
    public int updateActivityCommit(Map<String, Object> hashMaps) {
        return this.getSqlSession().update(Namespace + "updateState", hashMaps);
    }

    @Override
    public int updateState(Map<String, Object> hashMaps) {
        return this.getSqlSession().update(Namespace + "updatePaymentState", hashMaps);
    }

    @Override
    public int updateLastAuditingDate(Map<String, Object> hashMaps) {
        return this.getSqlSession().update(Namespace + "updateLastAuditingDate", hashMaps);
    }

    @Override
    public int updateBillamountIdByPaymentId(Map<String, Object> hashMaps) {
        int count = this.getSqlSession().update(Namespace + "updateBillamountIdByPaymentId", hashMaps);
        return count;
    }

    @Override
    public Page<List<VPaymentVO>> queryContractPaymentByNoAmount(Map<String, Object> hashMaps) {
        // TODO Auto-generated method stub
        if (StrUtil.isNotBlank(hashMaps.get("pageNumber").toString()) && StrUtil.isNotBlank(hashMaps.get("pageSize").toString()))
            PageInterceptor.startPage(Integer.parseInt(hashMaps.get("pageNumber").toString()), Integer.parseInt(hashMaps.get("pageSize").toString()));
        this.getSqlSession().selectList(Namespace + "queryContractPaymentByNoAmount", hashMaps);
        return PageInterceptor.endPage();
    }

    @Override
    public Page<List<VPaymentVO>> queryContractPaymentByNoAmountFcontract(Map<String, Object> hashMaps) {
        // TODO Auto-generated method stub
        if (StrUtil.isNotBlank(hashMaps.get("pageNumber").toString()) && StrUtil.isNotBlank(hashMaps.get("pageSize").toString()))
            PageInterceptor.startPage(Integer.parseInt(hashMaps.get("pageNumber").toString()), Integer.parseInt(hashMaps.get("pageSize").toString()));
        this.getSqlSession().selectList(Namespace + "queryContractPaymentByNoAmountFcontract", hashMaps);
        return PageInterceptor.endPage();
    }

    @Override
    public List<VPaymentVO> queryContractPaymentByNoAmountList(Map<String, Object> hashMaps) {
        // TODO Auto-generated method stub
        List<VPaymentVO> list = this.getSqlSession().selectList(Namespace + "queryContractPaymentByNoAmount", hashMaps);
        return list;
    }

    @Override
    public List<VPaymentVOExport> queryContractPaymentExportByNoAmountList(Map<String, Object> hashMaps) {
        // TODO Auto-generated method stub
        List<VPaymentVOExport> list = this.getSqlSession().selectList(Namespace + "queryContractPaymentExportByNoAmount", hashMaps);
        return list;
    }

    @Override
    public VPaymentVO queryPaymentContractById(Map<String, Object> map) {
        return this.getSqlSession().selectOne(Namespace + "queryPaymentContractById", map);
    }

    @Override
    public int updateBillamountIdIsNullByBillamountId(String BillamountId) {
        // TODO Auto-generated method stub
        return this.getSqlSession().update(Namespace + "updateBillamountIdIsNullByBillamountId", BillamountId);
    }

    @Override
    public int updateBillamountIdIsNullByBillamountDetailId(String BillamountId) {
        // TODO Auto-generated method stub
        return this.getSqlSession().update(Namespace + "updateBillamountIdIsNullByBillamountDetailId", BillamountId);
    }

    @Override
    public int updateStateToSendBack(Map<String, Object> map) {
        return this.getSqlSession().update(Namespace + "updateStateToSendBack", map);
    }

    @Override
    public List<Map<String, String>> queryPaymentDetailByCondition(Map<String, Object> map) {
        // TODO Auto-generated method stub
        return this.getSqlSession().selectList(Namespace + "queryRentPaymentDetailByCondition", map);
    }

    @Override
    public List<String> queryRentPayMentBySupplierMap(Map<String, Object> maps) {
        return this.getSqlSession().selectList(Namespace + "queryRentPayMentBySupplierMap", maps);
    }

    @Override
    public List<VSpecialPaymentVOExport> queryAllSpecialPayment(Map<String, Object> hashMaps) {
        return this.getSqlSession().selectList("queryAllSpecialPayment", hashMaps);
    }

    @Override
    public VPaymentVO queryPaymentByBillamountdetailId(String BillamountdetailId) {
        return this.getSqlSession().selectOne(Namespace + "queryPaymentByBillamountdetailId", BillamountdetailId);
    }

    @Override
    public int updateReceiptInfo(VPaymentVO record) {
        return this.getSqlSession().update(Namespace + "updateReceiptInfo", record);
    }


    @Override
    public int updateSupplierInfo(Map<String, Object> paraMap) {
        return this.getSqlSession().update(Namespace + "updateSupplierInfo", paraMap);

    }


    /* (non Javadoc)
     * Title: updateComtractInfo
     * Description:
     * @param paraMap
     * @see com.xunge.dao.selfrent.billaccount.IVPaymentDao#updateComtractInfo(java.util.Map)
     */

    @Override
    public int updateComtractInfo(Map<String, Object> paraMap) {
        return this.getSqlSession().update(Namespace + "updateComtractInfo", paraMap);

    }

    @Override
    public List<VPaymentVO> queryIntersection() {
        return this.getSqlSession().selectList(Namespace + "queryIntersection");
    }

    @Override
    public VPaymentVO queryById(String paymentId) {
        return this.getSqlSession().selectOne(Namespace + "queryById", paymentId);
    }

    @Override
    public List<PaymentOtherFeeDetailVo> queryOtherAmountByPaymentId(String paymentId) {
        return this.getSqlSession().selectList(Namespace + "queryOtherAmountByPaymentId", paymentId);
    }

    @Override
    public PaymentResourceMergeVo queryBaseResourceByPaymentId(String paymentId) {
        return this.getSqlSession().selectOne(Namespace + "queryBaseResourceByPaymentId", paymentId);
    }

    @Override
    public List<VPaymentVO> queryPaymentByBillamountdId(Map<String, Object> map) {
        return this.getSqlSession().selectList(Namespace + "queryPaymentByBillamountdId", map);
    }

    @Override
    public int updateIsBack(Map<String, Object> mapo) {
        return this.getSqlSession().update(Namespace + "updateIsBack", mapo);
    }

    @Override
    public int countBillAccountAndPaymentByStatus(Map<String, Object> map) {
        return this.getSqlSession().selectOne(Namespace + "countBillAccountAndPaymentByStatus", map);
    }


    @Override
    public PageInfo<RentPaymentMutiContractVO> getRentMultiContractInfo(Map<String, Object> map, int pageNumber, int pageSize) {
        PageInterceptor.startPage(pageNumber, pageSize);
        return new PageInfo<>(this.getSqlSession().selectList(Namespace + "getRentMultiContractInfo", map));
    }

    @Override
    public int ifNeedPrvNetCheck(String paymentId) {
        return this.getSqlSession().selectOne(Namespace + "ifNeedPrvNetCheck", paymentId);
    }


    @Override
    public List<DatBaseresource> getBillaccountResources(@Param("billaccountId")String billaccountId){
        return this.getSqlSession().selectList(Namespace + "getBillaccountResources", billaccountId);
    }

    @Override
    public String getCancelReason(String paymentId) {
        return this.getSqlSession().selectOne(Namespace + "getCancelReason", paymentId);
    }

    @Override
    public int updateCancelReason(Map<String, Object> paramMap) {
        return this.getSqlSession().update(Namespace + "updateCancelReason", paramMap);
    }

    @Override
    public BillamountLogVO queryBillAmountLogByRemark(String billAmountCode) {
        return this.getSqlSession().selectOne(Namespace + "queryBillAmountLogByRemark", billAmountCode);
    }

    @Override
    public VPaymentVO queryRentPaymentDatBaseResourceByPaymentId(String paymentId) {
        return this.getSqlSession().selectOne(Namespace + "queryRentPaymentDatBaseResourceByPaymentId", paymentId);
    }
}
