package com.xunge.dao.selfelec;

import com.xunge.model.basedata.PowerDataForPaymentVO;
import com.xunge.model.selfelec.EleBillaccountpowerdata;
import com.xunge.model.selfelec.ElePaymentPowerdataDetail;

import java.util.List;
import java.util.Map;

public interface EleBillaccountpowerdataMapper {
    // 下面是我用到的方法

    int updateByPrimaryKeySelective(EleBillaccountpowerdata record);

    int insertSelective(EleBillaccountpowerdata record);

    /**
     * 当审核通过时更新当前报账点动环站点关联关系
     *
     * @param paraMap
     */
    int updateWhenAuditPass(Map<String, Object> paraMap);

    /**
     * 当审核通过时若之前有审核后删除的记录，记录解除关联时间
     *
     * @param paraMap
     */
    int updateLastRelationEnddate(Map<String, Object> paraMap);

    List<PowerDataForPaymentVO> selectPowerDataByCondition(Map<String, Object> paraMap);

    List<ElePaymentPowerdataDetail> selectPowerDataByBillaccountId(String billaccountId);

    Map<String, String> selectSiteCodeOfPowerDataBySiteCode(String siteCode);

}