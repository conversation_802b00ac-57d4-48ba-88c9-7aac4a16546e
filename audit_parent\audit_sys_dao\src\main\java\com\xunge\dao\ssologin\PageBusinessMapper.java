package com.xunge.dao.ssologin;

import com.xunge.model.ssologin.BusinessInfo;
import com.xunge.model.ssologin.TowerBusinessInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface PageBusinessMapper {

    /**
     * 租费三方塔报账汇总-根据汇总单编码查询id和类型
     * @param billamountCode
     * @return
     */
    List<BusinessInfo> queryRentOrThreeTowerBillamountId(@Param("billamountCode") String billamountCode);

    /**
     * 租费三方塔计提汇总-根据汇总单编码查询id和类型
     * @param billamountCode
     * @return
     */
    List<BusinessInfo> queryRentOrThreeTowerAccrualSummaryId(@Param("billamountCode") String billamountCode);


    /**
     * 铁塔服务费报账汇总-根据汇总单编码查询id
     * @param billamountCode
     * @return
     */
    List<TowerBusinessInfo> queryTowerAccountSummaryId(@Param("billamountCode") String billamountCode);

    /**
     * 油机发电费报账汇总-根据汇总单编码查询id
     * @param billamountCode
     * @return
     */
    List<BusinessInfo> queryOilEleAccountSummaryId(@Param("billamountCode") String billamountCode);

    /**
     * 铁塔服务费计提汇总-根据汇总单编码查询id和类型
     * @param billamountCode
     * @return
     */
    List<TowerBusinessInfo> queryTowerAccrualSummaryId(@Param("billamountCode") String billamountCode);


    /**
     * 铁塔服务费手工计提汇总-根据汇总单编码查询id
     * @param billamountCode
     * @return
     */
    List<BusinessInfo> queryTowerManualAccrualSummaryId(@Param("billamountCode") String billamountCode);

    /**
     * 电费计提汇总-根据汇总单编码查询id
     * @param billamountCode
     * @return
     */
    List<BusinessInfo> queryEleAccrualBillamountId(@Param("billamountCode") String billamountCode);

    /**
     * 电费计提冲销-根据冲销单编码查询id
     * @param offsCode
     * @return
     */
    List<BusinessInfo> queryEleAccrualOffsId(@Param("offsCode") String offsCode);

    /**
     * 电费后付费汇总-根据汇总单编码查询id
     * @param billamountCode
     * @return
     */
    List<BusinessInfo> queryEleBillamountId(@Param("billamountCode") String billamountCode);

    /**
     * 电费核销汇总-根据汇总单编码查询id
     * @param billamountCode
     * @return
     */
    List<BusinessInfo> queryEleVerificationBillamountId(@Param("billamountCode") String billamountCode);

    /**
     * 电费预付费汇总-根据汇总单编码查询id
     * @param billamountCode
     * @return
     */
    List<BusinessInfo> queryEleLoanBillamountId(@Param("billamountCode") String billamountCode);

    /**
     * 电费塔维汇总-根据汇总单编码查询id
     * @param billamountCode
     * @return
     */
    List<BusinessInfo> queryTeleBillamountId(@Param("billamountCode") String billamountCode);

    /**
     * 租费三方塔计提冲销汇总-根据汇总单编码查询id和类型
     * @param billamountCode
     * @return
     */
    List<BusinessInfo> queryRentOrThreeTowerAccrualWriteOffSummaryId(@Param("billamountCode") String billamountCode);

    /**
     * 铁塔服务费计提冲销汇总-根据汇总单编码查询id和类型
     * @param billamountCode
     * @return
     */
    List<TowerBusinessInfo> queryTowerAccrualWriteOffSummaryId(@Param("billamountCode") String billamountCode);
}
