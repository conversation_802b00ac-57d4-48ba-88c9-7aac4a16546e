package com.xunge.dao.selfelec.accrual;

import java.util.Date;
import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.xunge.model.selfelec.accrual.BillaccountNoAccrual;

public interface BillaccountNoAccrualMapper {
    int deleteByPrimaryKey(String noAccrualId);

    int insert(BillaccountNoAccrual record);

    int insertSelective(BillaccountNoAccrual record);

    BillaccountNoAccrual selectByPrimaryKey(String noAccrualId);

    int updateByPrimaryKeySelective(BillaccountNoAccrual record);

    int updateByPrimaryKey(BillaccountNoAccrual record);

    /**根据billaccountId和状态获取不需要计提配置信息
     * @param billaccountId
     * @param noAccrualState
     * @return
     */
    List<BillaccountNoAccrual> findByBillaccountIdState(@Param("billaccountId")String billaccountId,@Param("state")Integer noAccrualState);

    void updateNoAccrualsTakeTime(@Param("accrualId")String accrualId, @Param("takeTime")Date takeTime);
}