package com.xunge.model.basedata;

import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

public class DatContract {
    
    private String contractId;

    private String contractIdRel;

    private String contractStatus;
    private String contractStatusRel;

    
    private String prvId;

    
    private String prvSname;

    
    private String pregId;

    
    private String pregName;

    
    private String regId;

    
    private String regName;

    
    private Integer isDownshare;

    
    private String sysDepId;

    
    private String userId;

    
    private String contractCode;
    private String contractCodeRel;

    
    private String contractName;
    private String contractNameRel;

    
    private Integer contractType;

    
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date contractStartdate;

    
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date contractEnddate;

    
    private Date contractSigndate;

    
    private BigDecimal contractYearquantity;

    
    private String contractCheckname1;

    
    private String contractCheckname2;

    
    private String oldContractId;

    
    private String oldContractCode;

    
    private String contractFlow;

    
    private String contractIntroduction;

    
    private String contractSpaceresource;

    
    private Integer contractState;

    
    private String contractNote;

    
    private Integer dataFrom;

    private Integer dataType;


    public Integer getDataType() {
        return dataType;
    }

    public void setDataType(Integer dataType) {
        this.dataType = dataType;
    }

    
    public String getContractId() {
        return contractId;
    }

    
    public void setContractId(String contractId) {
        this.contractId = contractId == null ? null : contractId.trim();
    }

    
    public String getPrvId() {
        return prvId;
    }

    
    public void setPrvId(String prvId) {
        this.prvId = prvId == null ? null : prvId.trim();
    }

    
    public String getPrvSname() {
        return prvSname;
    }

    
    public void setPrvSname(String prvSname) {
        this.prvSname = prvSname == null ? null : prvSname.trim();
    }

    
    public String getPregId() {
        return pregId;
    }

    
    public void setPregId(String pregId) {
        this.pregId = pregId == null ? null : pregId.trim();
    }

    
    public String getPregName() {
        return pregName;
    }

    
    public void setPregName(String pregName) {
        this.pregName = pregName == null ? null : pregName.trim();
    }

    
    public String getRegId() {
        return regId;
    }

    
    public void setRegId(String regId) {
        this.regId = regId == null ? null : regId.trim();
    }

    
    public String getRegName() {
        return regName;
    }

    
    public void setRegName(String regName) {
        this.regName = regName == null ? null : regName.trim();
    }

    
    public Integer getIsDownshare() {
        return isDownshare;
    }

    
    public void setIsDownshare(Integer isDownshare) {
        this.isDownshare = isDownshare;
    }

    
    public String getSysDepId() {
        return sysDepId;
    }

    
    public void setSysDepId(String sysDepId) {
        this.sysDepId = sysDepId == null ? null : sysDepId.trim();
    }

    
    public String getUserId() {
        return userId;
    }

    
    public void setUserId(String userId) {
        this.userId = userId == null ? null : userId.trim();
    }

    
    public String getContractCode() {
        return contractCode;
    }

    
    public void setContractCode(String contractCode) {
        this.contractCode = contractCode == null ? null : contractCode.trim();
    }

    
    public String getContractName() {
        return contractName;
    }

    
    public void setContractName(String contractName) {
        this.contractName = contractName == null ? null : contractName.trim();
    }

    
    public Integer getContractType() {
        return contractType;
    }

    
    public void setContractType(Integer contractType) {
        this.contractType = contractType;
    }

    
    public Date getContractStartdate() {
        return contractStartdate;
    }

    
    public void setContractStartdate(Date contractStartdate) {
        this.contractStartdate = contractStartdate;
    }

    
    public Date getContractEnddate() {
        return contractEnddate;
    }

    
    public void setContractEnddate(Date contractEnddate) {
        this.contractEnddate = contractEnddate;
    }

    
    public Date getContractSigndate() {
        return contractSigndate;
    }

    
    public void setContractSigndate(Date contractSigndate) {
        this.contractSigndate = contractSigndate;
    }

    
    public BigDecimal getContractYearquantity() {
        return contractYearquantity;
    }

    
    public void setContractYearquantity(BigDecimal contractYearquantity) {
        this.contractYearquantity = contractYearquantity;
    }

    
    public String getContractCheckname1() {
        return contractCheckname1;
    }

    
    public void setContractCheckname1(String contractCheckname1) {
        this.contractCheckname1 = contractCheckname1 == null ? null : contractCheckname1.trim();
    }

    
    public String getContractCheckname2() {
        return contractCheckname2;
    }

    
    public void setContractCheckname2(String contractCheckname2) {
        this.contractCheckname2 = contractCheckname2 == null ? null : contractCheckname2.trim();
    }

    
    public String getOldContractId() {
        return oldContractId;
    }

    
    public void setOldContractId(String oldContractId) {
        this.oldContractId = oldContractId == null ? null : oldContractId.trim();
    }

    
    public String getOldContractCode() {
        return oldContractCode;
    }

    
    public void setOldContractCode(String oldContractCode) {
        this.oldContractCode = oldContractCode == null ? null : oldContractCode.trim();
    }

    
    public String getContractFlow() {
        return contractFlow;
    }

    
    public void setContractFlow(String contractFlow) {
        this.contractFlow = contractFlow == null ? null : contractFlow.trim();
    }

    
    public String getContractIntroduction() {
        return contractIntroduction;
    }

    
    public void setContractIntroduction(String contractIntroduction) {
        this.contractIntroduction = contractIntroduction == null ? null : contractIntroduction.trim();
    }

    
    public String getContractSpaceresource() {
        return contractSpaceresource;
    }

    
    public void setContractSpaceresource(String contractSpaceresource) {
        this.contractSpaceresource = contractSpaceresource == null ? null : contractSpaceresource.trim();
    }

    
    public Integer getContractState() {
        return contractState;
    }

    
    public void setContractState(Integer contractState) {
        this.contractState = contractState;
    }

    
    public String getContractNote() {
        return contractNote;
    }

    
    public void setContractNote(String contractNote) {
        this.contractNote = contractNote == null ? null : contractNote.trim();
    }

    
    public Integer getDataFrom() {
        return dataFrom;
    }

    
    public void setDataFrom(Integer dataFrom) {
        this.dataFrom = dataFrom;
    }

    public String getContractIdRel() {
        return contractIdRel;
    }

    public void setContractIdRel(String contractIdRel) {
        this.contractIdRel = contractIdRel;
    }

    public String getContractStatus() {
        return contractStatus;
    }

    public void setContractStatus(String contractStatus) {
        this.contractStatus = contractStatus;
    }

    public String getContractCodeRel() {
        return contractCodeRel;
    }

    public void setContractCodeRel(String contractCodeRel) {
        this.contractCodeRel = contractCodeRel;
    }

    public String getContractNameRel() {
        return contractNameRel;
    }

    public void setContractNameRel(String contractNameRel) {
        this.contractNameRel = contractNameRel;
    }

    public String getContractStatusRel() {
        return contractStatusRel;
    }

    public void setContractStatusRel(String contractStatusRel) {
        this.contractStatusRel = contractStatusRel;
    }
}