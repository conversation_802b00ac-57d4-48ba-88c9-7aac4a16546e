package com.xunge.dao.finance.ext.accrualmapper;

import com.xunge.model.finance.ext.accClaim.accrual.EleAccrualBillamount;
import com.xunge.model.finance.ext.accClaim.accrual.EleAccrualSecondBillamountDetail;
import com.xunge.model.selfelec.accrual.EleAccrualOtherAmount;
import com.xunge.model.system.user.SysUserVO;

import java.util.List;
import java.util.Map;

public interface EleAccrualBillamountMapper {

    /**
     * 根据计提汇总单ID，查询汇总单信息
     */
    EleAccrualBillamount getEleAccrualBillamountInfo(String billamountId);

    /**
     * 根据计提汇总单ID，查询二次汇总信息
     */
    List<EleAccrualSecondBillamountDetail> getEleAccrualSecondBillamountDetail(String billamountId);

    /**
     * 根据计提汇总单id，更新汇总单信息
     */
    int updateEleAccrualBillamountById(Map<String, Object> map);

    /**
     * 根据计提汇总单id，更新计提单信息
     */
    int updateEleAccrualByBillamountId(Map<String, Object> map);

    List<EleAccrualSecondBillamountDetail> getHisEleAccrualBillamountDetail(String billamountId);

    int updateEleAccrualBillamountByIdNew(Map<String, Object> map);

	List<EleAccrualSecondBillamountDetail> getEleAccrualSecondBillamountPushDetail(String billamountId);

	List<EleAccrualOtherAmount> getEleAccrualSecondOtherPushDetail(String billamountId);

	void updateClaimUser(SysUserVO sysUserVO);
}
