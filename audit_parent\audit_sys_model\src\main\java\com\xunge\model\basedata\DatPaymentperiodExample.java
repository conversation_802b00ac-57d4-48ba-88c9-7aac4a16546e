package com.xunge.model.basedata;

import java.util.ArrayList;
import java.util.List;

public class DatPaymentperiodExample {
    
    protected String orderByClause;

    
    protected boolean distinct;

    
    protected List<Criteria> oredCriteria;

    
    public DatPaymentperiodExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    
    public String getOrderByClause() {
        return orderByClause;
    }

    
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    
    public boolean isDistinct() {
        return distinct;
    }

    
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andPaymentperiodIdIsNull() {
            addCriterion("paymentperiod_id is null");
            return (Criteria) this;
        }

        public Criteria andPaymentperiodIdIsNotNull() {
            addCriterion("paymentperiod_id is not null");
            return (Criteria) this;
        }

        public Criteria andPaymentperiodIdEqualTo(String value) {
            addCriterion("paymentperiod_id =", value, "paymentperiodId");
            return (Criteria) this;
        }

        public Criteria andPaymentperiodIdNotEqualTo(String value) {
            addCriterion("paymentperiod_id <>", value, "paymentperiodId");
            return (Criteria) this;
        }

        public Criteria andPaymentperiodIdGreaterThan(String value) {
            addCriterion("paymentperiod_id >", value, "paymentperiodId");
            return (Criteria) this;
        }

        public Criteria andPaymentperiodIdGreaterThanOrEqualTo(String value) {
            addCriterion("paymentperiod_id >=", value, "paymentperiodId");
            return (Criteria) this;
        }

        public Criteria andPaymentperiodIdLessThan(String value) {
            addCriterion("paymentperiod_id <", value, "paymentperiodId");
            return (Criteria) this;
        }

        public Criteria andPaymentperiodIdLessThanOrEqualTo(String value) {
            addCriterion("paymentperiod_id <=", value, "paymentperiodId");
            return (Criteria) this;
        }

        public Criteria andPaymentperiodIdLike(String value) {
            addCriterion("paymentperiod_id like", value, "paymentperiodId");
            return (Criteria) this;
        }

        public Criteria andPaymentperiodIdNotLike(String value) {
            addCriterion("paymentperiod_id not like", value, "paymentperiodId");
            return (Criteria) this;
        }

        public Criteria andPaymentperiodIdIn(List<String> values) {
            addCriterion("paymentperiod_id in", values, "paymentperiodId");
            return (Criteria) this;
        }

        public Criteria andPaymentperiodIdNotIn(List<String> values) {
            addCriterion("paymentperiod_id not in", values, "paymentperiodId");
            return (Criteria) this;
        }

        public Criteria andPaymentperiodIdBetween(String value1, String value2) {
            addCriterion("paymentperiod_id between", value1, value2, "paymentperiodId");
            return (Criteria) this;
        }

        public Criteria andPaymentperiodIdNotBetween(String value1, String value2) {
            addCriterion("paymentperiod_id not between", value1, value2, "paymentperiodId");
            return (Criteria) this;
        }

        public Criteria andPaymentperiodNameIsNull() {
            addCriterion("paymentperiod_name is null");
            return (Criteria) this;
        }

        public Criteria andPaymentperiodNameIsNotNull() {
            addCriterion("paymentperiod_name is not null");
            return (Criteria) this;
        }

        public Criteria andPaymentperiodNameEqualTo(String value) {
            addCriterion("paymentperiod_name =", value, "paymentperiodName");
            return (Criteria) this;
        }

        public Criteria andPaymentperiodNameNotEqualTo(String value) {
            addCriterion("paymentperiod_name <>", value, "paymentperiodName");
            return (Criteria) this;
        }

        public Criteria andPaymentperiodNameGreaterThan(String value) {
            addCriterion("paymentperiod_name >", value, "paymentperiodName");
            return (Criteria) this;
        }

        public Criteria andPaymentperiodNameGreaterThanOrEqualTo(String value) {
            addCriterion("paymentperiod_name >=", value, "paymentperiodName");
            return (Criteria) this;
        }

        public Criteria andPaymentperiodNameLessThan(String value) {
            addCriterion("paymentperiod_name <", value, "paymentperiodName");
            return (Criteria) this;
        }

        public Criteria andPaymentperiodNameLessThanOrEqualTo(String value) {
            addCriterion("paymentperiod_name <=", value, "paymentperiodName");
            return (Criteria) this;
        }

        public Criteria andPaymentperiodNameLike(String value) {
            addCriterion("paymentperiod_name like", value, "paymentperiodName");
            return (Criteria) this;
        }

        public Criteria andPaymentperiodNameNotLike(String value) {
            addCriterion("paymentperiod_name not like", value, "paymentperiodName");
            return (Criteria) this;
        }

        public Criteria andPaymentperiodNameIn(List<String> values) {
            addCriterion("paymentperiod_name in", values, "paymentperiodName");
            return (Criteria) this;
        }

        public Criteria andPaymentperiodNameNotIn(List<String> values) {
            addCriterion("paymentperiod_name not in", values, "paymentperiodName");
            return (Criteria) this;
        }

        public Criteria andPaymentperiodNameBetween(String value1, String value2) {
            addCriterion("paymentperiod_name between", value1, value2, "paymentperiodName");
            return (Criteria) this;
        }

        public Criteria andPaymentperiodNameNotBetween(String value1, String value2) {
            addCriterion("paymentperiod_name not between", value1, value2, "paymentperiodName");
            return (Criteria) this;
        }

        public Criteria andPaymentperiodValueIsNull() {
            addCriterion("paymentperiod_value is null");
            return (Criteria) this;
        }

        public Criteria andPaymentperiodValueIsNotNull() {
            addCriterion("paymentperiod_value is not null");
            return (Criteria) this;
        }

        public Criteria andPaymentperiodValueEqualTo(Long value) {
            addCriterion("paymentperiod_value =", value, "paymentperiodValue");
            return (Criteria) this;
        }

        public Criteria andPaymentperiodValueNotEqualTo(Long value) {
            addCriterion("paymentperiod_value <>", value, "paymentperiodValue");
            return (Criteria) this;
        }

        public Criteria andPaymentperiodValueGreaterThan(Long value) {
            addCriterion("paymentperiod_value >", value, "paymentperiodValue");
            return (Criteria) this;
        }

        public Criteria andPaymentperiodValueGreaterThanOrEqualTo(Long value) {
            addCriterion("paymentperiod_value >=", value, "paymentperiodValue");
            return (Criteria) this;
        }

        public Criteria andPaymentperiodValueLessThan(Long value) {
            addCriterion("paymentperiod_value <", value, "paymentperiodValue");
            return (Criteria) this;
        }

        public Criteria andPaymentperiodValueLessThanOrEqualTo(Long value) {
            addCriterion("paymentperiod_value <=", value, "paymentperiodValue");
            return (Criteria) this;
        }

        public Criteria andPaymentperiodValueIn(List<Long> values) {
            addCriterion("paymentperiod_value in", values, "paymentperiodValue");
            return (Criteria) this;
        }

        public Criteria andPaymentperiodValueNotIn(List<Long> values) {
            addCriterion("paymentperiod_value not in", values, "paymentperiodValue");
            return (Criteria) this;
        }

        public Criteria andPaymentperiodValueBetween(Long value1, Long value2) {
            addCriterion("paymentperiod_value between", value1, value2, "paymentperiodValue");
            return (Criteria) this;
        }

        public Criteria andPaymentperiodValueNotBetween(Long value1, Long value2) {
            addCriterion("paymentperiod_value not between", value1, value2, "paymentperiodValue");
            return (Criteria) this;
        }

        public Criteria andPaymentperiodUnitIsNull() {
            addCriterion("paymentperiod_unit is null");
            return (Criteria) this;
        }

        public Criteria andPaymentperiodUnitIsNotNull() {
            addCriterion("paymentperiod_unit is not null");
            return (Criteria) this;
        }

        public Criteria andPaymentperiodUnitEqualTo(Integer value) {
            addCriterion("paymentperiod_unit =", value, "paymentperiodUnit");
            return (Criteria) this;
        }

        public Criteria andPaymentperiodUnitNotEqualTo(Integer value) {
            addCriterion("paymentperiod_unit <>", value, "paymentperiodUnit");
            return (Criteria) this;
        }

        public Criteria andPaymentperiodUnitGreaterThan(Integer value) {
            addCriterion("paymentperiod_unit >", value, "paymentperiodUnit");
            return (Criteria) this;
        }

        public Criteria andPaymentperiodUnitGreaterThanOrEqualTo(Integer value) {
            addCriterion("paymentperiod_unit >=", value, "paymentperiodUnit");
            return (Criteria) this;
        }

        public Criteria andPaymentperiodUnitLessThan(Integer value) {
            addCriterion("paymentperiod_unit <", value, "paymentperiodUnit");
            return (Criteria) this;
        }

        public Criteria andPaymentperiodUnitLessThanOrEqualTo(Integer value) {
            addCriterion("paymentperiod_unit <=", value, "paymentperiodUnit");
            return (Criteria) this;
        }

        public Criteria andPaymentperiodUnitIn(List<Integer> values) {
            addCriterion("paymentperiod_unit in", values, "paymentperiodUnit");
            return (Criteria) this;
        }

        public Criteria andPaymentperiodUnitNotIn(List<Integer> values) {
            addCriterion("paymentperiod_unit not in", values, "paymentperiodUnit");
            return (Criteria) this;
        }

        public Criteria andPaymentperiodUnitBetween(Integer value1, Integer value2) {
            addCriterion("paymentperiod_unit between", value1, value2, "paymentperiodUnit");
            return (Criteria) this;
        }

        public Criteria andPaymentperiodUnitNotBetween(Integer value1, Integer value2) {
            addCriterion("paymentperiod_unit not between", value1, value2, "paymentperiodUnit");
            return (Criteria) this;
        }

        public Criteria andPaymentperiodOrderIsNull() {
            addCriterion("paymentperiod_order is null");
            return (Criteria) this;
        }

        public Criteria andPaymentperiodOrderIsNotNull() {
            addCriterion("paymentperiod_order is not null");
            return (Criteria) this;
        }

        public Criteria andPaymentperiodOrderEqualTo(Integer value) {
            addCriterion("paymentperiod_order =", value, "paymentperiodOrder");
            return (Criteria) this;
        }

        public Criteria andPaymentperiodOrderNotEqualTo(Integer value) {
            addCriterion("paymentperiod_order <>", value, "paymentperiodOrder");
            return (Criteria) this;
        }

        public Criteria andPaymentperiodOrderGreaterThan(Integer value) {
            addCriterion("paymentperiod_order >", value, "paymentperiodOrder");
            return (Criteria) this;
        }

        public Criteria andPaymentperiodOrderGreaterThanOrEqualTo(Integer value) {
            addCriterion("paymentperiod_order >=", value, "paymentperiodOrder");
            return (Criteria) this;
        }

        public Criteria andPaymentperiodOrderLessThan(Integer value) {
            addCriterion("paymentperiod_order <", value, "paymentperiodOrder");
            return (Criteria) this;
        }

        public Criteria andPaymentperiodOrderLessThanOrEqualTo(Integer value) {
            addCriterion("paymentperiod_order <=", value, "paymentperiodOrder");
            return (Criteria) this;
        }

        public Criteria andPaymentperiodOrderIn(List<Integer> values) {
            addCriterion("paymentperiod_order in", values, "paymentperiodOrder");
            return (Criteria) this;
        }

        public Criteria andPaymentperiodOrderNotIn(List<Integer> values) {
            addCriterion("paymentperiod_order not in", values, "paymentperiodOrder");
            return (Criteria) this;
        }

        public Criteria andPaymentperiodOrderBetween(Integer value1, Integer value2) {
            addCriterion("paymentperiod_order between", value1, value2, "paymentperiodOrder");
            return (Criteria) this;
        }

        public Criteria andPaymentperiodOrderNotBetween(Integer value1, Integer value2) {
            addCriterion("paymentperiod_order not between", value1, value2, "paymentperiodOrder");
            return (Criteria) this;
        }

        public Criteria andPaymentperiodStateIsNull() {
            addCriterion("paymentperiod_state is null");
            return (Criteria) this;
        }

        public Criteria andPaymentperiodStateIsNotNull() {
            addCriterion("paymentperiod_state is not null");
            return (Criteria) this;
        }

        public Criteria andPaymentperiodStateEqualTo(Integer value) {
            addCriterion("paymentperiod_state =", value, "paymentperiodState");
            return (Criteria) this;
        }

        public Criteria andPaymentperiodStateNotEqualTo(Integer value) {
            addCriterion("paymentperiod_state <>", value, "paymentperiodState");
            return (Criteria) this;
        }

        public Criteria andPaymentperiodStateGreaterThan(Integer value) {
            addCriterion("paymentperiod_state >", value, "paymentperiodState");
            return (Criteria) this;
        }

        public Criteria andPaymentperiodStateGreaterThanOrEqualTo(Integer value) {
            addCriterion("paymentperiod_state >=", value, "paymentperiodState");
            return (Criteria) this;
        }

        public Criteria andPaymentperiodStateLessThan(Integer value) {
            addCriterion("paymentperiod_state <", value, "paymentperiodState");
            return (Criteria) this;
        }

        public Criteria andPaymentperiodStateLessThanOrEqualTo(Integer value) {
            addCriterion("paymentperiod_state <=", value, "paymentperiodState");
            return (Criteria) this;
        }

        public Criteria andPaymentperiodStateIn(List<Integer> values) {
            addCriterion("paymentperiod_state in", values, "paymentperiodState");
            return (Criteria) this;
        }

        public Criteria andPaymentperiodStateNotIn(List<Integer> values) {
            addCriterion("paymentperiod_state not in", values, "paymentperiodState");
            return (Criteria) this;
        }

        public Criteria andPaymentperiodStateBetween(Integer value1, Integer value2) {
            addCriterion("paymentperiod_state between", value1, value2, "paymentperiodState");
            return (Criteria) this;
        }

        public Criteria andPaymentperiodStateNotBetween(Integer value1, Integer value2) {
            addCriterion("paymentperiod_state not between", value1, value2, "paymentperiodState");
            return (Criteria) this;
        }
    }

    
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value) {
            super();
            this.condition = condition;
            this.value = value;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.betweenValue = true;
        }

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }
    }
}