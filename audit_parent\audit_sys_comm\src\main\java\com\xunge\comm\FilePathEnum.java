package com.xunge.comm;

/**
 * 枚举类，定义文件上传的路径常量
 * 只包含固定路径部分，变量部分通过方法参数传入（如省份，日期）
 * 后续添加的所有文件固定路径都按类别添加下列前缀，旧的文件路径不变
 * 公共	/common
 * 电费	/ele
 * 铁塔服务费	/twr
 * 租费三方塔	/rent
 * app	/app
 * 临时文件 /temp
 * 
 * 有效期说明：
 * -1: 永不过期
 * 正整数: 有效期天数
 */
public enum FilePathEnum {
    /**
     * 所有业务通用路径
     */
    FILES("files", -1, "通用业务文件存储路径"),

    /**
     * 公共方法产生的附件、部分网络电费附件
     */
    PAYMENT("files/payment", -1, "支付相关附件存储路径"),

    /**
     * 电费计提附件
     */
    ACCRUAL_FILES("accrualFiles/files", 0, "电费计提相关附件"),

    /**
     * 异步导出明细文件
     */
    TEMP("temp", 0, "临时文件存储路径"),
    
    /**
     * 网络电费、塔维电费缴费单明细导出
     */
    PRV_CODE_TIMESTAMP("", 0, "电费缴费单明细导出"),

    /**
     * 各省电费生成差额计提数据
     */
    ACCRUALCALCULATE("accrualcalculate", -1, "电费差额计提数据"),

    /**
     * 大集中合同同步报文数据
     */
    BIG_CONTRACT("bigContract", -1, "大集中合同同步数据"),

    /**
     * 成本中心同步报文数据
     */
    COST_CENTER("costCneter", -1, "成本中心同步数据"),

    /**
     * 集团财务成本月报数据
     */
    COST_MONTH_RPT_FILES("costMonthRptFiles", -1, "集团财务成本月报数据"),

    /**
     * 用户服务数据
     */
    CUST_SERVICE("cust_service", -1, "用户服务相关数据"),

    /**
     * 数据共享
     */
    DATASHARE("datashare", -1, "数据共享文件"),

    /**
     * 数据巡检
     */
    INSPECT("inspect", 90, "数据巡检结果文件"),

    /**
     * 网优数据
     */
    NETWORK_OPTIMIZATION("networkOptimization", -1, "网络优化相关数据"),

    /**
     * 铁塔服务费场地费重复缴费明细
     */
    PRACE_FEE_LOG("PraceFeeLog", -1, "场地费重复缴费明细"),

    /**
     * 动环站点用电量数据
     */
    SMAP_TEMP_ELECTRICITY("smap/temp/electricity", -1, "动环站点用电量数据"),

    /**
     * smap同步
     */
    SMAP_ORG("smapOrg", -1, "SMAP组织机构同步数据"),

    /**
     * 日耗电量及分摊比例原始采集文件
     */
    TELE("tele", -1, "日耗电量及分摊比例原始数据"),

    /**
     * 铁塔起租与结算单采集
     */
    TOWER("tower", -1, "铁塔起租与结算单数据"),

    /**
     * 铁塔移动侧起租单采集
     */
    TOWER_CTL("tower_ctl", -1, "铁塔移动侧起租单数据"),

    /**
     * 铁塔计提采集
     */
    TOWER_CTL_ACCRUAL("tower_ctl_accrual", -1, "铁塔计提采集数据"),

    /**
     * 铁塔塔类计提文件
     */
    TWR_ACCRUAL("accrual/twrAccrual", -1, "铁塔塔类计提文件"),

    /**
     * 铁塔微站计提文件
     */
    TWR_TINY_ACCRUAL("twrTinyAccrual", -1, "铁塔微站计提文件"),

    /**
     * 综资，集团综资，动环数据
     */
    RESOURCE("resource", -1, "综资、集团综资、动环基础数据"),

    /**
     * 油机发电记录的一些模板xlsx
     */
    TWR("twr", -1, "油机发电记录模板文件"),
    
    /**
     * 起租单对比临时文件，随时可删
     */
    TWR_COMPARE_TEMP("twr/compare/temp",0, "起租单对比临时文件"),

    /**
     * 起租单对比同步铁塔文件
     */
    TWR_COMPARE_TOWER("twr/compare/tower",-1, "起租单对比同步铁塔文件");
    ;
    // 存储固定路径部分
    private final String basePath;
    
    // 文件有效期（天数，-1表示永不过期,0表示随时可删）
    private final int validDays;
    
    // 路径功能描述
    private final String description;
    
    FilePathEnum(String basePath, int validDays, String description) {
        this.basePath = basePath;
        this.validDays = validDays;
        this.description = description;
    }
    
    /**
     * 获取基础路径
     */
    public String getBasePath() {
        return basePath;
    }
    
    /**
     * 获取文件有效期天数
     * @return 文件保留天数，-1表示永不过期
     */
    public int getValidDays() {
        return validDays;
    }
    
    /**
     * 获取路径功能描述
     * @return 路径用途描述
     */
    public String getDescription() {
        return description;
    }
    
    /**
     * 判断文件是否永不过期
     * @return true表示永不过期，false表示有过期时间
     */
    public boolean isNeverExpire() {
        return validDays == -1;
    }
}