package com.xunge.comm.rent.accrual;

public class RentAccrualStateCommon {

    /**
     * 审核状态：未提交
     */
    public static final int RENT_ACCRUAL_AUDIT_STATE_UNCOMMITTED = -1;

    /**
     * 审核状态：审核通过
     */
    public static final int RENT_ACCRUAL_AUDIT_STATE_PASS = 0;

    /**
     * 审核状态：审核未通过
     */
    public static final int RENT_ACCRUAL_AUDIT_STATE_UNPASS = 8;

    /**
     * 审核状态：审核中
     */
    public static final int RENT_ACCRUAL_AUDIT_STATE_AUDITING = 9;

    /**
     * 审核状态：删除审核中
     */
    public static final int RENT_ACCRUAL_AUDIT_STATE_DELETE_AUDITING = 7;

    /**
     * 计提类型：租费-累提累冲
     */
    public static final int RENT_ACCRUAL_MODE_ACC = 1;

    /**
     * 计提类型：三方塔-累提累冲
     */
    public static final int RENT_TOWER_ACCRUAL_MODE_ACC = 2;


    /**
     * 计提类型：租费-差额冲销
     */
    public static final int RENT_ACCRUAL_MODE_DIFF = 3;

    /**
     * 计提类型：三方塔-差额冲销
     */
    public static final int RENT_TOWER_ACCRUAL_MODE_DIFF = 4;

    /**
     * 是否属于到期计提：是
     */
    public static final int RENT_ACCRUAL_EXPIRE_YES = 1;

    /**
     * 是否属于到期计提：否
     */
    public static final int RENT_ACCRUAL_EXPIRE_NO = 0;
}
