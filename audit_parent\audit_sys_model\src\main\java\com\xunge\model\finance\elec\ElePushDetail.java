 package com.xunge.model.finance.elec;

import java.math.BigDecimal;

import lombok.Data;

/** 
* @ClassName: ElePushDetail 
* @Description: 电费汇总推送明细
* @Author：tian
* @Date：2023年3月29日 
*/
 @Data
public class ElePushDetail {

	/**
	 * 汇总单id
	 */
	private String billamountId;
	/**
	 * 其他费用id
	 */
	private String otherAmountId;
	/**
	 * 缴费单，核销，计提code
	 */
	private String paymentCode;
	/**
	 * 类型：1：后付费，2：核销，3：塔维， 4：计提
	 */
	private Integer type;
	/**
	 * 二次汇总编码
	 */
	private String secondBillamountCode;
	/**
	 * 业务活动编码
	 */
	private String activityCode;
	/**
	 * 业务活动名称
	 */
	private String activityName;
	/**
	 * 成本中心编码
	 */
	private String costCenter;
	/**
	 * 市场段
	 */
	private String productCode;
	/**
	 * 产品段
	 */
	private String marketCode;
	/**
	 * 合同编码
	 */
	private String contractCode;
	/**
	 * 是否包干（0：否，1：是）
	 */
	private Integer isIncludeAll;
	/**
	 * 不含税金额
	 */
	private BigDecimal amountNotax;
	/**
	 * 含税金额
	 */
	private BigDecimal amountWithtax;
	/**
	 * 电量
	 */
	private BigDecimal degree;
	/**
	 * 财务接口类型（0：旧接口，1：新接口）
	 */
	private Integer financeType;
}
