package com.xunge.comm.elec;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.util.List;

/**
 * 稽核配置文件
 */
@Component
@Data
public class EleInspectAiProperties implements Serializable {

    /**
     * 稽核报账基础数据信息传报至省公司（多个省份对应同一url。省份之间用,分割，省份与url之间用@分割，多个配置用;分割）
     */
    @Value("#{'${prvCodeInfoApplyAiUrl}'.split(';')}")
    private List<String> prvCodeInfoApplyAiUrl;
    /**
     * 稽核报账图片数据信息传报至研究院AI稽核信息管理系统（多个省份对应同一url。省份之间用,分割，省份与url之间用@分割，多个配置用;分割）
     */
    @Value("#{'${prvCodeImagesApplyAiUrl}'.split(';')}")
    private List<String> prvCodeImagesApplyAiUrl;
    /**
     * ai图片稽核结果业务人员标注信息反馈传报至研究院AI稽核信息管理系统（多个省份对应同一url。省份之间用,分割，省份与url之间用@分割，多个配置用;分割）
     */
    @Value("#{'${prvCodeImagesApplyAiFlagUrl}'.split(';')}")
    private List<String> prvCodeImagesApplyAiFlagUrl;
}
