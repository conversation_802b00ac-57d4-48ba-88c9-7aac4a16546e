package com.xunge.dao.basedata;

import com.xunge.model.basedata.DatElectricmeter;
import com.xunge.model.basedata.DatElectricmeterExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface DatElectricmeterMapper {
    
    int countByExample(DatElectricmeterExample example);

    
    int deleteByExample(DatElectricmeterExample example);

    
    int deleteByPrimaryKey(String meterId);

    
    int insert(DatElectricmeter record);

    
    int insertSelective(DatElectricmeter record);

    
    List<DatElectricmeter> selectByExample(DatElectricmeterExample example);

    
    DatElectricmeter selectByPrimaryKey(String meterId);

    
    int updateByExampleSelective(@Param("record") DatElectricmeter record, @Param("example") DatElectricmeterExample example);

    
    int updateByExample(@Param("record") DatElectricmeter record, @Param("example") DatElectricmeterExample example);

    
    int updateByPrimaryKeySelective(DatElectricmeter record);

    
    int updateByPrimaryKey(DatElectricmeter record);

    DatElectricmeter selectOldMeter(Map<String, String> map);

    /**
     * 更新旧表字段
     *
     * @param meterId 旧表id
     */
    void updateOldMeterFieldById(@Param("meterId") String meterId);

    /**
     * 更新新表字段
     *
     * @param meterId 新表id
     */
    void updateNewMeterFieldById(@Param("meterId") String meterId);

    /**
     * 根据电表查询有换表记录的电表
     * @param meterIds
     * @return
     */
    List<DatElectricmeter> selectMeterByIds(@Param("meterIds") List<String> meterIds);

    /**
     * 查询电表是否被其他电表引用
     *
     * @param meterIds 电表ids
     * @return
     */
    List<DatElectricmeter> selectOldMeterByIds(@Param("meterIds") List<String> meterIds);
}