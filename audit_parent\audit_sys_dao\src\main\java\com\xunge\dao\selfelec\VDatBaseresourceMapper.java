package com.xunge.dao.selfelec;

import com.xunge.model.selfelec.VDatBaseresource;
import com.xunge.model.selfelec.VDatBaseresourceExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface VDatBaseresourceMapper {
    
    int countByExample(VDatBaseresourceExample example);

    
    int deleteByExample(VDatBaseresourceExample example);

    
    int insert(VDatBaseresource record);

    
    int insertSelective(VDatBaseresource record);

    
    List<VDatBaseresource> selectByExample(VDatBaseresourceExample example);

    
    int updateByExampleSelective(@Param("record") VDatBaseresource record, @Param("example") VDatBaseresourceExample example);

    
    int updateByExample(@Param("record") VDatBaseresource record, @Param("example") VDatBaseresourceExample example);
}