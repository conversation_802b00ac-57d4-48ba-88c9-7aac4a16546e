package com.xunge.dao.twrrent.resourceinfo.impl;

import com.xunge.dao.AbstractBaseDao;
import com.xunge.dao.twrrent.resourceinfo.ITowerLinkDao;
import com.xunge.model.towerrent.rentmanager.TowerRegionVO;

import java.util.List;
import java.util.Map;

/**
 * 站址编码关联关系表dao实现类
 *
 * <AUTHOR>
 */
public class TowerLinkDaoImpl extends AbstractBaseDao implements ITowerLinkDao {

    final String Namespace = "com.xunge.dao.towerrent.rentmanager.TowerLinkVOMapper.";

    /**
     * 查询关联关系信息
     */
    @Override
    public List<String> queryAllTowerLink(Map<String, Object> paraMap) {
        return this.getSqlSession().selectList(Namespace + "queryAllTowerLink", paraMap);
    }

    @Override
    public List<TowerRegionVO> querySiteRegion(String prvId) {
        return this.getSqlSession().selectList(Namespace + "querySiteRegion", prvId);
    }

    @Override
    public List<TowerRegionVO> querySourceRegion(String prvId) {
        return this.getSqlSession().selectList(Namespace + "querySourceRegion", prvId);
    }

    @Override
    public List<TowerRegionVO> querySiteRegionWithResType0(String prvId) {
        return this.getSqlSession().selectList(Namespace + "querySiteRegionWithResType0", prvId);
    }

    @Override
    public List<TowerRegionVO> queryOldSiteRegionWithResType0(String prvId) {
        return this.getSqlSession().selectList(Namespace + "querySourceRegionWithResType0", prvId);
    }

    @Override
    public List<TowerRegionVO> querySourceRegionWithResType1(String prvId) {
        return this.getSqlSession().selectList(Namespace + "querySourceRegionWithResType1", prvId);
    }

    @Override
    public List<TowerRegionVO> querySourceRegionWithResType2(String prvId) {
        return this.getSqlSession().selectList(Namespace + "querySourceRegionWithResType2", prvId);
    }

    @Override
    public List<TowerRegionVO> querySourceRegionWithResType3(String prvId) {
        return this.getSqlSession().selectList(Namespace + "querySourceRegionWithResType3", prvId);
    }
}