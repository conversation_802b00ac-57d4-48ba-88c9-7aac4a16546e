package com.xunge.model.basedata;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class DatBaseresourceExample {
    
    protected String orderByClause;

    
    protected boolean distinct;

    
    protected List<Criteria> oredCriteria;

    
    public DatBaseresourceExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    
    public String getOrderByClause() {
        return orderByClause;
    }

    
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    
    public boolean isDistinct() {
        return distinct;
    }

    
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andBaseresourceIdIsNull() {
            addCriterion("baseresource_id is null");
            return (Criteria) this;
        }

        public Criteria andBaseresourceIdIsNotNull() {
            addCriterion("baseresource_id is not null");
            return (Criteria) this;
        }

        public Criteria andBaseresourceIdEqualTo(String value) {
            addCriterion("baseresource_id =", value, "baseresourceId");
            return (Criteria) this;
        }

        public Criteria andBaseresourceIdNotEqualTo(String value) {
            addCriterion("baseresource_id <>", value, "baseresourceId");
            return (Criteria) this;
        }

        public Criteria andBaseresourceIdGreaterThan(String value) {
            addCriterion("baseresource_id >", value, "baseresourceId");
            return (Criteria) this;
        }

        public Criteria andBaseresourceIdGreaterThanOrEqualTo(String value) {
            addCriterion("baseresource_id >=", value, "baseresourceId");
            return (Criteria) this;
        }

        public Criteria andBaseresourceIdLessThan(String value) {
            addCriterion("baseresource_id <", value, "baseresourceId");
            return (Criteria) this;
        }

        public Criteria andBaseresourceIdLessThanOrEqualTo(String value) {
            addCriterion("baseresource_id <=", value, "baseresourceId");
            return (Criteria) this;
        }

        public Criteria andBaseresourceIdLike(String value) {
            addCriterion("baseresource_id like", value, "baseresourceId");
            return (Criteria) this;
        }

        public Criteria andBaseresourceIdNotLike(String value) {
            addCriterion("baseresource_id not like", value, "baseresourceId");
            return (Criteria) this;
        }

        public Criteria andBaseresourceIdIn(List<String> values) {
            addCriterion("baseresource_id in", values, "baseresourceId");
            return (Criteria) this;
        }

        public Criteria andBaseresourceIdNotIn(List<String> values) {
            addCriterion("baseresource_id not in", values, "baseresourceId");
            return (Criteria) this;
        }

        public Criteria andBaseresourceIdBetween(String value1, String value2) {
            addCriterion("baseresource_id between", value1, value2, "baseresourceId");
            return (Criteria) this;
        }

        public Criteria andBaseresourceIdNotBetween(String value1, String value2) {
            addCriterion("baseresource_id not between", value1, value2, "baseresourceId");
            return (Criteria) this;
        }

        public Criteria andBasesiteIdIsNull() {
            addCriterion("basesite_id is null");
            return (Criteria) this;
        }

        public Criteria andBasesiteIdIsNotNull() {
            addCriterion("basesite_id is not null");
            return (Criteria) this;
        }

        public Criteria andBasesiteIdEqualTo(String value) {
            addCriterion("basesite_id =", value, "basesiteId");
            return (Criteria) this;
        }

        public Criteria andBasesiteIdNotEqualTo(String value) {
            addCriterion("basesite_id <>", value, "basesiteId");
            return (Criteria) this;
        }

        public Criteria andBasesiteIdGreaterThan(String value) {
            addCriterion("basesite_id >", value, "basesiteId");
            return (Criteria) this;
        }

        public Criteria andBasesiteIdGreaterThanOrEqualTo(String value) {
            addCriterion("basesite_id >=", value, "basesiteId");
            return (Criteria) this;
        }

        public Criteria andBasesiteIdLessThan(String value) {
            addCriterion("basesite_id <", value, "basesiteId");
            return (Criteria) this;
        }

        public Criteria andBasesiteIdLessThanOrEqualTo(String value) {
            addCriterion("basesite_id <=", value, "basesiteId");
            return (Criteria) this;
        }

        public Criteria andBasesiteIdLike(String value) {
            addCriterion("basesite_id like", value, "basesiteId");
            return (Criteria) this;
        }

        public Criteria andBasesiteIdNotLike(String value) {
            addCriterion("basesite_id not like", value, "basesiteId");
            return (Criteria) this;
        }

        public Criteria andBasesiteIdIn(List<String> values) {
            addCriterion("basesite_id in", values, "basesiteId");
            return (Criteria) this;
        }

        public Criteria andBasesiteIdNotIn(List<String> values) {
            addCriterion("basesite_id not in", values, "basesiteId");
            return (Criteria) this;
        }

        public Criteria andBasesiteIdBetween(String value1, String value2) {
            addCriterion("basesite_id between", value1, value2, "basesiteId");
            return (Criteria) this;
        }

        public Criteria andBasesiteIdNotBetween(String value1, String value2) {
            addCriterion("basesite_id not between", value1, value2, "basesiteId");
            return (Criteria) this;
        }

        public Criteria andPrvIdIsNull() {
            addCriterion("prv_id is null");
            return (Criteria) this;
        }

        public Criteria andPrvIdIsNotNull() {
            addCriterion("prv_id is not null");
            return (Criteria) this;
        }

        public Criteria andPrvIdEqualTo(String value) {
            addCriterion("prv_id =", value, "prvId");
            return (Criteria) this;
        }

        public Criteria andPrvIdNotEqualTo(String value) {
            addCriterion("prv_id <>", value, "prvId");
            return (Criteria) this;
        }

        public Criteria andPrvIdGreaterThan(String value) {
            addCriterion("prv_id >", value, "prvId");
            return (Criteria) this;
        }

        public Criteria andPrvIdGreaterThanOrEqualTo(String value) {
            addCriterion("prv_id >=", value, "prvId");
            return (Criteria) this;
        }

        public Criteria andPrvIdLessThan(String value) {
            addCriterion("prv_id <", value, "prvId");
            return (Criteria) this;
        }

        public Criteria andPrvIdLessThanOrEqualTo(String value) {
            addCriterion("prv_id <=", value, "prvId");
            return (Criteria) this;
        }

        public Criteria andPrvIdLike(String value) {
            addCriterion("prv_id like", value, "prvId");
            return (Criteria) this;
        }

        public Criteria andPrvIdNotLike(String value) {
            addCriterion("prv_id not like", value, "prvId");
            return (Criteria) this;
        }

        public Criteria andPrvIdIn(List<String> values) {
            addCriterion("prv_id in", values, "prvId");
            return (Criteria) this;
        }

        public Criteria andPrvIdNotIn(List<String> values) {
            addCriterion("prv_id not in", values, "prvId");
            return (Criteria) this;
        }

        public Criteria andPrvIdBetween(String value1, String value2) {
            addCriterion("prv_id between", value1, value2, "prvId");
            return (Criteria) this;
        }

        public Criteria andPrvIdNotBetween(String value1, String value2) {
            addCriterion("prv_id not between", value1, value2, "prvId");
            return (Criteria) this;
        }

        public Criteria andRegIdIsNull() {
            addCriterion("reg_id is null");
            return (Criteria) this;
        }

        public Criteria andRegIdIsNotNull() {
            addCriterion("reg_id is not null");
            return (Criteria) this;
        }

        public Criteria andRegIdEqualTo(String value) {
            addCriterion("reg_id =", value, "regId");
            return (Criteria) this;
        }

        public Criteria andRegIdNotEqualTo(String value) {
            addCriterion("reg_id <>", value, "regId");
            return (Criteria) this;
        }

        public Criteria andRegIdGreaterThan(String value) {
            addCriterion("reg_id >", value, "regId");
            return (Criteria) this;
        }

        public Criteria andRegIdGreaterThanOrEqualTo(String value) {
            addCriterion("reg_id >=", value, "regId");
            return (Criteria) this;
        }

        public Criteria andRegIdLessThan(String value) {
            addCriterion("reg_id <", value, "regId");
            return (Criteria) this;
        }

        public Criteria andRegIdLessThanOrEqualTo(String value) {
            addCriterion("reg_id <=", value, "regId");
            return (Criteria) this;
        }

        public Criteria andRegIdLike(String value) {
            addCriterion("reg_id like", value, "regId");
            return (Criteria) this;
        }

        public Criteria andRegIdNotLike(String value) {
            addCriterion("reg_id not like", value, "regId");
            return (Criteria) this;
        }

        public Criteria andRegIdIn(List<String> values) {
            addCriterion("reg_id in", values, "regId");
            return (Criteria) this;
        }

        public Criteria andRegIdNotIn(List<String> values) {
            addCriterion("reg_id not in", values, "regId");
            return (Criteria) this;
        }

        public Criteria andRegIdBetween(String value1, String value2) {
            addCriterion("reg_id between", value1, value2, "regId");
            return (Criteria) this;
        }

        public Criteria andRegIdNotBetween(String value1, String value2) {
            addCriterion("reg_id not between", value1, value2, "regId");
            return (Criteria) this;
        }

        public Criteria andBaseresourceTypeIsNull() {
            addCriterion("baseresource_type is null");
            return (Criteria) this;
        }

        public Criteria andBaseresourceTypeIsNotNull() {
            addCriterion("baseresource_type is not null");
            return (Criteria) this;
        }

        public Criteria andBaseresourceTypeEqualTo(Integer value) {
            addCriterion("baseresource_type =", value, "baseresourceType");
            return (Criteria) this;
        }

        public Criteria andBaseresourceTypeNotEqualTo(Integer value) {
            addCriterion("baseresource_type <>", value, "baseresourceType");
            return (Criteria) this;
        }

        public Criteria andBaseresourceTypeGreaterThan(Integer value) {
            addCriterion("baseresource_type >", value, "baseresourceType");
            return (Criteria) this;
        }

        public Criteria andBaseresourceTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("baseresource_type >=", value, "baseresourceType");
            return (Criteria) this;
        }

        public Criteria andBaseresourceTypeLessThan(Integer value) {
            addCriterion("baseresource_type <", value, "baseresourceType");
            return (Criteria) this;
        }

        public Criteria andBaseresourceTypeLessThanOrEqualTo(Integer value) {
            addCriterion("baseresource_type <=", value, "baseresourceType");
            return (Criteria) this;
        }

        public Criteria andBaseresourceTypeIn(List<Integer> values) {
            addCriterion("baseresource_type in", values, "baseresourceType");
            return (Criteria) this;
        }

        public Criteria andBaseresourceTypeNotIn(List<Integer> values) {
            addCriterion("baseresource_type not in", values, "baseresourceType");
            return (Criteria) this;
        }

        public Criteria andBaseresourceTypeBetween(Integer value1, Integer value2) {
            addCriterion("baseresource_type between", value1, value2, "baseresourceType");
            return (Criteria) this;
        }

        public Criteria andBaseresourceTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("baseresource_type not between", value1, value2, "baseresourceType");
            return (Criteria) this;
        }

        public Criteria andBaseresourceCategoryIsNull() {
            addCriterion("baseresource_category is null");
            return (Criteria) this;
        }

        public Criteria andBaseresourceCategoryIsNotNull() {
            addCriterion("baseresource_category is not null");
            return (Criteria) this;
        }

        public Criteria andBaseresourceCategoryEqualTo(Integer value) {
            addCriterion("baseresource_category =", value, "baseresourceCategory");
            return (Criteria) this;
        }

        public Criteria andBaseresourceCategoryNotEqualTo(Integer value) {
            addCriterion("baseresource_category <>", value, "baseresourceCategory");
            return (Criteria) this;
        }

        public Criteria andBaseresourceCategoryGreaterThan(Integer value) {
            addCriterion("baseresource_category >", value, "baseresourceCategory");
            return (Criteria) this;
        }

        public Criteria andBaseresourceCategoryGreaterThanOrEqualTo(Integer value) {
            addCriterion("baseresource_category >=", value, "baseresourceCategory");
            return (Criteria) this;
        }

        public Criteria andBaseresourceCategoryLessThan(Integer value) {
            addCriterion("baseresource_category <", value, "baseresourceCategory");
            return (Criteria) this;
        }

        public Criteria andBaseresourceCategoryLessThanOrEqualTo(Integer value) {
            addCriterion("baseresource_category <=", value, "baseresourceCategory");
            return (Criteria) this;
        }

        public Criteria andBaseresourceCategoryIn(List<Integer> values) {
            addCriterion("baseresource_category in", values, "baseresourceCategory");
            return (Criteria) this;
        }

        public Criteria andBaseresourceCategoryNotIn(List<Integer> values) {
            addCriterion("baseresource_category not in", values, "baseresourceCategory");
            return (Criteria) this;
        }

        public Criteria andBaseresourceCategoryBetween(Integer value1, Integer value2) {
            addCriterion("baseresource_category between", value1, value2, "baseresourceCategory");
            return (Criteria) this;
        }

        public Criteria andBaseresourceCategoryNotBetween(Integer value1, Integer value2) {
            addCriterion("baseresource_category not between", value1, value2, "baseresourceCategory");
            return (Criteria) this;
        }

        public Criteria andBaseresourceCuidIsNull() {
            addCriterion("baseresource_cuid is null");
            return (Criteria) this;
        }

        public Criteria andBaseresourceCuidIsNotNull() {
            addCriterion("baseresource_cuid is not null");
            return (Criteria) this;
        }

        public Criteria andBaseresourceCuidEqualTo(String value) {
            addCriterion("baseresource_cuid =", value, "baseresourceCuid");
            return (Criteria) this;
        }

        public Criteria andBaseresourceCuidNotEqualTo(String value) {
            addCriterion("baseresource_cuid <>", value, "baseresourceCuid");
            return (Criteria) this;
        }

        public Criteria andBaseresourceCuidGreaterThan(String value) {
            addCriterion("baseresource_cuid >", value, "baseresourceCuid");
            return (Criteria) this;
        }

        public Criteria andBaseresourceCuidGreaterThanOrEqualTo(String value) {
            addCriterion("baseresource_cuid >=", value, "baseresourceCuid");
            return (Criteria) this;
        }

        public Criteria andBaseresourceCuidLessThan(String value) {
            addCriterion("baseresource_cuid <", value, "baseresourceCuid");
            return (Criteria) this;
        }

        public Criteria andBaseresourceCuidLessThanOrEqualTo(String value) {
            addCriterion("baseresource_cuid <=", value, "baseresourceCuid");
            return (Criteria) this;
        }

        public Criteria andBaseresourceCuidLike(String value) {
            addCriterion("baseresource_cuid like", value, "baseresourceCuid");
            return (Criteria) this;
        }

        public Criteria andBaseresourceCuidNotLike(String value) {
            addCriterion("baseresource_cuid not like", value, "baseresourceCuid");
            return (Criteria) this;
        }

        public Criteria andBaseresourceCuidIn(List<String> values) {
            addCriterion("baseresource_cuid in", values, "baseresourceCuid");
            return (Criteria) this;
        }

        public Criteria andBaseresourceCuidNotIn(List<String> values) {
            addCriterion("baseresource_cuid not in", values, "baseresourceCuid");
            return (Criteria) this;
        }

        public Criteria andBaseresourceCuidBetween(String value1, String value2) {
            addCriterion("baseresource_cuid between", value1, value2, "baseresourceCuid");
            return (Criteria) this;
        }

        public Criteria andBaseresourceCuidNotBetween(String value1, String value2) {
            addCriterion("baseresource_cuid not between", value1, value2, "baseresourceCuid");
            return (Criteria) this;
        }

        public Criteria andBaseresourceCodeIsNull() {
            addCriterion("baseresource_code is null");
            return (Criteria) this;
        }

        public Criteria andBaseresourceCodeIsNotNull() {
            addCriterion("baseresource_code is not null");
            return (Criteria) this;
        }

        public Criteria andBaseresourceCodeEqualTo(String value) {
            addCriterion("baseresource_code =", value, "baseresourceCode");
            return (Criteria) this;
        }

        public Criteria andBaseresourceCodeNotEqualTo(String value) {
            addCriterion("baseresource_code <>", value, "baseresourceCode");
            return (Criteria) this;
        }

        public Criteria andBaseresourceCodeGreaterThan(String value) {
            addCriterion("baseresource_code >", value, "baseresourceCode");
            return (Criteria) this;
        }

        public Criteria andBaseresourceCodeGreaterThanOrEqualTo(String value) {
            addCriterion("baseresource_code >=", value, "baseresourceCode");
            return (Criteria) this;
        }

        public Criteria andBaseresourceCodeLessThan(String value) {
            addCriterion("baseresource_code <", value, "baseresourceCode");
            return (Criteria) this;
        }

        public Criteria andBaseresourceCodeLessThanOrEqualTo(String value) {
            addCriterion("baseresource_code <=", value, "baseresourceCode");
            return (Criteria) this;
        }

        public Criteria andBaseresourceCodeLike(String value) {
            addCriterion("baseresource_code like", value, "baseresourceCode");
            return (Criteria) this;
        }

        public Criteria andBaseresourceCodeNotLike(String value) {
            addCriterion("baseresource_code not like", value, "baseresourceCode");
            return (Criteria) this;
        }

        public Criteria andBaseresourceCodeIn(List<String> values) {
            addCriterion("baseresource_code in", values, "baseresourceCode");
            return (Criteria) this;
        }

        public Criteria andBaseresourceCodeNotIn(List<String> values) {
            addCriterion("baseresource_code not in", values, "baseresourceCode");
            return (Criteria) this;
        }

        public Criteria andBaseresourceCodeBetween(String value1, String value2) {
            addCriterion("baseresource_code between", value1, value2, "baseresourceCode");
            return (Criteria) this;
        }

        public Criteria andBaseresourceCodeNotBetween(String value1, String value2) {
            addCriterion("baseresource_code not between", value1, value2, "baseresourceCode");
            return (Criteria) this;
        }

        public Criteria andBaseresourceNameIsNull() {
            addCriterion("baseresource_name is null");
            return (Criteria) this;
        }

        public Criteria andBaseresourceNameIsNotNull() {
            addCriterion("baseresource_name is not null");
            return (Criteria) this;
        }

        public Criteria andBaseresourceNameEqualTo(String value) {
            addCriterion("baseresource_name =", value, "baseresourceName");
            return (Criteria) this;
        }

        public Criteria andBaseresourceNameNotEqualTo(String value) {
            addCriterion("baseresource_name <>", value, "baseresourceName");
            return (Criteria) this;
        }

        public Criteria andBaseresourceNameGreaterThan(String value) {
            addCriterion("baseresource_name >", value, "baseresourceName");
            return (Criteria) this;
        }

        public Criteria andBaseresourceNameGreaterThanOrEqualTo(String value) {
            addCriterion("baseresource_name >=", value, "baseresourceName");
            return (Criteria) this;
        }

        public Criteria andBaseresourceNameLessThan(String value) {
            addCriterion("baseresource_name <", value, "baseresourceName");
            return (Criteria) this;
        }

        public Criteria andBaseresourceNameLessThanOrEqualTo(String value) {
            addCriterion("baseresource_name <=", value, "baseresourceName");
            return (Criteria) this;
        }

        public Criteria andBaseresourceNameLike(String value) {
            addCriterion("baseresource_name like", value, "baseresourceName");
            return (Criteria) this;
        }

        public Criteria andBaseresourceNameNotLike(String value) {
            addCriterion("baseresource_name not like", value, "baseresourceName");
            return (Criteria) this;
        }

        public Criteria andBaseresourceNameIn(List<String> values) {
            addCriterion("baseresource_name in", values, "baseresourceName");
            return (Criteria) this;
        }

        public Criteria andBaseresourceNameNotIn(List<String> values) {
            addCriterion("baseresource_name not in", values, "baseresourceName");
            return (Criteria) this;
        }

        public Criteria andBaseresourceNameBetween(String value1, String value2) {
            addCriterion("baseresource_name between", value1, value2, "baseresourceName");
            return (Criteria) this;
        }

        public Criteria andBaseresourceNameNotBetween(String value1, String value2) {
            addCriterion("baseresource_name not between", value1, value2, "baseresourceName");
            return (Criteria) this;
        }

        public Criteria andBaseresourceAddressIsNull() {
            addCriterion("baseresource_address is null");
            return (Criteria) this;
        }

        public Criteria andBaseresourceAddressIsNotNull() {
            addCriterion("baseresource_address is not null");
            return (Criteria) this;
        }

        public Criteria andBaseresourceAddressEqualTo(String value) {
            addCriterion("baseresource_address =", value, "baseresourceAddress");
            return (Criteria) this;
        }

        public Criteria andBaseresourceAddressNotEqualTo(String value) {
            addCriterion("baseresource_address <>", value, "baseresourceAddress");
            return (Criteria) this;
        }

        public Criteria andBaseresourceAddressGreaterThan(String value) {
            addCriterion("baseresource_address >", value, "baseresourceAddress");
            return (Criteria) this;
        }

        public Criteria andBaseresourceAddressGreaterThanOrEqualTo(String value) {
            addCriterion("baseresource_address >=", value, "baseresourceAddress");
            return (Criteria) this;
        }

        public Criteria andBaseresourceAddressLessThan(String value) {
            addCriterion("baseresource_address <", value, "baseresourceAddress");
            return (Criteria) this;
        }

        public Criteria andBaseresourceAddressLessThanOrEqualTo(String value) {
            addCriterion("baseresource_address <=", value, "baseresourceAddress");
            return (Criteria) this;
        }

        public Criteria andBaseresourceAddressLike(String value) {
            addCriterion("baseresource_address like", value, "baseresourceAddress");
            return (Criteria) this;
        }

        public Criteria andBaseresourceAddressNotLike(String value) {
            addCriterion("baseresource_address not like", value, "baseresourceAddress");
            return (Criteria) this;
        }

        public Criteria andBaseresourceAddressIn(List<String> values) {
            addCriterion("baseresource_address in", values, "baseresourceAddress");
            return (Criteria) this;
        }

        public Criteria andBaseresourceAddressNotIn(List<String> values) {
            addCriterion("baseresource_address not in", values, "baseresourceAddress");
            return (Criteria) this;
        }

        public Criteria andBaseresourceAddressBetween(String value1, String value2) {
            addCriterion("baseresource_address between", value1, value2, "baseresourceAddress");
            return (Criteria) this;
        }

        public Criteria andBaseresourceAddressNotBetween(String value1, String value2) {
            addCriterion("baseresource_address not between", value1, value2, "baseresourceAddress");
            return (Criteria) this;
        }

        public Criteria andBaseresourceAreaIsNull() {
            addCriterion("baseresource_area is null");
            return (Criteria) this;
        }

        public Criteria andBaseresourceAreaIsNotNull() {
            addCriterion("baseresource_area is not null");
            return (Criteria) this;
        }

        public Criteria andBaseresourceAreaEqualTo(Long value) {
            addCriterion("baseresource_area =", value, "baseresourceArea");
            return (Criteria) this;
        }

        public Criteria andBaseresourceAreaNotEqualTo(Long value) {
            addCriterion("baseresource_area <>", value, "baseresourceArea");
            return (Criteria) this;
        }

        public Criteria andBaseresourceAreaGreaterThan(Long value) {
            addCriterion("baseresource_area >", value, "baseresourceArea");
            return (Criteria) this;
        }

        public Criteria andBaseresourceAreaGreaterThanOrEqualTo(Long value) {
            addCriterion("baseresource_area >=", value, "baseresourceArea");
            return (Criteria) this;
        }

        public Criteria andBaseresourceAreaLessThan(Long value) {
            addCriterion("baseresource_area <", value, "baseresourceArea");
            return (Criteria) this;
        }

        public Criteria andBaseresourceAreaLessThanOrEqualTo(Long value) {
            addCriterion("baseresource_area <=", value, "baseresourceArea");
            return (Criteria) this;
        }

        public Criteria andBaseresourceAreaIn(List<Long> values) {
            addCriterion("baseresource_area in", values, "baseresourceArea");
            return (Criteria) this;
        }

        public Criteria andBaseresourceAreaNotIn(List<Long> values) {
            addCriterion("baseresource_area not in", values, "baseresourceArea");
            return (Criteria) this;
        }

        public Criteria andBaseresourceAreaBetween(Long value1, Long value2) {
            addCriterion("baseresource_area between", value1, value2, "baseresourceArea");
            return (Criteria) this;
        }

        public Criteria andBaseresourceAreaNotBetween(Long value1, Long value2) {
            addCriterion("baseresource_area not between", value1, value2, "baseresourceArea");
            return (Criteria) this;
        }

        public Criteria andBaseresourceOpendateIsNull() {
            addCriterion("baseresource_opendate is null");
            return (Criteria) this;
        }

        public Criteria andBaseresourceOpendateIsNotNull() {
            addCriterion("baseresource_opendate is not null");
            return (Criteria) this;
        }

        public Criteria andBaseresourceOpendateEqualTo(Date value) {
            addCriterion("baseresource_opendate =", value, "baseresourceOpendate");
            return (Criteria) this;
        }

        public Criteria andBaseresourceOpendateNotEqualTo(Date value) {
            addCriterion("baseresource_opendate <>", value, "baseresourceOpendate");
            return (Criteria) this;
        }

        public Criteria andBaseresourceOpendateGreaterThan(Date value) {
            addCriterion("baseresource_opendate >", value, "baseresourceOpendate");
            return (Criteria) this;
        }

        public Criteria andBaseresourceOpendateGreaterThanOrEqualTo(Date value) {
            addCriterion("baseresource_opendate >=", value, "baseresourceOpendate");
            return (Criteria) this;
        }

        public Criteria andBaseresourceOpendateLessThan(Date value) {
            addCriterion("baseresource_opendate <", value, "baseresourceOpendate");
            return (Criteria) this;
        }

        public Criteria andBaseresourceOpendateLessThanOrEqualTo(Date value) {
            addCriterion("baseresource_opendate <=", value, "baseresourceOpendate");
            return (Criteria) this;
        }

        public Criteria andBaseresourceOpendateIn(List<Date> values) {
            addCriterion("baseresource_opendate in", values, "baseresourceOpendate");
            return (Criteria) this;
        }

        public Criteria andBaseresourceOpendateNotIn(List<Date> values) {
            addCriterion("baseresource_opendate not in", values, "baseresourceOpendate");
            return (Criteria) this;
        }

        public Criteria andBaseresourceOpendateBetween(Date value1, Date value2) {
            addCriterion("baseresource_opendate between", value1, value2, "baseresourceOpendate");
            return (Criteria) this;
        }

        public Criteria andBaseresourceOpendateNotBetween(Date value1, Date value2) {
            addCriterion("baseresource_opendate not between", value1, value2, "baseresourceOpendate");
            return (Criteria) this;
        }

        public Criteria andBaseresourceStopdateIsNull() {
            addCriterion("baseresource_stopdate is null");
            return (Criteria) this;
        }

        public Criteria andBaseresourceStopdateIsNotNull() {
            addCriterion("baseresource_stopdate is not null");
            return (Criteria) this;
        }

        public Criteria andBaseresourceStopdateEqualTo(Date value) {
            addCriterion("baseresource_stopdate =", value, "baseresourceStopdate");
            return (Criteria) this;
        }

        public Criteria andBaseresourceStopdateNotEqualTo(Date value) {
            addCriterion("baseresource_stopdate <>", value, "baseresourceStopdate");
            return (Criteria) this;
        }

        public Criteria andBaseresourceStopdateGreaterThan(Date value) {
            addCriterion("baseresource_stopdate >", value, "baseresourceStopdate");
            return (Criteria) this;
        }

        public Criteria andBaseresourceStopdateGreaterThanOrEqualTo(Date value) {
            addCriterion("baseresource_stopdate >=", value, "baseresourceStopdate");
            return (Criteria) this;
        }

        public Criteria andBaseresourceStopdateLessThan(Date value) {
            addCriterion("baseresource_stopdate <", value, "baseresourceStopdate");
            return (Criteria) this;
        }

        public Criteria andBaseresourceStopdateLessThanOrEqualTo(Date value) {
            addCriterion("baseresource_stopdate <=", value, "baseresourceStopdate");
            return (Criteria) this;
        }

        public Criteria andBaseresourceStopdateIn(List<Date> values) {
            addCriterion("baseresource_stopdate in", values, "baseresourceStopdate");
            return (Criteria) this;
        }

        public Criteria andBaseresourceStopdateNotIn(List<Date> values) {
            addCriterion("baseresource_stopdate not in", values, "baseresourceStopdate");
            return (Criteria) this;
        }

        public Criteria andBaseresourceStopdateBetween(Date value1, Date value2) {
            addCriterion("baseresource_stopdate between", value1, value2, "baseresourceStopdate");
            return (Criteria) this;
        }

        public Criteria andBaseresourceStopdateNotBetween(Date value1, Date value2) {
            addCriterion("baseresource_stopdate not between", value1, value2, "baseresourceStopdate");
            return (Criteria) this;
        }

        public Criteria andRoomOwnerIsNull() {
            addCriterion("room_owner is null");
            return (Criteria) this;
        }

        public Criteria andRoomOwnerIsNotNull() {
            addCriterion("room_owner is not null");
            return (Criteria) this;
        }

        public Criteria andRoomOwnerEqualTo(String value) {
            addCriterion("room_owner =", value, "roomOwner");
            return (Criteria) this;
        }

        public Criteria andRoomOwnerNotEqualTo(String value) {
            addCriterion("room_owner <>", value, "roomOwner");
            return (Criteria) this;
        }

        public Criteria andRoomOwnerGreaterThan(String value) {
            addCriterion("room_owner >", value, "roomOwner");
            return (Criteria) this;
        }

        public Criteria andRoomOwnerGreaterThanOrEqualTo(String value) {
            addCriterion("room_owner >=", value, "roomOwner");
            return (Criteria) this;
        }

        public Criteria andRoomOwnerLessThan(String value) {
            addCriterion("room_owner <", value, "roomOwner");
            return (Criteria) this;
        }

        public Criteria andRoomOwnerLessThanOrEqualTo(String value) {
            addCriterion("room_owner <=", value, "roomOwner");
            return (Criteria) this;
        }

        public Criteria andRoomOwnerIn(List<String> values) {
            addCriterion("room_owner in", values, "roomOwner");
            return (Criteria) this;
        }

        public Criteria andRoomOwnerNotIn(List<String> values) {
            addCriterion("room_owner not in", values, "roomOwner");
            return (Criteria) this;
        }

        public Criteria andRoomOwnerBetween(String value1, String value2) {
            addCriterion("room_owner between", value1, value2, "roomOwner");
            return (Criteria) this;
        }

        public Criteria andRoomOwnerNotBetween(String value1, String value2) {
            addCriterion("room_owner not between", value1, value2, "roomOwner");
            return (Criteria) this;
        }

        public Criteria andRoomPropertyIsNull() {
            addCriterion("room_property is null");
            return (Criteria) this;
        }

        public Criteria andRoomPropertyIsNotNull() {
            addCriterion("room_property is not null");
            return (Criteria) this;
        }

        public Criteria andRoomPropertyEqualTo(Integer value) {
            addCriterion("room_property =", value, "roomProperty");
            return (Criteria) this;
        }

        public Criteria andRoomPropertyNotEqualTo(Integer value) {
            addCriterion("room_property <>", value, "roomProperty");
            return (Criteria) this;
        }

        public Criteria andRoomPropertyGreaterThan(Integer value) {
            addCriterion("room_property >", value, "roomProperty");
            return (Criteria) this;
        }

        public Criteria andRoomPropertyGreaterThanOrEqualTo(Integer value) {
            addCriterion("room_property >=", value, "roomProperty");
            return (Criteria) this;
        }

        public Criteria andRoomPropertyLessThan(Integer value) {
            addCriterion("room_property <", value, "roomProperty");
            return (Criteria) this;
        }

        public Criteria andRoomPropertyLessThanOrEqualTo(Integer value) {
            addCriterion("room_property <=", value, "roomProperty");
            return (Criteria) this;
        }

        public Criteria andRoomPropertyIn(List<Integer> values) {
            addCriterion("room_property in", values, "roomProperty");
            return (Criteria) this;
        }

        public Criteria andRoomPropertyNotIn(List<Integer> values) {
            addCriterion("room_property not in", values, "roomProperty");
            return (Criteria) this;
        }

        public Criteria andRoomPropertyBetween(Integer value1, Integer value2) {
            addCriterion("room_property between", value1, value2, "roomProperty");
            return (Criteria) this;
        }

        public Criteria andRoomPropertyNotBetween(Integer value1, Integer value2) {
            addCriterion("room_property not between", value1, value2, "roomProperty");
            return (Criteria) this;
        }

        public Criteria andRoomShareIsNull() {
            addCriterion("room_share is null");
            return (Criteria) this;
        }

        public Criteria andRoomShareIsNotNull() {
            addCriterion("room_share is not null");
            return (Criteria) this;
        }

        public Criteria andRoomShareEqualTo(String value) {
            addCriterion("room_share =", value, "roomShare");
            return (Criteria) this;
        }

        public Criteria andRoomShareNotEqualTo(String value) {
            addCriterion("room_share <>", value, "roomShare");
            return (Criteria) this;
        }

        public Criteria andRoomShareGreaterThan(String value) {
            addCriterion("room_share >", value, "roomShare");
            return (Criteria) this;
        }

        public Criteria andRoomShareGreaterThanOrEqualTo(String value) {
            addCriterion("room_share >=", value, "roomShare");
            return (Criteria) this;
        }

        public Criteria andRoomShareLessThan(String value) {
            addCriterion("room_share <", value, "roomShare");
            return (Criteria) this;
        }

        public Criteria andRoomShareLessThanOrEqualTo(String value) {
            addCriterion("room_share <=", value, "roomShare");
            return (Criteria) this;
        }

        public Criteria andRoomShareIn(List<String> values) {
            addCriterion("room_share in", values, "roomShare");
            return (Criteria) this;
        }

        public Criteria andRoomShareNotIn(List<String> values) {
            addCriterion("room_share not in", values, "roomShare");
            return (Criteria) this;
        }

        public Criteria andRoomShareBetween(String value1, String value2) {
            addCriterion("room_share between", value1, value2, "roomShare");
            return (Criteria) this;
        }

        public Criteria andRoomShareNotBetween(String value1, String value2) {
            addCriterion("room_share not between", value1, value2, "roomShare");
            return (Criteria) this;
        }

        public Criteria andBaseresourceLongitudeIsNull() {
            addCriterion("baseresource_longitude is null");
            return (Criteria) this;
        }

        public Criteria andBaseresourceLongitudeIsNotNull() {
            addCriterion("baseresource_longitude is not null");
            return (Criteria) this;
        }

        public Criteria andBaseresourceLongitudeEqualTo(Long value) {
            addCriterion("baseresource_longitude =", value, "baseresourceLongitude");
            return (Criteria) this;
        }

        public Criteria andBaseresourceLongitudeNotEqualTo(Long value) {
            addCriterion("baseresource_longitude <>", value, "baseresourceLongitude");
            return (Criteria) this;
        }

        public Criteria andBaseresourceLongitudeGreaterThan(Long value) {
            addCriterion("baseresource_longitude >", value, "baseresourceLongitude");
            return (Criteria) this;
        }

        public Criteria andBaseresourceLongitudeGreaterThanOrEqualTo(Long value) {
            addCriterion("baseresource_longitude >=", value, "baseresourceLongitude");
            return (Criteria) this;
        }

        public Criteria andBaseresourceLongitudeLessThan(Long value) {
            addCriterion("baseresource_longitude <", value, "baseresourceLongitude");
            return (Criteria) this;
        }

        public Criteria andBaseresourceLongitudeLessThanOrEqualTo(Long value) {
            addCriterion("baseresource_longitude <=", value, "baseresourceLongitude");
            return (Criteria) this;
        }

        public Criteria andBaseresourceLongitudeIn(List<Long> values) {
            addCriterion("baseresource_longitude in", values, "baseresourceLongitude");
            return (Criteria) this;
        }

        public Criteria andBaseresourceLongitudeNotIn(List<Long> values) {
            addCriterion("baseresource_longitude not in", values, "baseresourceLongitude");
            return (Criteria) this;
        }

        public Criteria andBaseresourceLongitudeBetween(Long value1, Long value2) {
            addCriterion("baseresource_longitude between", value1, value2, "baseresourceLongitude");
            return (Criteria) this;
        }

        public Criteria andBaseresourceLongitudeNotBetween(Long value1, Long value2) {
            addCriterion("baseresource_longitude not between", value1, value2, "baseresourceLongitude");
            return (Criteria) this;
        }

        public Criteria andBaseresourceLatitudeIsNull() {
            addCriterion("baseresource_latitude is null");
            return (Criteria) this;
        }

        public Criteria andBaseresourceLatitudeIsNotNull() {
            addCriterion("baseresource_latitude is not null");
            return (Criteria) this;
        }

        public Criteria andBaseresourceLatitudeEqualTo(Long value) {
            addCriterion("baseresource_latitude =", value, "baseresourceLatitude");
            return (Criteria) this;
        }

        public Criteria andBaseresourceLatitudeNotEqualTo(Long value) {
            addCriterion("baseresource_latitude <>", value, "baseresourceLatitude");
            return (Criteria) this;
        }

        public Criteria andBaseresourceLatitudeGreaterThan(Long value) {
            addCriterion("baseresource_latitude >", value, "baseresourceLatitude");
            return (Criteria) this;
        }

        public Criteria andBaseresourceLatitudeGreaterThanOrEqualTo(Long value) {
            addCriterion("baseresource_latitude >=", value, "baseresourceLatitude");
            return (Criteria) this;
        }

        public Criteria andBaseresourceLatitudeLessThan(Long value) {
            addCriterion("baseresource_latitude <", value, "baseresourceLatitude");
            return (Criteria) this;
        }

        public Criteria andBaseresourceLatitudeLessThanOrEqualTo(Long value) {
            addCriterion("baseresource_latitude <=", value, "baseresourceLatitude");
            return (Criteria) this;
        }

        public Criteria andBaseresourceLatitudeIn(List<Long> values) {
            addCriterion("baseresource_latitude in", values, "baseresourceLatitude");
            return (Criteria) this;
        }

        public Criteria andBaseresourceLatitudeNotIn(List<Long> values) {
            addCriterion("baseresource_latitude not in", values, "baseresourceLatitude");
            return (Criteria) this;
        }

        public Criteria andBaseresourceLatitudeBetween(Long value1, Long value2) {
            addCriterion("baseresource_latitude between", value1, value2, "baseresourceLatitude");
            return (Criteria) this;
        }

        public Criteria andBaseresourceLatitudeNotBetween(Long value1, Long value2) {
            addCriterion("baseresource_latitude not between", value1, value2, "baseresourceLatitude");
            return (Criteria) this;
        }

        public Criteria andAirconditionerPowerIsNull() {
            addCriterion("airconditioner_power is null");
            return (Criteria) this;
        }

        public Criteria andAirconditionerPowerIsNotNull() {
            addCriterion("airconditioner_power is not null");
            return (Criteria) this;
        }

        public Criteria andAirconditionerPowerEqualTo(Long value) {
            addCriterion("airconditioner_power =", value, "airconditionerPower");
            return (Criteria) this;
        }

        public Criteria andAirconditionerPowerNotEqualTo(Long value) {
            addCriterion("airconditioner_power <>", value, "airconditionerPower");
            return (Criteria) this;
        }

        public Criteria andAirconditionerPowerGreaterThan(Long value) {
            addCriterion("airconditioner_power >", value, "airconditionerPower");
            return (Criteria) this;
        }

        public Criteria andAirconditionerPowerGreaterThanOrEqualTo(Long value) {
            addCriterion("airconditioner_power >=", value, "airconditionerPower");
            return (Criteria) this;
        }

        public Criteria andAirconditionerPowerLessThan(Long value) {
            addCriterion("airconditioner_power <", value, "airconditionerPower");
            return (Criteria) this;
        }

        public Criteria andAirconditionerPowerLessThanOrEqualTo(Long value) {
            addCriterion("airconditioner_power <=", value, "airconditionerPower");
            return (Criteria) this;
        }

        public Criteria andAirconditionerPowerIn(List<Long> values) {
            addCriterion("airconditioner_power in", values, "airconditionerPower");
            return (Criteria) this;
        }

        public Criteria andAirconditionerPowerNotIn(List<Long> values) {
            addCriterion("airconditioner_power not in", values, "airconditionerPower");
            return (Criteria) this;
        }

        public Criteria andAirconditionerPowerBetween(Long value1, Long value2) {
            addCriterion("airconditioner_power between", value1, value2, "airconditionerPower");
            return (Criteria) this;
        }

        public Criteria andAirconditionerPowerNotBetween(Long value1, Long value2) {
            addCriterion("airconditioner_power not between", value1, value2, "airconditionerPower");
            return (Criteria) this;
        }

        public Criteria andBaseresourceStateIsNull() {
            addCriterion("baseresource_state is null");
            return (Criteria) this;
        }

        public Criteria andBaseresourceStateIsNotNull() {
            addCriterion("baseresource_state is not null");
            return (Criteria) this;
        }

        public Criteria andBaseresourceStateEqualTo(Integer value) {
            addCriterion("baseresource_state =", value, "baseresourceState");
            return (Criteria) this;
        }

        public Criteria andBaseresourceStateNotEqualTo(Integer value) {
            addCriterion("baseresource_state <>", value, "baseresourceState");
            return (Criteria) this;
        }

        public Criteria andBaseresourceStateGreaterThan(Integer value) {
            addCriterion("baseresource_state >", value, "baseresourceState");
            return (Criteria) this;
        }

        public Criteria andBaseresourceStateGreaterThanOrEqualTo(Integer value) {
            addCriterion("baseresource_state >=", value, "baseresourceState");
            return (Criteria) this;
        }

        public Criteria andBaseresourceStateLessThan(Integer value) {
            addCriterion("baseresource_state <", value, "baseresourceState");
            return (Criteria) this;
        }

        public Criteria andBaseresourceStateLessThanOrEqualTo(Integer value) {
            addCriterion("baseresource_state <=", value, "baseresourceState");
            return (Criteria) this;
        }

        public Criteria andBaseresourceStateIn(List<Integer> values) {
            addCriterion("baseresource_state in", values, "baseresourceState");
            return (Criteria) this;
        }

        public Criteria andBaseresourceStateNotIn(List<Integer> values) {
            addCriterion("baseresource_state not in", values, "baseresourceState");
            return (Criteria) this;
        }

        public Criteria andBaseresourceStateBetween(Integer value1, Integer value2) {
            addCriterion("baseresource_state between", value1, value2, "baseresourceState");
            return (Criteria) this;
        }

        public Criteria andBaseresourceStateNotBetween(Integer value1, Integer value2) {
            addCriterion("baseresource_state not between", value1, value2, "baseresourceState");
            return (Criteria) this;
        }

        public Criteria andBaseresourceNoteIsNull() {
            addCriterion("baseresource_note is null");
            return (Criteria) this;
        }

        public Criteria andBaseresourceNoteIsNotNull() {
            addCriterion("baseresource_note is not null");
            return (Criteria) this;
        }

        public Criteria andBaseresourceNoteEqualTo(String value) {
            addCriterion("baseresource_note =", value, "baseresourceNote");
            return (Criteria) this;
        }

        public Criteria andBaseresourceNoteNotEqualTo(String value) {
            addCriterion("baseresource_note <>", value, "baseresourceNote");
            return (Criteria) this;
        }

        public Criteria andBaseresourceNoteGreaterThan(String value) {
            addCriterion("baseresource_note >", value, "baseresourceNote");
            return (Criteria) this;
        }

        public Criteria andBaseresourceNoteGreaterThanOrEqualTo(String value) {
            addCriterion("baseresource_note >=", value, "baseresourceNote");
            return (Criteria) this;
        }

        public Criteria andBaseresourceNoteLessThan(String value) {
            addCriterion("baseresource_note <", value, "baseresourceNote");
            return (Criteria) this;
        }

        public Criteria andBaseresourceNoteLessThanOrEqualTo(String value) {
            addCriterion("baseresource_note <=", value, "baseresourceNote");
            return (Criteria) this;
        }

        public Criteria andBaseresourceNoteLike(String value) {
            addCriterion("baseresource_note like", value, "baseresourceNote");
            return (Criteria) this;
        }

        public Criteria andBaseresourceNoteNotLike(String value) {
            addCriterion("baseresource_note not like", value, "baseresourceNote");
            return (Criteria) this;
        }

        public Criteria andBaseresourceNoteIn(List<String> values) {
            addCriterion("baseresource_note in", values, "baseresourceNote");
            return (Criteria) this;
        }

        public Criteria andBaseresourceNoteNotIn(List<String> values) {
            addCriterion("baseresource_note not in", values, "baseresourceNote");
            return (Criteria) this;
        }

        public Criteria andBaseresourceNoteBetween(String value1, String value2) {
            addCriterion("baseresource_note between", value1, value2, "baseresourceNote");
            return (Criteria) this;
        }

        public Criteria andBaseresourceNoteNotBetween(String value1, String value2) {
            addCriterion("baseresource_note not between", value1, value2, "baseresourceNote");
            return (Criteria) this;
        }

        public Criteria andDataFromIsNull() {
            addCriterion("data_from is null");
            return (Criteria) this;
        }

        public Criteria andDataFromIsNotNull() {
            addCriterion("data_from is not null");
            return (Criteria) this;
        }

        public Criteria andDataFromEqualTo(Integer value) {
            addCriterion("data_from =", value, "dataFrom");
            return (Criteria) this;
        }

        public Criteria andDataFromNotEqualTo(Integer value) {
            addCriterion("data_from <>", value, "dataFrom");
            return (Criteria) this;
        }

        public Criteria andDataFromGreaterThan(Integer value) {
            addCriterion("data_from >", value, "dataFrom");
            return (Criteria) this;
        }

        public Criteria andDataFromGreaterThanOrEqualTo(Integer value) {
            addCriterion("data_from >=", value, "dataFrom");
            return (Criteria) this;
        }

        public Criteria andDataFromLessThan(Integer value) {
            addCriterion("data_from <", value, "dataFrom");
            return (Criteria) this;
        }

        public Criteria andDataFromLessThanOrEqualTo(Integer value) {
            addCriterion("data_from <=", value, "dataFrom");
            return (Criteria) this;
        }

        public Criteria andDataFromIn(List<Integer> values) {
            addCriterion("data_from in", values, "dataFrom");
            return (Criteria) this;
        }

        public Criteria andDataFromNotIn(List<Integer> values) {
            addCriterion("data_from not in", values, "dataFrom");
            return (Criteria) this;
        }

        public Criteria andDataFromBetween(Integer value1, Integer value2) {
            addCriterion("data_from between", value1, value2, "dataFrom");
            return (Criteria) this;
        }

        public Criteria andDataFromNotBetween(Integer value1, Integer value2) {
            addCriterion("data_from not between", value1, value2, "dataFrom");
            return (Criteria) this;
        }
    }

    
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value) {
            super();
            this.condition = condition;
            this.value = value;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.betweenValue = true;
        }

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }
    }
}