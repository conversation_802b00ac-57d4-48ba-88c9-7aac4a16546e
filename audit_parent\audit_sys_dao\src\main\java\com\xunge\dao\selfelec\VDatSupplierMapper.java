package com.xunge.dao.selfelec;

import com.xunge.model.selfelec.VDatSupplier;
import com.xunge.model.selfelec.VDatSupplierExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface VDatSupplierMapper {
    
    int countByExample(VDatSupplierExample example);

    
    int deleteByExample(VDatSupplierExample example);

    
    int insert(VDatSupplier record);

    
    int insertSelective(VDatSupplier record);

    
    List<VDatSupplier> selectByExample(VDatSupplierExample example);

    
    int updateByExampleSelective(@Param("record") VDatSupplier record, @Param("example") VDatSupplierExample example);

    
    int updateByExample(@Param("record") VDatSupplier record, @Param("example") VDatSupplierExample example);
}