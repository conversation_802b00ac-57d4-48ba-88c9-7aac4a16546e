package com.xunge.dao.selfelec.focuscontract;

import com.xunge.model.basedata.DatSupplierVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface DatContractSupplierMapper {

    /**
     * 查询合同所有供应商信息
     *
     * @param contractId
     * @param prvId
     * @return
     */
    List<DatSupplierVO> queryContractAllSupplier(@Param("contractId") String contractId, @Param("prvId") String prvId);

    /**
     * 查询合同所有供应商数量
     *
     * @param contractId
     * @param prvId
     * @return
     */
    int queryContractAllSupplierCount(@Param("contractId") String contractId, @Param("prvId") String prvId);

}