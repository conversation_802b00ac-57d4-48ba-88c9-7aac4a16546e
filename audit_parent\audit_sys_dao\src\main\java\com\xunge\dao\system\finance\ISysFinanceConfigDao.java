package com.xunge.dao.system.finance;

import com.xunge.core.page.Page;
import com.xunge.model.system.finance.SysFinanceVo;

import java.util.List;
import java.util.Map;

/**
 * 财务接口配置实体
 */
public interface ISysFinanceConfigDao {

    int insertSelective(SysFinanceVo record);

    Page<List<SysFinanceVo>> queryFinanceByPage(Map<String, Object> paraMap, int pageNumber, int pageSize);

    int updateByPrimaryKeySelective(SysFinanceVo record);

    SysFinanceVo selectByPrimaryKey(Long sysFinanceId);

    SysFinanceVo queryFinanceByPrvCode(Map<String, Object> paraMap);

    List<SysFinanceVo> queryClass(Map<String, Object> paraMap);

    int insertClass(Map<String, Object> map);

    int deleteClass();

    List<SysFinanceVo> queryIsTowerByCode(Map<String, Object> paraMap);
}