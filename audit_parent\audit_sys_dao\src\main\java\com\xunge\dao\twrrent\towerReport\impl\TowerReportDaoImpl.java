package com.xunge.dao.twrrent.towerReport.impl;

import com.xunge.core.page.Page;
import com.xunge.dao.AbstractBaseDao;
import com.xunge.dao.twrrent.towerReport.ITowerReportDao;
import com.xunge.filter.PageInterceptor;
import com.xunge.model.towerrent.rentmanager.RentAndBillImportReport;
import com.xunge.model.towerrent.rentmanager.TowerReconciliationReportVO;
import com.xunge.model.towerrent.rentmanager.TowerRentInformationReport;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * TODO：
 *
 * <AUTHOR>
 * @version 1.0 2018-08-02/14:14
 */
@Repository("towerReportDao")
public class TowerReportDaoImpl extends AbstractBaseDao implements ITowerReportDao {
    private final String nameSpace = "com.xunge.dao.twrrent.towerReport.TowerReportMapper.";

    @Override
    public Page<TowerRentInformationReport> queryTowerRentinformationReport(Map<String, Object> paraMap, int pageNumber, int pageSize) {
        PageInterceptor.startPage(pageNumber, pageSize);
        this.getSqlSession().selectList(nameSpace + "queryTowerRentinformationReport", paraMap);
        return PageInterceptor.endPage();
    }

    @Override
    public List<TowerRentInformationReport> queryTowerRentinformationReport(Map<String, Object> paraMap) {
        return this.getSqlSession().selectList(nameSpace + "queryTowerRentinformationReport", paraMap);
    }

    /**
     * 分页查询对账数据统计结果
     *
     * @param paraMap
     * @return
     */
    @Override
    public Page<TowerReconciliationReportVO> queryPageTowerReconciliationReport(Map<String, Object> paraMap) {
        PageInterceptor.startPage(Integer.parseInt(paraMap.get("pageNumber").toString()), Integer.parseInt(paraMap.get("pageSize").toString()));
        this.getSqlSession().selectList(nameSpace + "queryTowerReconciliationReport", paraMap);
        return PageInterceptor.endPage();
    }

    public List<TowerReconciliationReportVO> queryTowerReconciliationReport(Map<String, Object> paraMap) {
        return this.getSqlSession().selectList(nameSpace + "queryTowerReconciliationReport", paraMap);
    }

    @Override
    public List<RentAndBillImportReport> queryRentAndBillImportReport(Map<String, Object> paraMap) {
        return this.getSqlSession().selectList(nameSpace + "queryRentAndBillImportReport", paraMap);
    }
}
