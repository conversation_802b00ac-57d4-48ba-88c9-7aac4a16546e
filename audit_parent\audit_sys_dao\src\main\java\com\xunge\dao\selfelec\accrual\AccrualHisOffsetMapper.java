package com.xunge.dao.selfelec.accrual;

import java.util.List;
import java.util.Map;

import com.xunge.model.selfelec.accrual.AccrualHisOffset;
import com.xunge.model.selfelec.accrual.AccrualHisOffsetDetail;

public interface AccrualHisOffsetMapper {

	List<AccrualHisOffset> queryList(Map<String, Object> map);

	List<AccrualHisOffset> selectContract(Map<String, Object> map);

	void saveHisConfig(AccrualHisOffset ah);

	List<AccrualHisOffsetDetail> exportDetail(Map<String, Object> map);

}
