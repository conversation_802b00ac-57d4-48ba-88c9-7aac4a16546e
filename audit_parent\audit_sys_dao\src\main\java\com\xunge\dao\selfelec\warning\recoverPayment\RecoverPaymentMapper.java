package com.xunge.dao.selfelec.warning.recoverPayment;

import com.xunge.model.basedata.EleAnomalousWarningVo;
import com.xunge.model.basedata.RecoverPaymentVo;
import com.xunge.model.selfelec.recoverPayment.RecoverPayment;
import com.xunge.model.selfelec.recoverPayment.RecoverPaymentUnusualResource;
import com.xunge.model.system.region.RegionVO;
import org.apache.ibatis.annotations.Param;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public interface RecoverPaymentMapper {

    List<RecoverPayment> queryElecRecoverPaymentList(Map<String, Object> map);

    List<RecoverPaymentUnusualResource> queryRecoverIdFromResource(Map<String, Object> map);

    List<RecoverPaymentUnusualResource> queryCIDResourceList(@Param("recoverId") String recoverId);

    List<RecoverPaymentUnusualResource> queryUnusualResourceList(@Param("recoverId") String recoverId);

    RecoverPayment queryRecoverPaymentDetail(@Param("recoverId") String recoverId);

    RecoverPayment queryBillaccountDetail(@Param("businessId") String businessId);

    RecoverPayment queryBillaccountDetailPayment(@Param("businessId") String businessId);

    List<RecoverPaymentUnusualResource> queryResourceUpdate(@Param("businessId") String businessId);

    void editOrUpdateElecRecoverPayment(Map<String, Object> map);

    void addElecRecoverPayment(RecoverPayment paymentDetail);

    RecoverPaymentVo selectByPrimaryKey2(Map<String, Object> map);

    int updateByPrimaryKey2(RecoverPaymentVo record);

    List<EleAnomalousWarningVo> selectEleAnomalousWarning(Map<String, Object> map);

    List<EleAnomalousWarningVo> selectEleAnomalousWarningToDo(Map<String, Object> map);

    void synchronizationUnusualResource(RecoverPayment paymentDetail);

    void insertOperateTime(HashMap<String, Object> timeMap);

    List<RecoverPayment> getAuditingList(Map<String, Object> map);

    Long getAuditingSize(Map<String, Object> map);

    RecoverPayment queryRecoverPaymentInfo(Map<String, Object> paraMap);

    RecoverPayment queryInfoByid(String id);

    List<RegionVO> queryPrv(Map<String, Object> map);

    List<RegionVO> queryPreg(Map<String, Object> map);

    List<RegionVO> queryReg(Map<String, Object> map);

    /**
     * 根据缴费单编码查询缴费记录
     *
     * @param recoverCode
     * @return
     */
    List<RecoverPayment> queryRecoverPaymentByRecoverCode(String recoverCode);

    /**
     * 查询数据库中最大数值的code
     *
     * @param param
     * @return
     */
    Map<String, Object> queryMaxCode(Map<String, Object> param);

    String queryPrvCodeByPrvId(@Param("prvId") String prvId);

    RecoverPayment queryBillaccountDetailWarning(@Param("businessId") String businessId);
}