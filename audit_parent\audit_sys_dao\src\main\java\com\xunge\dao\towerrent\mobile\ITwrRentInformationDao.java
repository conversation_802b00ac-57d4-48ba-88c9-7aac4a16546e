package com.xunge.dao.towerrent.mobile;

import com.xunge.core.page.Page;
import com.xunge.model.basedata.DatAttachment;
import com.xunge.model.towerrent.mobile.TwrRentInformationVO;
import com.xunge.model.towerrent.rentmanager.TowerResourceInfoVO;
import com.xunge.model.towerrent.rentmanager.TwrRentVsResourceLinkInfo;
import com.xunge.model.towerrent.settlement.TwrUploadVO;

import java.util.List;
import java.util.Map;


/**
 * 移动资源信息dao
 *
 * <AUTHOR>
 */
public interface ITwrRentInformationDao {
    /**
     * 分页查询移动侧数据
     *
     * @param map
     * @param pageNum
     * @param pageSize
     * @return
     */
    public Page<TwrRentInformationVO> queryTwrRentInformation(Map<String, Object> map, int pageNum, int pageSize);

    /**
     * 查询移动侧数据
     *
     * @param map
     * @return
     */
    public List<TwrRentInformationVO> queryTwrRentInformationList(Map<String, Object> map);

    /**
     * 查询移动侧数据
     *
     * @param map
     * @return
     * <AUTHOR>
     */
    public List<TowerResourceInfoVO> queryTwrRentInformation(Map<String, Object> map);

    /**
     * 获取移动侧某省的所有资源的总数
     *
     * @param prvId
     * @return
     */
    Long getCountAllTowerResourceInfoByPrvId(String prvId);


    /**
     * 根据拆分表id查询拆分表信息
     *
     * @param id
     * @return
     * <AUTHOR>
     */
    TwrRentInformationVO queryByRentinformationId(String id);

    TwrRentInformationVO queryByHisRentinformationId(String id);

    /**
     * 查询新增的移动起租信息的期始和期终
     *
     * @param paraMap
     * @return
     * <AUTHOR>
     */
    public TwrRentInformationVO queryTime(Map<String, Object> paraMap);

    /**
     * 导出移动起租信息全查
     *
     * @param paraMap
     * @return
     * <AUTHOR>
     */
    public List<TwrRentInformationVO> queryExportTwrRentInformation(Map<String, Object> paraMap);

    /**
     * 根据审核通过的铁塔信息变更数据更新移动起租表
     *
     * @param paraMap
     * @return
     */
    public int updateTwrRentInformationByBizChange(Map<String, Object> paraMap);

    int updateHisTwrRentInformationByBizChange(Map<String, Object> paraMap);

    /**
     * 根据业务确认单号 铁塔站址编码  生效日期判断是否在服务有效期查找TwrRentInformation
     *
     * @param paraMap
     * @return
     * <AUTHOR>
     */
    public TwrRentInformationVO queryTwrRentInformationByBusinessNumDate(Map<String, Object> paraMap);

    TwrRentInformationVO queryHisTwrRentInformationByBusinessNumDate(Map<String, Object> paraMap);

    /**
     * 根据铁塔站址编码查询移动起租信息
     *
     * @param paraMap
     * @return
     */
    public List<TwrRentInformationVO> queryMsgByTwrStaCode(Map<String, Object> paraMap);

    /**
     * 根据省份ID查询移动侧起租信息
     *
     * @param prvId
     * @return
     */
    public List<TwrRentInformationVO> queryMobileRentByPrvId(String prvId);

    public int setRecordDeptId(String[] twrRentinformationArr, String deptIds);

    /**
     * 分页查询
     *
     * @param map
     * @param pageNum
     * @param pageSize
     * @return
     */
    public Page<TwrRentInformationVO> queryByPage(Map<String, Object> map, int pageNum, int pageSize);

    Page<TwrRentInformationVO> queryByPageHistory(Map<String, Object> map, int pageNum, int pageSize);

    /**
     * 导出查询
     *
     * @param map
     * @return
     */
    public List<TwrRentInformationVO> queryByCondition(Map<String, Object> map);

    List<TwrRentInformationVO> queryHisByCondition(Map<String, Object> map);


    /**
     * 修改移动侧信息
     *
     * @param map
     * @return
     */
    public int updateMobileRentinfirmation(Map<String, Object> map);

    int updateHisMobileRentinfirmation(Map<String, Object> map);

    /**
     * 根据3个业务主键查询数据
     *
     * @param twrRentInformationVO
     * @return
     */
    public int queryTwrRentInfoCountByPriKey(TwrRentInformationVO twrRentInformationVO);

    /**
     * 老综资判断是否关联
     *
     * @param twrRentInformationVO
     * @return
     */
    public int queryOldIsLink(TwrRentInformationVO twrRentInformationVO);

    /**
     * 新综资判断是否关联
     *
     * @param twrRentInformationVO
     * @return
     */
    public int queryNewIsLink(TwrRentInformationVO twrRentInformationVO);


    int deleteRentInformationChangeByIdAndState(Map<String, Object> map);

    int deleteHisRentInformationChangeByIdAndState(Map<String, Object> map);

    /**
     * 老综资获取生命周期
     *
     * @param param 铁塔编号和省份ID
     * @return
     */
    public List<Map<String, Integer>> queryOldResBaseState(Map<String, String> param);

    /**
     * 新综资获取生命周期
     *
     * @param param 铁塔编号和省份ID
     * @return
     */
    public List<Map<String, Integer>> queryNewResBaseState(Map<String, String> param);

    public List<TwrRentInformationVO> selectRentInformationList(Map<String, Object> map);

    List<TwrRentInformationVO> selectHisRentInformationList(Map<String, Object> map);

    List findRentAndBillByBussPriKey(List param);

    List findTowerRentIdsByMobileParam(Map<String, Object> param);

    List<Map> findHisRentAndBillByBussPriKey(List<Map> historyMonthList);

    public List<TwrRentInformationVO> selectRentInformationListConfig(Map<String, Object> map);

    List<TwrRentInformationVO> selectHisRentInformationListConfig(Map<String, Object> map);

    TwrRentVsResourceLinkInfo getResourceLinkInfoByTowerStationCode(Map<String, String> param);

    /**
     * 删除移动侧资源信息
     *
     * @param map
     * @return
     */
    int deleteMobileRentById(List list);

    int deleteHisMobileRentById(List list);


    List<String> queryFiles(String businessId, String businessType, String yearmonth);

    String queryFileName(String businessId, String yearmonth, String showType);

    int insert(DatAttachment record);

    Page<TwrUploadVO> queryDownFiles(Map<String, Object> paraMap, int pageNumber, int pageSize);

    List<String> queryMobileFiles(String towerStationCode, String businessConfirmNumber, String businessType, String accountPeroid);

    List<String> queryAccountFiles(String accountsummaryId, String businessType, String yearmonth);

    List<Map> queryAccountFiles2(String accountsummaryId, String[] businessTypes);


    /**
     * 当前月移动侧数据批量插入
     *
     * @param temp
     */
    int batchSave(List<TwrRentInformationVO> temp);

    /**
     * 历史月移动侧数据批量插入
     *
     * @param temp
     */
    int hisBatchSave(List<TwrRentInformationVO> temp);

    TwrRentInformationVO queryBeanById(Map<String, Object> param);

    TwrRentInformationVO queryHisBeanById(Map<String, Object> param);

    /**
     * 重复性判断
     *
     * @param paraMap
     * @return
     */
    List<Map<String, String>> judgeRepetition(String showType, Map<String, Object> paraMap);

}
