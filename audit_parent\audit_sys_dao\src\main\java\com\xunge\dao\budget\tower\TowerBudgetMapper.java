package com.xunge.dao.budget.tower;

import com.xunge.model.budget.tower.TowerBudgetConfig;
import com.xunge.model.budget.tower.TwrBudgetReport;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * @Description:
 * @Author: dxd
 * @Date: 2022/8/9 9:46
 */
public interface TowerBudgetMapper {

    List<TowerBudgetConfig> getVariablesByPrvId();

    int updateTowerBudget(TwrBudgetReport e);

    List<TwrBudgetReport> getReportDataByPrvIds(@Param("prvIds") Set<String> prvIdSet,@Param("workOrderId") String workOrderId);

    List<String> getOilPrvIds();

    List<TwrBudgetReport> getReportDataByStepType(@Param("prvIds")  Set<String> prvIdSet,
                                                  @Param("budgetTime")  String budgetTime,
                                                  @Param("flowType")  Integer flowType);
}
