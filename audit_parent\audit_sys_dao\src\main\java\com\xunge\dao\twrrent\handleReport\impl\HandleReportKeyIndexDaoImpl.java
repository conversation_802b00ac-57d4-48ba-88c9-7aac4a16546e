package com.xunge.dao.twrrent.handleReport.impl;

import com.xunge.dao.AbstractBaseDao;
import com.xunge.dao.twrrent.handleReport.IHandleReportKeyIndexDao;
import com.xunge.model.towerrent.monthlyReport.KeyIndexTowerVO;
import com.xunge.model.towerrent.monthlyReport.TowerChargeAnnualVo;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public class HandleReportKeyIndexDaoImpl extends AbstractBaseDao implements IHandleReportKeyIndexDao {

    private final String nameSpace = "com.xunge.dao.twrrent.monthlyReport.TwrMonthlyReportKeyIndexMapper.";

    @Override
    public List<KeyIndexTowerVO> getBaseKeyIndexCopeDataPrv(Map<String, Object> paraMap) {
        return this.getSqlSession().selectList(nameSpace + "getBaseKeyIndexCopeDataPrv", paraMap);
    }

    @Override
    public List<KeyIndexTowerVO> getBaseKeyIndexCopeDataPreg(Map<String, Object> paraMap) {
        return this.getSqlSession().selectList(nameSpace + "getBaseKeyIndexCopeDataPreg", paraMap);
    }

    @Override
    public List<KeyIndexTowerVO> getBaseKeyIndexCopeDataReg(Map<String, Object> paraMap) {
        return this.getSqlSession().selectList(nameSpace + "getBaseKeyIndexCopeDataReg", paraMap);
    }

    @Override
    public List<KeyIndexTowerVO> getBaseKeyIndexCopeDataWhole(Map<String, Object> paraMap) {
        return this.getSqlSession().selectList(nameSpace + "getBaseKeyIndexCopeDataWhole", paraMap);
    }

    @Override
    public List<TowerChargeAnnualVo> queryAnnualStatement(Map<String, Object> paraMap) {
        return this.getSqlSession().selectList(nameSpace + "queryAnnualStatement", paraMap);
    }

}
