package com.xunge.dao.roletype;

import com.xunge.model.roletype.SysRoleTypeExclusionLog;
import com.xunge.model.roletype.SysRoleTypeExclusionLogVo;

import java.util.List;

public interface SysRoleTypeExclusionLogMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table sys_roletype_exclusion_log
     *
     * @mbggenerated Thu May 05 08:38:59 CST 2022
     */
    int deleteByPrimaryKey(Integer logId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table sys_roletype_exclusion_log
     *
     * @mbggenerated Thu May 05 08:38:59 CST 2022
     */
    int insert(SysRoleTypeExclusionLog record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table sys_roletype_exclusion_log
     *
     * @mbggenerated Thu May 05 08:38:59 CST 2022
     */
    int insertSelective(SysRoleTypeExclusionLog record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table sys_roletype_exclusion_log
     *
     * @mbggenerated Thu May 05 08:38:59 CST 2022
     */
    SysRoleTypeExclusionLog selectByPrimaryKey(Integer logId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table sys_roletype_exclusion_log
     *
     * @mbggenerated Thu May 05 08:38:59 CST 2022
     */
    int updateByPrimaryKeySelective(SysRoleTypeExclusionLog record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table sys_roletype_exclusion_log
     *
     * @mbggenerated Thu May 05 08:38:59 CST 2022
     */
    int updateByPrimaryKeyWithBLOBs(SysRoleTypeExclusionLog record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table sys_roletype_exclusion_log
     *
     * @mbggenerated Thu May 05 08:38:59 CST 2022
     */
    int updateByPrimaryKey(SysRoleTypeExclusionLog record);

    int batchInsert(List<SysRoleTypeExclusionLog> list);

    List<SysRoleTypeExclusionLogVo>  queryExclusionLogByRoleTypeId(Integer roleTypeId);


}