package com.xunge.dao.selfelec;

import com.xunge.model.selfelec.VDatBasesite;
import com.xunge.model.selfelec.VDatBasesiteExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface VDatBasesiteMapper {
    
    int countByExample(VDatBasesiteExample example);

    
    int deleteByExample(VDatBasesiteExample example);

    
    int insert(VDatBasesite record);

    
    int insertSelective(VDatBasesite record);

    
    List<VDatBasesite> selectByExample(VDatBasesiteExample example);

    
    int updateByExampleSelective(@Param("record") VDatBasesite record, @Param("example") VDatBasesiteExample example);

    
    int updateByExample(@Param("record") VDatBasesite record, @Param("example") VDatBasesiteExample example);
}