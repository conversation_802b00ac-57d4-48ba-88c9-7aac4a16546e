package com.xunge.model.basedata;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

/**
 * @ClassName NetworkOptimization
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/12/8 17:14
 */
@Data
public class NetworkOptimization {
    private String id;
    @Excel(name="年份",replace = {"-_null"})
    private String year;
    @Excel(name="月份",replace = {"-_null"})
    private String month;
    @Excel(name="省份",replace = {"-_null"})
    private String province;
    @Excel(name="地市",replace = {"-_null"})
    private String city;
    @Excel(name="区县",replace = {"-_null"})
    private String region;
    @Excel(name="小区标识",replace = {"-_null"})
    private String cellCode;
    @Excel(name="小区名称",replace = {"-_null"})
    private String cellName;
    @Excel(name="小区所属综资空间资源（机房或资源点）标识",replace = {"-_null"})
    private String resourceCode;
    @Excel(name="小区所属综资空间资源（机房或资源点）名称",replace = {"-_null"})
    private String resourceName;
    @Excel(name="小区月总流量",replace = {"-_null"})
    private String cellTotalFlow;
    @Excel(name="小区月平均无线利用率",replace = {"-_null"})
    private String cellWlanAvgUtilization;
    @Excel(name="小区频率类型",replace = {"-_null"})
    private String cellRateType;
}
