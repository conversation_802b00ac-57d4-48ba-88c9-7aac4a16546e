package com.xunge.dao.selfrent.billamount;

import com.xunge.model.selfrent.billamount.BillamountLogVO;

import java.util.List;

/**
 * <AUTHOR>
 * @version 2017-06-26
 * @description 租费报账汇总报账日志记录DAO接口
 */
public interface BillamountLogDao {
    /**
     * @param rentBillamountDetail 租费报账汇总明细
     * @return
     * @description 保存报账汇总日志记录明细
     */
    public int insertBillamountLog(BillamountLogVO billamountLog);

    /**
     * 更新汇总单报账日志记录
     *
     * @param billamountLog
     * @return
     */
    public int updateBillamountLogById(BillamountLogVO billamountLog);

    /**
     * 查询汇总单日志
     *
     * @param billamountCode
     * @return
     */
    public List<BillamountLogVO> selectByPrimaryCode(String billamountCode);


    public List<BillamountLogVO> selectBillamountPushLog(List<String> list);
}