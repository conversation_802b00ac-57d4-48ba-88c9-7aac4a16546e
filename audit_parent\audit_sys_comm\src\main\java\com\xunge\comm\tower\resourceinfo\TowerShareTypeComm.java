package com.xunge.comm.tower.resourceinfo;

/**
 * <AUTHOR>
 * @date 2017年10月17日
 * @describe 铁塔共享属性
 */
public class TowerShareTypeComm {
    /**
     * 文字--原产权方
     */
    public static String PARTY_FORMER_PROPERTY_RIGHT = "原产权方";
    /**
     * 原产权方--1
     */
    public static int PARTY_FORMER_PROPERTY_RIGHT_int = 1;

    /**
     * 文字--既有共享
     */
    public static String BOTH_SHARED = "既有共享";
    /**
     * 既有共享--2
     */
    public static int BOTH_SHARED_int = 2;

    /**
     * 文字--存量自改
     */
    public static String STOCK_CHANGE = "存量自改";
    /**
     * 存量自改--3
     */
    public static int STOCK_CHANGE_int = 3;

    /**
     * 文字--存量改造
     */
    public static String STOCK_REFORM = "存量改造";
    /**
     * 存量改造--4
     */
    public static int STOCK_REFORM_int = 4;

    /**
     * 文字--新建铁塔
     */
    public static String NEW_TOWER = "新建铁塔";
    /**
     * 新建铁塔--5
     */
    public static int NEW_TOWER_int = 5;
}
