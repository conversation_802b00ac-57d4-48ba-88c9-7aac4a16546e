package com.xunge.dao.selfrent.contractcentralization;

import com.xunge.dto.selfelec.AuthorityUser;
import com.xunge.model.selfrent.contractcentralization.RentContractCentralizationAuditExportVO;
import com.xunge.model.selfrent.contractcentralization.RentContractCentralizationExportVO;
import com.xunge.model.selfrent.contractcentralization.RentContractConcentrationQueryDto;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.cursor.Cursor;

/**
 * @Description:
 * @Author: dxd
 * @Date: 2023/5/5 10:07
 */
public interface ContractCentralizationVOMapper {

    Cursor<RentContractCentralizationExportVO> queryRentContractCentralizationByCursor(@Param("user") AuthorityUser userRegInfo,
                                                                                       @Param("queryDto")  RentContractConcentrationQueryDto dto);

    Cursor<RentContractCentralizationAuditExportVO> queryRentContractCentralizationAuditByCursor(@Param("user") AuthorityUser userRegInfo,
                                                                                                 @Param("queryDto") RentContractConcentrationQueryDto dto);
}
