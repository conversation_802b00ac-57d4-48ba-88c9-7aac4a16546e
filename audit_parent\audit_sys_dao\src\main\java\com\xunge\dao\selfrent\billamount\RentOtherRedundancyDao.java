package com.xunge.dao.selfrent.billamount;

import com.xunge.model.selfrent.billamount.RentOtherRedundancyFinance;

import java.util.List;

/**
 * @Version: 1.0.0
 * @Author: <PERSON>
 * @Email: <EMAIL>
 * @Time: 2019-07-09 10:39
 */
public interface RentOtherRedundancyDao<T> {
    /**
     * 查询对应缴费单主键的所有其它费用集合
     *
     * @param billaccountpaymentdetailId 核销缴费单主键
     * @return 集合
     */
    List<RentOtherRedundancyFinance> queryOtherAmountFinanceList(String billaccountpaymentdetailId);

    int insert(RentOtherRedundancyFinance rentOtherRedundancyFinance);

    /**
     * 根据汇总单ID删除明细信息
     *
     * @param billamountId
     */
    int deleteByBillamountId(String billamountId);

    /**
     * 根据汇总单详情ID删除明细信息
     *
     * @param billamountDetailId
     */
    int deleteByBillamountDetailId(String billamountDetailId);

    void updateSecondBillamountById(String secondBillamountId,String otherId);

}