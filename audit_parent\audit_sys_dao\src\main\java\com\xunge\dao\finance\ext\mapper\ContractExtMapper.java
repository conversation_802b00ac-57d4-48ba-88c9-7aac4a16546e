package com.xunge.dao.finance.ext.mapper;

import com.xunge.model.finance.ext.accClaim.Contract;

import java.util.Map;

public interface ContractExtMapper {

    Contract queryByDatContractId(String contactId);

    Contract queryRelByDatContractId(String contactId);

    Contract queryByRentContractId(String contactId);

    Contract queryByRelRentContractId(String contactId);

    Contract queryByEleLoanContractId(Map<String, Object> paraMap);

    Contract queryByDsContractId(String contactId);

    Contract queryByTeleContractId(String contactId);

    Contract queryConfigContract(String pregId);

}
