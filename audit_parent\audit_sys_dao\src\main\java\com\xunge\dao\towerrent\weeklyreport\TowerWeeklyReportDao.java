package com.xunge.dao.towerrent.weeklyreport;

import com.xunge.model.towerrent.weeklyreport.DateScopeRelationVO;
import com.xunge.model.towerrent.weeklyreport.TowerWeeklyReportVO;
import com.xunge.model.towerrent.weeklyreport.TowerWeeklySuspectedProblemVO;
import com.xunge.model.towerrent.weeklyreport.TwrRentWeeklySnapshoot;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface TowerWeeklyReportDao {

    List<DateScopeRelationVO> queryDateScopeWeeklyReport(Map<String,Object> map);

    Map<String,Object> getPeroidAndUpdateTime(Map<String,Object> map);

    List<TowerWeeklyReportVO> queryTowerWeeklyReportListSum(Map<String,Object> map);

    List<TowerWeeklySuspectedProblemVO> queryTowerWeeklyProblemListSum(Map<String,Object> map);

    List<TowerWeeklyReportVO> queryTowerWeeklyReportList(Map<String,Object> map);

    List<TowerWeeklySuspectedProblemVO> queryTowerWeeklyProblemList(Map<String,Object> map);

    List<String> queryDetailMonths(@Param("prvId") String prvId);

    List<TwrRentWeeklySnapshoot> queryDetailByMonth(@Param("prvId") String prvId, @Param("accountPeroid") String accountPeroid);

}
