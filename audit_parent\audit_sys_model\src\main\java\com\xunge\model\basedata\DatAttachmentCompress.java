package com.xunge.model.basedata;

import lombok.Data;

/**
 * @author: LiangCheng
 * Date: 2023/2/23 8:45
 * Description: 压缩文件明细(仅图片)
 */
@Data
public class DatAttachmentCompress {

    /**
     * id
     */
    private Integer attachmentId;
    /**
     * 附件名称
     */
    private String attachmentName;
    /**
     * 压缩包名称
     */
    private String compressName;
    /**
     * 压缩包id
     */
    private String compressId;
    /**
     * 解压后路径
     */
    private String attachmentUrl;
    /**
     * 业务类型
     */
    private String businessType;
    /**
     * 业务id
     */
    private String businessId;
    /**
     * MD5
     */
    private String md5Num;

}
