package com.xunge.dao.finance;

import com.xunge.model.finance.Token;

public interface TokenMapper {


    Token selectByTime();

    
    int deleteByPrimaryKey(String guid);

    
    int insert(Token record);

    
    int insertSelective(Token record);

    
    Token selectByPrimaryKey(String guid);

    
    int updateByPrimaryKeySelective(Token record);

    
    int updateByPrimaryKey(Token record);

    Token getLatestToken();
}