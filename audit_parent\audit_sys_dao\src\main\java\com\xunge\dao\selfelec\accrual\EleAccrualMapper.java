package com.xunge.dao.selfelec.accrual;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

import com.xunge.model.selfelec.VEleBillaccountbaseresource;
import com.xunge.model.selfelec.VEleContract;
import com.xunge.model.selfelec.accrual.EleAccrual;
import com.xunge.model.selfelec.accrual.EleAccrualAll;

public interface EleAccrualMapper {

	List<EleAccrual> getEleAccrualByBillaccountId(String billaccountId);

	EleAccrual queryLastAccrual(@Param("billaccountId") String billaccountId, @Param("mode") Integer mode);

	Map<String, Object> queryMaxCode(Map<String, Object> param);

	void insertEleAccrual(EleAccrual eleAccrual);

	void insertContract(@Param("vcontract") VEleContract vcontract, @Param("accrualId") String accrualId, @Param("fromTable") String fromTable);

	void insertResource(@Param("resList") List<VEleBillaccountbaseresource> resList, @Param("accrualId") String accrualId);

	List<EleAccrualAll> queryAccrualList(Map<String, Object> map);
	List<EleAccrualAll> queryAccrualListV1(Map<String, Object> map);

	EleAccrual getEleAccrualById(@Param("accrualId") String accrualId, @Param("type") String type);

	int updateEleAccrual(EleAccrual eleAccrual);

	void deleteAccrual(String accrualId);

	void updateActivityCommit(Map<String, Object> maps);

	EleAccrual getEleBillaccount(@Param("billaccountId")String billaccountId, @Param("fromTable")String fromTable);

	List<EleAccrualAll> queryMultAuditList(List<EleAccrual> list);

	void deleteBakContract(String accrualId);

	void deleteRelationRes(String accrualId);

	void updateEleAccrualSnap(EleAccrual old);

	List<EleAccrual> getAccrualByIds(List<String> accrualIds);

	/**
     * @param billaccountId
     * @param accrualStartdate
     * @param accrualEnddate
     * @param accrualModel
     * @return
     * @Description: 根据报账点及计提区间判断计提是否存在
     * <AUTHOR>
     * @date 2022年7月7日 上午8:52:28
     */
	EleAccrual checkExistByBillaccount(@Param("billaccountId")String billaccountId, @Param("accrualStartdate")String accrualStartdate, @Param("accrualEnddate")String accrualEnddate, @Param("accrualModel") Integer accrualModel);

    /**
     * @Description: 根据计提单编码查询计提
     * <AUTHOR>
     * @date 2022年7月25日 上午10:38:42
     * @param accrualCode
	* @return
	 */
	EleAccrual getAccrualInfoByAccrualCode(String accrualCode);

    /**
     * @Description: 根据报账点查询计提
     * <AUTHOR>
     * @date 2022年8月17日 下午4:51:10
     * @param billaccountId
	* @return
	 */
	List<EleAccrual> queryByBillaccountId(String billaccountId);

	/**
	 * 查询同一合同下其他计提金额
	 * @param accrualId
	 * @param elecontractId
	 * @param accrualModel
	 * @return
	 */
	BigDecimal getOtherAccrualContractAmount(@Param("accrualId")String accrualId, @Param("contractId")String contractId, @Param("accrualModel")Integer accrualModel);

	/**
	 * 查询计提单下合同剩余金额及计提金额
	 * @param accrualIds
	 * @return
	 */
	List<EleAccrual> queryContractLeftAmountByAccruals(Map<String, Object> maps);

	/**
	 * 查询计提单下合同剩余金额
	 * @param accrualIds
	 * @return
	 */
	List<EleAccrual> queryContractLeftAmountByMap(Map<String, Object> amountMap);

	/**
	 * 获取计提合同id
	 * @param accrualId
	 * @return
	 */
	String getContractId(String accrualId);


	/**
	 * 批量获取计提合同id
	 * @param amountMap
	 * @return
	 */
	List<EleAccrual> getContractIds(Map<String, Object> amountMap);

	/**
	 * 查询计提或快照合同剩余金额
	 * @param accrualId
	 * @return
	 */
	EleAccrual queryContractLeftAmount(String accrualId);

    /**
     * @Description: 刷新时删除计提单
     * <AUTHOR>
     * @date 2024年1月31日 下午4:08:44
     * @param accrualId
     * @return
     */
    int deleteAccrualByRefresh(String accrualId);

    /**
     * @param eleAccrual
     * @return
     * @Description: TODO (这里用一句话描述这个类的作用)
     * <AUTHOR>
     * @date 2024年2月1日 下午11:00:35
     */
    int modifyEleAccrual(EleAccrual eleAccrual);

    EleAccrual getEleAccrualByAccrualId(String accrualId);
}
