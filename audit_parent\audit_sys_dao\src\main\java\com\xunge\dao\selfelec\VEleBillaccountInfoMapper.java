package com.xunge.dao.selfelec;

import com.xunge.model.selfelec.VEleBillaccountInfo;
import com.xunge.model.selfelec.VEleBillaccountInfoExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface VEleBillaccountInfoMapper {
    
    int countByExample(VEleBillaccountInfoExample example);

    
    int deleteByExample(VEleBillaccountInfoExample example);

    
    int insert(VEleBillaccountInfo record);

    
    int insertSelective(VEleBillaccountInfo record);

    
    List<VEleBillaccountInfo> selectByExample(VEleBillaccountInfoExample example);

    List<VEleBillaccountInfo> selectVEleBillaccountInfoByBPaymentdetailId(VEleBillaccountInfoExample example);

    
    int updateByExampleSelective(@Param("record") VEleBillaccountInfo record, @Param("example") VEleBillaccountInfoExample example);

    
    int updateByExample(@Param("record") VEleBillaccountInfo record, @Param("example") VEleBillaccountInfoExample example);

    List<VEleBillaccountInfo> selectEleBillaccountInfoByPaymentdetailId(Map<String, Object> map);

    List<VEleBillaccountInfo> selectEleBillaccountInfoByVerificationdetailId(Map<String, Object> map);

    List<VEleBillaccountInfo> selectEleBillaccountPaymentDateByBillaccountId(Map<String, Object> map);

    /**
     * 根据meterID查询最近的一条数据
     *
     * @param cond
     * @return
     */
    List<VEleBillaccountInfo> queryLastPaymentCmcc(Map<String, Object> cond);

    /**
     * 根据meterID查后付费的
     *
     * @param cond
     * @return
     */
    List<VEleBillaccountInfo> queryCmccElePaymentDetaillList(Map<String, Object> cond);

    /**
     * 根据meterID查核销
     *
     * @param cond
     * @return
     */
    List<VEleBillaccountInfo> queryCmccEleVerificationdetaillList(Map<String, Object> cond);

    /**
     * 根据meterID查塔维
     *
     * @param cond
     * @return
     */
    List<VEleBillaccountInfo> queryCmccDsElePaymentDetaillList(Map<String, Object> cond);

    /**
     * 根据meterID查直供电
     *
     * @param cond
     * @return
     */
    List<VEleBillaccountInfo> queryCmccTelePaymentDetaillList(Map<String, Object> cond);
}