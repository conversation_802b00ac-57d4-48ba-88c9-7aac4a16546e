package com.xunge.dao.twrrent.monthlyReport.impl;

import com.xunge.dao.AbstractBaseDao;
import com.xunge.dao.twrrent.monthlyReport.IMonthlyReportCreateDao;
import com.xunge.model.towerrent.monthlyReport.*;
import com.xunge.model.towerrent.settlement.OtherFeeVO;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;


@Repository("monthlyReportCreateDao")
public class MonthlyReportCreateDaoImpl extends AbstractBaseDao implements IMonthlyReportCreateDao {

    public final String nameSpace = "com.xunge.dao.twrrent.monthlyReport.TwrMonthlyReportMapper.";

    @Override
    public List<RptMonthlyTwrSite> getPrvMonthTwrSiteBaseReport(Map<String, Object> param) {
        return this.getSqlSession().selectList(nameSpace + "getPrvMonthTwrSiteBaseReport", param);
    }

    @Override
    public List<PayCompleteTwrSite> getPrvPayCompleteTowerSite() {
        return this.getSqlSession().selectList(nameSpace + "getPrvPayCompleteTowerSite");
    }

    @Override
    public List<MobileTwrSiteShareNum> getMobileTwrSiteShareNum() {
        return this.getSqlSession().selectList(nameSpace + "getMobileTwrSiteShareNum");
    }

    @Override
    public int deletePrvTwrSiteData(String prvId) {
        return this.getSqlSession().delete(nameSpace + "deletePrvTwrSiteData", prvId);
    }

    @Override
    public int batchInsertTwrSiteData(List<RptMonthlyTwrSite> reportBaseList) {
        return this.getSqlSession().insert(nameSpace + "batchInsertTwrSiteData", reportBaseList);
    }

    @Override
    public List<RptMonthlyTwrServiceCost> getTowerTypeServiceChargeMonthPrv() {
        return this.getSqlSession().selectList(nameSpace + "getTowerTypeServiceChargeMonthPrv");
    }

    @Override
    public List<RptMonthlyTwrServiceCost> getTowerTypeServiceChargeMonthPreg() {
        return this.getSqlSession().selectList(nameSpace + "getTowerTypeServiceChargeMonthPreg");
    }

    @Override
    public List<RptMonthlyTwrServiceCost> getTowerTypeServiceChargeMonthReg() {
        return this.getSqlSession().selectList(nameSpace + "getTowerTypeServiceChargeMonthReg");
    }

    @Override
    public BigDecimal getPrvBackAmount() {
        return this.getSqlSession().selectOne(nameSpace + "getPrvBackAmount");
    }

    @Override
    public List<TowerMonthlyPunish> getRegBackAmount() {
        return this.getSqlSession().selectList(nameSpace + "getRegBackAmount");
    }

    @Override
    public List<TowerMonthlyPunish> getPregBackAmount() {
        return this.getSqlSession().selectList(nameSpace + "getPregBackAmount");
    }

    @Override
    public int deleteByMonAndPrvId(Map<String, String> param) {
        return this.getSqlSession().delete(nameSpace + "deleteByMonAndPrvId", param);
    }

    @Override
    public int addTowerTypeServiceChargeMonth(List<RptMonthlyTwrServiceCost> list) {
        return this.getSqlSession().insert(nameSpace + "addTowerTypeServiceChargeMonth", list);
    }

    //省份预算及进度月报-大集中省份
    //地市预算及进度月报-大集中省份
    //区县预算及进度月报-大集中省份
    @Override
    public List<RptMonthlyBudgetAndProgress> getBudgetAndProgressMonthPrv(Map<String, Object> param) {
        return this.getSqlSession().selectList(nameSpace + "getBudgetAndProgressMonthPrv", param);
    }

    @Override
    public List<RptMonthlyBudgetAndProgress> getBudgetAndProgressMonthPreg(Map<String, Object> param) {
        return this.getSqlSession().selectList(nameSpace + "getBudgetAndProgressMonthPreg", param);
    }

    @Override
    public List<RptMonthlyBudgetAndProgress> getBudgetAndProgressMonthReg(Map<String, Object> param) {
        return this.getSqlSession().selectList(nameSpace + "getBudgetAndProgressMonthReg", param);
    }

    //省份预算及进度月报-非大集中省份
    //地市预算及进度月报-非大集中省份
    //区县预算及进度月报-非大集中省份
    @Override
    public List<RptMonthlyBudgetAndProgress> getBudgetAndProgressMonthPrv1(Map<String, Object> param) {
        return this.getSqlSession().selectList(nameSpace + "getBudgetAndProgressMonthPrv1", param);
    }

    @Override
    public List<RptMonthlyBudgetAndProgress> getBudgetAndProgressMonthPreg1(Map<String, Object> param) {
        return this.getSqlSession().selectList(nameSpace + "getBudgetAndProgressMonthPreg1", param);
    }

    @Override
    public List<RptMonthlyBudgetAndProgress> getBudgetAndProgressMonthReg1(Map<String, Object> param) {
        return this.getSqlSession().selectList(nameSpace + "getBudgetAndProgressMonthReg1", param);
    }

    //查询其他费用表- 非直辖市省份
    @Override
    public List<OtherFeeVO> getRegSumOther() {
        return this.getSqlSession().selectList(nameSpace + "getRegSumOther");
    }

    //查询其他费用表- 直辖市省份
    @Override
    public List<OtherFeeVO> getRegSumOtherforMunicipality() {
        return this.getSqlSession().selectList(nameSpace + "getRegSumOtherforMunicipality");
    }

    //删除数据
    @Override
    public int deleteBudgetAndProgress(Map<String, Object> param) {
        return this.getSqlSession().delete(nameSpace + "deleteBudgetAndProgress", param);
    }

    //插入数据
    @Override
    public int addBudgetAndProgressesMonth(List<RptMonthlyBudgetAndProgress> list) {
        return this.getSqlSession().insert(nameSpace + "addBudgetAndProgressesMonth", list);
    }


    @Override
    public List<RptMonthlyIconTowerCost> getIronTowerMonthPrv() {
        return this.getSqlSession().selectList(nameSpace + "getIronTowerMonthPrv");
    }

    @Override
    public int addIronTowerTMonth(List<RptMonthlyIconTowerCost> list) {
        return this.getSqlSession().insert(nameSpace + "addIronTowerTMonth", list);
    }

    @Override
    public int deleteIronByMonAndPrvId(Map<String, String> param) {
        return this.getSqlSession().delete(nameSpace + "deleteIronByMonAndPrvId", param);
    }

    @Override
    public List<RptMonthlyIconTowerCost> getSitePregList() {
        return this.getSqlSession().selectList(nameSpace + "getSitePregList");
    }

    @Override
    public List<RptMonthlyIconTowerCost> getChargePregList() {
        return this.getSqlSession().selectList(nameSpace + "getChargePregList");
    }

    @Override
    public List<RptMonthlyIconTowerCost> getSiteRegList() {
        return this.getSqlSession().selectList(nameSpace + "getSiteRegList");
    }

    @Override
    public List<RptMonthlyIconTowerCost> getChargeRegList() {
        return this.getSqlSession().selectList(nameSpace + "getChargeRegList");
    }

    @Override
    public List<RptMonthlyTwrServiceCost> getIronTowerTypeServiceChargeMonthPrv(Map<String, String> map) {
        return this.getSqlSession().selectList(nameSpace + "getIronTowerTypeServiceChargeMonthPrv", map);
    }

    @Override
    public List<RptMonthlyTwrServiceCost> getIronTowerTypeServiceChargeMonthPreg(Map<String, String> map) {
        return this.getSqlSession().selectList(nameSpace + "getIronTowerTypeServiceChargeMonthPreg", map);
    }

    @Override
    public List<RptMonthlyTwrServiceCost> getIronTowerTypeServiceChargeMonthReg(Map<String, String> map) {
        return this.getSqlSession().selectList(nameSpace + "getIronTowerTypeServiceChargeMonthReg", map);
    }

    @Override
    public List<TowerMonthlyPunish> getIronRegBackAmount() {
        return this.getSqlSession().selectList(nameSpace + "getIronRegBackAmount");
    }


    @Override
    public int deleteIronTowerByMonAndPrvId(Map<String, String> param) {
        return this.getSqlSession().delete(nameSpace + "deleteIronTowerByMonAndPrvId", param);
    }

    @Override
    public int addIronTowerTypeServiceChargeMonth(List<RptMonthlyTwrServiceCost> list) {
        return this.getSqlSession().insert(nameSpace + "addIronTowerTypeServiceChargeMonth", list);
    }
}
