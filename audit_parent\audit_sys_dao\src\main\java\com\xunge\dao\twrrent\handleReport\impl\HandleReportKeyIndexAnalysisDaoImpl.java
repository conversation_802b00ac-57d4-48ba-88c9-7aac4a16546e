package com.xunge.dao.twrrent.handleReport.impl;

import com.xunge.dao.AbstractBaseDao;
import com.xunge.dao.twrrent.handleReport.IHandleReportKeyIndexAnalysisDao;
import com.xunge.model.towerrent.monthlyReport.RptMonthlyKeyIndexAnalysisVo;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public class HandleReportKeyIndexAnalysisDaoImpl extends AbstractBaseDao implements IHandleReportKeyIndexAnalysisDao {

    private final String nameSpace = "com.xunge.dao.twrrent.handleReport.TwrHandleReportMapper.";

    @Override
    public Map<Object, Object> createMonthlyReportKeyIndexAnalysisThis(Map<String, Object> map) {
        return this.getSqlSession().selectOne(nameSpace + "createMonthlyReportKeyIndexAnalysisThis", map);
    }

    @Override
    public Map<Object, Object> createMonthlyReportKeyIndexAnalysisBefore(Map<String, Object> map) {
        return this.getSqlSession().selectOne(nameSpace + "createMonthlyReportKeyIndexAnalysisBefore", map);
    }

    @Override
    public int insertMonthlyReportKeyIndexAnalysis(List<RptMonthlyKeyIndexAnalysisVo> list) {
        return this.getSqlSession().insert(nameSpace + "insertMonthlyReportKeyIndexAnalysis", list);
    }

    @Override
    public int deleteMonthlyReportKeyIndexAnalysis(Map<String, Object> map) {
        return this.getSqlSession().delete(nameSpace + "deleteMonthlyReportKeyIndexAnalysis", map);
    }

    @Override
    public List<RptMonthlyKeyIndexAnalysisVo> selectMonthlyReportKeyIndexAnalysis(Map<String, Object> map) {
        return this.getSqlSession().selectList(nameSpace + "selectMonthlyReportKeyIndexAnalysis", map);
    }

    @Override
    public List<RptMonthlyKeyIndexAnalysisVo> queryMonthlyReportKeyIndexAnalysis(Map<String, Object> map) {
        return this.getSqlSession().selectList(nameSpace + "queryMonthlyReportKeyIndexAnalysis", map);
    }
}
