package com.xunge.comm.basedata.collection;

/**
 * 位置点枚举字段常量(对应dat_baseresource_enum表中的字段)
 */
public class PositionResourceFieldEnum {

    //位置点业务类型
    public static final String POSITION_SERVICE_SITE_TYPE = "positionServiceSiteType";
    //位置点类型
    public static final String POSITION_TYPE = "positionType";
    //位置点生命周期状态
    public static final String POSITION_BASERESOUCE_STATE = "positionBaseresouceState";
    //位置点产权性质
    public static final String PROPERTY_NATURE = "propertyNature";
    //位置点产权单位
    public static final String PROPERTY_UNIT = "propertyUnit";
    //位置点共享单位
    public static final String POSITION_SHARE_UNIT = "positionShareUnit";

 }
