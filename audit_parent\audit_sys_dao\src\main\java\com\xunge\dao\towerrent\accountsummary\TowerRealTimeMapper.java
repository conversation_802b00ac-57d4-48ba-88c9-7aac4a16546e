package com.xunge.dao.towerrent.accountsummary;

import com.xunge.model.towerrent.accountsummary.TwrAccrualPushDetail;
import com.xunge.model.towerrent.accountsummary.WriteOffDetail;
import com.xunge.model.towerrent.accountsummary.query.TwrAccrualWriteOffQueryDto;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

public interface TowerRealTimeMapper {

    /**
     * 调用桔桢平台传输报账数据接口时新增初始记录
     * @param accountsummaryId
     * @param version
     * @return
     */
    int addCompareSiteFee(@Param("accountsummaryId") String accountsummaryId,@Param("version") String version);
}
