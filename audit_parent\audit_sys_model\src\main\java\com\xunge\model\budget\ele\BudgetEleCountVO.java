package com.xunge.model.budget.ele;

import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR> LiangCheng
 * @date : 2022-07-04
 * @desc 预算总表
 */
@Data
@ToString
public class BudgetEleCountVO implements Serializable {
    /**
     *省份
     */
    private String prvId;

    /**
     *省份名称
     */
    private String prvName;

    /**
     *年份
     */
    private Integer onYear;

    /**
     *站点类型
     */
    private Double siteType;

    /**
     *次年总电费
     */
    private BigDecimal followTotalAmount;

    /**
     *次年总电量
     */
    private BigDecimal followTotalDegree;

    /**
     *次年用电成本
     */
    private BigDecimal followTotalElectroCost;

    /**
     *存量用电成本
     */
    private BigDecimal stockTotalElectroCost;

    /**
     *存量总电量
     */
    private BigDecimal stockTotalDegree;

    /**
     *存量总电费
     */
    private BigDecimal stockTotalAmount;

    /**
     *补齐总电量
     */
    private BigDecimal supplementaryTotalDegree;

    /**
     *补齐总电费
     */
    private BigDecimal supplementaryTotalAmount;

    /**
     *新增总电量
     */
    private BigDecimal addTotalDegree;

    /**
     *新增总电费
     */
    private BigDecimal addTotalAmount;

    /**
     *退网总电量
     */
    private BigDecimal backnetTotalDegree;

    /**
     *退网总电费
     */
    private BigDecimal backnetTotalAmount;

    /**
     *包干电费
     */
    private BigDecimal includeAmount;

    /**
     *调整金额
     */
    private BigDecimal revisionAmount;

    /**
     *调整后总预算金额
     */
    private BigDecimal revisionTotalAmount;

    /**
     * 节点
     */
    private Integer nodeType;

    private String workOrderId;
}

