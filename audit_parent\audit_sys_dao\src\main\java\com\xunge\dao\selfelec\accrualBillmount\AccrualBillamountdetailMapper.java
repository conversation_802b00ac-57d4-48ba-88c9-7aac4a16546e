package com.xunge.dao.selfelec.accrualBillmount;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.cursor.Cursor;

import com.xunge.model.costcenter.CostCenterVO;
import com.xunge.model.finance.ext.accClaim.accrual.EleAccrualSecondBillamountDetail;
import com.xunge.model.selfelec.accrual.EleAccrual;
import com.xunge.model.selfelec.accrual.EleAccrualDetail;
import com.xunge.model.selfelec.accrualBillamount.AccrualBillamountTime;
import com.xunge.model.selfelec.accrualBillamount.AccrualBillamountdetail;
import com.xunge.model.selfelec.accrualBillamount.EleAccrualoffsetHistory;

public interface AccrualBillamountdetailMapper {

    int insertBach(List<AccrualBillamountdetail> re);

    List<AccrualBillamountdetail> selectAccrualAll(AccrualBillamountdetail record);

    
    int deleteByPrimaryKey(String billamountdetailId);

    
    int insert(AccrualBillamountdetail record);

    
    int insertSelective(AccrualBillamountdetail record);

    
    AccrualBillamountdetail selectByPrimaryKey(String billamountdetailId);

    
    int updateByPrimaryKeySelective(AccrualBillamountdetail record);

    
    int updateByPrimaryKey(AccrualBillamountdetail record);


    List<AccrualBillamountdetail> selectByaccrualIds(List<String> ids);

    int updateEleAccrual(List<EleAccrual> eleAccruals);

    List<AccrualBillamountdetail> selectAccrualAllByIdsIsExist(List<String> accrualIdList);

    int updateEleAccrualDetail(List<EleAccrualDetail> eleAccruals);

    List<AccrualBillamountdetail> queryBillamountDetailsByBillamountId(Map<String, Object> paraMap);

    List<AccrualBillamountdetail> queryBillamountDetailsByBillamountIdPage(Map<String, Object> paraMap);

    int deleteByBillmounts(List<String> accrualIdList);

    int updeateEleAccrulNull(List<String> accrualIdList);
    
    int updeateEleAccrulSnapNull(List<String> accrualIdList);

    int updeateEleAccrulDetailNull(List<String> accrualIdList);

    int deleteByBillmountsKey(List<String> strings);

    int updeateEleAccrulDetailNullKey(List<String> strings);

    int updeateEleAccrulNullKey(@Param("list")List<String> strings, @Param("type")String type);

    int updateBillamountData(Map<String, Object> paraMap);

    List<AccrualBillamountdetail> queryDownBillamountDetailsByBillamountId(Map<String, Object> map);

    List<String> queryDatSupplierByPregIdAccrual(Map<String, Object> maps);

    List<String> queryregGroup(Map<String, Object> paraMap);

    List<EleAccrualoffsetHistory> queryeAccrualHisData(Map<String, Object> paraMap);

    int getEleBillamountHis(Map<String, Object> parmaMap);

    int insertBachHis(List<EleAccrualoffsetHistory> re);

    List<EleAccrualoffsetHistory> queryBillamountDetailsHis(List<String> list);

    int deleteEleHisData(List<String> strings);

    int deleteHisBillamount(Map<String, Object> paraMap);

    EleAccrualoffsetHistory getEleBillamountHisByBillAmountId(@Param("billamountId") String billamountId);

    List<CostCenterVO> queryCostCenterByAccrual(Map<String, Object> maps);
    
    List<AccrualBillamountdetail> queryByBillamountIdSecondId(@Param("billamountId") String billamountId,@Param("secondBillamountId") String secondBillamountId);

	List<EleAccrualoffsetHistory> queryBillamountHisById(Map<String, Object> paraMap);

	void updateSecondsData(EleAccrualSecondBillamountDetail esd);
 
	Cursor<AccrualBillamountdetail> queryDownBillamountDetailsByBillamountIdCursor(Map<String, Object> map);

	List<AccrualBillamountTime> selectAccrualTime(@Param("accrualId")String accrualId);
}