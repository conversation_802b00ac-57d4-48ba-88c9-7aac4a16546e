package com.xunge.dao.costcenter;

import com.xunge.model.costcenter.CostCenterVO;
import com.xunge.model.costcenter.dataobject.CostCenterDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface CostCenterDao {
    /**
     * 查询成本中心
     * @return 成本中心集合
     */
    List<CostCenterVO> selectAll();

    /**
     * 根据成本中心编号获取成本中心名称
     * @param costCenterList 成本中心编号
     * @return List<CostCenterDO>  成本中心集合
     */
    List<CostCenterDO> queryCostCenterName(@Param("costCenterList") List<String> costCenterList);
}
