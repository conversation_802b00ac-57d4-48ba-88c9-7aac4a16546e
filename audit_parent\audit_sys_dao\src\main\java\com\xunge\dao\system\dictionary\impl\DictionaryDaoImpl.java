package com.xunge.dao.system.dictionary.impl;

import com.xunge.core.page.Page;
import com.xunge.dao.AbstractBaseDao;
import com.xunge.dao.system.dictionary.IDictionaryDao;
import com.xunge.filter.PageInterceptor;
import com.xunge.model.system.dictionary.AttachmentRequiredVO;
import com.xunge.model.system.dictionary.DictionaryGroupVO;
import com.xunge.model.system.dictionary.DictionaryVO;

import java.util.List;
import java.util.Map;

@SuppressWarnings("unchecked")
public class DictionaryDaoImpl extends AbstractBaseDao implements IDictionaryDao {

    final String Namespace = "com.xunge.dao.system_conf_man.DictionaryMapper.";

    @Override
    public List<DictionaryGroupVO> queryAllDictionaryGroup(String prv_id) {
        return this.getSqlSession().selectList(Namespace + "queryAllDictionaryGroup", prv_id);
    }

    @Override
    public Page<List<DictionaryVO>> queryDictionary(Map<String, Object> param, int pageNumber, int pageSize) {
        PageInterceptor.startPage(pageNumber, pageSize);
        this.getSqlSession().selectList(Namespace + "queryDictionary", param);
        return PageInterceptor.endPage();
    }

    @Override
    public DictionaryVO queryDictionaryByID(Map<String, Object> param) {
        return this.getSqlSession().selectOne(Namespace + "queryDictionaryByID", param);
    }

    @Override
    public List<DictionaryVO> queryDictionaryByName(Map<String, Object> param) {
        return this.getSqlSession().selectList(Namespace + "queryDictionaryByName", param);
    }

    @Override
    public List<DictionaryGroupVO> queryDictionarysByCodes(Map<String, Object> param) {
        return this.getSqlSession().selectList(Namespace + "queryDictionarysByCodes", param);
    }

    @Override
    public int insertDictionary(DictionaryVO dictionaryVO) {
        return this.getSqlSession().insert(Namespace + "insertDictionary", dictionaryVO);
    }

    @Override
    public int updateDictionaryStateBatch(Map<String, Object> param) {
        return this.getSqlSession().delete(Namespace + "updateDictionaryStateBatch", param);
    }

    @Override
    public int updateDictionary(DictionaryVO dictionaryVO) {
        return this.getSqlSession().update(Namespace + "updateDictionary", dictionaryVO);
    }

    @Override
    public int updateDictionaryByName(DictionaryVO dictionaryVO) {
        return this.getSqlSession().update(Namespace + "updateDictionaryByName", dictionaryVO);
    }

    @Override
    public int updateValueAndStateByName(DictionaryVO dictionaryVO) {
        return this.getSqlSession().update(Namespace + "updateValueAndStateByName", dictionaryVO);
    }

    @Override
    public int delDictionaryByGroupID(String dictgroup_id) {
        return this.getSqlSession().delete(Namespace + "delDictionaryByGroupID", dictgroup_id);
    }

    @Override
    public List<Map<String, Object>> queryDictionaryByCode(Map<String, Object> map) {
        return this.getSqlSession().selectList(Namespace + "queryDictionaryByGroupCode", map);
    }

    @Override
    public List<DictionaryGroupVO> queryDictionaryGroup(String prv_id) {
        return this.getSqlSession().selectList(Namespace + "queryDictionaryGroup", prv_id);
    }

    @Override
    public int deleteDictionaryByDictName(String cmccRatioDiff) {
        return this.getSqlSession().delete(Namespace + "deleteDictionaryByDictName", cmccRatioDiff);
    }

    @Override
    public DictionaryVO queryDictionaryByCondition(DictionaryVO dictionary) {
        return this.getSqlSession().selectOne(Namespace + "queryDictionaryByCondition", dictionary);
    }

    @Override
    public List<DictionaryVO> queryDictionaryListByCondition(List<DictionaryVO> dictionaryList) {
        return this.getSqlSession().selectList(Namespace + "queryDictionaryListByCondition", dictionaryList);
    }

    @Override
    public List<DictionaryVO> queryDictionaryListByName(List<String> names) {
        return this.getSqlSession().selectList(Namespace + "queryDictionaryListByName", names);
    }

    @Override
    public int insertAttachmentRequired(AttachmentRequiredVO attachmentRequiredVO) {
        return this.getSqlSession().insert(Namespace + "insertAttachmentRequired", attachmentRequiredVO);
    }

    @Override
    public int deleteAttachmentRequiredByDictId(String dictId) {
        return this.getSqlSession().delete(Namespace + "deleteAttachmentRequiredByDictId", dictId);
    }

    @Override
    public List<AttachmentRequiredVO> selectAttachmentRequired(Map<String,Object> paramMap) {
        return this.getSqlSession().selectList(Namespace + "selectAttachmentRequired", paramMap);
    }

    @Override
    public List<DictionaryVO> selectDictList(DictionaryVO dictionaryVO) {
        return this.getSqlSession().selectList(Namespace + "selectDictList", dictionaryVO);
    }

    @Override
    public DictionaryVO selectDictionaryOne(DictionaryVO dictionaryVO) {
        return this.getSqlSession().selectOne(Namespace + "selectDictionaryOne", dictionaryVO);
    }
}
