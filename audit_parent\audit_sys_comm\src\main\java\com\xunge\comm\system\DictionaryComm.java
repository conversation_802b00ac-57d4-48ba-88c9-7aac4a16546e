package com.xunge.comm.system;

public class DictionaryComm {
    /**
     * 字典id
     */
    public static String DICT_ID = "dict_id";
    /**
     * 字典分组id
     */
    public static String DICT_GROUP_ID = "dictgroup_id";
    /**
     * 字典名称
     */
    public static String DICT_NAME = "dict_name";
    public static String DICT_NAME_1 = "dict_name1";
    public static String DICT_NAME_2 = "dict_name2";
    /**
     * 字典值
     */
    public static String DICT_VALUE = "dict_value";
    /**
     * 字典状态
     */
    public static String DICT_STATE = "dict_state";
    /**
     * id-list
     */
    public static String IDS_LIST = "idsList";
    /**
     * 状态
     */
    public static String STATE = "state";
    /**
     * 络电费模块‘电表实际分摊比例’环比差异稽核配置
     */
    public static String CMCC_RATIO_DIFF = "CMCC_RATIO_DIFF";
    /**
     * 络电费模块'直供电用电成本'配置
     */
    public static String DIRECT_ELEC_COST = "direct_elec_cost";
    /**
     * 络电费模块'转供电用电成本'配置
     */
    public static String TURN_ELEC_COST_ZW = "turn_elec_cost_zw";
    public static String TURN_ELEC_COST_TW = "turn_elec_cost_tw";
    public static String TURN_ELEC_COST_DC = "turn_elec_cost_dc";

    public static String ELE_REMARK_TYPE = "ELE_REMARK_TYPE";

    /**
     * 计提模式字典组中的dict_name-租费计提模式
     */
    public static String DICT_NAME_RENT_ACCRUAL_CODE = "rentAccrualMode";
    /**
     * 计提模式字典组中的dict_name-三方塔计提模式
     */
    public static String DICT_NAME_THREE_TOWER_ACCRUAL_CODE = "threeTowerAccrualMode";
    /**
     * 计提模式字典组中的dict_name-铁塔服务费计提模式
     */
    public static String DICT_NAME_TOWER_ACCRUAL_CODE = "towerAccrualMode";
    /**
     * 络电费模块‘电表实际分摊比例’环比差异稽核配置
     */
    public static String ELE_QUANTITY_PREDICTION_CONFIG = "ELE_QUANTITY_PREDICTION_CONFIG";

    /**
     * 网络电费 共享数量与分摊比例不匹配
     */
    public static String SHARENUM_AND_SHARERATIO_NOTMATCH = "SHARENUM_AND_SHARERATIO_NOTMATCH";
    /**
     * 缴费期始异常稽核配置
     */
    public static String ELE_PERIOD_ABNORMAL_SWITCH = "ELE_PERIOD_ABNORMAL_SWITCH";
    /**
     * 缴费单提交，若电表OCR识别&防重复稽核出现异常，该流程需要省管理员稽核
     */
    public static String METER_OCR_DUPLICATE = "METER_OCR_DUPLICATE";
    /**
     * 缴费单提交，若AI图片稽核出现异常，该流程需要省管理员稽核
     */
    public static String IMAGE_AI_CHECK = "IMAGE_AI_CHECK";
    /**
     * 转供电缴费超期判定规则配置
     */
    public static String ELE_PERIOD_OVERDUE_SWITCH = "ELE_PERIOD_OVERDUE_SWITCH";

}
