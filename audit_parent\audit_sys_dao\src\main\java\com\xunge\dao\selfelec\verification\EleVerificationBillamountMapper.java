package com.xunge.dao.selfelec.verification;

import com.xunge.model.selfelec.billamount.AccrualBillamountQueryDto;
import com.xunge.model.selfelec.billamount.AccrualSecondBillamountDto;
import com.xunge.model.selfelec.billamount.PaymentOffsDetailDto;
import com.xunge.model.selfelec.verification.EleVerification;
import com.xunge.model.selfelec.verification.EleVerificationBillamount;
import com.xunge.model.selfelec.verification.EleVerificationBillamountExample;
import com.xunge.model.selfelec.verification.EleVerificationBillamountVo;
import com.xunge.model.selfrent.billamount.EleVerificationBillamountDetailVO;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

public interface EleVerificationBillamountMapper {
    int countByExample(EleVerificationBillamountExample example);

    int deleteByExample(EleVerificationBillamountExample example);

    int deleteByPrimaryKey(String billamountId);

    int insert(EleVerificationBillamount record);

    int insertSelective(EleVerificationBillamount record);

    List<EleVerificationBillamount> selectByExample(EleVerificationBillamountExample example);

    EleVerificationBillamount selectByPrimaryKey(String billamountId);

    int updateByExampleSelective(@Param("record") EleVerificationBillamount record, @Param("example") EleVerificationBillamountExample example);

    int updateByExample(@Param("record") EleVerificationBillamount record, @Param("example") EleVerificationBillamountExample example);

    int updateByPrimaryKeySelective(EleVerificationBillamount record);

    int updateByPrimaryKey(EleVerificationBillamount record);

    //核销汇总信息列表
    List<EleVerificationBillamountVo> selectEleVerificationBillamountList(Map<String, Object> params);
    List<EleVerificationBillamountVo> selectEleVerificationBillAmountListV1(Map<String, Object> params);

    //List<String> selectEleLoanBillamountIds(String billamountCode);
    List<EleVerificationBillamountVo> selectEleVerificationExportList(Map<String, Object> params);

    void updateBillamountIdById(Map<String, Object> map);

    int updateBillAmountStateById(EleVerificationBillamount ele);

    /**
     * @Title: getBillamountByode @Description: TODO(这里用一句话描述这个方法的作用) @param @param
     * collectNum @param @return 设定文件 @return EleVerificationBillamount 返回类型 @throws
     */

    EleVerificationBillamount getBillamountByode(@Param("billamountCode") String billamountCode);

    /**
     * 核销汇总查询详情信息
     *
     * @param billamountId
     * @return
     */
    EleVerificationBillamount queryEleVerificationBillamountById(@Param("billamountId") String billamountId);

    /**
     * 更新核销汇总金额
     *
     * @param param
     * @return
     */
    int updateBillamountAdjustById(EleVerificationBillamount ele);

    /**
     * 根据汇总单编码查询核销信息
     *
     * @param param
     * @return
     */
    List<EleVerification> selectEleVerificationInfo(@Param("billamountId") String billamountId);

    List<EleVerification> selectEleVerificationInfoBybillamountId(Map<String, Object> map);

    void editPayment(EleVerificationBillamount ele);

    int rebuildBillamount(EleVerificationBillamount record);

    int rebuildBillamountdetail(EleVerificationBillamount record);

    int rebuildBillamountPayment(EleVerificationBillamount record);

    List<EleVerificationBillamountDetailVO> exportVerificationBillamountDetail(Map<String, Object> params);

    List<String> selectBillamountidSForExport(Map<String, Object> params);

    List<EleVerificationBillamount> selectBillamountByIds(Map<String, Object> params);

    List<AccrualSecondBillamountDto> selectVerificationOffsDetail(AccrualBillamountQueryDto accrualBillamountQueryDto);

    int addVerificationOffsDetail(List<PaymentOffsDetailDto> list);

    /**
     * 修改电费汇总单冲销金额同时修改计提二次汇总可冲销余额
     * @param dto
     * @return
     */
    int updateVerificationOffsDetail(PaymentOffsDetailDto dto);

    /**
     * 根据汇总单冲销明细id查询二次汇总id
     * @param id
     * @return
     */
    String selectAccrualSecondDetailId(String id);

    /**
     * 删除电费汇总单的冲销明细
     * @param id
     * @return
     */
    int deleteVerificationOffsDetail(String id);

    PaymentOffsDetailDto selectLeftOffsAmount(String secondBillamountId);

    BigDecimal getEleVerificationOffsAmount(String billamountId);

    PaymentOffsDetailDto selectOldOffsAmount(String id);

    List<AccrualSecondBillamountDto> selectAccrualSecondDetail(AccrualBillamountQueryDto accrualBillamountQueryDto);

	/** 
	* @Description: 查询列账冲销金额
	* <AUTHOR>   
	* @date 2024年5月27日 下午3:46:23 
	* @param params
	* @return  
	*/ 
	BigDecimal selectVerificationOffsDetailAmount(AccrualBillamountQueryDto params);

    List<EleVerificationBillamount> selectBillamountById(String billamountId);
}
