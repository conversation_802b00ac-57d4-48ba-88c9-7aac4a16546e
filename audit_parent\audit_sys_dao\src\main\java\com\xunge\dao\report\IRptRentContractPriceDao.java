package com.xunge.dao.report;

import com.xunge.model.report.RptRentContractPriceVO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019年10月31日
 */
public interface IRptRentContractPriceDao {

    List<RptRentContractPriceVO> queryRptRentContractPrice(Map<String, Object> paraMap);

    RptRentContractPriceVO queryRptRentContractPriceTotal(Map<String, Object> paraMap);

    List<RptRentContractPriceVO> queryRptRentContractPriceByPrvId(Map<String, Object> paraMap);

    RptRentContractPriceVO queryRptRentContractPriceByPrvIdTotal(Map<String, Object> paraMap);

    List<RptRentContractPriceVO> queryRptRentContractPriceByPregId(Map<String, Object> paraMap);

    RptRentContractPriceVO queryRptRentContractPriceByPregIdTotal(Map<String, Object> paraMap);

}
