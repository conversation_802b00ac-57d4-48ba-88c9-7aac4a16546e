package com.xunge.dao.selfelec;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

public class EleIcVerificationdetailExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public EleIcVerificationdetailExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        protected void addCriterionForJDBCDate(String condition, Date value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            addCriterion(condition, new java.sql.Date(value.getTime()), property);
        }

        protected void addCriterionForJDBCDate(String condition, List<Date> values, String property) {
            if (values == null || values.size() == 0) {
                throw new RuntimeException("Value list for " + property + " cannot be null or empty");
            }
            List<java.sql.Date> dateList = new ArrayList<java.sql.Date>();
            Iterator<Date> iter = values.iterator();
            while (iter.hasNext()) {
                dateList.add(new java.sql.Date(iter.next().getTime()));
            }
            addCriterion(condition, dateList, property);
        }

        protected void addCriterionForJDBCDate(String condition, Date value1, Date value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            addCriterion(condition, new java.sql.Date(value1.getTime()), new java.sql.Date(value2.getTime()), property);
        }

        public Criteria andIcVerificationdetailIdIsNull() {
            addCriterion("ic_verificationdetail_id is null");
            return (Criteria) this;
        }

        public Criteria andIcVerificationdetailIdIsNotNull() {
            addCriterion("ic_verificationdetail_id is not null");
            return (Criteria) this;
        }

        public Criteria andIcVerificationdetailIdEqualTo(String value) {
            addCriterion("ic_verificationdetail_id =", value, "icVerificationdetailId");
            return (Criteria) this;
        }

        public Criteria andIcVerificationdetailIdNotEqualTo(String value) {
            addCriterion("ic_verificationdetail_id <>", value, "icVerificationdetailId");
            return (Criteria) this;
        }

        public Criteria andIcVerificationdetailIdGreaterThan(String value) {
            addCriterion("ic_verificationdetail_id >", value, "icVerificationdetailId");
            return (Criteria) this;
        }

        public Criteria andIcVerificationdetailIdGreaterThanOrEqualTo(String value) {
            addCriterion("ic_verificationdetail_id >=", value, "icVerificationdetailId");
            return (Criteria) this;
        }

        public Criteria andIcVerificationdetailIdLessThan(String value) {
            addCriterion("ic_verificationdetail_id <", value, "icVerificationdetailId");
            return (Criteria) this;
        }

        public Criteria andIcVerificationdetailIdLessThanOrEqualTo(String value) {
            addCriterion("ic_verificationdetail_id <=", value, "icVerificationdetailId");
            return (Criteria) this;
        }

        public Criteria andIcVerificationdetailIdLike(String value) {
            addCriterion("ic_verificationdetail_id like", value, "icVerificationdetailId");
            return (Criteria) this;
        }

        public Criteria andIcVerificationdetailIdNotLike(String value) {
            addCriterion("ic_verificationdetail_id not like", value, "icVerificationdetailId");
            return (Criteria) this;
        }

        public Criteria andIcVerificationdetailIdIn(List<String> values) {
            addCriterion("ic_verificationdetail_id in", values, "icVerificationdetailId");
            return (Criteria) this;
        }

        public Criteria andIcVerificationdetailIdNotIn(List<String> values) {
            addCriterion("ic_verificationdetail_id not in", values, "icVerificationdetailId");
            return (Criteria) this;
        }

        public Criteria andIcVerificationdetailIdBetween(String value1, String value2) {
            addCriterion("ic_verificationdetail_id between", value1, value2, "icVerificationdetailId");
            return (Criteria) this;
        }

        public Criteria andIcVerificationdetailIdNotBetween(String value1, String value2) {
            addCriterion("ic_verificationdetail_id not between", value1, value2, "icVerificationdetailId");
            return (Criteria) this;
        }

        public Criteria andVerificationIdIsNull() {
            addCriterion("verification_id is null");
            return (Criteria) this;
        }

        public Criteria andVerificationIdIsNotNull() {
            addCriterion("verification_id is not null");
            return (Criteria) this;
        }

        public Criteria andVerificationIdEqualTo(String value) {
            addCriterion("verification_id =", value, "verificationId");
            return (Criteria) this;
        }

        public Criteria andVerificationIdNotEqualTo(String value) {
            addCriterion("verification_id <>", value, "verificationId");
            return (Criteria) this;
        }

        public Criteria andVerificationIdGreaterThan(String value) {
            addCriterion("verification_id >", value, "verificationId");
            return (Criteria) this;
        }

        public Criteria andVerificationIdGreaterThanOrEqualTo(String value) {
            addCriterion("verification_id >=", value, "verificationId");
            return (Criteria) this;
        }

        public Criteria andVerificationIdLessThan(String value) {
            addCriterion("verification_id <", value, "verificationId");
            return (Criteria) this;
        }

        public Criteria andVerificationIdLessThanOrEqualTo(String value) {
            addCriterion("verification_id <=", value, "verificationId");
            return (Criteria) this;
        }

        public Criteria andVerificationIdLike(String value) {
            addCriterion("verification_id like", value, "verificationId");
            return (Criteria) this;
        }

        public Criteria andVerificationIdNotLike(String value) {
            addCriterion("verification_id not like", value, "verificationId");
            return (Criteria) this;
        }

        public Criteria andVerificationIdIn(List<String> values) {
            addCriterion("verification_id in", values, "verificationId");
            return (Criteria) this;
        }

        public Criteria andVerificationIdNotIn(List<String> values) {
            addCriterion("verification_id not in", values, "verificationId");
            return (Criteria) this;
        }

        public Criteria andVerificationIdBetween(String value1, String value2) {
            addCriterion("verification_id between", value1, value2, "verificationId");
            return (Criteria) this;
        }

        public Criteria andVerificationIdNotBetween(String value1, String value2) {
            addCriterion("verification_id not between", value1, value2, "verificationId");
            return (Criteria) this;
        }

        public Criteria andMeterIdIsNull() {
            addCriterion("meter_id is null");
            return (Criteria) this;
        }

        public Criteria andMeterIdIsNotNull() {
            addCriterion("meter_id is not null");
            return (Criteria) this;
        }

        public Criteria andMeterIdEqualTo(String value) {
            addCriterion("meter_id =", value, "meterId");
            return (Criteria) this;
        }

        public Criteria andMeterIdNotEqualTo(String value) {
            addCriterion("meter_id <>", value, "meterId");
            return (Criteria) this;
        }

        public Criteria andMeterIdGreaterThan(String value) {
            addCriterion("meter_id >", value, "meterId");
            return (Criteria) this;
        }

        public Criteria andMeterIdGreaterThanOrEqualTo(String value) {
            addCriterion("meter_id >=", value, "meterId");
            return (Criteria) this;
        }

        public Criteria andMeterIdLessThan(String value) {
            addCriterion("meter_id <", value, "meterId");
            return (Criteria) this;
        }

        public Criteria andMeterIdLessThanOrEqualTo(String value) {
            addCriterion("meter_id <=", value, "meterId");
            return (Criteria) this;
        }

        public Criteria andMeterIdLike(String value) {
            addCriterion("meter_id like", value, "meterId");
            return (Criteria) this;
        }

        public Criteria andMeterIdNotLike(String value) {
            addCriterion("meter_id not like", value, "meterId");
            return (Criteria) this;
        }

        public Criteria andMeterIdIn(List<String> values) {
            addCriterion("meter_id in", values, "meterId");
            return (Criteria) this;
        }

        public Criteria andMeterIdNotIn(List<String> values) {
            addCriterion("meter_id not in", values, "meterId");
            return (Criteria) this;
        }

        public Criteria andMeterIdBetween(String value1, String value2) {
            addCriterion("meter_id between", value1, value2, "meterId");
            return (Criteria) this;
        }

        public Criteria andMeterIdNotBetween(String value1, String value2) {
            addCriterion("meter_id not between", value1, value2, "meterId");
            return (Criteria) this;
        }

        public Criteria andBillamountIdIsNull() {
            addCriterion("billamount_id is null");
            return (Criteria) this;
        }

        public Criteria andBillamountIdIsNotNull() {
            addCriterion("billamount_id is not null");
            return (Criteria) this;
        }

        public Criteria andBillamountIdEqualTo(String value) {
            addCriterion("billamount_id =", value, "billamountId");
            return (Criteria) this;
        }

        public Criteria andBillamountIdNotEqualTo(String value) {
            addCriterion("billamount_id <>", value, "billamountId");
            return (Criteria) this;
        }

        public Criteria andBillamountIdGreaterThan(String value) {
            addCriterion("billamount_id >", value, "billamountId");
            return (Criteria) this;
        }

        public Criteria andBillamountIdGreaterThanOrEqualTo(String value) {
            addCriterion("billamount_id >=", value, "billamountId");
            return (Criteria) this;
        }

        public Criteria andBillamountIdLessThan(String value) {
            addCriterion("billamount_id <", value, "billamountId");
            return (Criteria) this;
        }

        public Criteria andBillamountIdLessThanOrEqualTo(String value) {
            addCriterion("billamount_id <=", value, "billamountId");
            return (Criteria) this;
        }

        public Criteria andBillamountIdLike(String value) {
            addCriterion("billamount_id like", value, "billamountId");
            return (Criteria) this;
        }

        public Criteria andBillamountIdNotLike(String value) {
            addCriterion("billamount_id not like", value, "billamountId");
            return (Criteria) this;
        }

        public Criteria andBillamountIdIn(List<String> values) {
            addCriterion("billamount_id in", values, "billamountId");
            return (Criteria) this;
        }

        public Criteria andBillamountIdNotIn(List<String> values) {
            addCriterion("billamount_id not in", values, "billamountId");
            return (Criteria) this;
        }

        public Criteria andBillamountIdBetween(String value1, String value2) {
            addCriterion("billamount_id between", value1, value2, "billamountId");
            return (Criteria) this;
        }

        public Criteria andBillamountIdNotBetween(String value1, String value2) {
            addCriterion("billamount_id not between", value1, value2, "billamountId");
            return (Criteria) this;
        }

        public Criteria andReadmeteruserIdIsNull() {
            addCriterion("readmeteruser_id is null");
            return (Criteria) this;
        }

        public Criteria andReadmeteruserIdIsNotNull() {
            addCriterion("readmeteruser_id is not null");
            return (Criteria) this;
        }

        public Criteria andReadmeteruserIdEqualTo(String value) {
            addCriterion("readmeteruser_id =", value, "readmeteruserId");
            return (Criteria) this;
        }

        public Criteria andReadmeteruserIdNotEqualTo(String value) {
            addCriterion("readmeteruser_id <>", value, "readmeteruserId");
            return (Criteria) this;
        }

        public Criteria andReadmeteruserIdGreaterThan(String value) {
            addCriterion("readmeteruser_id >", value, "readmeteruserId");
            return (Criteria) this;
        }

        public Criteria andReadmeteruserIdGreaterThanOrEqualTo(String value) {
            addCriterion("readmeteruser_id >=", value, "readmeteruserId");
            return (Criteria) this;
        }

        public Criteria andReadmeteruserIdLessThan(String value) {
            addCriterion("readmeteruser_id <", value, "readmeteruserId");
            return (Criteria) this;
        }

        public Criteria andReadmeteruserIdLessThanOrEqualTo(String value) {
            addCriterion("readmeteruser_id <=", value, "readmeteruserId");
            return (Criteria) this;
        }

        public Criteria andReadmeteruserIdLike(String value) {
            addCriterion("readmeteruser_id like", value, "readmeteruserId");
            return (Criteria) this;
        }

        public Criteria andReadmeteruserIdNotLike(String value) {
            addCriterion("readmeteruser_id not like", value, "readmeteruserId");
            return (Criteria) this;
        }

        public Criteria andReadmeteruserIdIn(List<String> values) {
            addCriterion("readmeteruser_id in", values, "readmeteruserId");
            return (Criteria) this;
        }

        public Criteria andReadmeteruserIdNotIn(List<String> values) {
            addCriterion("readmeteruser_id not in", values, "readmeteruserId");
            return (Criteria) this;
        }

        public Criteria andReadmeteruserIdBetween(String value1, String value2) {
            addCriterion("readmeteruser_id between", value1, value2, "readmeteruserId");
            return (Criteria) this;
        }

        public Criteria andReadmeteruserIdNotBetween(String value1, String value2) {
            addCriterion("readmeteruser_id not between", value1, value2, "readmeteruserId");
            return (Criteria) this;
        }

        public Criteria andBaseresourceelectricmeterIdIsNull() {
            addCriterion("baseresourceelectricmeter_id is null");
            return (Criteria) this;
        }

        public Criteria andBaseresourceelectricmeterIdIsNotNull() {
            addCriterion("baseresourceelectricmeter_id is not null");
            return (Criteria) this;
        }

        public Criteria andBaseresourceelectricmeterIdEqualTo(String value) {
            addCriterion("baseresourceelectricmeter_id =", value, "baseresourceelectricmeterId");
            return (Criteria) this;
        }

        public Criteria andBaseresourceelectricmeterIdNotEqualTo(String value) {
            addCriterion("baseresourceelectricmeter_id <>", value, "baseresourceelectricmeterId");
            return (Criteria) this;
        }

        public Criteria andBaseresourceelectricmeterIdGreaterThan(String value) {
            addCriterion("baseresourceelectricmeter_id >", value, "baseresourceelectricmeterId");
            return (Criteria) this;
        }

        public Criteria andBaseresourceelectricmeterIdGreaterThanOrEqualTo(String value) {
            addCriterion("baseresourceelectricmeter_id >=", value, "baseresourceelectricmeterId");
            return (Criteria) this;
        }

        public Criteria andBaseresourceelectricmeterIdLessThan(String value) {
            addCriterion("baseresourceelectricmeter_id <", value, "baseresourceelectricmeterId");
            return (Criteria) this;
        }

        public Criteria andBaseresourceelectricmeterIdLessThanOrEqualTo(String value) {
            addCriterion("baseresourceelectricmeter_id <=", value, "baseresourceelectricmeterId");
            return (Criteria) this;
        }

        public Criteria andBaseresourceelectricmeterIdLike(String value) {
            addCriterion("baseresourceelectricmeter_id like", value, "baseresourceelectricmeterId");
            return (Criteria) this;
        }

        public Criteria andBaseresourceelectricmeterIdNotLike(String value) {
            addCriterion("baseresourceelectricmeter_id not like", value, "baseresourceelectricmeterId");
            return (Criteria) this;
        }

        public Criteria andBaseresourceelectricmeterIdIn(List<String> values) {
            addCriterion("baseresourceelectricmeter_id in", values, "baseresourceelectricmeterId");
            return (Criteria) this;
        }

        public Criteria andBaseresourceelectricmeterIdNotIn(List<String> values) {
            addCriterion("baseresourceelectricmeter_id not in", values, "baseresourceelectricmeterId");
            return (Criteria) this;
        }

        public Criteria andBaseresourceelectricmeterIdBetween(String value1, String value2) {
            addCriterion("baseresourceelectricmeter_id between", value1, value2, "baseresourceelectricmeterId");
            return (Criteria) this;
        }

        public Criteria andBaseresourceelectricmeterIdNotBetween(String value1, String value2) {
            addCriterion("baseresourceelectricmeter_id not between", value1, value2, "baseresourceelectricmeterId");
            return (Criteria) this;
        }

        public Criteria andBillTypeIsNull() {
            addCriterion("bill_type is null");
            return (Criteria) this;
        }

        public Criteria andBillTypeIsNotNull() {
            addCriterion("bill_type is not null");
            return (Criteria) this;
        }

        public Criteria andBillTypeEqualTo(String value) {
            addCriterion("bill_type =", value, "billType");
            return (Criteria) this;
        }

        public Criteria andBillTypeNotEqualTo(String value) {
            addCriterion("bill_type <>", value, "billType");
            return (Criteria) this;
        }

        public Criteria andBillTypeGreaterThan(String value) {
            addCriterion("bill_type >", value, "billType");
            return (Criteria) this;
        }

        public Criteria andBillTypeGreaterThanOrEqualTo(String value) {
            addCriterion("bill_type >=", value, "billType");
            return (Criteria) this;
        }

        public Criteria andBillTypeLessThan(String value) {
            addCriterion("bill_type <", value, "billType");
            return (Criteria) this;
        }

        public Criteria andBillTypeLessThanOrEqualTo(String value) {
            addCriterion("bill_type <=", value, "billType");
            return (Criteria) this;
        }

        public Criteria andBillTypeLike(String value) {
            addCriterion("bill_type like", value, "billType");
            return (Criteria) this;
        }

        public Criteria andBillTypeNotLike(String value) {
            addCriterion("bill_type not like", value, "billType");
            return (Criteria) this;
        }

        public Criteria andBillTypeIn(List<String> values) {
            addCriterion("bill_type in", values, "billType");
            return (Criteria) this;
        }

        public Criteria andBillTypeNotIn(List<String> values) {
            addCriterion("bill_type not in", values, "billType");
            return (Criteria) this;
        }

        public Criteria andBillTypeBetween(String value1, String value2) {
            addCriterion("bill_type between", value1, value2, "billType");
            return (Criteria) this;
        }

        public Criteria andBillTypeNotBetween(String value1, String value2) {
            addCriterion("bill_type not between", value1, value2, "billType");
            return (Criteria) this;
        }

        public Criteria andBusinessTypeIsNull() {
            addCriterion("business_type is null");
            return (Criteria) this;
        }

        public Criteria andBusinessTypeIsNotNull() {
            addCriterion("business_type is not null");
            return (Criteria) this;
        }

        public Criteria andBusinessTypeEqualTo(String value) {
            addCriterion("business_type =", value, "businessType");
            return (Criteria) this;
        }

        public Criteria andBusinessTypeNotEqualTo(String value) {
            addCriterion("business_type <>", value, "businessType");
            return (Criteria) this;
        }

        public Criteria andBusinessTypeGreaterThan(String value) {
            addCriterion("business_type >", value, "businessType");
            return (Criteria) this;
        }

        public Criteria andBusinessTypeGreaterThanOrEqualTo(String value) {
            addCriterion("business_type >=", value, "businessType");
            return (Criteria) this;
        }

        public Criteria andBusinessTypeLessThan(String value) {
            addCriterion("business_type <", value, "businessType");
            return (Criteria) this;
        }

        public Criteria andBusinessTypeLessThanOrEqualTo(String value) {
            addCriterion("business_type <=", value, "businessType");
            return (Criteria) this;
        }

        public Criteria andBusinessTypeLike(String value) {
            addCriterion("business_type like", value, "businessType");
            return (Criteria) this;
        }

        public Criteria andBusinessTypeNotLike(String value) {
            addCriterion("business_type not like", value, "businessType");
            return (Criteria) this;
        }

        public Criteria andBusinessTypeIn(List<String> values) {
            addCriterion("business_type in", values, "businessType");
            return (Criteria) this;
        }

        public Criteria andBusinessTypeNotIn(List<String> values) {
            addCriterion("business_type not in", values, "businessType");
            return (Criteria) this;
        }

        public Criteria andBusinessTypeBetween(String value1, String value2) {
            addCriterion("business_type between", value1, value2, "businessType");
            return (Criteria) this;
        }

        public Criteria andBusinessTypeNotBetween(String value1, String value2) {
            addCriterion("business_type not between", value1, value2, "businessType");
            return (Criteria) this;
        }

        public Criteria andAmountTypeIsNull() {
            addCriterion("amount_type is null");
            return (Criteria) this;
        }

        public Criteria andAmountTypeIsNotNull() {
            addCriterion("amount_type is not null");
            return (Criteria) this;
        }

        public Criteria andAmountTypeEqualTo(String value) {
            addCriterion("amount_type =", value, "amountType");
            return (Criteria) this;
        }

        public Criteria andAmountTypeNotEqualTo(String value) {
            addCriterion("amount_type <>", value, "amountType");
            return (Criteria) this;
        }

        public Criteria andAmountTypeGreaterThan(String value) {
            addCriterion("amount_type >", value, "amountType");
            return (Criteria) this;
        }

        public Criteria andAmountTypeGreaterThanOrEqualTo(String value) {
            addCriterion("amount_type >=", value, "amountType");
            return (Criteria) this;
        }

        public Criteria andAmountTypeLessThan(String value) {
            addCriterion("amount_type <", value, "amountType");
            return (Criteria) this;
        }

        public Criteria andAmountTypeLessThanOrEqualTo(String value) {
            addCriterion("amount_type <=", value, "amountType");
            return (Criteria) this;
        }

        public Criteria andAmountTypeLike(String value) {
            addCriterion("amount_type like", value, "amountType");
            return (Criteria) this;
        }

        public Criteria andAmountTypeNotLike(String value) {
            addCriterion("amount_type not like", value, "amountType");
            return (Criteria) this;
        }

        public Criteria andAmountTypeIn(List<String> values) {
            addCriterion("amount_type in", values, "amountType");
            return (Criteria) this;
        }

        public Criteria andAmountTypeNotIn(List<String> values) {
            addCriterion("amount_type not in", values, "amountType");
            return (Criteria) this;
        }

        public Criteria andAmountTypeBetween(String value1, String value2) {
            addCriterion("amount_type between", value1, value2, "amountType");
            return (Criteria) this;
        }

        public Criteria andAmountTypeNotBetween(String value1, String value2) {
            addCriterion("amount_type not between", value1, value2, "amountType");
            return (Criteria) this;
        }

        public Criteria andInvoiceTypeIsNull() {
            addCriterion("invoice_type is null");
            return (Criteria) this;
        }

        public Criteria andInvoiceTypeIsNotNull() {
            addCriterion("invoice_type is not null");
            return (Criteria) this;
        }

        public Criteria andInvoiceTypeEqualTo(String value) {
            addCriterion("invoice_type =", value, "invoiceType");
            return (Criteria) this;
        }

        public Criteria andInvoiceTypeNotEqualTo(String value) {
            addCriterion("invoice_type <>", value, "invoiceType");
            return (Criteria) this;
        }

        public Criteria andInvoiceTypeGreaterThan(String value) {
            addCriterion("invoice_type >", value, "invoiceType");
            return (Criteria) this;
        }

        public Criteria andInvoiceTypeGreaterThanOrEqualTo(String value) {
            addCriterion("invoice_type >=", value, "invoiceType");
            return (Criteria) this;
        }

        public Criteria andInvoiceTypeLessThan(String value) {
            addCriterion("invoice_type <", value, "invoiceType");
            return (Criteria) this;
        }

        public Criteria andInvoiceTypeLessThanOrEqualTo(String value) {
            addCriterion("invoice_type <=", value, "invoiceType");
            return (Criteria) this;
        }

        public Criteria andInvoiceTypeLike(String value) {
            addCriterion("invoice_type like", value, "invoiceType");
            return (Criteria) this;
        }

        public Criteria andInvoiceTypeNotLike(String value) {
            addCriterion("invoice_type not like", value, "invoiceType");
            return (Criteria) this;
        }

        public Criteria andInvoiceTypeIn(List<String> values) {
            addCriterion("invoice_type in", values, "invoiceType");
            return (Criteria) this;
        }

        public Criteria andInvoiceTypeNotIn(List<String> values) {
            addCriterion("invoice_type not in", values, "invoiceType");
            return (Criteria) this;
        }

        public Criteria andInvoiceTypeBetween(String value1, String value2) {
            addCriterion("invoice_type between", value1, value2, "invoiceType");
            return (Criteria) this;
        }

        public Criteria andInvoiceTypeNotBetween(String value1, String value2) {
            addCriterion("invoice_type not between", value1, value2, "invoiceType");
            return (Criteria) this;
        }

        public Criteria andPaymentMethodIsNull() {
            addCriterion("payment_method is null");
            return (Criteria) this;
        }

        public Criteria andPaymentMethodIsNotNull() {
            addCriterion("payment_method is not null");
            return (Criteria) this;
        }

        public Criteria andPaymentMethodEqualTo(String value) {
            addCriterion("payment_method =", value, "paymentMethod");
            return (Criteria) this;
        }

        public Criteria andPaymentMethodNotEqualTo(String value) {
            addCriterion("payment_method <>", value, "paymentMethod");
            return (Criteria) this;
        }

        public Criteria andPaymentMethodGreaterThan(String value) {
            addCriterion("payment_method >", value, "paymentMethod");
            return (Criteria) this;
        }

        public Criteria andPaymentMethodGreaterThanOrEqualTo(String value) {
            addCriterion("payment_method >=", value, "paymentMethod");
            return (Criteria) this;
        }

        public Criteria andPaymentMethodLessThan(String value) {
            addCriterion("payment_method <", value, "paymentMethod");
            return (Criteria) this;
        }

        public Criteria andPaymentMethodLessThanOrEqualTo(String value) {
            addCriterion("payment_method <=", value, "paymentMethod");
            return (Criteria) this;
        }

        public Criteria andPaymentMethodLike(String value) {
            addCriterion("payment_method like", value, "paymentMethod");
            return (Criteria) this;
        }

        public Criteria andPaymentMethodNotLike(String value) {
            addCriterion("payment_method not like", value, "paymentMethod");
            return (Criteria) this;
        }

        public Criteria andPaymentMethodIn(List<String> values) {
            addCriterion("payment_method in", values, "paymentMethod");
            return (Criteria) this;
        }

        public Criteria andPaymentMethodNotIn(List<String> values) {
            addCriterion("payment_method not in", values, "paymentMethod");
            return (Criteria) this;
        }

        public Criteria andPaymentMethodBetween(String value1, String value2) {
            addCriterion("payment_method between", value1, value2, "paymentMethod");
            return (Criteria) this;
        }

        public Criteria andPaymentMethodNotBetween(String value1, String value2) {
            addCriterion("payment_method not between", value1, value2, "paymentMethod");
            return (Criteria) this;
        }

        public Criteria andBuyMethodIsNull() {
            addCriterion("buy_method is null");
            return (Criteria) this;
        }

        public Criteria andBuyMethodIsNotNull() {
            addCriterion("buy_method is not null");
            return (Criteria) this;
        }

        public Criteria andBuyMethodEqualTo(String value) {
            addCriterion("buy_method =", value, "buyMethod");
            return (Criteria) this;
        }

        public Criteria andBuyMethodNotEqualTo(String value) {
            addCriterion("buy_method <>", value, "buyMethod");
            return (Criteria) this;
        }

        public Criteria andBuyMethodGreaterThan(String value) {
            addCriterion("buy_method >", value, "buyMethod");
            return (Criteria) this;
        }

        public Criteria andBuyMethodGreaterThanOrEqualTo(String value) {
            addCriterion("buy_method >=", value, "buyMethod");
            return (Criteria) this;
        }

        public Criteria andBuyMethodLessThan(String value) {
            addCriterion("buy_method <", value, "buyMethod");
            return (Criteria) this;
        }

        public Criteria andBuyMethodLessThanOrEqualTo(String value) {
            addCriterion("buy_method <=", value, "buyMethod");
            return (Criteria) this;
        }

        public Criteria andBuyMethodLike(String value) {
            addCriterion("buy_method like", value, "buyMethod");
            return (Criteria) this;
        }

        public Criteria andBuyMethodNotLike(String value) {
            addCriterion("buy_method not like", value, "buyMethod");
            return (Criteria) this;
        }

        public Criteria andBuyMethodIn(List<String> values) {
            addCriterion("buy_method in", values, "buyMethod");
            return (Criteria) this;
        }

        public Criteria andBuyMethodNotIn(List<String> values) {
            addCriterion("buy_method not in", values, "buyMethod");
            return (Criteria) this;
        }

        public Criteria andBuyMethodBetween(String value1, String value2) {
            addCriterion("buy_method between", value1, value2, "buyMethod");
            return (Criteria) this;
        }

        public Criteria andBuyMethodNotBetween(String value1, String value2) {
            addCriterion("buy_method not between", value1, value2, "buyMethod");
            return (Criteria) this;
        }

        public Criteria andUseDegreesIsNull() {
            addCriterion("use_degrees is null");
            return (Criteria) this;
        }

        public Criteria andUseDegreesIsNotNull() {
            addCriterion("use_degrees is not null");
            return (Criteria) this;
        }

        public Criteria andUseDegreesEqualTo(String value) {
            addCriterion("use_degrees =", value, "useDegrees");
            return (Criteria) this;
        }

        public Criteria andUseDegreesNotEqualTo(String value) {
            addCriterion("use_degrees <>", value, "useDegrees");
            return (Criteria) this;
        }

        public Criteria andUseDegreesGreaterThan(String value) {
            addCriterion("use_degrees >", value, "useDegrees");
            return (Criteria) this;
        }

        public Criteria andUseDegreesGreaterThanOrEqualTo(String value) {
            addCriterion("use_degrees >=", value, "useDegrees");
            return (Criteria) this;
        }

        public Criteria andUseDegreesLessThan(String value) {
            addCriterion("use_degrees <", value, "useDegrees");
            return (Criteria) this;
        }

        public Criteria andUseDegreesLessThanOrEqualTo(String value) {
            addCriterion("use_degrees <=", value, "useDegrees");
            return (Criteria) this;
        }

        public Criteria andUseDegreesLike(String value) {
            addCriterion("use_degrees like", value, "useDegrees");
            return (Criteria) this;
        }

        public Criteria andUseDegreesNotLike(String value) {
            addCriterion("use_degrees not like", value, "useDegrees");
            return (Criteria) this;
        }

        public Criteria andUseDegreesIn(List<String> values) {
            addCriterion("use_degrees in", values, "useDegrees");
            return (Criteria) this;
        }

        public Criteria andUseDegreesNotIn(List<String> values) {
            addCriterion("use_degrees not in", values, "useDegrees");
            return (Criteria) this;
        }

        public Criteria andUseDegreesBetween(String value1, String value2) {
            addCriterion("use_degrees between", value1, value2, "useDegrees");
            return (Criteria) this;
        }

        public Criteria andUseDegreesNotBetween(String value1, String value2) {
            addCriterion("use_degrees not between", value1, value2, "useDegrees");
            return (Criteria) this;
        }

        public Criteria andMeterRateIsNull() {
            addCriterion("meter_rate is null");
            return (Criteria) this;
        }

        public Criteria andMeterRateIsNotNull() {
            addCriterion("meter_rate is not null");
            return (Criteria) this;
        }

        public Criteria andMeterRateEqualTo(BigDecimal value) {
            addCriterion("meter_rate =", value, "meterRate");
            return (Criteria) this;
        }

        public Criteria andMeterRateNotEqualTo(BigDecimal value) {
            addCriterion("meter_rate <>", value, "meterRate");
            return (Criteria) this;
        }

        public Criteria andMeterRateGreaterThan(BigDecimal value) {
            addCriterion("meter_rate >", value, "meterRate");
            return (Criteria) this;
        }

        public Criteria andMeterRateGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("meter_rate >=", value, "meterRate");
            return (Criteria) this;
        }

        public Criteria andMeterRateLessThan(BigDecimal value) {
            addCriterion("meter_rate <", value, "meterRate");
            return (Criteria) this;
        }

        public Criteria andMeterRateLessThanOrEqualTo(BigDecimal value) {
            addCriterion("meter_rate <=", value, "meterRate");
            return (Criteria) this;
        }

        public Criteria andMeterRateIn(List<BigDecimal> values) {
            addCriterion("meter_rate in", values, "meterRate");
            return (Criteria) this;
        }

        public Criteria andMeterRateNotIn(List<BigDecimal> values) {
            addCriterion("meter_rate not in", values, "meterRate");
            return (Criteria) this;
        }

        public Criteria andMeterRateBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("meter_rate between", value1, value2, "meterRate");
            return (Criteria) this;
        }

        public Criteria andMeterRateNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("meter_rate not between", value1, value2, "meterRate");
            return (Criteria) this;
        }

        public Criteria andMeterLossIsNull() {
            addCriterion("meter_loss is null");
            return (Criteria) this;
        }

        public Criteria andMeterLossIsNotNull() {
            addCriterion("meter_loss is not null");
            return (Criteria) this;
        }

        public Criteria andMeterLossEqualTo(BigDecimal value) {
            addCriterion("meter_loss =", value, "meterLoss");
            return (Criteria) this;
        }

        public Criteria andMeterLossNotEqualTo(BigDecimal value) {
            addCriterion("meter_loss <>", value, "meterLoss");
            return (Criteria) this;
        }

        public Criteria andMeterLossGreaterThan(BigDecimal value) {
            addCriterion("meter_loss >", value, "meterLoss");
            return (Criteria) this;
        }

        public Criteria andMeterLossGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("meter_loss >=", value, "meterLoss");
            return (Criteria) this;
        }

        public Criteria andMeterLossLessThan(BigDecimal value) {
            addCriterion("meter_loss <", value, "meterLoss");
            return (Criteria) this;
        }

        public Criteria andMeterLossLessThanOrEqualTo(BigDecimal value) {
            addCriterion("meter_loss <=", value, "meterLoss");
            return (Criteria) this;
        }

        public Criteria andMeterLossIn(List<BigDecimal> values) {
            addCriterion("meter_loss in", values, "meterLoss");
            return (Criteria) this;
        }

        public Criteria andMeterLossNotIn(List<BigDecimal> values) {
            addCriterion("meter_loss not in", values, "meterLoss");
            return (Criteria) this;
        }

        public Criteria andMeterLossBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("meter_loss between", value1, value2, "meterLoss");
            return (Criteria) this;
        }

        public Criteria andMeterLossNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("meter_loss not between", value1, value2, "meterLoss");
            return (Criteria) this;
        }

        public Criteria andLossDegreesIsNull() {
            addCriterion("loss_degrees is null");
            return (Criteria) this;
        }

        public Criteria andLossDegreesIsNotNull() {
            addCriterion("loss_degrees is not null");
            return (Criteria) this;
        }

        public Criteria andLossDegreesEqualTo(String value) {
            addCriterion("loss_degrees =", value, "lossDegrees");
            return (Criteria) this;
        }

        public Criteria andLossDegreesNotEqualTo(String value) {
            addCriterion("loss_degrees <>", value, "lossDegrees");
            return (Criteria) this;
        }

        public Criteria andLossDegreesGreaterThan(String value) {
            addCriterion("loss_degrees >", value, "lossDegrees");
            return (Criteria) this;
        }

        public Criteria andLossDegreesGreaterThanOrEqualTo(String value) {
            addCriterion("loss_degrees >=", value, "lossDegrees");
            return (Criteria) this;
        }

        public Criteria andLossDegreesLessThan(String value) {
            addCriterion("loss_degrees <", value, "lossDegrees");
            return (Criteria) this;
        }

        public Criteria andLossDegreesLessThanOrEqualTo(String value) {
            addCriterion("loss_degrees <=", value, "lossDegrees");
            return (Criteria) this;
        }

        public Criteria andLossDegreesLike(String value) {
            addCriterion("loss_degrees like", value, "lossDegrees");
            return (Criteria) this;
        }

        public Criteria andLossDegreesNotLike(String value) {
            addCriterion("loss_degrees not like", value, "lossDegrees");
            return (Criteria) this;
        }

        public Criteria andLossDegreesIn(List<String> values) {
            addCriterion("loss_degrees in", values, "lossDegrees");
            return (Criteria) this;
        }

        public Criteria andLossDegreesNotIn(List<String> values) {
            addCriterion("loss_degrees not in", values, "lossDegrees");
            return (Criteria) this;
        }

        public Criteria andLossDegreesBetween(String value1, String value2) {
            addCriterion("loss_degrees between", value1, value2, "lossDegrees");
            return (Criteria) this;
        }

        public Criteria andLossDegreesNotBetween(String value1, String value2) {
            addCriterion("loss_degrees not between", value1, value2, "lossDegrees");
            return (Criteria) this;
        }

        public Criteria andLossNotaxAmountIsNull() {
            addCriterion("loss_notax_amount is null");
            return (Criteria) this;
        }

        public Criteria andLossNotaxAmountIsNotNull() {
            addCriterion("loss_notax_amount is not null");
            return (Criteria) this;
        }

        public Criteria andLossNotaxAmountEqualTo(BigDecimal value) {
            addCriterion("loss_notax_amount =", value, "lossNotaxAmount");
            return (Criteria) this;
        }

        public Criteria andLossNotaxAmountNotEqualTo(BigDecimal value) {
            addCriterion("loss_notax_amount <>", value, "lossNotaxAmount");
            return (Criteria) this;
        }

        public Criteria andLossNotaxAmountGreaterThan(BigDecimal value) {
            addCriterion("loss_notax_amount >", value, "lossNotaxAmount");
            return (Criteria) this;
        }

        public Criteria andLossNotaxAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("loss_notax_amount >=", value, "lossNotaxAmount");
            return (Criteria) this;
        }

        public Criteria andLossNotaxAmountLessThan(BigDecimal value) {
            addCriterion("loss_notax_amount <", value, "lossNotaxAmount");
            return (Criteria) this;
        }

        public Criteria andLossNotaxAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("loss_notax_amount <=", value, "lossNotaxAmount");
            return (Criteria) this;
        }

        public Criteria andLossNotaxAmountIn(List<BigDecimal> values) {
            addCriterion("loss_notax_amount in", values, "lossNotaxAmount");
            return (Criteria) this;
        }

        public Criteria andLossNotaxAmountNotIn(List<BigDecimal> values) {
            addCriterion("loss_notax_amount not in", values, "lossNotaxAmount");
            return (Criteria) this;
        }

        public Criteria andLossNotaxAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("loss_notax_amount between", value1, value2, "lossNotaxAmount");
            return (Criteria) this;
        }

        public Criteria andLossNotaxAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("loss_notax_amount not between", value1, value2, "lossNotaxAmount");
            return (Criteria) this;
        }

        public Criteria andLossTaxIsNull() {
            addCriterion("loss_tax is null");
            return (Criteria) this;
        }

        public Criteria andLossTaxIsNotNull() {
            addCriterion("loss_tax is not null");
            return (Criteria) this;
        }

        public Criteria andLossTaxEqualTo(BigDecimal value) {
            addCriterion("loss_tax =", value, "lossTax");
            return (Criteria) this;
        }

        public Criteria andLossTaxNotEqualTo(BigDecimal value) {
            addCriterion("loss_tax <>", value, "lossTax");
            return (Criteria) this;
        }

        public Criteria andLossTaxGreaterThan(BigDecimal value) {
            addCriterion("loss_tax >", value, "lossTax");
            return (Criteria) this;
        }

        public Criteria andLossTaxGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("loss_tax >=", value, "lossTax");
            return (Criteria) this;
        }

        public Criteria andLossTaxLessThan(BigDecimal value) {
            addCriterion("loss_tax <", value, "lossTax");
            return (Criteria) this;
        }

        public Criteria andLossTaxLessThanOrEqualTo(BigDecimal value) {
            addCriterion("loss_tax <=", value, "lossTax");
            return (Criteria) this;
        }

        public Criteria andLossTaxIn(List<BigDecimal> values) {
            addCriterion("loss_tax in", values, "lossTax");
            return (Criteria) this;
        }

        public Criteria andLossTaxNotIn(List<BigDecimal> values) {
            addCriterion("loss_tax not in", values, "lossTax");
            return (Criteria) this;
        }

        public Criteria andLossTaxBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("loss_tax between", value1, value2, "lossTax");
            return (Criteria) this;
        }

        public Criteria andLossTaxNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("loss_tax not between", value1, value2, "lossTax");
            return (Criteria) this;
        }

        public Criteria andNowDegreeIsNull() {
            addCriterion("now_degree is null");
            return (Criteria) this;
        }

        public Criteria andNowDegreeIsNotNull() {
            addCriterion("now_degree is not null");
            return (Criteria) this;
        }

        public Criteria andNowDegreeEqualTo(BigDecimal value) {
            addCriterion("now_degree =", value, "nowDegree");
            return (Criteria) this;
        }

        public Criteria andNowDegreeNotEqualTo(BigDecimal value) {
            addCriterion("now_degree <>", value, "nowDegree");
            return (Criteria) this;
        }

        public Criteria andNowDegreeGreaterThan(BigDecimal value) {
            addCriterion("now_degree >", value, "nowDegree");
            return (Criteria) this;
        }

        public Criteria andNowDegreeGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("now_degree >=", value, "nowDegree");
            return (Criteria) this;
        }

        public Criteria andNowDegreeLessThan(BigDecimal value) {
            addCriterion("now_degree <", value, "nowDegree");
            return (Criteria) this;
        }

        public Criteria andNowDegreeLessThanOrEqualTo(BigDecimal value) {
            addCriterion("now_degree <=", value, "nowDegree");
            return (Criteria) this;
        }

        public Criteria andNowDegreeIn(List<BigDecimal> values) {
            addCriterion("now_degree in", values, "nowDegree");
            return (Criteria) this;
        }

        public Criteria andNowDegreeNotIn(List<BigDecimal> values) {
            addCriterion("now_degree not in", values, "nowDegree");
            return (Criteria) this;
        }

        public Criteria andNowDegreeBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("now_degree between", value1, value2, "nowDegree");
            return (Criteria) this;
        }

        public Criteria andNowDegreeNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("now_degree not between", value1, value2, "nowDegree");
            return (Criteria) this;
        }

        public Criteria andNowReadnumIsNull() {
            addCriterion("now_readnum is null");
            return (Criteria) this;
        }

        public Criteria andNowReadnumIsNotNull() {
            addCriterion("now_readnum is not null");
            return (Criteria) this;
        }

        public Criteria andNowReadnumEqualTo(BigDecimal value) {
            addCriterion("now_readnum =", value, "nowReadnum");
            return (Criteria) this;
        }

        public Criteria andNowReadnumNotEqualTo(BigDecimal value) {
            addCriterion("now_readnum <>", value, "nowReadnum");
            return (Criteria) this;
        }

        public Criteria andNowReadnumGreaterThan(BigDecimal value) {
            addCriterion("now_readnum >", value, "nowReadnum");
            return (Criteria) this;
        }

        public Criteria andNowReadnumGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("now_readnum >=", value, "nowReadnum");
            return (Criteria) this;
        }

        public Criteria andNowReadnumLessThan(BigDecimal value) {
            addCriterion("now_readnum <", value, "nowReadnum");
            return (Criteria) this;
        }

        public Criteria andNowReadnumLessThanOrEqualTo(BigDecimal value) {
            addCriterion("now_readnum <=", value, "nowReadnum");
            return (Criteria) this;
        }

        public Criteria andNowReadnumIn(List<BigDecimal> values) {
            addCriterion("now_readnum in", values, "nowReadnum");
            return (Criteria) this;
        }

        public Criteria andNowReadnumNotIn(List<BigDecimal> values) {
            addCriterion("now_readnum not in", values, "nowReadnum");
            return (Criteria) this;
        }

        public Criteria andNowReadnumBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("now_readnum between", value1, value2, "nowReadnum");
            return (Criteria) this;
        }

        public Criteria andNowReadnumNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("now_readnum not between", value1, value2, "nowReadnum");
            return (Criteria) this;
        }

        public Criteria andNowRechargeReadnumIsNull() {
            addCriterion("now_recharge_readnum is null");
            return (Criteria) this;
        }

        public Criteria andNowRechargeReadnumIsNotNull() {
            addCriterion("now_recharge_readnum is not null");
            return (Criteria) this;
        }

        public Criteria andNowRechargeReadnumEqualTo(BigDecimal value) {
            addCriterion("now_recharge_readnum =", value, "nowRechargeReadnum");
            return (Criteria) this;
        }

        public Criteria andNowRechargeReadnumNotEqualTo(BigDecimal value) {
            addCriterion("now_recharge_readnum <>", value, "nowRechargeReadnum");
            return (Criteria) this;
        }

        public Criteria andNowRechargeReadnumGreaterThan(BigDecimal value) {
            addCriterion("now_recharge_readnum >", value, "nowRechargeReadnum");
            return (Criteria) this;
        }

        public Criteria andNowRechargeReadnumGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("now_recharge_readnum >=", value, "nowRechargeReadnum");
            return (Criteria) this;
        }

        public Criteria andNowRechargeReadnumLessThan(BigDecimal value) {
            addCriterion("now_recharge_readnum <", value, "nowRechargeReadnum");
            return (Criteria) this;
        }

        public Criteria andNowRechargeReadnumLessThanOrEqualTo(BigDecimal value) {
            addCriterion("now_recharge_readnum <=", value, "nowRechargeReadnum");
            return (Criteria) this;
        }

        public Criteria andNowRechargeReadnumIn(List<BigDecimal> values) {
            addCriterion("now_recharge_readnum in", values, "nowRechargeReadnum");
            return (Criteria) this;
        }

        public Criteria andNowRechargeReadnumNotIn(List<BigDecimal> values) {
            addCriterion("now_recharge_readnum not in", values, "nowRechargeReadnum");
            return (Criteria) this;
        }

        public Criteria andNowRechargeReadnumBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("now_recharge_readnum between", value1, value2, "nowRechargeReadnum");
            return (Criteria) this;
        }

        public Criteria andNowRechargeReadnumNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("now_recharge_readnum not between", value1, value2, "nowRechargeReadnum");
            return (Criteria) this;
        }

        public Criteria andLastRechargeReadnumIsNull() {
            addCriterion("last_recharge_readnum is null");
            return (Criteria) this;
        }

        public Criteria andLastRechargeReadnumIsNotNull() {
            addCriterion("last_recharge_readnum is not null");
            return (Criteria) this;
        }

        public Criteria andLastRechargeReadnumEqualTo(BigDecimal value) {
            addCriterion("last_recharge_readnum =", value, "lastRechargeReadnum");
            return (Criteria) this;
        }

        public Criteria andLastRechargeReadnumNotEqualTo(BigDecimal value) {
            addCriterion("last_recharge_readnum <>", value, "lastRechargeReadnum");
            return (Criteria) this;
        }

        public Criteria andLastRechargeReadnumGreaterThan(BigDecimal value) {
            addCriterion("last_recharge_readnum >", value, "lastRechargeReadnum");
            return (Criteria) this;
        }

        public Criteria andLastRechargeReadnumGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("last_recharge_readnum >=", value, "lastRechargeReadnum");
            return (Criteria) this;
        }

        public Criteria andLastRechargeReadnumLessThan(BigDecimal value) {
            addCriterion("last_recharge_readnum <", value, "lastRechargeReadnum");
            return (Criteria) this;
        }

        public Criteria andLastRechargeReadnumLessThanOrEqualTo(BigDecimal value) {
            addCriterion("last_recharge_readnum <=", value, "lastRechargeReadnum");
            return (Criteria) this;
        }

        public Criteria andLastRechargeReadnumIn(List<BigDecimal> values) {
            addCriterion("last_recharge_readnum in", values, "lastRechargeReadnum");
            return (Criteria) this;
        }

        public Criteria andLastRechargeReadnumNotIn(List<BigDecimal> values) {
            addCriterion("last_recharge_readnum not in", values, "lastRechargeReadnum");
            return (Criteria) this;
        }

        public Criteria andLastRechargeReadnumBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("last_recharge_readnum between", value1, value2, "lastRechargeReadnum");
            return (Criteria) this;
        }

        public Criteria andLastRechargeReadnumNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("last_recharge_readnum not between", value1, value2, "lastRechargeReadnum");
            return (Criteria) this;
        }

        public Criteria andOtherAmountIsNull() {
            addCriterion("other_amount is null");
            return (Criteria) this;
        }

        public Criteria andOtherAmountIsNotNull() {
            addCriterion("other_amount is not null");
            return (Criteria) this;
        }

        public Criteria andOtherAmountEqualTo(BigDecimal value) {
            addCriterion("other_amount =", value, "otherAmount");
            return (Criteria) this;
        }

        public Criteria andOtherAmountNotEqualTo(BigDecimal value) {
            addCriterion("other_amount <>", value, "otherAmount");
            return (Criteria) this;
        }

        public Criteria andOtherAmountGreaterThan(BigDecimal value) {
            addCriterion("other_amount >", value, "otherAmount");
            return (Criteria) this;
        }

        public Criteria andOtherAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("other_amount >=", value, "otherAmount");
            return (Criteria) this;
        }

        public Criteria andOtherAmountLessThan(BigDecimal value) {
            addCriterion("other_amount <", value, "otherAmount");
            return (Criteria) this;
        }

        public Criteria andOtherAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("other_amount <=", value, "otherAmount");
            return (Criteria) this;
        }

        public Criteria andOtherAmountIn(List<BigDecimal> values) {
            addCriterion("other_amount in", values, "otherAmount");
            return (Criteria) this;
        }

        public Criteria andOtherAmountNotIn(List<BigDecimal> values) {
            addCriterion("other_amount not in", values, "otherAmount");
            return (Criteria) this;
        }

        public Criteria andOtherAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("other_amount between", value1, value2, "otherAmount");
            return (Criteria) this;
        }

        public Criteria andOtherAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("other_amount not between", value1, value2, "otherAmount");
            return (Criteria) this;
        }

        public Criteria andPaymentDateIsNull() {
            addCriterion("payment_date is null");
            return (Criteria) this;
        }

        public Criteria andPaymentDateIsNotNull() {
            addCriterion("payment_date is not null");
            return (Criteria) this;
        }

        public Criteria andPaymentDateEqualTo(Date value) {
            addCriterionForJDBCDate("payment_date =", value, "paymentDate");
            return (Criteria) this;
        }

        public Criteria andPaymentDateNotEqualTo(Date value) {
            addCriterionForJDBCDate("payment_date <>", value, "paymentDate");
            return (Criteria) this;
        }

        public Criteria andPaymentDateGreaterThan(Date value) {
            addCriterionForJDBCDate("payment_date >", value, "paymentDate");
            return (Criteria) this;
        }

        public Criteria andPaymentDateGreaterThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("payment_date >=", value, "paymentDate");
            return (Criteria) this;
        }

        public Criteria andPaymentDateLessThan(Date value) {
            addCriterionForJDBCDate("payment_date <", value, "paymentDate");
            return (Criteria) this;
        }

        public Criteria andPaymentDateLessThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("payment_date <=", value, "paymentDate");
            return (Criteria) this;
        }

        public Criteria andPaymentDateIn(List<Date> values) {
            addCriterionForJDBCDate("payment_date in", values, "paymentDate");
            return (Criteria) this;
        }

        public Criteria andPaymentDateNotIn(List<Date> values) {
            addCriterionForJDBCDate("payment_date not in", values, "paymentDate");
            return (Criteria) this;
        }

        public Criteria andPaymentDateBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("payment_date between", value1, value2, "paymentDate");
            return (Criteria) this;
        }

        public Criteria andPaymentDateNotBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("payment_date not between", value1, value2, "paymentDate");
            return (Criteria) this;
        }

        public Criteria andPaymentStartdateIsNull() {
            addCriterion("payment_startdate is null");
            return (Criteria) this;
        }

        public Criteria andPaymentStartdateIsNotNull() {
            addCriterion("payment_startdate is not null");
            return (Criteria) this;
        }

        public Criteria andPaymentStartdateEqualTo(Date value) {
            addCriterionForJDBCDate("payment_startdate =", value, "paymentStartdate");
            return (Criteria) this;
        }

        public Criteria andPaymentStartdateNotEqualTo(Date value) {
            addCriterionForJDBCDate("payment_startdate <>", value, "paymentStartdate");
            return (Criteria) this;
        }

        public Criteria andPaymentStartdateGreaterThan(Date value) {
            addCriterionForJDBCDate("payment_startdate >", value, "paymentStartdate");
            return (Criteria) this;
        }

        public Criteria andPaymentStartdateGreaterThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("payment_startdate >=", value, "paymentStartdate");
            return (Criteria) this;
        }

        public Criteria andPaymentStartdateLessThan(Date value) {
            addCriterionForJDBCDate("payment_startdate <", value, "paymentStartdate");
            return (Criteria) this;
        }

        public Criteria andPaymentStartdateLessThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("payment_startdate <=", value, "paymentStartdate");
            return (Criteria) this;
        }

        public Criteria andPaymentStartdateIn(List<Date> values) {
            addCriterionForJDBCDate("payment_startdate in", values, "paymentStartdate");
            return (Criteria) this;
        }

        public Criteria andPaymentStartdateNotIn(List<Date> values) {
            addCriterionForJDBCDate("payment_startdate not in", values, "paymentStartdate");
            return (Criteria) this;
        }

        public Criteria andPaymentStartdateBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("payment_startdate between", value1, value2, "paymentStartdate");
            return (Criteria) this;
        }

        public Criteria andPaymentStartdateNotBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("payment_startdate not between", value1, value2, "paymentStartdate");
            return (Criteria) this;
        }

        public Criteria andPaymentEnddateIsNull() {
            addCriterion("payment_enddate is null");
            return (Criteria) this;
        }

        public Criteria andPaymentEnddateIsNotNull() {
            addCriterion("payment_enddate is not null");
            return (Criteria) this;
        }

        public Criteria andPaymentEnddateEqualTo(Date value) {
            addCriterionForJDBCDate("payment_enddate =", value, "paymentEnddate");
            return (Criteria) this;
        }

        public Criteria andPaymentEnddateNotEqualTo(Date value) {
            addCriterionForJDBCDate("payment_enddate <>", value, "paymentEnddate");
            return (Criteria) this;
        }

        public Criteria andPaymentEnddateGreaterThan(Date value) {
            addCriterionForJDBCDate("payment_enddate >", value, "paymentEnddate");
            return (Criteria) this;
        }

        public Criteria andPaymentEnddateGreaterThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("payment_enddate >=", value, "paymentEnddate");
            return (Criteria) this;
        }

        public Criteria andPaymentEnddateLessThan(Date value) {
            addCriterionForJDBCDate("payment_enddate <", value, "paymentEnddate");
            return (Criteria) this;
        }

        public Criteria andPaymentEnddateLessThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("payment_enddate <=", value, "paymentEnddate");
            return (Criteria) this;
        }

        public Criteria andPaymentEnddateIn(List<Date> values) {
            addCriterionForJDBCDate("payment_enddate in", values, "paymentEnddate");
            return (Criteria) this;
        }

        public Criteria andPaymentEnddateNotIn(List<Date> values) {
            addCriterionForJDBCDate("payment_enddate not in", values, "paymentEnddate");
            return (Criteria) this;
        }

        public Criteria andPaymentEnddateBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("payment_enddate between", value1, value2, "paymentEnddate");
            return (Criteria) this;
        }

        public Criteria andPaymentEnddateNotBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("payment_enddate not between", value1, value2, "paymentEnddate");
            return (Criteria) this;
        }

        public Criteria andPayamountWithtaxIsNull() {
            addCriterion("payamount_withtax is null");
            return (Criteria) this;
        }

        public Criteria andPayamountWithtaxIsNotNull() {
            addCriterion("payamount_withtax is not null");
            return (Criteria) this;
        }

        public Criteria andPayamountWithtaxEqualTo(BigDecimal value) {
            addCriterion("payamount_withtax =", value, "payamountWithtax");
            return (Criteria) this;
        }

        public Criteria andPayamountWithtaxNotEqualTo(BigDecimal value) {
            addCriterion("payamount_withtax <>", value, "payamountWithtax");
            return (Criteria) this;
        }

        public Criteria andPayamountWithtaxGreaterThan(BigDecimal value) {
            addCriterion("payamount_withtax >", value, "payamountWithtax");
            return (Criteria) this;
        }

        public Criteria andPayamountWithtaxGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("payamount_withtax >=", value, "payamountWithtax");
            return (Criteria) this;
        }

        public Criteria andPayamountWithtaxLessThan(BigDecimal value) {
            addCriterion("payamount_withtax <", value, "payamountWithtax");
            return (Criteria) this;
        }

        public Criteria andPayamountWithtaxLessThanOrEqualTo(BigDecimal value) {
            addCriterion("payamount_withtax <=", value, "payamountWithtax");
            return (Criteria) this;
        }

        public Criteria andPayamountWithtaxIn(List<BigDecimal> values) {
            addCriterion("payamount_withtax in", values, "payamountWithtax");
            return (Criteria) this;
        }

        public Criteria andPayamountWithtaxNotIn(List<BigDecimal> values) {
            addCriterion("payamount_withtax not in", values, "payamountWithtax");
            return (Criteria) this;
        }

        public Criteria andPayamountWithtaxBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("payamount_withtax between", value1, value2, "payamountWithtax");
            return (Criteria) this;
        }

        public Criteria andPayamountWithtaxNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("payamount_withtax not between", value1, value2, "payamountWithtax");
            return (Criteria) this;
        }

        public Criteria andPayamountNotaxIsNull() {
            addCriterion("payamount_notax is null");
            return (Criteria) this;
        }

        public Criteria andPayamountNotaxIsNotNull() {
            addCriterion("payamount_notax is not null");
            return (Criteria) this;
        }

        public Criteria andPayamountNotaxEqualTo(BigDecimal value) {
            addCriterion("payamount_notax =", value, "payamountNotax");
            return (Criteria) this;
        }

        public Criteria andPayamountNotaxNotEqualTo(BigDecimal value) {
            addCriterion("payamount_notax <>", value, "payamountNotax");
            return (Criteria) this;
        }

        public Criteria andPayamountNotaxGreaterThan(BigDecimal value) {
            addCriterion("payamount_notax >", value, "payamountNotax");
            return (Criteria) this;
        }

        public Criteria andPayamountNotaxGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("payamount_notax >=", value, "payamountNotax");
            return (Criteria) this;
        }

        public Criteria andPayamountNotaxLessThan(BigDecimal value) {
            addCriterion("payamount_notax <", value, "payamountNotax");
            return (Criteria) this;
        }

        public Criteria andPayamountNotaxLessThanOrEqualTo(BigDecimal value) {
            addCriterion("payamount_notax <=", value, "payamountNotax");
            return (Criteria) this;
        }

        public Criteria andPayamountNotaxIn(List<BigDecimal> values) {
            addCriterion("payamount_notax in", values, "payamountNotax");
            return (Criteria) this;
        }

        public Criteria andPayamountNotaxNotIn(List<BigDecimal> values) {
            addCriterion("payamount_notax not in", values, "payamountNotax");
            return (Criteria) this;
        }

        public Criteria andPayamountNotaxBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("payamount_notax between", value1, value2, "payamountNotax");
            return (Criteria) this;
        }

        public Criteria andPayamountNotaxNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("payamount_notax not between", value1, value2, "payamountNotax");
            return (Criteria) this;
        }

        public Criteria andPayamountTaxIsNull() {
            addCriterion("payamount_tax is null");
            return (Criteria) this;
        }

        public Criteria andPayamountTaxIsNotNull() {
            addCriterion("payamount_tax is not null");
            return (Criteria) this;
        }

        public Criteria andPayamountTaxEqualTo(BigDecimal value) {
            addCriterion("payamount_tax =", value, "payamountTax");
            return (Criteria) this;
        }

        public Criteria andPayamountTaxNotEqualTo(BigDecimal value) {
            addCriterion("payamount_tax <>", value, "payamountTax");
            return (Criteria) this;
        }

        public Criteria andPayamountTaxGreaterThan(BigDecimal value) {
            addCriterion("payamount_tax >", value, "payamountTax");
            return (Criteria) this;
        }

        public Criteria andPayamountTaxGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("payamount_tax >=", value, "payamountTax");
            return (Criteria) this;
        }

        public Criteria andPayamountTaxLessThan(BigDecimal value) {
            addCriterion("payamount_tax <", value, "payamountTax");
            return (Criteria) this;
        }

        public Criteria andPayamountTaxLessThanOrEqualTo(BigDecimal value) {
            addCriterion("payamount_tax <=", value, "payamountTax");
            return (Criteria) this;
        }

        public Criteria andPayamountTaxIn(List<BigDecimal> values) {
            addCriterion("payamount_tax in", values, "payamountTax");
            return (Criteria) this;
        }

        public Criteria andPayamountTaxNotIn(List<BigDecimal> values) {
            addCriterion("payamount_tax not in", values, "payamountTax");
            return (Criteria) this;
        }

        public Criteria andPayamountTaxBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("payamount_tax between", value1, value2, "payamountTax");
            return (Criteria) this;
        }

        public Criteria andPayamountTaxNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("payamount_tax not between", value1, value2, "payamountTax");
            return (Criteria) this;
        }

        public Criteria andPayamountIsNull() {
            addCriterion("payamount is null");
            return (Criteria) this;
        }

        public Criteria andPayamountIsNotNull() {
            addCriterion("payamount is not null");
            return (Criteria) this;
        }

        public Criteria andPayamountEqualTo(BigDecimal value) {
            addCriterion("payamount =", value, "payamount");
            return (Criteria) this;
        }

        public Criteria andPayamountNotEqualTo(BigDecimal value) {
            addCriterion("payamount <>", value, "payamount");
            return (Criteria) this;
        }

        public Criteria andPayamountGreaterThan(BigDecimal value) {
            addCriterion("payamount >", value, "payamount");
            return (Criteria) this;
        }

        public Criteria andPayamountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("payamount >=", value, "payamount");
            return (Criteria) this;
        }

        public Criteria andPayamountLessThan(BigDecimal value) {
            addCriterion("payamount <", value, "payamount");
            return (Criteria) this;
        }

        public Criteria andPayamountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("payamount <=", value, "payamount");
            return (Criteria) this;
        }

        public Criteria andPayamountIn(List<BigDecimal> values) {
            addCriterion("payamount in", values, "payamount");
            return (Criteria) this;
        }

        public Criteria andPayamountNotIn(List<BigDecimal> values) {
            addCriterion("payamount not in", values, "payamount");
            return (Criteria) this;
        }

        public Criteria andPayamountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("payamount between", value1, value2, "payamount");
            return (Criteria) this;
        }

        public Criteria andPayamountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("payamount not between", value1, value2, "payamount");
            return (Criteria) this;
        }

        public Criteria andPaymentdetailNoteIsNull() {
            addCriterion("paymentdetail_note is null");
            return (Criteria) this;
        }

        public Criteria andPaymentdetailNoteIsNotNull() {
            addCriterion("paymentdetail_note is not null");
            return (Criteria) this;
        }

        public Criteria andPaymentdetailNoteEqualTo(String value) {
            addCriterion("paymentdetail_note =", value, "paymentdetailNote");
            return (Criteria) this;
        }

        public Criteria andPaymentdetailNoteNotEqualTo(String value) {
            addCriterion("paymentdetail_note <>", value, "paymentdetailNote");
            return (Criteria) this;
        }

        public Criteria andPaymentdetailNoteGreaterThan(String value) {
            addCriterion("paymentdetail_note >", value, "paymentdetailNote");
            return (Criteria) this;
        }

        public Criteria andPaymentdetailNoteGreaterThanOrEqualTo(String value) {
            addCriterion("paymentdetail_note >=", value, "paymentdetailNote");
            return (Criteria) this;
        }

        public Criteria andPaymentdetailNoteLessThan(String value) {
            addCriterion("paymentdetail_note <", value, "paymentdetailNote");
            return (Criteria) this;
        }

        public Criteria andPaymentdetailNoteLessThanOrEqualTo(String value) {
            addCriterion("paymentdetail_note <=", value, "paymentdetailNote");
            return (Criteria) this;
        }

        public Criteria andPaymentdetailNoteLike(String value) {
            addCriterion("paymentdetail_note like", value, "paymentdetailNote");
            return (Criteria) this;
        }

        public Criteria andPaymentdetailNoteNotLike(String value) {
            addCriterion("paymentdetail_note not like", value, "paymentdetailNote");
            return (Criteria) this;
        }

        public Criteria andPaymentdetailNoteIn(List<String> values) {
            addCriterion("paymentdetail_note in", values, "paymentdetailNote");
            return (Criteria) this;
        }

        public Criteria andPaymentdetailNoteNotIn(List<String> values) {
            addCriterion("paymentdetail_note not in", values, "paymentdetailNote");
            return (Criteria) this;
        }

        public Criteria andPaymentdetailNoteBetween(String value1, String value2) {
            addCriterion("paymentdetail_note between", value1, value2, "paymentdetailNote");
            return (Criteria) this;
        }

        public Criteria andPaymentdetailNoteNotBetween(String value1, String value2) {
            addCriterion("paymentdetail_note not between", value1, value2, "paymentdetailNote");
            return (Criteria) this;
        }

        public Criteria andBillamountStateIsNull() {
            addCriterion("billamount_state is null");
            return (Criteria) this;
        }

        public Criteria andBillamountStateIsNotNull() {
            addCriterion("billamount_state is not null");
            return (Criteria) this;
        }

        public Criteria andBillamountStateEqualTo(Integer value) {
            addCriterion("billamount_state =", value, "billamountState");
            return (Criteria) this;
        }

        public Criteria andBillamountStateNotEqualTo(Integer value) {
            addCriterion("billamount_state <>", value, "billamountState");
            return (Criteria) this;
        }

        public Criteria andBillamountStateGreaterThan(Integer value) {
            addCriterion("billamount_state >", value, "billamountState");
            return (Criteria) this;
        }

        public Criteria andBillamountStateGreaterThanOrEqualTo(Integer value) {
            addCriterion("billamount_state >=", value, "billamountState");
            return (Criteria) this;
        }

        public Criteria andBillamountStateLessThan(Integer value) {
            addCriterion("billamount_state <", value, "billamountState");
            return (Criteria) this;
        }

        public Criteria andBillamountStateLessThanOrEqualTo(Integer value) {
            addCriterion("billamount_state <=", value, "billamountState");
            return (Criteria) this;
        }

        public Criteria andBillamountStateIn(List<Integer> values) {
            addCriterion("billamount_state in", values, "billamountState");
            return (Criteria) this;
        }

        public Criteria andBillamountStateNotIn(List<Integer> values) {
            addCriterion("billamount_state not in", values, "billamountState");
            return (Criteria) this;
        }

        public Criteria andBillamountStateBetween(Integer value1, Integer value2) {
            addCriterion("billamount_state between", value1, value2, "billamountState");
            return (Criteria) this;
        }

        public Criteria andBillamountStateNotBetween(Integer value1, Integer value2) {
            addCriterion("billamount_state not between", value1, value2, "billamountState");
            return (Criteria) this;
        }

        public Criteria andPayamountDatatypeIsNull() {
            addCriterion("payamount_datatype is null");
            return (Criteria) this;
        }

        public Criteria andPayamountDatatypeIsNotNull() {
            addCriterion("payamount_datatype is not null");
            return (Criteria) this;
        }

        public Criteria andPayamountDatatypeEqualTo(Integer value) {
            addCriterion("payamount_datatype =", value, "payamountDatatype");
            return (Criteria) this;
        }

        public Criteria andPayamountDatatypeNotEqualTo(Integer value) {
            addCriterion("payamount_datatype <>", value, "payamountDatatype");
            return (Criteria) this;
        }

        public Criteria andPayamountDatatypeGreaterThan(Integer value) {
            addCriterion("payamount_datatype >", value, "payamountDatatype");
            return (Criteria) this;
        }

        public Criteria andPayamountDatatypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("payamount_datatype >=", value, "payamountDatatype");
            return (Criteria) this;
        }

        public Criteria andPayamountDatatypeLessThan(Integer value) {
            addCriterion("payamount_datatype <", value, "payamountDatatype");
            return (Criteria) this;
        }

        public Criteria andPayamountDatatypeLessThanOrEqualTo(Integer value) {
            addCriterion("payamount_datatype <=", value, "payamountDatatype");
            return (Criteria) this;
        }

        public Criteria andPayamountDatatypeIn(List<Integer> values) {
            addCriterion("payamount_datatype in", values, "payamountDatatype");
            return (Criteria) this;
        }

        public Criteria andPayamountDatatypeNotIn(List<Integer> values) {
            addCriterion("payamount_datatype not in", values, "payamountDatatype");
            return (Criteria) this;
        }

        public Criteria andPayamountDatatypeBetween(Integer value1, Integer value2) {
            addCriterion("payamount_datatype between", value1, value2, "payamountDatatype");
            return (Criteria) this;
        }

        public Criteria andPayamountDatatypeNotBetween(Integer value1, Integer value2) {
            addCriterion("payamount_datatype not between", value1, value2, "payamountDatatype");
            return (Criteria) this;
        }

        public Criteria andCmccRatioIsNull() {
            addCriterion("cmcc_ratio is null");
            return (Criteria) this;
        }

        public Criteria andCmccRatioIsNotNull() {
            addCriterion("cmcc_ratio is not null");
            return (Criteria) this;
        }

        public Criteria andCmccRatioEqualTo(String value) {
            addCriterion("cmcc_ratio =", value, "cmccRatio");
            return (Criteria) this;
        }

        public Criteria andCmccRatioNotEqualTo(String value) {
            addCriterion("cmcc_ratio <>", value, "cmccRatio");
            return (Criteria) this;
        }

        public Criteria andCmccRatioGreaterThan(String value) {
            addCriterion("cmcc_ratio >", value, "cmccRatio");
            return (Criteria) this;
        }

        public Criteria andCmccRatioGreaterThanOrEqualTo(String value) {
            addCriterion("cmcc_ratio >=", value, "cmccRatio");
            return (Criteria) this;
        }

        public Criteria andCmccRatioLessThan(String value) {
            addCriterion("cmcc_ratio <", value, "cmccRatio");
            return (Criteria) this;
        }

        public Criteria andCmccRatioLessThanOrEqualTo(String value) {
            addCriterion("cmcc_ratio <=", value, "cmccRatio");
            return (Criteria) this;
        }

        public Criteria andCmccRatioLike(String value) {
            addCriterion("cmcc_ratio like", value, "cmccRatio");
            return (Criteria) this;
        }

        public Criteria andCmccRatioNotLike(String value) {
            addCriterion("cmcc_ratio not like", value, "cmccRatio");
            return (Criteria) this;
        }

        public Criteria andCmccRatioIn(List<String> values) {
            addCriterion("cmcc_ratio in", values, "cmccRatio");
            return (Criteria) this;
        }

        public Criteria andCmccRatioNotIn(List<String> values) {
            addCriterion("cmcc_ratio not in", values, "cmccRatio");
            return (Criteria) this;
        }

        public Criteria andCmccRatioBetween(String value1, String value2) {
            addCriterion("cmcc_ratio between", value1, value2, "cmccRatio");
            return (Criteria) this;
        }

        public Criteria andCmccRatioNotBetween(String value1, String value2) {
            addCriterion("cmcc_ratio not between", value1, value2, "cmccRatio");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }
    }
}