package com.xunge.dao.report;

import com.xunge.model.report.RptBillPayLedgerVO;

import java.util.List;

public interface IrptBillPayLedgerDao {

    /**
     * 查询报账点信息
     *
     * @param rptBillPayLedgerVO
     * @return
     */
    List<RptBillPayLedgerVO> queryBillaccount(RptBillPayLedgerVO rptBillPayLedgerVO);

    /**
     * queryBillPayLedger
     *
     * @param rptBillPayLedgerVO
     * @return List<RptBillPayLedgerVO> 返回类型
     * @description << 查询报账点缴费台帐信息 >>
     * <AUTHOR>
     * @version V1.0
     * @date 2018年8月1日
     * @email <EMAIL>
     */
    List<RptBillPayLedgerVO> queryBillPayLedger(RptBillPayLedgerVO rptBillPayLedgerVO);

    /**
     * 查询报账点信息总数
     *
     * @param rptBillPayLedgerVO
     * @return
     */
    int queryBillaccountCount(RptBillPayLedgerVO rptBillPayLedgerVO);

    /**
     * queryBillPayLedgerCount
     *
     * @param rptBillPayLedgerVO
     * @return int 返回类型
     * @description << 查询报账点缴费台帐信息总数>>
     * <AUTHOR>
     * @version V1.0
     * @date 2018年8月1日
     * @email <EMAIL>
     */
    int queryBillPayLedgerCount(RptBillPayLedgerVO rptBillPayLedgerVO);

}