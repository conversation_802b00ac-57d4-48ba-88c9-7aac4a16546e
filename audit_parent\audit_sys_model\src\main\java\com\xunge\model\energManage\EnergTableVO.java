package com.xunge.model.energManage;

import java.io.Serializable;

public class EnergTableVO implements Serializable {

    private static final long serialVersionUID = 6702812400588221251L;

    private String prvSname;//所属省份名称
    private String pregName;//所属地市名称
    private String regName;//所属区县名称
    private String resourceType;//资源类型
    private String resourceTitle;//资源类型title
    private String resourceName;//资源名称
    private String resourceCode;//资源标识
    private String startTime;//记录日期
    private Float totalDegree;//总表总耗电
    private Integer totalState;//电表状态
    private Float equipmentDegree;//主设备耗电
    private Integer equipmentState;//主设备状态
    private Float acDegree;//空调总耗电
    private Integer acState;//空调状态
    private String PUE;

    public Integer getTotalState() {
        return totalState;
    }

    public void setTotalState(Integer totalState) {
        this.totalState = totalState;
    }

    public Float getAcDegree() {
        return acDegree;
    }

    public void setAcDegree(Float acDegree) {
        this.acDegree = acDegree;
    }

    public String getRegName() {
        return regName;
    }

    public void setRegName(String regName) {
        this.regName = regName;
    }

    public Integer getEquipmentState() {
        return equipmentState;
    }

    public void setEquipmentState(Integer equipmentState) {
        this.equipmentState = equipmentState;
    }

    public Integer getAcState() {
        return acState;
    }

    public void setAcState(Integer acState) {
        this.acState = acState;
    }

    public String getPrvSname() {
        return prvSname;
    }

    public void setPrvSname(String prvSname) {
        this.prvSname = prvSname;
    }

    public String getPregName() {
        return pregName;
    }

    public void setPregName(String pregName) {
        this.pregName = pregName;
    }

    public String getResourceType() {
        return resourceType;
    }

    public void setResourceType(String resourceType) {
        this.resourceType = resourceType;
    }

    public String getResourceName() {
        return resourceName;
    }

    public void setResourceName(String resourceName) {
        this.resourceName = resourceName;
    }

    public String getResourceCode() {
        return resourceCode;
    }

    public void setResourceCode(String resourceCode) {
        this.resourceCode = resourceCode;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getResourceTitle() {
        return resourceTitle;
    }

    public void setResourceTitle(String resourceTitle) {
        this.resourceTitle = resourceTitle;
    }

    public Float getTotalDegree() {
        return totalDegree;
    }

    public void setTotalDegree(Float totalDegree) {
        this.totalDegree = totalDegree;
    }

    public Float getEquipmentDegree() {
        return equipmentDegree;
    }

    public void setEquipmentDegree(Float equipmentDegree) {
        this.equipmentDegree = equipmentDegree;
    }

    public String getPUE() {
        return PUE;
    }

    public void setPUE(String PUE) {
        this.PUE = PUE;
    }
}
