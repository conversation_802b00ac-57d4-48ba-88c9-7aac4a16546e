package com.xunge.model.budget;

import com.xunge.model.budget.tower.BudgetFiledValidate;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 租费预算表
 * @TableName rent_budget_report
 */
@Data
public class RentBudgetReport extends BudgetBaseVo implements Serializable {

    public RentBudgetReport() {
        super();
    }

    public RentBudgetReport(String prvId, String prvName, Integer budgetSheetType, Integer serviceSiteType){
        this.prvId = prvId;
        this.prvName = prvName;
        this.budgetSheetType = budgetSheetType;
        this.serviceSiteType = serviceSiteType;
    }

    public RentBudgetReport(String prvId, String prvName, Integer budgetSheetType, Integer serviceSiteType, String budgetSheetName, String serviceSiteName) {
        this(prvId, prvName, budgetSheetType, serviceSiteType);
        this.budgetSheetName = budgetSheetName;
        this.serviceSiteName = serviceSiteName;
    }

    /**
     * 主键
     */
    private String id;

    /**
     * 预算表类型名称
     */
    private String budgetSheetName;

    /**
     * 省份ID
     */
    private String prvId;

    /**
     * 省份名称
     */
    private String prvName;

    /**
     * 预算表类型，1：年底存量；2：明年新增；3：明年退网；0：总览
     */
    private Integer budgetSheetType;

    /**
     * 流转类型，代表各个节点1：工单或草稿；2：省侧提交或驳回；3：确认完结后维护；4：最终预算；5：预算测算
     */
    private Integer flowType;

    /**
     * 业务类型({传输位置点,-1}, {综合位置点,0}, {核心机楼,1}, {汇聚传输站点,2}, {基站,3}, {室分及WLAN,4}, {家客集客,5}, {IDC机房,6}, {基地,7}, {其他,8}, {合计行,9})
     */
    private Integer serviceSiteType;

    private String serviceSiteName;

    /**
     * 单站租赁费（系统数据）
     */
    @BudgetFiledValidate(true)
    private BigDecimal avgSiteAmountSys;

    /**
     * 站址数量固定值（后续计算用）
     */
    @BudgetFiledValidate(true)
    private Integer siteNum;

    /**
     * 规模（系统数据）
     */
    private Integer siteScaleSys;

    /**
     * 规模
     */
    private Integer siteScale;

    /**
     * 规模调整原因
     */
    private String siteScaleAdjustReason;

    /**
     * 月数
     */
    @BudgetFiledValidate(true)
    private BigDecimal months;

    /**
     * 月数调整原因
     */
    private String monthsAdjustReason;

    /**
     * 预算金额
     */
    private BigDecimal budgetAmount;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 核减金额合计（存量+新增+退网）
     */
    private BigDecimal accountAmount;

    /**
     * 总览计算核减后金额的核减金额值（存量+新增-退网）
     */
    private BigDecimal calAccountAmount;

    /**
     * 核减后金额
     */
    private BigDecimal afterAccountAmount;

    /**
     * 调整金额：各预算表每个省一份（存量+新增+退网）
     */
    private BigDecimal adjustAmount;

    /**
     * 总览计算调整后金额的调整金额值（存量+新增-退网）
     */
    private BigDecimal calAdjustAmount;

    /**
     * 调整后金额：各预算表每个省一份
     */
    private BigDecimal afterAdjustAmount;

    /**
     * 快照数据对应流转记录表id
     */
    private String auditInfoId;

    /**
     * 如果需要更改同一节点的信息则需要传递此字段
     */
    private Integer oldFlowType;

    /**
     * 总览表字段：预算金额（存量表）
     */
    private BigDecimal existBudgetAmount;

    /**
     * 总览表字段：预算金额（新增表）
     */
    private BigDecimal addBudgetAmount;

    /**
     * 总览表字段：预算金额（退网表）
     */
    private BigDecimal delBudgetAmount;

    /**
     * 总览表字段：预算金额（年底新增表）
     */
    private BigDecimal supplementBudgetAmount;

    /**
     * 报账点数量（合同期内，系统数据）
     */
    @BudgetFiledValidate(true)
    private Integer billAccountNumWithin;

    /**
     * 站点数量（合同期内，系统数据）
     */
    @BudgetFiledValidate(true)
    private Integer siteNumWithin;

    /**
     * 预算金额（合同期内，系统数据）
     */
    @BudgetFiledValidate(true)
    private BigDecimal budgetAmountWithin;
    /**
     * 报账点数量（合同期外，系统数据）
     */
    @BudgetFiledValidate(true)
    private Integer billAccountNumOutside;

    /**
     * 站点数量（合同期外，系统数据）
     */
    @BudgetFiledValidate(true)
    private Integer siteNumOutside;

    /**
     * 预算金额（合同期外，系统数据）
     */
    @BudgetFiledValidate(true)
    private BigDecimal budgetAmountOutside;

    /**
     * 省侧调整后预算金额（合同期外）
     */
    private BigDecimal budgetAmountOutsideAdjust;

    /**
     * 预算金额（合同期外）调整原因
     */
    private String budgetAmountOutsideAdjustReason;

    /**
     * 站点数量合计（合同期内+合同期外去重）
     */
    private Integer siteNumTotal;

    private static final long serialVersionUID = 1L;
}