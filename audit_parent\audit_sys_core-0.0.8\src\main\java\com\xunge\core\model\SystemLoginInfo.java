package com.xunge.core.model;

import java.io.Serializable;
import java.util.List;

public class SystemLoginInfo implements Serializable {

    private static final long serialVersionUID = -4176550641008951753L;
    /**
     * 系统id
     */
    private String clientId;

    /**
     * 系统本次访问的省份code
     */
    private String prvCode;

    private String prvId;

    private List<String> pregIds;

    private List<String> regIds;

    private String prvFlag;

    public String getClientId() {
        return clientId;
    }

    public void setClientId(String clientId) {
        this.clientId = clientId;
    }

    public String getPrvCode() {
        return prvCode;
    }

    public void setPrvCode(String prvCode) {
        this.prvCode = prvCode;
    }

    public String getPrvId() {
        return prvId;
    }

    public void setPrvId(String prvId) {
        this.prvId = prvId;
    }

    public List<String> getPregIds() {
        return pregIds;
    }

    public void setPregIds(List<String> pregIds) {
        this.pregIds = pregIds;
    }

    public List<String> getRegIds() {
        return regIds;
    }

    public void setRegIds(List<String> regIds) {
        this.regIds = regIds;
    }

    public String getPrvFlag() {
        return prvFlag;
    }

    public void setPrvFlag(String prvFlag) {
        this.prvFlag = prvFlag;
    }
}
