package com.xunge.model.basedata.vo;

import com.xunge.core.model.UserLoginInfo;

import java.io.Serializable;

/**
 * 设备查询 VO
 * <p>
 * Title: DeviceQueryVO
 *
 * <AUTHOR>
 */
public class DeviceQueryVO extends BaseDataVO implements Serializable {

    private static final long serialVersionUID = 4779790854632941595L;
    private Integer devType;
    private String reg;
    private Integer status;
    private String basestationCode;
    private String basestationName;
    // 登录用户信息
    private UserLoginInfo loginUser;

    public String getBasestationCode() {
        return basestationCode;
    }

    public void setBasestationCode(String basestationCode) {
        this.basestationCode = basestationCode;
    }

    public String getBasestationName() {
        return basestationName;
    }

    public void setBasestationName(String basestationName) {
        this.basestationName = basestationName;
    }

    public Integer getDevType() {
        return devType;
    }

    public void setDevType(Integer devType) {
        this.devType = devType;
    }

    public String getReg() {
        return reg;
    }

    public void setReg(String reg) {
        this.reg = reg;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public UserLoginInfo getLoginUser() {
        return loginUser;
    }

    public void setLoginUser(UserLoginInfo loginUser) {
        this.loginUser = loginUser;
    }
}