package com.xunge.dao.selfelec.electricmeter;

import com.xunge.model.app.AppEleMeterDegree;
import com.xunge.model.selfelec.EleBillaccount;
import com.xunge.model.selfelec.electricmeter.DatElectricMeterVO;
import com.xunge.model.selfelec.electricmeter.DatElectricMeterVOExample;
import com.xunge.model.selfelec.electricmeter.MeterReadNumHistory;
import com.xunge.model.selfelec.paymentDate.PaymentDateVO;

import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Map;

public interface DatElectricMeterVOMapper {
    int countByExample(DatElectricMeterVOExample example);

    boolean deleteByExample(DatElectricMeterVOExample example);

    boolean deleteByPrimaryKey(String meterId);

    boolean insert(DatElectricMeterVO record);

    boolean insertSelective(DatElectricMeterVO record);

    List<DatElectricMeterVO> selectByExample(DatElectricMeterVOExample example);

    DatElectricMeterVO selectByPrimaryKey(String meterId);

    DatElectricMeterVO selectMeterAuditingSate(String meterId);
    DatElectricMeterVO selectMetersAuditingSate(String meterId);

    boolean updateByExampleSelective(@Param("record") DatElectricMeterVO record, @Param("example") DatElectricMeterVOExample example);

    boolean updateByExample(@Param("record") DatElectricMeterVO record, @Param("example") DatElectricMeterVOExample example);

    boolean updateByPrimaryKeySelective(DatElectricMeterVO record);

    boolean updateByPrimaryKey(DatElectricMeterVO record);

    List<DatElectricMeterVO> selectByCondition(DatElectricMeterVO datElectricMeterVO);

    List<DatElectricMeterVO> selectByConditionNew(DatElectricMeterVO datElectricMeterVO);

    /**
     * 查询电费列表
     */
    List<DatElectricMeterVO> queryMeterList(Map<String, Object> map);

    /**
     * 电费审核列表
     * @param map
     * @return
     */
    List<DatElectricMeterVO> queryAuditingMeterList(Map<String, Object> map);

    /**
     * 查询电表抄表管理
     * @param paramMap
     * @return
     */

    List<DatElectricMeterVO> queryRecordMeterList(Map<String, Object> paramMap);

//    List<DatElectricMeterVO> updateAttachMent(Map<String,Object> map);
List<DatElectricMeterVO> queryAttachMent(Map<String, Object> map);

    List<DatElectricMeterVO> exportMeterDate(DatElectricMeterVO datElectricMeterVO);

    List<DatElectricMeterVO> exportAttachMent(DatElectricMeterVO datElectricMeterVO);

    List<DatElectricMeterVO> exportMeterDateNew(DatElectricMeterVO datElectricMeterVO);

    boolean delDatElectricMeter(String[] ids);

    boolean delMeterById(String meterId);

    List<DatElectricMeterVO> selectByMeterCode(Map<String, Object> map);

    List<DatElectricMeterVO> selectByTricmeterPropertyCode(Map<String, Object> map);

    void updateforSysMeterCodeIsNull(DatElectricMeterVO datElectricMeterVO);

    DatElectricMeterVO queryElecMeterId(@Param("meterId") String meterId);

    List<DatElectricMeterVO> getElectricmeterList(@Param("meterIdList") List<String> meterIdList);

    List<DatElectricMeterVO> queryMeterByResourceId(Map<String, Object> map);

    List<DatElectricMeterVO> queryMeterByMeterId(Map<String, Object> map);

    List<Map<String, Object>> queryMeterNumByPrvid(Map<String, Object> map);

    int selectLinkedElecMeter(Map<String, Object> map);

    int selectAllElecMeter(Map<String, Object> map);

    /**
     * @description 根据meterCode以及省份编码查询电表
     * <AUTHOR>
     * @date 创建时间：2017年10月17日
     */
    List<DatElectricMeterVO> selectByMeterCodeMap(Map<String, Object> map);

    List<DatElectricMeterVO> selectBySysMeterCodeisNull();

    /**
     * @description 修改电表状态
     * <AUTHOR>
     * @date 创建时间：2017年10月18日
     */
    boolean updateMeterState(Map<String, Object> map);

    /**
     * @description 根据电表id查询已关联资源名称
     * <AUTHOR>
     * @date 创建时间：2017年10月27日
     */
    public List<String> selectResourceNameByMeterId(Map<String, Object> map);

    public List<String> selectResourceNameByMeterIdForTW(Map<String, Object> map);

    /**
     * @description 根据电表id 查找报账点id
     * <AUTHOR>
     * @date 创建时间：2017年11月20日
     */
    public String selectBillacountIdByMeterId(Map<String, Object> paraMap);

    /**
     * 根据电表id 查询缴费code
     * 用于删除判断该电表5年内是否存在缴费
     *
     * @param paraMap
     * @return
     */
    public List<String> queryPayDetailCode(Map<String, Object> paraMap);

    /**
     * 根据推送时间 和缴费code
     * 用于删除判断该电表5年内是否存在推送记录
     *
     * @param paraMap
     * @return
     */
    public int querySysBillamoutCode(Map<String, Object> paraMap);

    /**
     * 从超标app中查询电表的本期读数
     *
     * @param map
     * @return AppEleMeterDegree
     * @date 2019年01月21日
     * <AUTHOR>
     */
    List<AppEleMeterDegree> queryMeterDegreeByApp(Map<String, Object> map);

    /**
     * 查询缴费期始到缴费期终的所有附件app信息
     *
     * @param map
     * @return AppEleMeterDegree
     * @date 2019年05月22日
     * <AUTHOR>
     */
    List<AppEleMeterDegree> queryMeterInfoByMeterId(Map<String, Object> map);


    List<AppEleMeterDegree> queryMeterInfoByMeterIdGroupCreateTime(Map<String, Object> map);


    List<AppEleMeterDegree> queryMeterInfoByUuid(Map<String, Object> map);


    /**
     * 获取核销缴费编码
     *
     * @param map
     * @return
     */
    public List<String> queryPayICDetailCode(Map<String, Object> map);

    /**
     * 修改电表状态
     *
     * @param paramMap
     * @return
     */
    int updateElectricMeterState(Map paramMap);

    List<String> queryMeterInfoDate(Map<String, Object> paramMap);

    String queryMaxDateMeterInfo(Map<String, Object> paramMap);

    int addMeterSite(Map<String, Object> paramMap);

    int updateMeterSite(Map<String, Object> paramMap);

    Map<String, Object> selectMeterSite(Map<String, Object> paramMap);

    Integer checkUserRole(Map<String, Object> param);

    int updateAppMeter(Map<String, Object> param);
    /**
     * 根据电表id查询电表关联大报账点名称
     */
    List<Map<String,String>> queryBillAccountNameByMeterIds(@Param("meterIds") List<String> meterIds);

    //查询电表关联报账点
    EleBillaccount queryBillAccountByMeterId(@Param("meterId") String meterId);

    int updateMeterAudit(Map<String, Object> paramMap);

    /**
     * 查询电表读数记录
     */
    List<MeterReadNumHistory> queryMeterReadNumHistory(@Param("meterId")String meterId);

    /**
     * 查询可更换电表列表
     * @param meterId 原电表id
     * @param queryReplaceMeterInfo 关键字
     * @param prvId
     * @return
     */
	List<DatElectricMeterVO> selecReplaceMeterByPage(@Param("meterId")String meterId, @Param("queryReplaceMeterInfo")String queryReplaceMeterInfo, @Param("prvId")String prvId);

	/**
	 * 查询更换后电表相关信息
	 * @param datElectricMeterVO
	 * @return
	 */
	List<DatElectricMeterVO> queryReplaceMeter(DatElectricMeterVO datElectricMeterVO);

	/**
	 * 查询电表关联缴费信息
	 * @param datElectricMeterVO
	 * @return
	 */
	List<PaymentDateVO> queryPaymentsByMeter(DatElectricMeterVO datElectricMeterVO);

	/**
	* @Description: 根据电表id查询电表信息
	* <AUTHOR>
	* @date 2025年1月7日 下午2:31:48
	* @param meterId
	* @return
	*/
	DatElectricMeterVO queryMeterById(String meterId);

    /**
     * 获取报账点最后缴费期终
     *
     * @param billaccountId
     * @return
     */
    Date queryLastBillamountEndDate(String billaccountId);

    /**
     * 获取电表上次审核通过归零时间
     *
     * @param meterId
     * @return
     */
    Date queryLastResetDate(@Param("meterId")String meterId, @Param("timeName")String timeName);

    /**
     * @param datElectricMeterVO
     * @return
     * @Description: 置空电表更换信息
     * <AUTHOR>
     * @date 2025年1月9日 上午11:16:26
     */
	void clearReplaceInfo(DatElectricMeterVO datElectricMeterVO);

    /**
     * @Description: 查询电表作为更换电表的原电表信息
     * <AUTHOR>
     * @date 2025年1月9日 下午2:17:41
     * @param meterCode
	* @return
	*/
	List<DatElectricMeterVO> queryMaterMeter(String meterCode);

    /**
     * @Description: 解除旧电表与资源关联关系
     * <AUTHOR>
     * @date 2025年2月19日 上午9:35:16
     * @param meterId
	* @return
	*/
	void clearMeterRelation(@Param("meterId")String meterId);
}
