package com.xunge.dao.selfelec.inspect.province;

import com.xunge.model.basedata.DatAttachment;
import com.xunge.model.selfelec.inspect.province.EleInspect;
import com.xunge.model.selfelec.inspect.province.EleInspectImage;
import com.xunge.model.selfelec.inspect.province.EleInspectWeb;

import java.util.List;
import java.util.Map;

public interface EleInspectImageMapper {

    /**
     * 新增ele_inspect_image
     *
     * @param eleInspectImage 新增数据
     */
    void insertEleInspectImage(EleInspectImage eleInspectImage);

    /**
     * 更新ele_inspect_image
     *
     * @param eleInspectImage 更新数据
     */
    void updateEleInspectImage(EleInspectImage eleInspectImage);

    /**
     * 获取数据
     *
     * @param eleInspectImage 查询条件
     * @return 结果
     */
    List<EleInspectImage> getEleInspectImageByEleInspectImage(EleInspectImage eleInspectImage);

    /**
     * 页面获取稽核结果
     *
     * @param eleInspect 查询条件
     * @return 结果
     */
    List<EleInspectWeb> queryEleInspectInfoForWeb(EleInspect eleInspect);

    /**
     * 缴费查询附件使用
     *
     * @param paramMap
     * @return
     */
    List<DatAttachment> queryDatAttachmentByParam(Map<String, Object> paramMap);
}
