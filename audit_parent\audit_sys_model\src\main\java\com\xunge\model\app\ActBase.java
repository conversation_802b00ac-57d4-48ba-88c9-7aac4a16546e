package com.xunge.model.app;

import com.xunge.model.basedata.DatAttachment;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 给手机APP返回的基本信息bean
 * @date 2019/3/14 10:34
 */
public class ActBase implements Serializable {

    /**
     * 任务ID
     */
    private String taskId;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 流程实例ID
     */
    private String procInsId;

    /**
     * 业务主表
     */
    private String businessTable;

    /**
     * 业务主表ID
     */
    private String businessId;

    /**
     * 流程类型枚举值
     */
    private String tableEnum;

    /**
     * 流程类型枚举值
     */
    private String tableEnums;


    /**
     * 标题
     */
    private String title;

    /**
     * 流程类型
     */
    private String flowType;

    /**
     * 业务主键
     */
    private String businessKey;

    /**
     * 主键对应的名称
     */
    private String businessName;

    /**
     * 上级发起人
     */
    private String superior;

    /**
     * 发起时间
     */
    private String superDate;

    /**
     * 地区
     */
    private String regName;

    /**
     * 摘要
     */
    private String summary;

    /**
     * 审核状态
     */
    private String auditState;

    /**
     * 省份编码
     */
    private String prvCode;

    private String appDegreeNum;

    private String regId;

    private String pregId;
    /**
     * 附件列表
     */
    private List<DatAttachment> attachmentList;

    public String getBusinessName() {
        return businessName;
    }

    public void setBusinessName(String businessName) {
        this.businessName = businessName;
    }

    public String getAuditState() {
        return auditState;
    }

    public void setAuditState(String auditState) {
        this.auditState = auditState;
    }

    public String getTableEnum() {
        return tableEnum;
    }

    public void setTableEnum(String tableEnum) {
        this.tableEnum = tableEnum;
    }

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public String getTaskName() {
        return taskName;
    }

    public void setTaskName(String taskName) {
        this.taskName = taskName;
    }

    public String getProcInsId() {
        return procInsId;
    }

    public void setProcInsId(String procInsId) {
        this.procInsId = procInsId;
    }

    public String getBusinessTable() {
        return businessTable;
    }

    public void setBusinessTable(String businessTable) {
        this.businessTable = businessTable;
    }

    public String getBusinessId() {
        return businessId;
    }

    public void setBusinessId(String businessId) {
        this.businessId = businessId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getFlowType() {
        return flowType;
    }

    public void setFlowType(String flowType) {
        this.flowType = flowType;
    }

    public String getBusinessKey() {
        return businessKey;
    }

    public void setBusinessKey(String businessKey) {
        this.businessKey = businessKey;
    }

    public String getSuperior() {
        return superior;
    }

    public void setSuperior(String superior) {
        this.superior = superior;
    }

    public String getSuperDate() {
        return superDate;
    }

    public void setSuperDate(String superDate) {
        this.superDate = superDate;
    }

    public String getRegName() {
        return regName;
    }

    public void setRegName(String regName) {
        this.regName = regName;
    }

    public String getSummary() {
        return summary;
    }

    public void setSummary(String summary) {
        this.summary = summary;
    }

    public List<DatAttachment> getAttachmentList() {
        return attachmentList;
    }

    public void setAttachmentList(List<DatAttachment> attachmentList) {
        this.attachmentList = attachmentList;
    }

    public String getPrvCode() {
        return prvCode;
    }

    public void setPrvCode(String prvCode) {
        this.prvCode = prvCode;
    }

    public String getTableEnums() {
        return tableEnums;
    }

    public void setTableEnums(String tableEnums) {
        this.tableEnums = tableEnums;
    }

    public String getAppDegreeNum() {
        return appDegreeNum;
    }

    public void setAppDegreeNum(String appDegreeNum) {
        this.appDegreeNum = appDegreeNum;
    }

    public String getRegId() {
        return regId;
    }

    public void setRegId(String regId) {
        this.regId = regId;
    }

    public String getPregId() {
        return pregId;
    }

    public void setPregId(String pregId) {
        this.pregId = pregId;
    }
}
