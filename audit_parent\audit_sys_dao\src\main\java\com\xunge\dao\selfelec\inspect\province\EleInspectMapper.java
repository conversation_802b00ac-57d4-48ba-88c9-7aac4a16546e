package com.xunge.dao.selfelec.inspect.province;

import com.xunge.model.selfelec.inspect.common.image.ImageLine;
import com.xunge.model.selfelec.inspect.province.EleInspect;
import com.xunge.model.selfelec.inspect.province.request.InforApply;
import com.xunge.model.selfelec.inspect.province.request.MeterLine;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * ele_inspect
 */
public interface EleInspectMapper {

    /**
     * 新增ele_inspect
     *
     * @param eleInspect 新增数据
     */
    void insertEleInspect(EleInspect eleInspect);

    /**
     * 更新ele_inspect
     *
     * @param eleInspect 更新参数
     */
    void updateEleInspectByEleInspect(EleInspect eleInspect);

    /**
     * 稽核报账基础数据信息及返回结果(审核开始)
     *
     * @param eleInspect 查询条件
     * @return 结果
     */
    List<EleInspect> getEleInspectByEleInspect(EleInspect eleInspect);

    /**
     * 获取主数据(后付费)
     *
     * @param billaccountpaymentdetailId 缴费单id
     * @return 结果
     */
    InforApply getInforApply(String billaccountpaymentdetailId);

    /**
     * 获取电表信息(后付费)
     *
     * @param billaccountpaymentdetailId 缴费单id
     * @return 结果
     */
    List<MeterLine> getMeterLine(String billaccountpaymentdetailId);

    /**
     * 获取图片信息
     *
     * @param businessIdList 缴费单id
     * @param businessType   业务类型
     * @return 结果
     */
    List<ImageLine> getImageLine(@Param("businessIdList") List<String> businessIdList, @Param("businessType") Integer businessType);


    /**
     * 获取主数据（核销）
     *
     * @param billaccountpaymentdetailId 缴费单id
     * @return 结果
     */
    InforApply getInforApplyVerification(String billaccountpaymentdetailId);

    /**
     * 获取电表信息（核销）
     *
     * @param billaccountpaymentdetailId 缴费单id
     * @return 结果
     */
    List<MeterLine> getMeterLineVerification(String billaccountpaymentdetailId);

    /**
     * 更新点击时间，是否有效标识
     *
     * @param eleInspect 更新数据
     */
    void updateEleInspectForWeb(EleInspect eleInspect);

    List<InforApply> getPaymentCodes(@Param("list") List<String> ids);

    List<InforApply> getVerificationCodes(@Param("list") List<String> ids);

    Integer getPaymentAuditState(@Param("billaccountpaymentdetailId") String billaccountpaymentdetailId);

    Integer getVerificationAuditState(@Param("verification_id") String verification_id);

    void updateReturnContent(@Param("returnContent") String returnContent, @Param("paymentCode") String paymentCode);

    List<InforApply> getTelePaymentCodes(@Param("list") List<String> ids);

}
