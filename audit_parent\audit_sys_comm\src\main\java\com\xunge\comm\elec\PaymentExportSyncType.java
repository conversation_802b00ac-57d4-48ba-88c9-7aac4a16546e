package com.xunge.comm.elec;

import com.xunge.core.util.NumberUtils;
import com.xunge.model.payment.PaymentExportCsvData;
import com.xunge.model.selfelec.VEleBillaccountPaymentInfo;
import com.xunge.model.selfelec.eleverificate.VEleBillaccountVerificateInfo;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;

/**
 * @创建人 LiXs
 * @创建时间 2019/10/25
 * @描述： 异步导出缴费明细类型
 */
public class PaymentExportSyncType {
    public static Logger logger = LoggerFactory.getLogger(PaymentExportSyncType.class);
    /**
     * 后付费缴费
     */
    public final static String PAYMENT_SYNC = "BZDDFDC";
    public final static String PAYMENT_SYNC_KAFKA = "BZDDFDC_KAFKA";
    public final static String PAYMENT_SYNC_TYPE = "后付费缴费明细导出";
    public final static String PAYMENT_RESULT_INFO = "电费缴费明细导出";


    /**
     * 预付费缴费
     */
    public final static String LOAN_SYNC = "LOAN_SYNC";
    public final static String LOAN_SYNC_TYPE = "预付费缴费明细导出";
    public final static String LOAN_RESULT_INFO = "预付费电费缴费明细导出";

    /**
     * 核销缴费
     */
    public final static String VERIFICATION_SYNC = "VERIFICATION_SYNC";
    public final static String VERIFICATION_SYNC_TYPE = "核销缴费明细导出";
    public final static String VERIFICATION_RESULT_INFO = "核销电费缴费明细导出";

    /**
     * 特殊缴费
     */
    public final static String SPECIAL_SYNC = "SPECIAL_SYNC";
    public final static String SPECIAL_SYNC_TYPE = "特殊缴费明细导出";
    public final static String SPECIAL_RESULT_INFO = "特殊电费缴费明细导出";

    /**
     * 特殊缴费
     */
    public final static String NEW_ENERGY_SYNC = "NEW_ENERGY_SYNC";
    public final static String NEW_ENERGY_SYNC_TYPE = "新能源缴费明细导出";
    public final static String NEW_ENERGY_RESULT_INFO = "新能源电费缴费明细导出";

    /**
     * 塔维特殊缴费
     */
    public final static String TPAYMENT_SPECIAL_ASYNC = "TPAYMENT_SPECIAL_ASYNC";
    public final static String TPAYMENT_SPECIAL_ASYNC_TYPE = "塔维特殊缴费明细导出";
    public final static String TPAYMENT_SPECIAL_RESULT_INFO = "特殊电费缴费明细导出";

    /**
     * 解析异常类型
     * @param overProof 异常类型
     * @return 多种类型用逗号分割
     */
    public static String parseOverProof(Integer overProof){
        if(overProof == null){
            return "-";
        }
        List<Integer> overProofs = NumberUtils.parseNumber2(overProof);
        StringBuilder str = new StringBuilder();
        String temp;
        for (Integer proof : overProofs) {
            switch (proof) {
                case 2:
                    temp = "电损占比超标，";
                    break;
                case 4:
                    temp = "其他费用超标，";
                    break;
                case 8:
                    temp = "实际分摊比例环比增大超限，";
                    break;
                case 16:
                    temp = "实际分摊比例环比减小超限，";
                    break;
                case 32:
                    temp = "用电成本超限，";
                    break;
                case 64:
                    temp = "共享数量与分摊比例不匹配，";
                    break;
                case 256:
                    temp = "换了电表，";
                    break;
                case 512:
                    temp = "系统计算金额与实际值差异超限，";
                    break;
                case 1024:
                    temp = "实际报账金额超限，";
                    break;
                case 2048:
                    temp = "历史缴费管控，";
                    break;
                case 4096:
                    temp = "入网前或退网后或工程状态资源缴费，";
                    break;
                case 8192:
                    temp = "生产用电拆分异常，";
                    break;
                case 16384:
                    temp = "缴费周期不连续，";
                    break;
                case 32768:
                    temp = "电表读数不连续，";
                    break;
                case 65536:
                    temp = "首次报账电表读数不为0，";
                    break;
                case 524288:
                    temp = "缴费超过合同约定周期，";
                    break;
                default:
                    temp = "";
            }
            str.append(temp);
        }
        if(StringUtils.isNotEmpty(str)){
            str.deleteCharAt(str.length()-1);
        }else {
            return "-";
        }
        return str.toString();
    }

    /**
     * 解析超标类型
     * 标杆类型 0-历史电费标杆-同比；1-历史电费标杆-环比；2-额定功率标杆；3-智能电表标杆；4-动环负载标杆；7-平峰谷均价标杆；8-历史日均电量标杆-同比; 9-历史日均电量标杆-环比
     * @param paybenchmarkType 超标类型,可能重复(1,2,3,1,2,3)
     * @return 超标类型逗号分割序列
     */
    public static String parsePaybenchmarkType(String paybenchmarkType){
        if(StringUtils.isEmpty(paybenchmarkType)){
            return "-";
        }
        String[] types = paybenchmarkType.split(",");
        List<String> typeList = new ArrayList<>();
        String type;
        String firstType = types[0];//第一个标杆的类型
        for (int i = 0; i < types.length; i++) {
            if(i != 0 && firstType.equals(types[i])){
                break;
            }
            switch (types[i]){
                case "0" :
                    type = "历史电费标杆-同比";
                    break;
                case "1" :
                    type = "历史电费标杆-环比";
                    break;
                case "2" :
                    type = "额定功率标杆";
                    break;
                case "3" :
                    type = "智能电表标杆";
                    break;
                case "4" :
                    type = "动环负载标杆";
                    break;
                case "7" :
                    type = "平峰谷均价标杆";
                    break;
                case "8" :
                    type = "历史日均电量标杆-同比";
                    break;
                case "9" :
                    type = "历史日均电量标杆-环比";
                    break;
                case "10" :
                    type = "智能电表标杆（集采）";
                    break;
                default:
                    type = "-";
            }
            typeList.add(type);
        }
        StringBuilder sb = new StringBuilder();
        typeList.forEach(e->{
            sb.append(e).append("，");
        });
        if(StringUtils.isNotEmpty(sb)){
            sb.deleteCharAt(sb.toString().length()-1);
        }else {
            return "-";
        }
        return sb.toString();
    }
    /**
     * 解析超标率
     * @param paybenchmarkRate 超标类型率,可能重复
     * @return 超标率逗号分割序列
     */
    public static String parsePaybenchmarkRate(String paybenchmarkRate,Integer paybenchmarkTypeSize){
        if(StringUtils.isEmpty(paybenchmarkRate)){
            return "-";
        }
        StringBuilder sb = new StringBuilder();
        try{
            String[] types = paybenchmarkRate.split(",");
            for (int i = 0; i < types.length && i< paybenchmarkTypeSize; i++) {
                if(!"-".equals(types[i])){
                    sb.append(new BigDecimal(types[i]).setScale(2, RoundingMode.HALF_UP)).append("% ，");
                }else {
                    sb.append(types[i]).append(" ，");
                }
            }
            if(StringUtils.isNotEmpty(sb)){
                sb.deleteCharAt(sb.toString().length()-1);
            }else {
                return "-";
            }
        }catch (Exception e){
            logger.error("解析超标率失败");
            e.printStackTrace();
            return "解析超标率失败";
        }
        return sb.toString();
    }

    public static void parsePaybenchmark(PaymentExportCsvData paymentExportCsvData, VEleBillaccountPaymentInfo paymentDetail) {
        String paybenchmarkType = paymentDetail.getPaybenchmarkType();
        String paybenchmarkRate = paymentDetail.getPaybenchmarkRate();
        String paybenchmarkOverflow = paymentDetail.getPaybenchmarkOverflow();
        if (StringUtils.isNotBlank(paybenchmarkType) && StringUtils.isNotBlank(paybenchmarkRate) && StringUtils.isNotBlank(paybenchmarkOverflow)) {
            String[] types = paybenchmarkType.split(",");
            String[] rates = paybenchmarkRate.split(",");
            String[] overflows = paybenchmarkOverflow.split(",");

            String firstType = types[0]; // 第一个标杆的类型
            for (int i = 0; i < types.length; i++) {
                if (i != 0 && firstType.equals(types[i])) {
                    break;
                }
                switch (types[i]) {
                    case "0":
                        // type = "历史电费标杆-同比";
                        if ("0".equals(overflows[i])) {
                            paymentExportCsvData.setHisEleYearBenchmark("否");
                        } else if ("1".equals(overflows[i])) {
                            paymentExportCsvData.setHisEleYearBenchmark("-".equals(rates[i]) ? "-" :
                                    "-" + new BigDecimal(rates[i]).setScale(2, BigDecimal.ROUND_HALF_UP).toPlainString() + "%");
                        } else {
                            paymentExportCsvData.setHisEleYearBenchmark("-".equals(rates[i]) ? "-" :
                                    new BigDecimal(rates[i]).setScale(2, BigDecimal.ROUND_HALF_UP).toPlainString() + "%");
                        }
                        break;
                    case "1":
                        // type = "历史电费标杆-环比";
                        if ("0".equals(overflows[i])) {
                            paymentExportCsvData.setHisEleMonthBenchmark("否");
                        } else if ("1".equals(overflows[i])) {
                            paymentExportCsvData.setHisEleMonthBenchmark("-".equals(rates[i]) ? "-" :
                                    "-" + new BigDecimal(rates[i]).setScale(2, BigDecimal.ROUND_HALF_UP).toPlainString() + "%");
                        } else {
                            paymentExportCsvData.setHisEleMonthBenchmark("-".equals(rates[i]) ? "-" :
                                    new BigDecimal(rates[i]).setScale(2, BigDecimal.ROUND_HALF_UP).toPlainString() + "%");
                        }
                        break;
                    case "2":
                        // type = "额定功率标杆";
                        if ("0" .equals(overflows[i])) {
                            paymentExportCsvData.setPowerratingBenchmark("否");
                        }  else if ("1".equals(overflows[i])) {
                            paymentExportCsvData.setPowerratingBenchmark("-".equals(rates[i]) ? "-" :
                                    "-" +new BigDecimal(rates[i]).setScale(2, BigDecimal.ROUND_HALF_UP).toPlainString() + "%");
                        } else {
                            paymentExportCsvData.setPowerratingBenchmark("-".equals(rates[i]) ? "-" :
                                    new BigDecimal(rates[i]).setScale(2, BigDecimal.ROUND_HALF_UP).toPlainString() + "%");
                        }
                        break;
                    case "3":
                        // type = "智能电表标杆";
                        if ("0" .equals(overflows[i])) {
                            paymentExportCsvData.setElectricmeterRingBenchmark("否");
                        } else if ("1".equals(overflows[i])) {
                            paymentExportCsvData.setElectricmeterRingBenchmark("-".equals(rates[i]) ? "-" :
                                    "-" + new BigDecimal(rates[i]).setScale(2, BigDecimal.ROUND_HALF_UP).toPlainString() + "%");
                        } else {
                            paymentExportCsvData.setElectricmeterRingBenchmark("-".equals(rates[i]) ? "-" :
                                    new BigDecimal(rates[i]).setScale(2, BigDecimal.ROUND_HALF_UP).toPlainString() + "%");
                        }
                        break;
                    case "4":
                        //  type = "动环负载标杆";
                        if ("0".equals(overflows[i])) {
                            paymentExportCsvData.setPowerloadBenchmark("否");
                        } else if ("1".equals(overflows[i])) {
                            paymentExportCsvData.setPowerloadBenchmark("-".equals(rates[i]) ? "-" :
                                    "-" + new BigDecimal(rates[i]).setScale(2, BigDecimal.ROUND_HALF_UP).toPlainString() + "%");
                        } else {
                            paymentExportCsvData.setPowerloadBenchmark("-".equals(rates[i]) ? "-" :
                                    new BigDecimal(rates[i]).setScale(2, BigDecimal.ROUND_HALF_UP).toPlainString() + "%");
                        }
                        break;
                    case "7":
                        // type = "平峰谷均价标杆";
                        if ("0".equals(overflows[i])) {
                            paymentExportCsvData.setPriceAvgNotaxBenchmark("否");
                        } else if ("1".equals(overflows[i])) {
                            paymentExportCsvData.setPriceAvgNotaxBenchmark("-".equals(rates[i]) ? "-" :
                                    "-" + new BigDecimal(rates[i]).setScale(2, BigDecimal.ROUND_HALF_UP).toPlainString() + "%");
                        } else {
                            paymentExportCsvData.setPriceAvgNotaxBenchmark("-".equals(rates[i]) ? "-" :
                                    new BigDecimal(rates[i]).setScale(2, BigDecimal.ROUND_HALF_UP).toPlainString() + "%");
                        }
                        break;
                    case "8":
                        // type = "历史日均电量标杆-同比";
                        if ("0".equals(overflows[i])) {
                            paymentExportCsvData.setHisDayDegreeYearBenchmark("否");
                        } else if ("1".equals(overflows[i])) {
                            paymentExportCsvData.setHisDayDegreeYearBenchmark("-".equals(rates[i]) ? "-" :
                                    "-" + new BigDecimal(rates[i]).setScale(2, BigDecimal.ROUND_HALF_UP).toPlainString() + "%");
                        } else {
                            paymentExportCsvData.setHisDayDegreeYearBenchmark("-".equals(rates[i]) ? "-" :
                                    new BigDecimal(rates[i]).setScale(2, BigDecimal.ROUND_HALF_UP).toPlainString() + "%");
                        }
                        break;
                    case "9":
                        // type = "历史日均电量标杆-环比";
                        if ("0".equals(overflows[i])) {
                            paymentExportCsvData.setHisDayDegreeMonthBenchmark("否");
                        } else if ("1".equals(overflows[i])) {
                            paymentExportCsvData.setHisDayDegreeMonthBenchmark("-".equals(rates[i]) ? "-" :
                                    "-" + new BigDecimal(rates[i]).setScale(2, BigDecimal.ROUND_HALF_UP).toPlainString() + "%");
                        } else {
                            paymentExportCsvData.setHisDayDegreeMonthBenchmark("-".equals(rates[i]) ? "-" :
                                    new BigDecimal(rates[i]).setScale(2, BigDecimal.ROUND_HALF_UP).toPlainString() + "%");
                        }
                        break;
                    case "10":
                        // type = "智能电表标杆（集采）";
                        if ("0".equals(overflows[i])) {
                            paymentExportCsvData.setElectricmeterInBenchmark("否");
                        } else if ("1".equals(overflows[i])) {
                            paymentExportCsvData.setElectricmeterInBenchmark("-".equals(rates[i]) ? "-" :
                                    "-" + new BigDecimal(rates[i]).setScale(2, BigDecimal.ROUND_HALF_UP).toPlainString() + "%");
                        } else {
                            paymentExportCsvData.setElectricmeterInBenchmark("-".equals(rates[i]) ? "-" :
                                    new BigDecimal(rates[i]).setScale(2, BigDecimal.ROUND_HALF_UP).toPlainString() + "%");
                        }
                        break;
                    default:
                        break;
                }

            }
        } else {
            paymentExportCsvData.setHisEleYearBenchmark("-");
//                 "历史电费标杆-环比";
            paymentExportCsvData.setHisEleMonthBenchmark("-");
//                 "额定功率标杆";
            paymentExportCsvData.setPowerratingBenchmark("-");
//                    "智能电表标杆";
            paymentExportCsvData.setElectricmeterRingBenchmark("-");
//                    "动环负载标杆";
            paymentExportCsvData.setPowerloadBenchmark("-");
//                  "平峰谷均价标杆";
//            paymentExportCsvData.setPriceAvgNotaxBenchmark("-");
//                  "历史日均电量标杆-同比";
            paymentExportCsvData.setHisDayDegreeYearBenchmark("-");
//              "历史日均电量标杆-环比";
            paymentExportCsvData.setHisDayDegreeMonthBenchmark("-");
            //  "智能电表标杆（集采）";
            paymentExportCsvData.setElectricmeterInBenchmark("-");
        }
    }

    public static void parsePaybenchmark(VEleBillaccountVerificateInfo info) {
        String paybenchmarkType = info.getPaybenchmarkType();
        String paybenchmarkRate = info.getPaybenchmarkRate();
        String paybenchmarkOverflow = info.getPaybenchmarkOverflow();
        if (StringUtils.isNotBlank(paybenchmarkType) && StringUtils.isNotBlank(paybenchmarkRate) && StringUtils.isNotBlank(paybenchmarkOverflow)) {
            String[] types = paybenchmarkType.split(",");
            String[] rates = paybenchmarkRate.split(",");
            String[] overflows = paybenchmarkOverflow.split(",");

            String firstType = types[0]; // 第一个标杆的类型
            for (int i = 0; i < types.length; i++) {
                if (i != 0 && firstType.equals(types[i])) {
                    break;
                }
                switch (types[i]) {
                    case "0":
                        // type = "历史电费标杆-同比";
                        if ("0".equals(overflows[i])) {
                            info.setHisEleYearBenchmark("否");
                            // 低于下限
                        } else if ("1".equals(overflows[i])) {
                            info.setHisEleYearBenchmark("-".equals(rates[i]) ? "-" :
                                    "-" + new BigDecimal(rates[i]).setScale(2, BigDecimal.ROUND_HALF_UP).toPlainString() + "%");
                        } else {
                            info.setHisEleYearBenchmark("-".equals(rates[i]) ? "-" :
                                    new BigDecimal(rates[i]).setScale(2, BigDecimal.ROUND_HALF_UP).toPlainString() + "%");
                        }
                        break;
                    case "1":
                        // type = "历史电费标杆-环比";
                        if ("0" .equals(overflows[i])) {
                            info.setHisEleMonthBenchmark("否");
                        }  else if ("1".equals(overflows[i])) {
                            info.setHisEleMonthBenchmark("-".equals(rates[i]) ? "-" :
                                    "-" + new BigDecimal(rates[i]).setScale(2, BigDecimal.ROUND_HALF_UP).toPlainString() + "%");
                        }else {
                            info.setHisEleMonthBenchmark("-".equals(rates[i]) ? "-" :
                                    new BigDecimal(rates[i]).setScale(2, BigDecimal.ROUND_HALF_UP).toPlainString() + "%");
                        }
                        break;
                    case "2":
                        // type = "额定功率标杆";
                        if ("0" .equals(overflows[i])) {
                            info.setPowerratingBenchmark("否");
                        } else if ("1".equals(overflows[i])) {
                            info.setPowerratingBenchmark("-".equals(rates[i]) ? "-" :
                                    "-" +  new BigDecimal(rates[i]).setScale(2, BigDecimal.ROUND_HALF_UP).toPlainString() + "%");
                        }else {
                            info.setPowerratingBenchmark("-".equals(rates[i]) ? "-" :
                                    new BigDecimal(rates[i]).setScale(2, BigDecimal.ROUND_HALF_UP).toPlainString() + "%");
                        }
                        break;
                    case "3":
                        // type = "智能电表标杆";
                        if ("0" .equals(overflows[i])) {
                            info.setElectricmeterRingBenchmark("否");
                        } else if ("1".equals(overflows[i])) {
                            info.setElectricmeterRingBenchmark("-".equals(rates[i]) ? "-" :
                                    "-" + new BigDecimal(rates[i]).setScale(2, BigDecimal.ROUND_HALF_UP).toPlainString() + "%");
                        }else {
                            info.setElectricmeterRingBenchmark("-".equals(rates[i]) ? "-" :
                                    new BigDecimal(rates[i]).setScale(2, BigDecimal.ROUND_HALF_UP).toPlainString() + "%");
                        }
                        break;
                    case "4":
                        //  type = "动环负载标杆";
                        if ("0".equals(overflows[i])) {
                            info.setPowerloadBenchmark("否");
                        } else if ("1".equals(overflows[i])) {
                            info.setPowerloadBenchmark("-".equals(rates[i]) ? "-" :
                                    "-" + new BigDecimal(rates[i]).setScale(2, BigDecimal.ROUND_HALF_UP).toPlainString() + "%");
                        }else {
                            info.setPowerloadBenchmark("-".equals(rates[i]) ? "-" :
                                    new BigDecimal(rates[i]).setScale(2, BigDecimal.ROUND_HALF_UP).toPlainString() + "%");
                        }
                        break;
                    case "7":
                        // type = "平峰谷均价标杆";
                        if ("0".equals(overflows[i])) {
                            info.setPriceAvgNotaxBenchmark("否");
                        } else if ("1".equals(overflows[i])) {
                            info.setPriceAvgNotaxBenchmark("-".equals(rates[i]) ? "-" :
                                    "-" + new BigDecimal(rates[i]).setScale(2, BigDecimal.ROUND_HALF_UP).toPlainString() + "%");
                        } else {
                            info.setPriceAvgNotaxBenchmark("-".equals(rates[i]) ? "-" :
                                    new BigDecimal(rates[i]).setScale(2, BigDecimal.ROUND_HALF_UP).toPlainString() + "%");
                        }
                        break;
                    case "8":
                        // type = "历史日均电量标杆-同比";
                        if ("0".equals(overflows[i])) {
                            info.setHisDayDegreeYearBenchmark("否");
                        } else if ("1".equals(overflows[i])) {
                            info.setHisDayDegreeYearBenchmark("-".equals(rates[i]) ? "-" :
                                    "-" + new BigDecimal(rates[i]).setScale(2, BigDecimal.ROUND_HALF_UP).toPlainString() + "%");
                        } else {
                            info.setHisDayDegreeYearBenchmark("-".equals(rates[i]) ? "-" :
                                    new BigDecimal(rates[i]).setScale(2, BigDecimal.ROUND_HALF_UP).toPlainString() + "%");
                        }
                        break;
                    case "9":
                        // type = "历史日均电量标杆-环比";
                        if ("0".equals(overflows[i])) {
                            info.setHisDayDegreeMonthBenchmark("否");
                        } else if ("1".equals(overflows[i])) {
                            info.setHisDayDegreeMonthBenchmark("-".equals(rates[i]) ? "-" :
                                    "-" + new BigDecimal(rates[i]).setScale(2, BigDecimal.ROUND_HALF_UP).toPlainString() + "%");
                        }else {
                            info.setHisDayDegreeMonthBenchmark("-".equals(rates[i]) ? "-" :
                                    new BigDecimal(rates[i]).setScale(2, BigDecimal.ROUND_HALF_UP).toPlainString() + "%");
                        }
                        break;
                    case "10":
                        // type = "智能电表标杆（集采）";
                        if ("0".equals(overflows[i])) {
                            info.setElectricmeterInBenchmark("否");
                        } else if ("1".equals(overflows[i])) {
                            info.setElectricmeterInBenchmark("-".equals(rates[i]) ? "-" :
                                    "-" +  new BigDecimal(rates[i]).setScale(2, BigDecimal.ROUND_HALF_UP).toPlainString() + "%");
                        } else {
                            info.setElectricmeterInBenchmark("-".equals(rates[i]) ? "-" :
                                    new BigDecimal(rates[i]).setScale(2, BigDecimal.ROUND_HALF_UP).toPlainString() + "%");
                        }
                        break;
                    default:
                        break;
                }

            }
        } else {
            info.setHisEleYearBenchmark("-");
//                 "历史电费标杆-环比";
            info.setHisEleMonthBenchmark("-");
//                 "额定功率标杆";
            info.setPowerratingBenchmark("-");
//                    "智能电表标杆";
            info.setElectricmeterRingBenchmark("-");
//                    "动环负载标杆";
            info.setPowerloadBenchmark("-");
//                  "平峰谷均价标杆";
//            info.setPriceAvgNotaxBenchmark("-");
//                  "历史日均电量标杆-同比";
            info.setHisDayDegreeYearBenchmark("-");
//              "历史日均电量标杆-环比";
            info.setHisDayDegreeMonthBenchmark("-");
            //  "智能电表标杆（集采）";
            info.setElectricmeterInBenchmark("-");
        }
    }

}
