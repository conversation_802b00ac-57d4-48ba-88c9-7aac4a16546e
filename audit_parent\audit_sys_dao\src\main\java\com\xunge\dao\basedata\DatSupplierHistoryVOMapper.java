package com.xunge.dao.basedata;

import com.xunge.model.basedata.DatSupplierHistoryVO;

public interface DatSupplierHistoryVOMapper {
    int deleteByPrimaryKey(String supplierId);

    int insert(DatSupplierHistoryVO record);

    int insertSelective(DatSupplierHistoryVO record);

    DatSupplierHistoryVO selectByPrimaryKey(String supplierId);

    int updateByPrimaryKeySelective(DatSupplierHistoryVO record);

    int updateByPrimaryKey(DatSupplierHistoryVO record);

    int generatingHistory(String supplierId);
}