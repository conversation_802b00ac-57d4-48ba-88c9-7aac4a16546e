package com.xunge.dao.selfelec;

import com.xunge.model.selfelec.EleOtherBillamountFinance;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Version: 1.0.0
 * @Author: <PERSON>
 * @Email: <EMAIL>
 * @Time: 2019-07-02 10:39
 * @Description:
 */
public interface EleOtherBillamountFinanceMapper {
    /**
     * 查询对应缴费单主键的所有其它费用集合
     *
     * @param billaccountpaymentdetailId 核销缴费单主键
     * @return 集合
     */
    List<EleOtherBillamountFinance> queryOtherAmountFinanceList(@Param("billaccountpaymentdetailId") String billaccountpaymentdetailId);

    int insert(EleOtherBillamountFinance eleOtherBillamountFinance);

    /**
     * 根据汇总单ID删除明细信息
     *
     * @param billamountId
     */
    int deleteByBillamountId(String billamountId);

    /**
     * 根据汇总单详情ID删除明细信息
     *
     * @param billamountDetailId
     */
    int deleteByBillamountDetailId(String billamountDetailId);
}
