package com.xunge.dao.selfelec.eleverificate;

import com.xunge.model.selfelec.eleverificate.EleVerificateBenchmark;
import com.xunge.model.selfelec.verification.ElecVerificationBenchmarkInfo;

import java.util.List;
import java.util.Map;

public interface IEleVerificateBenchmarkDao {
    /**
     * 条件查询缴费标杆信息
     *
     * @param paramMap
     * @return
     * <AUTHOR>
     */
    public List<EleVerificateBenchmark> queryAllByForginKey(Map<String, Object> paramMap);

    /**
     * 缴费标杆信息新增
     *
     * @param list
     * @return
     * <AUTHOR>
     */
    public int insertBenchmarkInfo(List<EleVerificateBenchmark> paramMap);

    /**
     * 删除缴费标杆数据
     *
     * @param param
     * @return
     * <AUTHOR>
     */
    public int delVerificateBenchmark(String verificationId);

    /**
     * 需要补录的核销单数据
     */
    public List<ElecVerificationBenchmarkInfo> getRecorBenchmarkData();

}
