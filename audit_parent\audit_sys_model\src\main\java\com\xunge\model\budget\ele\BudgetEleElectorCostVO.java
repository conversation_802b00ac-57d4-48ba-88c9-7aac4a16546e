package com.xunge.model.budget.ele;

import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;


/**
 * <AUTHOR> LiangCheng
 * @date : 2022-07-04
 * @desc 用电成本表
 */
@Data
@ToString
public class BudgetEleElectorCostVO implements Serializable {
    /**
     * 省份ID
     */
    private String prvId;

    /**
     * 省份名称
     */
    private String prvName;

    /**
     * 年份
     */
    private Integer onYear;

    /**
     * 站点类型
     */
    private Double siteType;

    /**
     * 直供+转供电费系统或集团建议值
     */
    private BigDecimal amountGroup;

    /**
     * 直供+转供电费实际值
     */
    private BigDecimal amountActual;

    /**
     * 直供+转供电费备注
     */
    private String amountRemark;

    /**
     * 直供+转供电量系统或集团建议值
     */
    private BigDecimal degreeGroup;

    /**
     * 直供+转供电量实际值
     */
    private BigDecimal degreeActual;

    /**
     * 直供+转供电量备注
     */
    private String degreeRemark;

    /**
     * 系统计算用电成本系统或集团建议值
     */
    private BigDecimal electroCostsGroup;

    /**
     * 系统计算用电成本实际值
     */
    private BigDecimal electroCostsActual;

    /**
     * 系统计算用电成本备注
     */
    private String electroCostsRemark;

    /**
     * 次年用电成本系统或集团建议值
     */
    private BigDecimal followElectroCostsGroup;

    /**
     * 次年用电成本实际值
     */
    private BigDecimal followElectroCostsActual;

    /**
     * 次年用电成本备注
     */
    private String followElectroCostsRemark;

    /**
     * 包干电费系统或集团建议值
     */
    private BigDecimal includeAmountGroup;

    /**
     * 包干电费实际值
     */
    private BigDecimal includeAmountActual;

    /**
     * 包干电费备注
     */
    private String includeAmountRemark;

    /**
     * 电费合计系统或集团建议值
     */
    private BigDecimal amountTotalGroup;

    /**
     * 电费合计实际值
     */
    private BigDecimal amountTotalActual;

    /**
     * 数据节点
     */
    private Integer nodeType;

    /**
     * 基站影响因子
     */
    private BudgetEleBasestationFactorVO basestationFactorVO;

    private String workOrderId;
}

