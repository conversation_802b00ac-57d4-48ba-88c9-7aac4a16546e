package com.xunge.dto.selfelec.contract;

import com.xunge.dto.selfelec.PageInfo;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2021/12/21 14:26
 */
@Getter
@Setter
public class ContractQueryDto extends PageInfo implements Serializable {
    private static final long serialVersionUID = 6911969938800557927L;
    /**
     * 合同名称或合同编号 模糊搜索
     */
    private String contractItem;
    private String pregId;
    private String regId;
    /**
     * 大集中合同状态
     */
    private String contractStatus;
    /**
     * 审核状态
     */
    private String auditState;
    private String draftType;
    /**
     * 推送状态
     */
    private String pushState;
    private String stayAuditingUser;
    private int isDownShare = 1;
    private String supplierItem;

    /**
     * 供电类型
     */
    private Integer supplyMethod;

    /**
     * 单价类型
     */
    private Integer priceType;

    /**
     * 购电方式
     */
    private Integer buyMethod;

    /**
     * 是否有电损
     */
    private Integer includeLoss;

    private String conRemainderAmount;

    /**
     * 合同来源 合同批量导入省侧接口3，批量导入修改后4
     */
    private Integer contractFrom;
}
