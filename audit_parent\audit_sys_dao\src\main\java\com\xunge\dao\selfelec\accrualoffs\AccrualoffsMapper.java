package com.xunge.dao.selfelec.accrualoffs;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import com.xunge.model.selfelec.billamount.AccrualSecondBillamountDto;
import org.apache.ibatis.annotations.Param;

import com.xunge.model.selfelec.accrualBillamount.AccrualBillamount;
import com.xunge.model.selfelec.accrualoffs.AccrualOffs;
import com.xunge.model.selfelec.accrualoffs.AccrualOffsDetail;
import com.xunge.model.system.user.SysUserVO;

public interface AccrualoffsMapper {

	/** 
	* @Description: 查询可冲销汇总单
	* <AUTHOR>   
	* @date 2023年8月18日 下午3:18:04 
	* @param params 查询参数
	* @return  
	*/ 
	List<AccrualBillamount> queryBillamountsMain(Map<String, Object> params);

	/** 
	* @Description: 查询二次汇总明细
	* <AUTHOR>   
	* @date 2023年8月22日 上午10:47:14 
	* @param params 查询参数
	* @return  
	*/ 
	List<AccrualOffsDetail> querySecondDetail(Map<String, Object> params);

	/** 
	* @Description: 查询冲销基本信息
	* <AUTHOR>   
	* @date 2023年8月23日 上午10:49:51 
	* @param offsId 冲销id
	* @return  
	*/ 
	AccrualOffs queryAccrualOffs(@Param("offsId") String offsId);

	/** 
	* @Description: 查询表中最大编码
	* <AUTHOR>   
	* @date 2023年8月23日 下午2:31:45 
	* @param param 查询参数
	* @return  
	*/ 
	Map<String, Object> queryMaxCode(Map<String, Object> param);

	/** 
	* @Description: 查询冲销单冲销总金额
	* <AUTHOR>   
	* @date 2023年8月23日 下午2:39:13 
	* @param list 汇总单id
	* @return  
	*/ 
	BigDecimal queryTotalOffsAmount(List<String> list);

	/** 
	* @Description: 根据编码查询冲销信息
	* <AUTHOR>   
	* @date 2023年8月24日 下午3:18:45 
	* @param offsCode 冲销编码
	* @return  
	*/ 
	AccrualOffs queryOffsByCode(@Param("offsCode") String offsCode);

	/** 
	* @Description: 新增冲销单
	* <AUTHOR>   
	* @date 2023年8月24日 下午3:39:50 
	* @param offs 冲销单信息
	*/ 
	void insertOffs(AccrualOffs offs);

	/** 
	* @Description: 新增冲销明细
	* <AUTHOR>   
	* @date 2023年8月24日 下午3:55:15 
	* @param details 明细行
	*/ 
	void insertOffsDetail(List<AccrualOffsDetail> details);

	/** 
	* @Description: 更新冲销单
	* <AUTHOR>   
	* @date 2023年8月24日 下午4:09:10 
	* @param offs  冲销单信息
	*/ 
	void updateOffs(AccrualOffs offs);

	/** 
	* @Description: 删除冲销明细
	* <AUTHOR>   
	* @date 2023年8月24日 下午4:09:28 
	* @param offsId  冲销单id
	*/ 
	void deleteOffsDetail(@Param("offsId") String offsId);

	/** 
	* @Description: 查询冲销单列表
	* <AUTHOR>   
	* @date 2023年8月25日 下午3:54:55 
	* @param params 查询参数
	* @return  
	*/ 
	List<AccrualOffs> queryOffsList(Map<String, Object> params);

	/** 
	* @Description: 查询冲销明细
	* <AUTHOR>   
	* @date 2023年8月28日 上午9:34:12 
	* @param params
	* @return  
	*/ 
	List<AccrualOffsDetail> queryOffsDetail(Map<String, Object> params);

	/** 
	* @Description: 根据id查询冲销单
	* <AUTHOR>   
	* @date 2023年8月30日 下午2:14:37 
	* @param offsIds 冲销id
	* @return  
	*/ 
	List<AccrualOffs> queryOffsListByIds(List<String> offsIds);

	/** 
	* @Description: 删除冲销明细
	* <AUTHOR>   
	* @date 2023年8月30日 下午2:15:02 
	* @param offsIds 冲销单id
	*/ 
	void deleteOffsDetails(List<String> offsIds);

	/** 
	* @Description: 删除冲销单
	* <AUTHOR>   
	* @date 2023年8月30日 下午2:15:20 
	* @param offsIds 冲销单id
	*/ 
	void deleteOffss(List<String> offsIds);

	/** 
	* @Description: 还原二次汇总单金额
	* <AUTHOR>   
	* @date 2023年8月30日 下午2:15:35 
	* @param offsIds  冲销单id
	*/ 
	void returnSecondBillamounts(List<String> offsIds);

	/** 
	* @Description: 还原汇总单金额
	* <AUTHOR>   
	* @date 2023年8月30日 下午2:16:48 
	* @param offsIds  冲销单id
	*/ 
	void returnBillamounts(List<String> offsIds);

	/** 
	* @Description: 还原二次汇总历史计提单金额
	* <AUTHOR>   
	* @date 2023年8月30日 下午2:45:17 
	* @param offsIds  冲销单id
	*/ 
	void returnSecondHisBillamounts(List<String> offsIds);

	/** 
	* @Description: 更新汇总单冲销金额
	* <AUTHOR>   
	* @date 2023年8月30日 下午2:51:24 
	* @param billamountIds  汇总单id
	*/ 
	void updateBillamountOffs(List<String> billamountIds);

	/** 
	* @Description: 更新二次汇总单冲销金额
	* <AUTHOR>   
	* @date 2023年8月30日 下午2:53:26 
	* @param secondBillamountIds 二次汇总单id
	*/ 
	void updateSecondBillamountOffs(List<String> secondBillamountIds);

	/** 
	* @Description: 更新二次汇总单历史计提冲销金额
	* <AUTHOR>   
	* @date 2023年8月30日 下午2:55:00 
	* @param secondBillamountIds  
	*/ 
	void updateSecondHisBillamountOffs(List<String> secondBillamountIds);

	/** 
	* @Description: 累提累冲判断是否被其他冲销单使用
	* <AUTHOR>   
	* @date 2023年9月1日 下午4:14:34 
	* @param secondBillamountIds 二次汇总id
	* @return  
	*/ 
	List<AccrualOffsDetail> queryIsOtherOffs(List<String> secondBillamountIds);
	
	/**
	 * 更新冲销单
	 * @param offs 冲销信息
	 * @return
	 */
	int updateOffsByOffsCode(AccrualOffs offs);

	/**
	 * 根据财务返回单号更新冲销单财务审核状态
	 * @param offs
	 * @return
	 */
	int updateOffsByClaimNum(AccrualOffs offs);

	/**
	 * 查询用户公司信息
	 * @param userId 用户id
	 * @return
	 */
	List<SysUserVO> queryClaimCompany(String userId);

	List<SysUserVO> queryClaimCompanyIneffectiveOrNot(Map<String, Object> map);

/*	*//**
	 * 查询用户公司信息
	 * @param userCode 用户工号
	 * @return
	 *//*
	List<SysUserVO> queryClaimCompanyByList(List<String> userCodes);*/

	/** 
	* @Description: 查询汇总单报账人
	* <AUTHOR>   
	* @date 2023年9月4日 下午2:45:24 
	* @param ids 汇总单id
	* @return  
	*/ 
	AccrualOffs queryCalimUser(List<String> ids);

	/**
	 * 导出
	 * @param params 参数
	 * @return
	 */
	List<AccrualOffsDetail> exportOffs(Map<String, Object> params);

	/**
	 * 查询报账人信息
	 * @param map 参数
	 * @return
	 */
	List<AccrualOffs> selectUserByPage(Map<String, Object> map);

	/**
	 * 更新累计冲销金额
	 * @param offsDetail
	 * @return
	 */
	int updateTotalOffsAmount(AccrualOffsDetail offsDetail);

	/**
	 * 更新累计冲销金额
	 * @param offsDetail
	 * @return
	 */
	int updateTotalOffsAmountHis(AccrualOffsDetail offsDetail);

	/**
	 * 查询冲销金额
	 * @param offsCode
	 * @return
	 */
	List<AccrualOffsDetail> selectOffsDetailAmount(String offsCode);

	/**
	 * 增加累计冲销金额后，更新冲销明细状态，防止重复计算
	 * @param ad
	 */
	void updatePaymentOffDetail(AccrualOffsDetail ad);

	/**
	 * 增加累计冲销金额后，更新冲销明细状态，防止重复计算
	 * @param ad
	 */
	void updateVerificationOffDetail(AccrualOffsDetail ad);

	List<AccrualSecondBillamountDto> selectAccrualSecondDetail(@Param("billamountId") String billamountId,@Param("secondBillamountId") String secondBillamountId);

	List<AccrualBillamount> queryBillamountCode(@Param("businessId") String businessId,@Param("businessType") String businessType,@Param("billamountCodes") List<String> billamountCodes);

	List<AccrualSecondBillamountDto> queryOffsDetailByOffsId(String offsId);
}