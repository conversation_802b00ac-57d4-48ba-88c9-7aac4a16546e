package com.xunge.dao.selfrent.billaccount.impl;

import com.xunge.dao.AbstractBaseDao;
import com.xunge.dao.selfrent.billaccount.IVBaseresourcePaymentdetailDao;
import com.xunge.model.selfrent.billAccount.VBaseresourcePaymentdetailVO;

import java.util.List;
import java.util.Map;

public class BaseresourcePaymentdetailDaoImpl extends AbstractBaseDao implements IVBaseresourcePaymentdetailDao {

    final String Namespace = "com.xunge.mapping.VBaseresourcePaymentdetailVOMapper.";

    @Override
    public List<VBaseresourcePaymentdetailVO> queryBaseresourcePaymentdetail(Map<String, Object> map) {
        return this.getSqlSession().selectList(Namespace + "queryBaseresourcePaymentdetail", map);
    }

    @Override
    public List<VBaseresourcePaymentdetailVO> queryBaseTowerPaymentdetail(Map<String, Object> map) {
        return this.getSqlSession().selectList(Namespace + "queryBaseTowerPaymentdetail", map);
    }

}
