package com.xunge.dao.basedata.ring;

import com.xunge.dto.selfelec.AuthorityUser;
import com.xunge.model.basedata.ring.PowerPerfVO;
import com.xunge.model.basedata.ring.PowerPerfVOExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface PowerPerfVOMapper {

    int countByExample(PowerPerfVOExample example);

    int deleteByExample(PowerPerfVOExample example);

    int deleteByPrimaryKey(String powerId);

    int insert(PowerPerfVO record);

    int insertSelective(PowerPerfVO record);

    List<PowerPerfVO> selectByExample(PowerPerfVOExample example);

    PowerPerfVO selectByPrimaryKey(String powerId);

    int updateByExampleSelective(@Param("record") PowerPerfVO record, @Param("example") PowerPerfVOExample example);

    int updateByExample(@Param("record") PowerPerfVO record, @Param("example") PowerPerfVOExample example);

    int updateByPrimaryKeySelective(PowerPerfVO record);

    int updateByPrimaryKey(PowerPerfVO record);

    boolean batchInsert(List<PowerPerfVO> datas);

    boolean delByCuidsAndPrvid(Map<String, Object> map);

    List<PowerPerfVO> selectByCondition(@Param("author")AuthorityUser author, @Param("powerPerfVO") PowerPerfVO powerPerfVO);
}