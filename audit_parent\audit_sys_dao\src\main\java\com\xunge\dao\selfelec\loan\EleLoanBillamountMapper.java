package com.xunge.dao.selfelec.loan;

import com.xunge.model.selfelec.loan.EleLoanBillamount;
import com.xunge.model.selfelec.loan.EleLoanBillamountExample;
import com.xunge.model.selfelec.loan.EleLoanBillamountVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface EleLoanBillamountMapper {
    int countByExample(EleLoanBillamountExample example);

    int deleteByExample(EleLoanBillamountExample example);

    int deleteByPrimaryKey(String billamountId);

    int insert(EleLoanBillamount record);

    int insertSelective(EleLoanBillamount record);

    List<EleLoanBillamount> selectByExample(EleLoanBillamountExample example);

    EleLoanBillamount selectByPrimaryKey(String billamountId);

    int updateByExampleSelective(@Param("record") EleLoanBillamount record, @Param("example") EleLoanBillamountExample example);

    int updateByExample(@Param("record") EleLoanBillamount record, @Param("example") EleLoanBillamountExample example);

    int updateByPrimaryKeySelective(EleLoanBillamount record);

    int updateByPrimaryKey(EleLoanBillamount record);

    List<EleLoanBillamountVo> selectEleLoanBillamountList(Map<String, Object> params);

    List<EleLoanBillamountVo> queryEleLoanBillAmount(Map<String, Object> params);
//    List<EleLoanBillamountVo> queryEleLoanBillAmount(@Param("billAmountIdList") List<String> billAmountIdList);

    int deleteByPrimaryKeys(@Param("ids") List<String> ids);

    EleLoanBillamount getEleLoanBillamountByode(@Param("billamountCode") String billamountCode);

    EleLoanBillamount queryEleLoanBillamountById(Map<String, Object> maps);

    /**
     * @Title: queryInfoByLoanCode @Description: TODO(这里用一句话描述这个方法的作用) @param @param
     * billamountCode @param @return 设定文件 @return List<String> 返回类型 @throws
     */

    List<String> queryInfoByLoanCode(@Param("billamountCode") String billamountCode);

    /**
     * @Title: getEleLoanBillamountCode @Description: TODO(根据核销信息查询关联的预付费单号) @param @param
     * maps @param @return 设定文件 @return List<EleLoanBillamount> 返回类型 @throws
     */

    List<String> getEleLoanBillamountCode(Map<String, Object> maps);

    List<EleLoanBillamount> getCodeByVerificationId(@Param("verificationId") String verificationId);

    List<EleLoanBillamountVo> selectEleLoanExportList(Map<String, Object> params);

    List<EleLoanBillamountVo> selectEleLoanExportListFocus(Map<String, Object> params);


    /**
     * @param @param supplierId
     * @param @param supplierIds    设定文件
     * @return void    返回类型
     * @throws
     * @Title: updateSupplierInfo
     * @Description: TODO(这里用一句话描述这个方法的作用)
     */

    void updateSupplierInfo(@Param("supplierId") String supplierId, @Param("supplierIds") List<String> supplierIds);


    /**
     * @param @param contractId
     * @param @param contractIds    设定文件
     * @return void    返回类型
     * @throws
     * @Title: updateComtractInfo
     * @Description: TODO(这里用一句话描述这个方法的作用)
     */

    void updateComtractInfo(@Param("contractId") String contractId, @Param("contractIds") List<String> contractIds);

    List<EleLoanBillamount> getEleLoanBillamount(@Param("loanCodes") String[] loanCodes);

    int rebuildBillamount(EleLoanBillamount record);

    int rebuildBillamountdetail(EleLoanBillamount record);

    int rebuildBillamountPayment(EleLoanBillamount record);

    List<String> selectBillamountidSForExport(Map<String, Object> params);

    List<EleLoanBillamount> selectBillamountByIds(Map<String, Object> params);
}