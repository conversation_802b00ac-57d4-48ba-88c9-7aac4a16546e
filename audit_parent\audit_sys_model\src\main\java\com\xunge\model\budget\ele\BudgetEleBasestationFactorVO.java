package com.xunge.model.budget.ele;

import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR> LiangCheng
 * @date : 2022-07-04
 * @desc 基站影响因子表
 */
@Data
@ToString
public class BudgetEleBasestationFactorVO implements Serializable {
    /**
     * 省份
     */
    private String prvId;

    /**
     * 省份名称
     */
    private String prvName;

    /**
     * 年份
     */
    private Integer onYear;

    /**
     * 基站-4G主设备日均能耗系统或集团建议值
     */
    private BigDecimal g4Group;

    /**
     * 基站-4G主设备日均能耗实际值
     */
    private BigDecimal g4Actual;

    /**
     * 基站-4G主设备日均能耗备注
     */
    private String g4Remark;

    /**
     * 基站-5G 700M主设备日均能耗系统或集团建议值
     */
    private BigDecimal g5700mGroup;

    /**
     * 基站-5G 700M主设备日均能耗实际值
     */
    private BigDecimal g5700mActual;

    /**
     * 基站-5G 700M主设备日均能耗备注
     */
    private String g5700mRemark;

    /**
     * 基站-5G 2.6G主设备日均能耗系统或集团建议值
     */
    private BigDecimal g526gGroup;

    /**
     * 基站-5G 2.6G主设备日均能耗实际值
     */
    private BigDecimal g526gActual;

    /**
     * 基站-5G 2.6G主设备日均能耗备注
     */
    private String g526gRemark;

    /**
     * 节点
     */
    private Integer nodeType;

    private String workOrderId;

    private String auditInfoId;
}

