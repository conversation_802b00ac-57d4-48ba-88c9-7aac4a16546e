package com.xunge.dao.system.usernew;

import com.xunge.model.system.user.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Mapper
public interface SysUserNewMapper {

    /**
     * 查询用户信息
     * @param paramMap 查询参数集
     * @return 用户数据
     */
    List<SysUserVO> querySysUser(Map<String, Object> paramMap);

    /**
     * 查询门户未关联或者三费已关联数据
     * @param searchType 查询类型
     * @param searchName 查询信息
     * @return 用户数据
     */
    List<SysUserVO> findUsersBySearchMsg(@Param("searchType") String searchType, @Param("searchName")String searchName);

    /**
     * 查询门户用户必要字段
     * @param userCode
     * @return
     */
    List<SysUserVO> queryUniUser(@Param("userCode") String userCode);

    /**
     * 判断手机号码唯一性
     * @param userPhone 手机号
     * @param userId 用户主键
     * @return 条数
     */
    int getDataByPhoneNumber(@Param("userPhone")String userPhone, @Param("userId")String userId);

    /**
     * 判断SMAP账号唯一性
     * @param smapId SMAP账号
     * @param userId 用户主键
     * @return 条数
     */
    int getDataBySmapId(@Param("smapId")String smapId, @Param("userId")String userId);

    /**
     * 判断用户工号唯一性
     * @param userCode 用户工号
     * @param userId 用户主键
     * @return 条数
     */
    int getDataByUserCode(@Param("userCode")String userCode, @Param("userId")String userId);

    /**
     * 根据老员工编号查询统一门户用户信息
     * @param sysUserVOList 三费用户
     * @return 关联统一门户数据
     */
    List<SysStaffUser> queryStaffUserData(@Param("sysUserVOList") List<SysUserVO> sysUserVOList);

    /**
     * 更新用户表sys_user
     * @param userCode 用户工号
     * @param smapId SMAP账号
     * @param userPhone 手机号
     * @param userEmail 邮箱
     * @param userName 用户名
//     * @param upState 启停用状态
//     * @param userState 用户状态
     * @return int
     */
    int updateSysUser(@Param("userCode") String userCode,@Param("smapId") String smapId,
                      @Param("userPhone") String userPhone,@Param("userEmail")  String userEmail,
                      @Param("userName")String userName);

    /**
     * 判断用户登录账号唯一性
     * @param userLoginname 用户登录账号
     * @param userId 用户主键
     * @return 条数
     */
    int getDataByUserLoginname(@Param("userLoginname") String userLoginname, @Param("userId") String userId);

    /**
     * 更新某个用户的各状态字段
     * @param userId
     * @param upState
     * @param userState
     * @param auditState
     * @return
     */
    int updateUserStateByUserId(@Param("userId")String userId, @Param("upState")Integer upState,
                                @Param("userState")Integer userState,@Param("auditState")Integer auditState);

    /**
     * 查询某个用户的各状态字段
     * @param userId
     * @return
     */
    SysUserVO selectUserStateByUserId(@Param("userId") String userId);

    /**
     * 新增用户流转环节信息
     * @param sysUserAuditProcess
     * @return
     */
    int insertUserProcessInfo(SysUserAuditProcess sysUserAuditProcess);

    /**
     * 更新环节结束时间、处理结果
     * @param endTime
     * @param operateResult
     * @return
     */
    int updateProcessResult(@Param("userId") String userId,@Param("endTime") Date endTime,@Param("operateResult") String operateResult,@Param("operateOpinion") String operateOpinion);

    /**
     * 查询环节信息，并按时间顺序返回
     * @param userId
     * @return
     */
    List<SysUserAuditProcess> queryUserProcessInfo(@Param("userId") String userId);

    /**
     * 根据用户选择角色查询角色大类得关联
     * @param userVO 用户角色数据
     * @return
     */
    List<SysRoleTypeVO> queryRoleDataByIds(SysUserVO userVO);

    /**
     * 用户管理审核导出
     * @param map
     * @return
     */
    List<SysUserExportVO> querySysUserExportData(Map<String, Object> map);

    SysMdmUserVO selectMdmUserInfoBySmapId(String smapId);

    List<SysMdmUserOrgVO> selectMdmUserOrgByEmployeeNumber(String employeeNumber);

    /**
     * app账号审批查详情
     * @param map
     * @return
     */
    SysUserVO querySysUserDetailById(Map<String, Object> map);

    /**
     * app代办查用户
     * @param paraMap
     * @return
     */
    SysUserVO queryBeanById(Map<String, Object> paraMap);


    List<SysUserVO> checkUserByRoleNameAndUserId(Map<String, Object> paraMap);
}
