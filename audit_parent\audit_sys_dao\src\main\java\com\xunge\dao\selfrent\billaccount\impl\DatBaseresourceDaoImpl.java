package com.xunge.dao.selfrent.billaccount.impl;

import com.xunge.dao.AbstractBaseDao;
import com.xunge.dao.selfrent.billaccount.IDatBaseresourceDao;
import com.xunge.model.selfrent.billAccount.DatBaseresourceVO;
import com.xunge.model.selfrent.billAccount.DatBaseresourcesVO;

import java.util.List;
import java.util.Map;

public class DatBaseresourceDaoImpl extends AbstractBaseDao implements IDatBaseresourceDao {

    final String Namespace = "com.xunge.mapping.DatBaseresourceVOMapper.";

    @Override
    public List<DatBaseresourceVO> queryDatBaseresourceByBillAccountId(String billAccountId) {
        // TODO Auto-generated method stub
        return this.getSqlSession().selectList(Namespace + "queryDatBaseresourceByBillAccountId", billAccountId);
    }

    @Override
    public List<DatBaseresourcesVO> queryBaseresourceByAccountId(String billAccountId) {
        return this.getSqlSession().selectList(Namespace + "queryBaseresourceByAccountId", billAccountId);
    }


    @Override
    public List<DatBaseresourceVO> queryOldResVO(Map<String, Object> map) {
        return this.getSqlSession().selectList(Namespace + "queryOldResVO", map);
    }

}
