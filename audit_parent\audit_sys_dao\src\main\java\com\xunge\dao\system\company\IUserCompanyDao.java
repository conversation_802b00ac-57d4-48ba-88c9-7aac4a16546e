package com.xunge.dao.system.company;


import com.xunge.model.system.company.UserCompany;
import com.xunge.model.system.company.UserCompanyQuery;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户公司Dao
 */
public interface IUserCompanyDao {

    /**
     * 查询用户、公司、部门对应List
     * @param userCompanyQuery
     * @return
     */
    List<UserCompany> queryUserCompany(UserCompanyQuery userCompanyQuery);


    /**
     * 查询不同用户的共同公司
     * @param userIdList 不同用户idList
     * @param userSize 不同用户数量
     * @param userState 用户状态
     * @return
     */
    List<String> queryCommonCompanysByUserIds(@Param("userIdList") List<String> userIdList,
                                              @Param("userSize") int userSize,
                                              @Param("userState") Integer userState);

    /**
     * 根据用户ID查询当前用户是否正常
     * @param userId 用户ID
     * @return int
     */
    int queryUserByUserId(@Param("userId") String userId);
}