package com.xunge.dao.selfrent.billaccount;

import com.xunge.core.page.Page;
import com.xunge.model.selfelec.EleResourceRelationHistory;
import com.xunge.model.selfelec.ElecontractRelationHistory;
import com.xunge.model.selfrent.billAccount.BillAccountVO;
import com.xunge.model.selfrent.billamount.RentBillAmountHisPayVo;
import com.xunge.model.selfrent.contract.RentWarningVO;
import com.xunge.model.towerrent.settlement.BillCheckPraceFeeRentVO;
import com.xunge.model.towerrent.settlement.BillCheckPraceFeeVO;

import java.util.List;
import java.util.Map;

public interface IBillAccountDao {
    /**
     * 查询所有报账点信息并显示分页
     *
     * <AUTHOR>
     */
    public Page<BillAccountVO> queryBillAccountVO(Map<String, Object> map, int pageNumber, int pageSize);

    public Page<BillAccountVO> queryFinanceBillAccountVO(Map<String, Object> map, int pageNumber, int pageSize);

    /**
     * 根据报账点id查询报账点对象
     *
     * <AUTHOR>
     */
    public BillAccountVO queryBillAccountById(String billAccountId);

    /**
     * 查询租费报账点关系图
     *
     * @param billaccountId
     * @return
     */
    public List<Map<String, Object>> queryBillaccountRelations(Map<String, Object> paraMap);

    /**
     * @description 分页查询租费缴费预警
     * <AUTHOR>
     * @date 创建时间：2017年11月2日
     */
    public Page<RentWarningVO> queryAllbillaccountWarning(Map<String, Object> paraMap, int pageNumber, int pageSize);

    /**
     * 根据报账点id查询关联的合状态
     *
     * @param paraMap
     * @return
     */
    public Map<String, Object> queryContractStateByBillaccountId(Map<String, Object> paraMap);

    /**
     * 根据报账点id查询关联的资源点状态
     *
     * @param billaccountId
     * @return
     */
    public List<Map<String, Object>> queryResourceStateByBillaccountId(String billaccountId);

    /**
     * 根据报账点id查询关联的铁塔状态
     *
     * @param billaccountId
     * @return
     */
    public List<Map<String, Object>> queryTowerStateByBillaccountId(String billaccountId);

    /**
     * @description 查询所有租费缴费预警
     * <AUTHOR>
     * @date 创建时间：2017年11月2日
     */
    public List<RentWarningVO> queryAllbillaccountWarning(Map<String, Object> map);

    /**
     * 根据租费报账点id更新计划缴费日期
     *
     * @param map
     * @return
     */
    public int updateRentBillPlanDate(Map<String, Object> map);

    void updateRentBillaccountPlanDate(Map<String, String> map);

    /**
     * 根据缴费id查询缴费当时的合同，供应商
     *
     * @param map
     * @return
     */
    public Map<String, Object> queryOldCS(Map<String, Object> map);

    /**
     * 根据缴费id查询最新的合同，供应商
     *
     * @param map
     * @return
     */
    public Map<String, Object> queryCS(Map<String, Object> map);

    /**
     * 根据缴费id查询缴费当时的机房id
     *
     * @param map
     * @return
     */
    public List<String> queryOldRes(Map<String, Object> map);

    /**
     * 根据缴费id查询最新的机房id
     *
     * @param map
     * @return
     */
    public List<String> queryRes(Map<String, Object> map);

    /**
     * 根据省份id查询当前省内的所有地市区县id以及name
     *
     * @param prvId
     * @return
     */
    public List<Map<String, Object>> queryRegIdAndNameByPrvId(String prvId);

    /**
     * 根据省份id查询未被关联的合同信息
     *
     * @param prvId
     * @return
     */
    public List<Map<String, Object>> queryUnusedContractByPrvId(String prvId);

    /**
     * 根据省份id查询未被关联的资源点信息
     *
     * @param prvId
     * @return
     */
    public List<Map<String, Object>> queryUnusedResourceByPrvId(String prvId);

    /**
     * 批量入库导入租费报账点
     *
     * @param map
     * @return
     */
    public int insertImportBillAcount(Map<String, Object> map);

    /**
     * 批量入库报账点与合同关系
     *
     * @param map
     * @return
     */
    public int insertImportBillcontract(Map<String, Object> map);

    /**
     * 批量入库报账点与资源关系
     *
     * @param map
     * @return
     */
    public int insertImportBillresource(Map<String, Object> map);

    /**
     * 根据报账点id查询关联的铁塔信息
     *
     * @param billaccountId
     * @return
     */
    List<Map<String, Object>> queryBillaccountTowerById(String billaccountId);

    List<BillAccountVO> queryBillaccountByPaymentIds(Map<String, Object> map);

    public List<ElecontractRelationHistory> selContractHistory(Map<String, Object> map);

    public List<EleResourceRelationHistory> queryResourceHistory(Map<String, Object> map);

    public List<EleResourceRelationHistory> queryRentTowerHistory(Map<String, Object> map);


    /**
     * 查询报账点关联资源点的tower_site_code信息
     *
     * @param billaccountId
     * @return
     */
    List<String> queryDatBaseResourceToTowerSiteCode(String billaccountId);

    /**
     * 查询报账点关联铁塔的tower_site_code信息
     *
     * @param billaccountId
     * @return
     */
    List<String> queryDatBaseTowerToTowerSiteCode(String billaccountId);

    /**
     * 查询报账点关联资源点的tower_site信息
     *
     * @param billaccountId
     * @return
     */
    List<BillCheckPraceFeeRentVO> queryDatBaseResourceToTowerSite(String billaccountId);

    /**
     * 查询报账点关联铁塔的tower_site信息
     *
     * @param billaccountId
     * @return
     */
    List<BillCheckPraceFeeRentVO> queryDatBaseTowerToTowerSite(String billaccountId);


    /**
     * 查询关联资源信息
     *
     * @param map
     * @return
     */
    List<BillCheckPraceFeeRentVO> queryDatBaseResourceToRelationSource(Map<String, Object> map);

    List<BillCheckPraceFeeRentVO> queryAllTowerSite(Map<String, Object> map);

    List<BillCheckPraceFeeVO> queryTowerSiteWithPraceFeeNotZero(Map<String, Object> map);

    /**
     * 根据账期、站址编码查询场地费信息
     *
     * @return
     */
    List<BillCheckPraceFeeVO> queryTowerBillCheckPraceFee(Map<String, Object> map);

    List<BillCheckPraceFeeVO> queryRoomBillCheckPraceFee(Map<String, Object> map);

    List<BillCheckPraceFeeVO> queryTinyBillCheckPraceFee(Map<String, Object> map);

    List<BillCheckPraceFeeVO> queryNonstandBillCheckPraceFee(Map<String, Object> map);

    List<BillCheckPraceFeeVO> queryCheckPraceFeeUnionAll(Map<String, Object> map);

    RentBillAmountHisPayVo queryHisPayedTotalAmount(Map<String, Object> map);

    int checkPaymentStartTime(Map<String, Object> map);

    RentBillAmountHisPayVo queryContractId(Map<String, Object> map);


    int checkResourceDateOverLap(Map<String, Object> map);

    public Integer queryBillAccountIsMutilContract(String billAccountId);
}
