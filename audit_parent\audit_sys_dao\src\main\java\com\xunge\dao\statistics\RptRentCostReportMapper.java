package com.xunge.dao.statistics;


import com.xunge.model.statistics.RptRentCostReport;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface RptRentCostReportMapper {
    List<RptRentCostReport> queryJtList(@Param("type") Integer type, @Param("year") String year, @Param("month")String month);

    List<RptRentCostReport> queryPrvList(@Param("type")Integer type,@Param("prvId")String prvId,@Param("year")String year,@Param("month")String month);

}