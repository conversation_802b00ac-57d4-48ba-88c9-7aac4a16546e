package com.xunge.model.app.basenum;

/**
 * TODO: 描述信息
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2019/5/13 15:37
 */
public class RoomTypeEnum extends BaseEnum<String> {

    public static final RoomTypeEnum self = new RoomTypeEnum();

    private RoomTypeEnum() {
        super.putEnum("传输机房", "1");
        super.putEnum("无线机房", "2");
        super.putEnum("核心网机房", "3");
        super.putEnum("数据网机房", "4");
        super.putEnum("动力环境机房", "5");
        super.putEnum("用户机房", "6");
        super.putEnum("IDC机房", "7");
        super.putEnum("地下进线室", "8");
        super.putEnum("综合机房", "9");
        super.putEnum("接入机房", "10");
        super.putEnum("交换机房", "11");
        super.putEnum("网管机房", "12");
        super.putEnum("微波机房", "13");
        super.putEnum("MDC机房", "14");
        super.putEnum("MDF机房", "15");
        super.putEnum("新业务机房", "16");
        super.putEnum("室外综合机柜", "17");
        super.putEnum("备件机房", "18");
        super.putEnum("其他", "19");
        super.putEnum("自建砖混机房", "01");
        super.putEnum("自建框架机房", "02");
        super.putEnum("自建彩钢板机房", "03");
        super.putEnum("一体化机柜", "04");
        super.putEnum("租用机房", "05");
        super.putEnum("其他机房", "06");
        super.putEnum("RRU拉远", "07");
        super.putEnum("一体化机房", "08");
        super.putEnum("无机房", "-1");
    }
}
