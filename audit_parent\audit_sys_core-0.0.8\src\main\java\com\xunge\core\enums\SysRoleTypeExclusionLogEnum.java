package com.xunge.core.enums;

/**
 * 系统角色大类操作日志枚举值
 * <AUTHOR>
public class SysRoleTypeExclusionLogEnum {


    public static final String CREATE_ROLE_TYPE ="新增角色大类：";
    public static final String ADD_EXCLUSION_NOTE ="添加互斥角色：";
    public static final String UPDATE_ROLE_TYPE_NAME ="修改角色大类名称：";
    public static final String DELETE_EXCLUSION_NOTE ="删除互斥角色：";

    public static final OperateLogType operateLogType = new OperateLogType();

    public static class OperateLogType extends BaseEnum<Object> {
        /**
         * 新增
         */
        public static final String CREATE = "0";
        /**
         * 修改
         */
        public static final String UPDATE = "1";
        /**
         * 删除
         */
        public static final String  DELETE = "2";

        private static final long serialVersionUID = 1L;

        private OperateLogType() {
            super.putEnum(CREATE, "创建角色大类");
            super.putEnum(UPDATE, "修改角色大类");
            super.putEnum(DELETE, "删除角色大类");
        }
    }




}
